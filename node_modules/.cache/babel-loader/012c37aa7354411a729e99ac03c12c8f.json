{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/intersect/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/intersect/index.js", "mtime": 1757335237020}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["inserted", "el", "binding", "vnode", "window", "modifiers", "value", "_ref", "_typeof", "handler", "options", "observer", "IntersectionObserver", "entries", "arguments", "length", "undefined", "_observe", "_a", "context", "_uid", "isIntersecting", "_someInstanceProperty", "call", "entry", "quiet", "init", "once", "unbind", "Object", "observe", "unobserve", "Intersect"], "sources": ["../../../src/directives/intersect/index.ts"], "sourcesContent": ["import { VNodeDirective } from 'vue/types/vnode'\nimport { VNode } from 'vue'\n\ntype ObserveHandler = (\n  entries: IntersectionObserverEntry[],\n  observer: IntersectionObserver,\n  isIntersecting: boolean,\n) => void\n\ninterface ObserveVNodeDirective extends Omit<VNodeDirective, 'modifiers'> {\n  value?: ObserveHandler | { handler: ObserveHand<PERSON>, options?: IntersectionObserverInit }\n  modifiers?: {\n    once?: boolean\n    quiet?: boolean\n  }\n}\n\nfunction inserted (el: HTMLElement, binding: ObserveVNodeDirective, vnode: VNode) {\n  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) return\n\n  const modifiers = binding.modifiers || {}\n  const value = binding.value\n  const { handler, options } = typeof value === 'object'\n    ? value\n    : { handler: value, options: {} }\n  const observer = new IntersectionObserver((\n    entries: IntersectionObserverEntry[] = [],\n    observer: IntersectionObserver\n  ) => {\n    const _observe = el._observe?.[vnode.context!._uid]\n    if (!_observe) return // Just in case, should never fire\n\n    const isIntersecting = entries.some(entry => entry.isIntersecting)\n\n    // If is not quiet or has already been\n    // initted, invoke the user callback\n    if (\n      handler && (\n        !modifiers.quiet ||\n        _observe.init\n      ) && (\n        !modifiers.once ||\n        isIntersecting ||\n        _observe.init\n      )\n    ) {\n      handler(entries, observer, isIntersecting)\n    }\n\n    if (isIntersecting && modifiers.once) unbind(el, binding, vnode)\n    else _observe.init = true\n  }, options)\n\n  el._observe = Object(el._observe)\n  el._observe![vnode.context!._uid] = { init: false, observer }\n\n  observer.observe(el)\n}\n\nfunction unbind (el: HTMLElement, binding: ObserveVNodeDirective, vnode: VNode) {\n  const observe = el._observe?.[vnode.context!._uid]\n  if (!observe) return\n\n  observe.observer.unobserve(el)\n  delete el._observe![vnode.context!._uid]\n}\n\nexport const Intersect = {\n  inserted,\n  unbind,\n}\n\nexport default Intersect\n"], "mappings": ";;AAiBA,SAASA,QAATA,CAAmBC,EAAnB,EAAoCC,OAApC,EAAoEC,KAApE,EAAgF;EAC9E,IAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,EAAE,0BAA0BA,MAA5B,CAArC,EAA0E;EAE1E,IAAMC,SAAS,GAAGH,OAAO,CAACG,SAAR,IAAqB,EAAvC;EACA,IAAMC,KAAK,GAAGJ,OAAO,CAACI,KAAtB;EACA,IAAAC,IAAA,GAA6BC,OAAA,CAAOF,KAAP,MAAiB,QAAjB,GACzBA,KADyB,GAEzB;MAAEG,OAAO,EAAEH,KAAX;MAAkBI,OAAO,EAAE;IAA3B,CAFJ;IAAQD,OAAF,GAAAF,IAAA,CAAEE,OAAF;IAAWC,OAAA,GAAAH,IAAA,CAAAG,OAAA;EAGjB,IAAMC,QAAQ,GAAG,IAAIC,oBAAJ,CAAyB,YAGtC;IAAA,IAFFC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuC,EADC;IAAA,IAExCH,QAFwC,GAAAG,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;;IAIxC,IAAMC,QAAQ,GAAG,CAAAC,EAAA,GAAAjB,EAAE,CAACgB,QAAH,MAAW,IAAX,IAAWC,EAAA,WAAX,GAAW,MAAX,GAAWA,EAAA,CAAGf,KAAK,CAACgB,OAAN,CAAeC,IAAlB,CAA5B;IACA,IAAI,CAACH,QAAL,EAAe,OAFb,CAEoB;;IAEtB,IAAMI,cAAc,GAAGC,qBAAA,CAAAT,OAAO,EAAAU,IAAA,CAAPV,OAAO,EAAM,UAAAW,KAAK;MAAA,OAAIA,KAAK,CAACH,cAA5B;IAAA,EAAvB,CAJE,CAMF;IACA;;IACA,IACEZ,OAAO,KACL,CAACJ,SAAS,CAACoB,KAAX,IACAR,QAAQ,CAACS,IAFJ,CAAP,KAIE,CAACrB,SAAS,CAACsB,IAAX,IACAN,cADA,IAEAJ,QAAQ,CAACS,IANX,CADF,EASE;MACAjB,OAAO,CAACI,OAAD,EAAUF,QAAV,EAAoBU,cAApB,CAAP;IACD;IAED,IAAIA,cAAc,IAAIhB,SAAS,CAACsB,IAAhC,EAAsCC,MAAM,CAAC3B,EAAD,EAAKC,OAAL,EAAcC,KAAd,CAAN,CAAtC,KACKc,QAAQ,CAACS,IAAT,GAAgB,IAAhB;EACN,CA1BgB,EA0BdhB,OA1Bc,CAAjB;EA4BAT,EAAE,CAACgB,QAAH,GAAcY,MAAM,CAAC5B,EAAE,CAACgB,QAAJ,CAApB;EACAhB,EAAE,CAACgB,QAAH,CAAad,KAAK,CAACgB,OAAN,CAAeC,IAA5B,IAAoC;IAAEM,IAAI,EAAE,KAAR;IAAef,QAAA,EAAAA;EAAf,CAApC;EAEAA,QAAQ,CAACmB,OAAT,CAAiB7B,EAAjB;AACD;AAED,SAAS2B,MAATA,CAAiB3B,EAAjB,EAAkCC,OAAlC,EAAkEC,KAAlE,EAA8E;;EAC5E,IAAM2B,OAAO,GAAG,CAAAZ,EAAA,GAAAjB,EAAE,CAACgB,QAAH,MAAW,IAAX,IAAWC,EAAA,WAAX,GAAW,MAAX,GAAWA,EAAA,CAAGf,KAAK,CAACgB,OAAN,CAAeC,IAAlB,CAA3B;EACA,IAAI,CAACU,OAAL,EAAc;EAEdA,OAAO,CAACnB,QAAR,CAAiBoB,SAAjB,CAA2B9B,EAA3B;EACA,OAAOA,EAAE,CAACgB,QAAH,CAAad,KAAK,CAACgB,OAAN,CAAeC,IAA5B,CAAP;AACD;AAED,OAAO,IAAMY,SAAS,GAAG;EACvBhC,QADuB,EACvBA,QADuB;EAEvB4B,MAAA,EAAAA;AAFuB,CAAlB;AAKP,eAAeI,SAAf", "ignoreList": []}]}