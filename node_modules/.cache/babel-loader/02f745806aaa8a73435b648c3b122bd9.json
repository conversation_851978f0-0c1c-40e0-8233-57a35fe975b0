{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/bootable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/bootable/index.js", "mtime": 1757335237053}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gVXRpbGl0aWVzCmltcG9ydCB7IHJlbW92ZWQgfSBmcm9tICcuLi8uLi91dGlsL2NvbnNvbGUnOyAvLyBUeXBlcwoKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwovKioKICogQm9vdGFibGUKICogQG1peGluCiAqCiAqIFVzZWQgdG8gYWRkIGxhenkgY29udGVudCBmdW5jdGlvbmFsaXR5IHRvIGNvbXBvbmVudHMKICogTG9va3MgZm9yIGNoYW5nZSBpbiAiaXNBY3RpdmUiIHRvIGF1dG9tYXRpY2FsbHkgYm9vdAogKiBPdGhlcndpc2UgY2FuIGJlIHNldCBtYW51YWxseQogKi8KCi8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBWdWUuZXh0ZW5kKCkuZXh0ZW5kKHsKICBuYW1lOiAnYm9vdGFibGUnLAogIHByb3BzOiB7CiAgICBlYWdlcjogQm9vbGVhbgogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzQm9vdGVkOiBmYWxzZQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBoYXNDb250ZW50OiBmdW5jdGlvbiBoYXNDb250ZW50KCkgewogICAgICByZXR1cm4gdGhpcy5pc0Jvb3RlZCB8fCB0aGlzLmVhZ2VyIHx8IHRoaXMuaXNBY3RpdmU7CiAgICB9CiAgfSwKICB3YXRjaDogewogICAgaXNBY3RpdmU6IGZ1bmN0aW9uIGlzQWN0aXZlKCkgewogICAgICB0aGlzLmlzQm9vdGVkID0gdHJ1ZTsKICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqLwogICAgaWYgKCdsYXp5JyBpbiB0aGlzLiRhdHRycykgewogICAgICByZW1vdmVkKCdsYXp5JywgdGhpcyk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBzaG93TGF6eUNvbnRlbnQ6IGZ1bmN0aW9uIHNob3dMYXp5Q29udGVudChjb250ZW50KSB7CiAgICAgIHJldHVybiB0aGlzLmhhc0NvbnRlbnQgJiYgY29udGVudCA/IGNvbnRlbnQoKSA6IFt0aGlzLiRjcmVhdGVFbGVtZW50KCldOwogICAgfQogIH0KfSk7"}, {"version": 3, "names": ["removed", "<PERSON><PERSON>", "extend", "name", "props", "eager", "Boolean", "data", "isBooted", "computed", "<PERSON><PERSON><PERSON><PERSON>", "isActive", "watch", "created", "$attrs", "methods", "showLazyContent", "content", "$createElement"], "sources": ["../../../src/mixins/bootable/index.ts"], "sourcesContent": ["// Utilities\nimport { removed } from '../../util/console'\n\n// Types\nimport Vue, { VNode } from 'vue'\ninterface Toggleable extends Vue {\n  isActive?: boolean\n}\n\n/**\n * Bootable\n * @mixin\n *\n * Used to add lazy content functionality to components\n * Looks for change in \"isActive\" to automatically boot\n * Otherwise can be set manually\n */\n/* @vue/component */\nexport default Vue.extend<Vue & Toggleable>().extend({\n  name: 'bootable',\n\n  props: {\n    eager: Boolean,\n  },\n\n  data: () => ({\n    isBooted: false,\n  }),\n\n  computed: {\n    hasContent (): boolean | undefined {\n      return this.isBooted || this.eager || this.isActive\n    },\n  },\n\n  watch: {\n    isActive () {\n      this.isBooted = true\n    },\n  },\n\n  created () {\n    /* istanbul ignore next */\n    if ('lazy' in this.$attrs) {\n      removed('lazy', this)\n    }\n  },\n\n  methods: {\n    showLazyContent (content?: () => VNode[]): VNode[] {\n      return (this.hasContent && content) ? content() : [this.$createElement()]\n    },\n  },\n})\n"], "mappings": "AAAA;AACA,SAASA,OAAT,QAAwB,oBAAxB,C,CAEA;;AACA,OAAOC,GAAP,MAA2B,KAA3B;AAKA;;;;;;;AAOG;;AACH;;AACA,eAAeA,GAAG,CAACC,MAAJ,GAA+BA,MAA/B,CAAsC;EACnDC,IAAI,EAAE,UAD6C;EAGnDC,KAAK,EAAE;IACLC,KAAK,EAAEC;EADF,CAH4C;EAOnDC,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,QAAQ,EAAE;IADC,CAAP;EAAA,CAP6C;EAWnDC,QAAQ,EAAE;IACRC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKF,QAAL,IAAiB,KAAKH,KAAtB,IAA+B,KAAKM,QAA3C;IACD;EAHO,CAXyC;EAiBnDC,KAAK,EAAE;IACLD,QAAQ,WAARA,QAAQA,CAAA;MACN,KAAKH,QAAL,GAAgB,IAAhB;IACD;EAHI,CAjB4C;EAuBnDK,OAAO,WAAPA,OAAOA,CAAA;IACL;IACA,IAAI,UAAU,KAAKC,MAAnB,EAA2B;MACzBd,OAAO,CAAC,MAAD,EAAS,IAAT,CAAP;IACD;EACF,CA5BkD;EA8BnDe,OAAO,EAAE;IACPC,eAAe,WAAfA,eAAeA,CAAEC,OAAF,EAAyB;MACtC,OAAQ,KAAKP,UAAL,IAAmBO,OAApB,GAA+BA,OAAO,EAAtC,GAA2C,CAAC,KAAKC,cAAL,EAAD,CAAlD;IACD;EAHM;AA9B0C,CAAtC,CAAf", "ignoreList": []}]}