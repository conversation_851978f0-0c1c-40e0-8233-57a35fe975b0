{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSubheader/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSubheader/index.js", "mtime": 1757335236967}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZTdWJoZWFkZXIgZnJvbSAnLi9WU3ViaGVhZGVyJzsKZXhwb3J0IHsgVlN1YmhlYWRlciB9OwpleHBvcnQgZGVmYXVsdCBWU3ViaGVhZGVyOw=="}, {"version": 3, "names": ["VSubheader"], "sources": ["../../../src/components/VSubheader/index.ts"], "sourcesContent": ["import VSubheader from './VSubheader'\n\nexport { VSubheader }\nexport default VSubheader\n"], "mappings": "AAAA,OAAOA,UAAP,MAAuB,cAAvB;AAEA,SAASA,UAAT;AACA,eAAeA,UAAf", "ignoreList": []}]}