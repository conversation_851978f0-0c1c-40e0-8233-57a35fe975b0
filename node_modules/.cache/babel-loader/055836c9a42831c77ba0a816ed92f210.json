{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/createForOfIteratorHelper.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/createForOfIteratorHelper.js", "mtime": 1757335237616}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_Symbol", "_getIteratorMethod", "_Array$isArray", "unsupportedIterableToArray", "_createForOfIteratorHelper", "r", "e", "t", "length", "_n", "F", "s", "n", "done", "value", "f", "TypeError", "o", "a", "u", "call", "next", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/createForOfIteratorHelper.js"], "sourcesContent": ["import _Symbol from \"core-js-pure/features/symbol/index.js\";\nimport _getIteratorMethod from \"core-js-pure/features/get-iterator-method.js\";\nimport _Array$isArray from \"core-js-pure/features/array/is-array.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nfunction _createForOfIteratorHelper(r, e) {\n  var t = \"undefined\" != typeof _Symbol && _getIteratorMethod(r) || r[\"@@iterator\"];\n  if (!t) {\n    if (_Array$isArray(r) || (t = unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) {\n      t && (r = t);\n      var _n = 0,\n        F = function F() {};\n      return {\n        s: F,\n        n: function n() {\n          return _n >= r.length ? {\n            done: !0\n          } : {\n            done: !1,\n            value: r[_n++]\n          };\n        },\n        e: function e(r) {\n          throw r;\n        },\n        f: F\n      };\n    }\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var o,\n    a = !0,\n    u = !1;\n  return {\n    s: function s() {\n      t = t.call(r);\n    },\n    n: function n() {\n      var r = t.next();\n      return a = r.done, r;\n    },\n    e: function e(r) {\n      u = !0, o = r;\n    },\n    f: function f() {\n      try {\n        a || null == t[\"return\"] || t[\"return\"]();\n      } finally {\n        if (u) throw o;\n      }\n    }\n  };\n}\nexport { _createForOfIteratorHelper as default };"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,uCAAuC;AAC3D,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,yCAAyC;AACpE,OAAOC,0BAA0B,MAAM,iCAAiC;AACxE,SAASC,0BAA0BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAIC,CAAC,GAAG,WAAW,IAAI,OAAOP,OAAO,IAAIC,kBAAkB,CAACI,CAAC,CAAC,IAAIA,CAAC,CAAC,YAAY,CAAC;EACjF,IAAI,CAACE,CAAC,EAAE;IACN,IAAIL,cAAc,CAACG,CAAC,CAAC,KAAKE,CAAC,GAAGJ,0BAA0B,CAACE,CAAC,CAAC,CAAC,IAAIC,CAAC,IAAID,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,CAACG,MAAM,EAAE;MACrGD,CAAC,KAAKF,CAAC,GAAGE,CAAC,CAAC;MACZ,IAAIE,EAAE,GAAG,CAAC;QACRC,CAAC,GAAG,SAASA,CAACA,CAAA,EAAG,CAAC,CAAC;MACrB,OAAO;QACLC,CAAC,EAAED,CAAC;QACJE,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;UACd,OAAOH,EAAE,IAAIJ,CAAC,CAACG,MAAM,GAAG;YACtBK,IAAI,EAAE,CAAC;UACT,CAAC,GAAG;YACFA,IAAI,EAAE,CAAC,CAAC;YACRC,KAAK,EAAET,CAAC,CAACI,EAAE,EAAE;UACf,CAAC;QACH,CAAC;QACDH,CAAC,EAAE,SAASA,CAACA,CAACD,CAAC,EAAE;UACf,MAAMA,CAAC;QACT,CAAC;QACDU,CAAC,EAAEL;MACL,CAAC;IACH;IACA,MAAM,IAAIM,SAAS,CAAC,uIAAuI,CAAC;EAC9J;EACA,IAAIC,CAAC;IACHC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAG,CAAC,CAAC;EACR,OAAO;IACLR,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MACdJ,CAAC,GAAGA,CAAC,CAACa,IAAI,CAACf,CAAC,CAAC;IACf,CAAC;IACDO,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MACd,IAAIP,CAAC,GAAGE,CAAC,CAACc,IAAI,CAAC,CAAC;MAChB,OAAOH,CAAC,GAAGb,CAAC,CAACQ,IAAI,EAAER,CAAC;IACtB,CAAC;IACDC,CAAC,EAAE,SAASA,CAACA,CAACD,CAAC,EAAE;MACfc,CAAC,GAAG,CAAC,CAAC,EAAEF,CAAC,GAAGZ,CAAC;IACf,CAAC;IACDU,CAAC,EAAE,SAASA,CAACA,CAAA,EAAG;MACd,IAAI;QACFG,CAAC,IAAI,IAAI,IAAIX,CAAC,CAAC,QAAQ,CAAC,IAAIA,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;MAC3C,CAAC,SAAS;QACR,IAAIY,CAAC,EAAE,MAAMF,CAAC;MAChB;IACF;EACF,CAAC;AACH;AACA,SAASb,0BAA0B,IAAIkB,OAAO", "ignoreList": []}]}