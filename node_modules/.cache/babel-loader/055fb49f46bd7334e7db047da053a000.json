{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ar.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ar.js", "mtime": 1757335234941}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/ar.ts"], "sourcesContent": ["export default {\n  badge: 'شارة',\n  close: 'إغلاق',\n  dataIterator: {\n    noResultsText: 'لا توجد سجلات مطابقة',\n    loadingText: 'تحميل العنصر...',\n  },\n  dataTable: {\n    itemsPerPageText: 'الصفوف لكل صفحة:',\n    ariaLabel: {\n      sortDescending: 'مفروز تنازلي. تنشيط لإزالة الفرز.',\n      sortAscending: 'مفروز تصاعدي. تنشيط للفرز التنازلي.',\n      sortNone: 'غير مفروزة. تفعيل لفرز تصاعدي.',\n      activateNone: 'Activate to remove sorting.',\n      activateDescending: 'Activate to sort descending.',\n      activateAscending: 'Activate to sort ascending.',\n    },\n    sortBy: 'مفروزة حسب',\n  },\n  dataFooter: {\n    itemsPerPageText: 'العناصر لكل صفحة:',\n    itemsPerPageAll: 'الكل',\n    nextPage: 'الصفحة التالية',\n    prevPage: 'الصفحة السابقة',\n    firstPage: 'الصفحة الأولى',\n    lastPage: 'الصفحة الأخيرة',\n    pageText: '{0}-{1} من {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} مختارة',\n    nextMonthAriaLabel: 'الشهر القادم',\n    nextYearAriaLabel: 'العام القادم',\n    prevMonthAriaLabel: 'الشهر الماضى',\n    prevYearAriaLabel: 'السنة الماضية',\n  },\n  noDataText: 'لا توجد بيانات متاحة',\n  carousel: {\n    prev: 'البصري السابق',\n    next: 'البصري التالي',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} أكثر',\n  },\n  fileInput: {\n    counter: '{0} ملفات',\n    counterSize: '{0} ملفات ({1} في المجموع)',\n  },\n  timePicker: {\n    am: 'صباحاً',\n    pm: 'مساءً',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'ترقيم الصفحات الملاحة',\n      next: 'الصفحة التالية',\n      previous: 'الصفحة السابقة',\n      page: '{0} انتقل إلى صفحة',\n      currentPage: '{0} الصفحة الحالية ، الصفحة',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,MADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,sBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,kBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,mCADP;MAETC,aAAa,EAAE,qCAFN;MAGTC,QAAQ,EAAE,gCAHD;MAITC,YAAY,EAAE,6BAJL;MAKTC,kBAAkB,EAAE,8BALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,mBADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,gBAHA;IAIVC,QAAQ,EAAE,gBAJA;IAKVC,SAAS,EAAE,eALD;IAMVC,QAAQ,EAAE,gBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,YADL;IAEVC,kBAAkB,EAAE,cAFV;IAGVC,iBAAiB,EAAE,cAHT;IAIVC,kBAAkB,EAAE,cAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,sBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,eADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,QADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,gBAFG;MAGTY,QAAQ,EAAE,gBAHD;MAITC,IAAI,EAAE,oBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}