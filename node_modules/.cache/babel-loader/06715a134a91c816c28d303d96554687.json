{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/zh-Hans.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/zh-Hans.js", "mtime": 1757335245285}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/zh-Hans.ts"], "sourcesContent": ["export default {\n  badge: '徽章',\n  close: '关闭',\n  dataIterator: {\n    noResultsText: '没有符合条件的结果',\n    loadingText: '加载中……',\n  },\n  dataTable: {\n    itemsPerPageText: '每页数目：',\n    ariaLabel: {\n      sortDescending: '：降序排列。',\n      sortAscending: '：升序排列。',\n      sortNone: '：未排序。',\n      activateNone: '点击以移除排序。',\n      activateDescending: '点击以降序排列。',\n      activateAscending: '点击以升序排列。',\n    },\n    sortBy: '排序方式',\n  },\n  dataFooter: {\n    itemsPerPageText: '每页数目：',\n    itemsPerPageAll: '全部',\n    nextPage: '下一页',\n    prevPage: '上一页',\n    firstPage: '首页',\n    lastPage: '尾页',\n    pageText: '{0}-{1} 共 {2}',\n  },\n  datePicker: {\n    itemsSelected: '已选择 {0}',\n    nextMonthAriaLabel: '下个月',\n    nextYearAriaLabel: '明年',\n    prevMonthAriaLabel: '前一个月',\n    prevYearAriaLabel: '前一年',\n  },\n  noDataText: '没有数据',\n  carousel: {\n    prev: '上一张',\n    next: '下一张',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '还有 {0} 项',\n  },\n  fileInput: {\n    counter: '{0} 个文件',\n    counterSize: '{0} 个文件（共 {1}）',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: '分页导航',\n      next: '下一页',\n      previous: '上一页',\n      page: '转到页面 {0}',\n      currentPage: '当前页 {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,IADM;EAEbC,KAAK,EAAE,IAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,WADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,OADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,QADP;MAETC,aAAa,EAAE,QAFN;MAGTC,QAAQ,EAAE,OAHD;MAITC,YAAY,EAAE,UAJL;MAKTC,kBAAkB,EAAE,UALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,OADR;IAEVU,eAAe,EAAE,IAFP;IAGVC,QAAQ,EAAE,KAHA;IAIVC,QAAQ,EAAE,KAJA;IAKVC,SAAS,EAAE,IALD;IAMVC,QAAQ,EAAE,IANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,SADL;IAEVC,kBAAkB,EAAE,KAFV;IAGVC,iBAAiB,EAAE,IAHT;IAIVC,kBAAkB,EAAE,MAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,MAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,KADE;IAERC,IAAI,EAAE,KAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,SADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,MADA;MAETX,IAAI,EAAE,KAFG;MAGTY,QAAQ,EAAE,KAHD;MAITC,IAAI,EAAE,UAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}