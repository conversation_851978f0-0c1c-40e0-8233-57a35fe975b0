{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/comparable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/comparable/index.js", "mtime": 1757335237067}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgeyBkZWVwRXF1YWwgfSBmcm9tICcuLi8uLi91dGlsL2hlbHBlcnMnOwpleHBvcnQgZGVmYXVsdCBWdWUuZXh0ZW5kKHsKICBuYW1lOiAnY29tcGFyYWJsZScsCiAgcHJvcHM6IHsKICAgIHZhbHVlQ29tcGFyYXRvcjogewogICAgICB0eXBlOiBGdW5jdGlvbiwKICAgICAgImRlZmF1bHQiOiBkZWVwRXF1YWwKICAgIH0KICB9Cn0pOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "deepEqual", "extend", "name", "props", "valueComparator", "type", "Function"], "sources": ["../../../src/mixins/comparable/index.ts"], "sourcesContent": ["import Vue from 'vue'\nimport { PropValidator } from 'vue/types/options'\nimport { deepEqual } from '../../util/helpers'\n\nexport default Vue.extend({\n  name: 'comparable',\n  props: {\n    valueComparator: {\n      type: Function,\n      default: deepEqual,\n    } as PropValidator<typeof deepEqual>,\n  },\n})\n"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA,SAASC,SAAT,QAA0B,oBAA1B;AAEA,eAAeD,GAAG,CAACE,MAAJ,CAAW;EACxBC,IAAI,EAAE,YADkB;EAExBC,KAAK,EAAE;IACLC,eAAe,EAAE;MACfC,IAAI,EAAEC,QADS;MAEf,WAASN;IAFM;EADZ;AAFiB,CAAX,CAAf", "ignoreList": []}]}