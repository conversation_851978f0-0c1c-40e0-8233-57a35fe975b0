{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VForm/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VForm/index.js", "mtime": 1757335236863}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZGb3JtIGZyb20gJy4vVkZvcm0nOwpleHBvcnQgeyBWRm9ybSB9OwpleHBvcnQgZGVmYXVsdCBWRm9ybTs="}, {"version": 3, "names": ["VForm"], "sources": ["../../../src/components/VForm/index.ts"], "sourcesContent": ["import VForm from './VForm'\n\nexport { VForm }\nexport default VForm\n"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,SAAlB;AAEA,SAASA,KAAT;AACA,eAAeA,KAAf", "ignoreList": []}]}