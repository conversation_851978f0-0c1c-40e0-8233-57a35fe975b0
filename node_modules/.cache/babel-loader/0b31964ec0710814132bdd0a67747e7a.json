{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VListItemGroup.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VListItemGroup.js", "mtime": 1757335238385}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKLy8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL2NvbXBvbmVudHMvVkxpc3QvVkxpc3RJdGVtR3JvdXAuc2FzcyI7IC8vIEV4dGVuc2lvbnMKCmltcG9ydCB7IEJhc2VJdGVtR3JvdXAgfSBmcm9tICcuLi9WSXRlbUdyb3VwL1ZJdGVtR3JvdXAnOyAvLyBNaXhpbnMKCmltcG9ydCBDb2xvcmFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL2NvbG9yYWJsZSc7IC8vIFV0aWxpdGllcwoKaW1wb3J0IG1peGlucyBmcm9tICcuLi8uLi91dGlsL21peGlucyc7CmV4cG9ydCBkZWZhdWx0IG1peGlucyhCYXNlSXRlbUdyb3VwLCBDb2xvcmFibGUpLmV4dGVuZCh7CiAgbmFtZTogJ3YtbGlzdC1pdGVtLWdyb3VwJywKICBwcm92aWRlOiBmdW5jdGlvbiBwcm92aWRlKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNJbkdyb3VwOiB0cnVlLAogICAgICBsaXN0SXRlbUdyb3VwOiB0aGlzCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNsYXNzZXM6IGZ1bmN0aW9uIGNsYXNzZXMoKSB7CiAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIEJhc2VJdGVtR3JvdXAub3B0aW9ucy5jb21wdXRlZC5jbGFzc2VzLmNhbGwodGhpcykpLCB7fSwgewogICAgICAgICd2LWxpc3QtaXRlbS1ncm91cCc6IHRydWUKICAgICAgfSk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZW5EYXRhOiBmdW5jdGlvbiBnZW5EYXRhKCkgewogICAgICByZXR1cm4gdGhpcy5zZXRUZXh0Q29sb3IodGhpcy5jb2xvciwgX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBCYXNlSXRlbUdyb3VwLm9wdGlvbnMubWV0aG9kcy5nZW5EYXRhLmNhbGwodGhpcykpLCB7fSwgewogICAgICAgIGF0dHJzOiB7CiAgICAgICAgICByb2xlOiAnbGlzdGJveCcKICAgICAgICB9CiAgICAgIH0pKTsKICAgIH0KICB9Cn0pOw=="}, {"version": 3, "names": ["BaseItemGroup", "Colorable", "mixins", "extend", "name", "provide", "isInGroup", "listItemGroup", "computed", "classes", "_objectSpread", "options", "call", "methods", "genData", "setTextColor", "color", "attrs", "role"], "sources": ["../../../src/components/VList/VListItemGroup.ts"], "sourcesContent": ["// Styles\nimport './VListItemGroup.sass'\n\n// Extensions\nimport { BaseItemGroup } from '../VItemGroup/VItemGroup'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\nexport default mixins(\n  BaseItemGroup,\n  Colorable\n).extend({\n  name: 'v-list-item-group',\n\n  provide () {\n    return {\n      isInGroup: true,\n      listItemGroup: this,\n    }\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...BaseItemGroup.options.computed.classes.call(this),\n        'v-list-item-group': true,\n      }\n    },\n  },\n\n  methods: {\n    genData (): object {\n      return this.setTextColor(this.color, {\n        ...BaseItemGroup.options.methods.genData.call(this),\n        attrs: {\n          role: 'listbox',\n        },\n      })\n    },\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAO,mDAAP,C,CAEA;;AACA,SAASA,aAAT,QAA8B,0BAA9B,C,CAEA;;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAEA,eAAeA,MAAM,CACnBF,aADmB,EAEnBC,SAFmB,CAAN,CAGbE,MAHa,CAGN;EACPC,IAAI,EAAE,mBADC;EAGPC,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,SAAS,EAAE,IADN;MAELC,aAAa,EAAE;IAFV,CAAP;EAID,CARM;EAUPC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKV,aAAa,CAACW,OAAd,CAAsBH,QAAtB,CAA+BC,OAA/B,CAAuCG,IAAvC,CAA4C,IAA5C,CADE;QAEL,qBAAqB;MAAA;IAExB;EANO,CAVH;EAmBPC,OAAO,EAAE;IACPC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAKC,YAAL,CAAkB,KAAKC,KAAvB,EAAAN,aAAA,CAAAA,aAAA,KACFV,aAAa,CAACW,OAAd,CAAsBE,OAAtB,CAA8BC,OAA9B,CAAsCF,IAAtC,CAA2C,IAA3C,CADgC;QAEnCK,KAAK,EAAE;UACLC,IAAI,EAAE;QADD;MAAA,EAFF,CAAP;IAMD;EARM;AAnBF,CAHM,CAAf", "ignoreList": []}]}