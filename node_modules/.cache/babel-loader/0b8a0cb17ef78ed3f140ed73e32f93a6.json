{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CoursewareDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CoursewareDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["TiwTranscode", "data", "showDialog", "courseware", "title", "url", "resolution", "pages", "transcodeMode", "uploadSteps", "showTranscodeFileDialog", "backupTranscodeFileUploading", "transcodeFileObj", "transcodeStart", "transcodePercent", "transcodeStaticMode", "transcodeErrMsg", "staus", "code", "error", "mounted", "cosInstance", "window", "COS", "getAuthorization", "options", "callback", "_asyncToGenerator", "_regenerator", "m", "_callee", "res", "credentials", "w", "_context", "n", "axios", "method", "responseType", "cmd", "v", "a", "console", "TmpSecretId", "tmpSecretId", "TmpSecretKey", "tmpSecret<PERSON>ey", "XCosSecurityToken", "sessionToken", "StartTime", "startTime", "ExpiredTime", "expiredTime", "methods", "show", "showTranscodeDialog", "addTranscodeFile", "teduBoard", "concat", "_Date$now", "viewDoc", "open", "uploadBackupTranscodeFile", "_this", "putObject", "Bucket", "Region", "Key", "name", "StorageClass", "Body", "onProgress", "progressData", "percent", "status", "_ref", "_callee2", "err", "cosUrl", "_context2", "Location", "_x", "_x2", "apply", "arguments", "createTranscodeTask", "_this2", "Url", "IsStaticPPT", "fileData", "Progress", "ResultUrl", "urlName", "Title", "Pages", "Resolution", "message"], "sources": ["src/components/ToolbarDialog/CoursewareDialog.vue"], "sourcesContent": ["<template>\n  <div class=\"courseware-dialog_container\">\n    <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n      <v-card>\n        <v-card-title class=\"headline lighten-2\"> 课件转码 </v-card-title>\n        <v-card-text>\n          <v-alert text type=\"info\">\n            1. ppt默认转码为动态转码，即保留ppt中的动画效果。<br />\n            2. word，pdf只能转换为静态图片，ppt也可以设置转换为静态图片。<br />\n          </v-alert>\n\n          <v-row>\n            <v-col cols=\"12\" md=\"8\">\n              <v-radio-group v-model=\"transcodeMode\" row>\n                <v-radio label=\"主转码\" :value=\"0\"></v-radio>\n                <v-radio label=\"备份转码\" :value=\"1\"></v-radio>\n              </v-radio-group>\n            </v-col>\n          </v-row>\n          <v-row>\n            <v-col cols=\"12\" md=\"8\">\n              <v-btn\n                prepend-icon=\"mdi-cloud-upload\"\n                @click=\"showTranscodeDialog\"\n                >点我选择文件</v-btn\n              >\n            </v-col>\n          </v-row>\n        </v-card-text>\n        <v-divider></v-divider>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <v-dialog\n      v-if=\"showTranscodeFileDialog\"\n      v-model=\"showTranscodeFileDialog\"\n      max-width=\"800\"\n    >\n      <v-stepper v-model=\"uploadSteps\" vertical>\n        <v-stepper-step :complete=\"uploadSteps > 1\" step=\"1\">\n          上传文件到COS桶{{ transcodeMode === 1 ? '(需开通文档预览)' : '' }}\n        </v-stepper-step>\n\n        <v-stepper-content step=\"1\">\n          <v-file-input\n            v-model=\"transcodeFileObj\"\n            label=\"先点我选择上传课件\"\n            prepend-icon=\"mdi-file\"\n            accept=\".doc,.docx,.ppt,.pptx,.pdf\"\n          ></v-file-input>\n          <v-btn\n            @click=\"uploadBackupTranscodeFile\"\n            :loading=\"backupTranscodeFileUploading\"\n            color=\"primary\"\n            >上传</v-btn\n          >\n        </v-stepper-content>\n\n        <v-stepper-step :complete=\"uploadSteps > 2\" step=\"2\">\n          {{\n            transcodeMode === 1\n              ? '获取文件COS地址并拼接白板参数'\n              : '发起转码任务'\n          }}\n        </v-stepper-step>\n\n        <v-stepper-content step=\"2\" v-if=\"transcodeMode === 1\">\n          <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n            >备份转码课件url:</span\n          >\n          <p style=\"word-break: break-all\">\n            {{ this.courseware.url }}\n          </p>\n          <v-text-field\n            v-model=\"courseware.title\"\n            label=\"课件名称\"\n          ></v-text-field>\n\n          <v-btn color=\"primary\" @click=\"addTranscodeFile\"> 确认添加 </v-btn>\n        </v-stepper-content>\n\n        <v-stepper-content step=\"2\" v-else>\n          <div v-if=\"!transcodeStart\">\n            <v-radio-group v-model=\"transcodeStaticMode\" row>\n              <v-radio\n                label=\"动态转码(暂时只支持ppt,pptx)\"\n                :value=\"false\"\n              ></v-radio>\n              <v-radio label=\"静态转码\" :value=\"true\"></v-radio>\n            </v-radio-group>\n\n            <v-btn color=\"primary\" @click=\"createTranscodeTask()\">\n              开始转码\n            </v-btn>\n          </div>\n\n          <div v-else>\n            <div class=\"mb-4\">\n              <v-alert type=\"error\" v-if=\"transcodeErrMsg.status === 'ERROR'\">\n                <h4>{{ transcodeErrMsg.code }}</h4>\n                {{ transcodeErrMsg.message }}\n              </v-alert>\n\n              <v-progress-linear\n                v-else\n                class=\"mb-2\"\n                stream\n                buffer-value=\"0\"\n                v-model=\"transcodePercent\"\n                height=\"25\"\n                :color=\"transcodePercent === 100 ? 'success' : 'primary'\"\n              >\n                <strong>{{ transcodePercent }}%</strong>\n              </v-progress-linear>\n\n              <div v-if=\"transcodePercent === 100\">\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >转码文件名</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.title }}\n                </p>\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >转码文件地址</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.url }}\n                </p>\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >文件页数</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.pages }}\n                </p>\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >分辨率</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.resolution }}\n                </p>\n              </div>\n            </div>\n\n            <v-btn\n              color=\"primary\"\n              :disabled=\"transcodePercent !== 100\"\n              @click=\"addTranscodeFile\"\n            >\n              确认添加\n            </v-btn>\n          </div>\n        </v-stepper-content>\n      </v-stepper>\n    </v-dialog>\n  </div>\n</template>\n\n<script>\nimport TiwTranscode from '../../util/TiwTranscode';\n\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      courseware: {\n        title: '课件',\n        url: '',\n        resolution: '',\n        pages: 0,\n      },\n      transcodeMode: 0, // 0 主转码，1 备份转码\n      uploadSteps: 1, // 转码步骤\n      showTranscodeFileDialog: false,\n      backupTranscodeFileUploading: false,\n      transcodeFileObj: {},\n      transcodeStart: false,\n      transcodePercent: 0,\n      transcodeStaticMode: false,\n      transcodeErrMsg: {\n        staus: 'SUCCESS',\n        code: '',\n        error: '',\n      },\n    };\n  },\n\n  mounted() {\n    this.cosInstance = new window.COS({\n      async getAuthorization(options, callback) {\n        // 异步获取临时密钥\n        const res = await window.axios({\n          method: 'post',\n          url:\n            'https://tiw-3gflb4tn0d7f550f-1259648581.ap-shanghai.app.tcloudbase.com/get_ci_cos_token',\n          responseType: 'json',\n          data: {\n            cmd: 'getToken',\n          },\n        });\n        const { data } = res;\n        const credentials = data && data.credentials;\n        if (!data || !credentials) return console.error('credentials invalid');\n        callback({\n          TmpSecretId: credentials.tmpSecretId,\n          TmpSecretKey: credentials.tmpSecretKey,\n          XCosSecurityToken: credentials.sessionToken,\n          StartTime: data.startTime,\n          ExpiredTime: data.expiredTime,\n        });\n      },\n    });\n  },\n\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n\n    showTranscodeDialog() {\n      this.showTranscodeFileDialog = true;\n      this.uploadSteps = 1;\n    },\n\n    addTranscodeFile() {\n      if (this.transcodeMode === 1) {\n        // 备份转码\n        window.teduBoard.addTranscodeFile({\n          url: this.courseware.url,\n          title: this.courseware.title || `测试文件-${Date.now()}`,\n        });\n      } else {\n        // 主转码\n        window.teduBoard.addTranscodeFile({\n          url: this.courseware.url,\n          title: this.courseware.title || `测试文件-${Date.now()}`,\n          pages: this.courseware.pages,\n          resolution: this.courseware.resolution,\n        });\n      }\n\n      // 重置数据\n      this.transcodeFileObj = null;\n\n      this.courseware.url = null;\n      this.courseware.title = null;\n      this.courseware.pages = 0;\n      this.courseware.resolution = '';\n\n      this.showDialog = false;\n      this.uploadSteps = 1;\n      this.showTranscodeFileDialog = false;\n    },\n\n    viewDoc() {\n      window.open(\n        'https://cloud.tencent.com/document/product/1137/55888',\n        '_blank'\n      );\n    },\n\n    /**\n     * 备份转码方案\n     */\n    uploadBackupTranscodeFile() {\n      if (!this.transcodeFileObj) return;\n\n      this.backupTranscodeFileUploading = true;\n      this.cosInstance.putObject(\n        {\n          Bucket: 'tiw-ci-1259648581' /* 必须 */,\n          Region: 'ap-nanjing' /* 存储桶所在地域，必须字段 */,\n          Key: this.transcodeFileObj.name /* 必须 */,\n          StorageClass: 'STANDARD',\n          Body: this.transcodeFileObj, // 上传文件对象\n          onProgress: (progressData) => {\n            if (progressData.percent === 1) {\n              this.backupTranscodeFileUploading = false;\n              this.uploadSteps = 2;\n              this.transcodeStart = false;\n              this.transcodeErrMsg.status = 'SUCCESS';\n              this.transcodePercent = 0;\n            }\n          },\n        },\n        async (err, data) => {\n          if (!err) {\n            const cosUrl = `https://${data.Location}`;\n            if (this.transcodeMode === 1) {\n              // 备份转码\n              this.courseware.url = `${cosUrl}?for_tiw=1`;\n              this.courseware.title = this.transcodeFileObj.name;\n            } else {\n              // 主转码\n              this.courseware.url = cosUrl;\n            }\n          }\n        }\n      );\n    },\n\n    createTranscodeTask() {\n      this.transcodeStart = true;\n      TiwTranscode.createTranscodeTask(\n        {\n          Url: this.courseware.url,\n          IsStaticPPT: this.transcodeStaticMode,\n        },\n        (data) => {\n          if (data.status === 'SUCCESS') {\n            this.transcodePercent = data.fileData.Progress;\n            this.transcodeErrMsg.status = 'SUCCESS';\n            if (this.transcodePercent === 100) {\n              this.courseware.url = `${data.fileData.ResultUrl}`;\n              this.courseware.urlName = `${data.fileData.Title}`;\n              this.courseware.pages = `${data.fileData.Pages}`;\n              this.courseware.resolution = `${data.fileData.Resolution}`;\n            }\n          } else {\n            this.transcodeErrMsg = {\n              status: 'ERROR',\n              code: data.code,\n              message: data.message,\n            };\n          }\n        }\n      );\n    },\n  },\n};\n</script>\n"], "mappings": ";;;;AAmKA,OAAAA,YAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,UAAA;QACAC,KAAA;QACAC,GAAA;QACAC,UAAA;QACAC,KAAA;MACA;MACAC,aAAA;MAAA;MACAC,WAAA;MAAA;MACAC,uBAAA;MACAC,4BAAA;MACAC,gBAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,eAAA;QACAC,KAAA;QACAC,IAAA;QACAC,KAAA;MACA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA,OAAAC,MAAA,CAAAC,GAAA;MACAC,gBAAA,WAAAA,iBAAAC,OAAA,EAAAC,QAAA;QAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;UAAA,IAAAC,GAAA,EAAA9B,IAAA,EAAA+B,WAAA;UAAA,OAAAJ,YAAA,GAAAK,CAAA,WAAAC,QAAA;YAAA,kBAAAA,QAAA,CAAAC,CAAA;cAAA;gBAAAD,QAAA,CAAAC,CAAA;gBAAA,OAEAb,MAAA,CAAAc,KAAA;kBACAC,MAAA;kBACAhC,GAAA,EACA;kBACAiC,YAAA;kBACArC,IAAA;oBACAsC,GAAA;kBACA;gBACA;cAAA;gBARAR,GAAA,GAAAG,QAAA,CAAAM,CAAA;gBASAvC,IAAA,GAAA8B,GAAA,CAAA9B,IAAA;gBACA+B,WAAA,GAAA/B,IAAA,IAAAA,IAAA,CAAA+B,WAAA;gBAAA,MACA,CAAA/B,IAAA,KAAA+B,WAAA;kBAAAE,QAAA,CAAAC,CAAA;kBAAA;gBAAA;gBAAA,OAAAD,QAAA,CAAAO,CAAA,IAAAC,OAAA,CAAAvB,KAAA;cAAA;gBACAO,QAAA;kBACAiB,WAAA,EAAAX,WAAA,CAAAY,WAAA;kBACAC,YAAA,EAAAb,WAAA,CAAAc,YAAA;kBACAC,iBAAA,EAAAf,WAAA,CAAAgB,YAAA;kBACAC,SAAA,EAAAhD,IAAA,CAAAiD,SAAA;kBACAC,WAAA,EAAAlD,IAAA,CAAAmD;gBACA;cAAA;gBAAA,OAAAlB,QAAA,CAAAO,CAAA;YAAA;UAAA,GAAAX,OAAA;QAAA;MACA;IACA;EACA;EAEAuB,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAApD,UAAA;IACA;IAEAqD,mBAAA,WAAAA,oBAAA;MACA,KAAA7C,uBAAA;MACA,KAAAD,WAAA;IACA;IAEA+C,gBAAA,WAAAA,iBAAA;MACA,SAAAhD,aAAA;QACA;QACAc,MAAA,CAAAmC,SAAA,CAAAD,gBAAA;UACAnD,GAAA,OAAAF,UAAA,CAAAE,GAAA;UACAD,KAAA,OAAAD,UAAA,CAAAC,KAAA,gCAAAsD,MAAA,CAAAC,SAAA;QACA;MACA;QACA;QACArC,MAAA,CAAAmC,SAAA,CAAAD,gBAAA;UACAnD,GAAA,OAAAF,UAAA,CAAAE,GAAA;UACAD,KAAA,OAAAD,UAAA,CAAAC,KAAA,gCAAAsD,MAAA,CAAAC,SAAA;UACApD,KAAA,OAAAJ,UAAA,CAAAI,KAAA;UACAD,UAAA,OAAAH,UAAA,CAAAG;QACA;MACA;;MAEA;MACA,KAAAM,gBAAA;MAEA,KAAAT,UAAA,CAAAE,GAAA;MACA,KAAAF,UAAA,CAAAC,KAAA;MACA,KAAAD,UAAA,CAAAI,KAAA;MACA,KAAAJ,UAAA,CAAAG,UAAA;MAEA,KAAAJ,UAAA;MACA,KAAAO,WAAA;MACA,KAAAC,uBAAA;IACA;IAEAkD,OAAA,WAAAA,QAAA;MACAtC,MAAA,CAAAuC,IAAA,CACA,yDACA,QACA;IACA;IAEA;AACA;AACA;IACAC,yBAAA,WAAAA,0BAAA;MAAA,IAAAC,KAAA;MACA,UAAAnD,gBAAA;MAEA,KAAAD,4BAAA;MACA,KAAAU,WAAA,CAAA2C,SAAA,CACA;QACAC,MAAA;QACAC,MAAA;QACAC,GAAA,OAAAvD,gBAAA,CAAAwD,IAAA;QACAC,YAAA;QACAC,IAAA,OAAA1D,gBAAA;QAAA;QACA2D,UAAA,WAAAA,WAAAC,YAAA;UACA,IAAAA,YAAA,CAAAC,OAAA;YACAV,KAAA,CAAApD,4BAAA;YACAoD,KAAA,CAAAtD,WAAA;YACAsD,KAAA,CAAAlD,cAAA;YACAkD,KAAA,CAAA/C,eAAA,CAAA0D,MAAA;YACAX,KAAA,CAAAjD,gBAAA;UACA;QACA;MACA;QAAA,IAAA6D,IAAA,GAAAhD,iBAAA,cAAAC,YAAA,GAAAC,CAAA,CACA,SAAA+C,SAAAC,GAAA,EAAA5E,IAAA;UAAA,IAAA6E,MAAA;UAAA,OAAAlD,YAAA,GAAAK,CAAA,WAAA8C,SAAA;YAAA,kBAAAA,SAAA,CAAA5C,CAAA;cAAA;gBACA,KAAA0C,GAAA;kBACAC,MAAA,cAAApB,MAAA,CAAAzD,IAAA,CAAA+E,QAAA;kBACA,IAAAjB,KAAA,CAAAvD,aAAA;oBACA;oBACAuD,KAAA,CAAA5D,UAAA,CAAAE,GAAA,MAAAqD,MAAA,CAAAoB,MAAA;oBACAf,KAAA,CAAA5D,UAAA,CAAAC,KAAA,GAAA2D,KAAA,CAAAnD,gBAAA,CAAAwD,IAAA;kBACA;oBACA;oBACAL,KAAA,CAAA5D,UAAA,CAAAE,GAAA,GAAAyE,MAAA;kBACA;gBACA;cAAA;gBAAA,OAAAC,SAAA,CAAAtC,CAAA;YAAA;UAAA,GAAAmC,QAAA;QAAA,CACA;QAAA,iBAAAK,EAAA,EAAAC,GAAA;UAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;QAAA;MAAA,GACA;IACA;IAEAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAAzE,cAAA;MACAb,YAAA,CAAAqF,mBAAA,CACA;QACAE,GAAA,OAAApF,UAAA,CAAAE,GAAA;QACAmF,WAAA,OAAAzE;MACA,GACA,UAAAd,IAAA;QACA,IAAAA,IAAA,CAAAyE,MAAA;UACAY,MAAA,CAAAxE,gBAAA,GAAAb,IAAA,CAAAwF,QAAA,CAAAC,QAAA;UACAJ,MAAA,CAAAtE,eAAA,CAAA0D,MAAA;UACA,IAAAY,MAAA,CAAAxE,gBAAA;YACAwE,MAAA,CAAAnF,UAAA,CAAAE,GAAA,MAAAqD,MAAA,CAAAzD,IAAA,CAAAwF,QAAA,CAAAE,SAAA;YACAL,MAAA,CAAAnF,UAAA,CAAAyF,OAAA,MAAAlC,MAAA,CAAAzD,IAAA,CAAAwF,QAAA,CAAAI,KAAA;YACAP,MAAA,CAAAnF,UAAA,CAAAI,KAAA,MAAAmD,MAAA,CAAAzD,IAAA,CAAAwF,QAAA,CAAAK,KAAA;YACAR,MAAA,CAAAnF,UAAA,CAAAG,UAAA,MAAAoD,MAAA,CAAAzD,IAAA,CAAAwF,QAAA,CAAAM,UAAA;UACA;QACA;UACAT,MAAA,CAAAtE,eAAA;YACA0D,MAAA;YACAxD,IAAA,EAAAjB,IAAA,CAAAiB,IAAA;YACA8E,OAAA,EAAA/F,IAAA,CAAA+F;UACA;QACA;MACA,CACA;IACA;EACA;AACA", "ignoreList": []}]}