{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/color/transformCIELAB.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/color/transformCIELAB.js", "mtime": 1757335237572}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9NYXRoJGNicnQgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9tYXRoL2NicnQiOwp2YXIgZGVsdGEgPSAwLjIwNjg5NjU1MTcyNDEzNzkzOyAvLyA2w7cyOQoKdmFyIGNpZWxhYkZvcndhcmRUcmFuc2Zvcm0gPSBmdW5jdGlvbiBjaWVsYWJGb3J3YXJkVHJhbnNmb3JtKHQpIHsKICByZXR1cm4gdCA+IE1hdGgucG93KGRlbHRhLCAzKSA/IF9NYXRoJGNicnQodCkgOiB0IC8gKDMgKiBNYXRoLnBvdyhkZWx0YSwgMikpICsgNCAvIDI5Owp9Owp2YXIgY2llbGFiUmV2ZXJzZVRyYW5zZm9ybSA9IGZ1bmN0aW9uIGNpZWxhYlJldmVyc2VUcmFuc2Zvcm0odCkgewogIHJldHVybiB0ID4gZGVsdGEgPyBNYXRoLnBvdyh0LCAzKSA6IDMgKiBNYXRoLnBvdyhkZWx0YSwgMikgKiAodCAtIDQgLyAyOSk7Cn07CmV4cG9ydCBmdW5jdGlvbiBmcm9tWFlaKHh5eikgewogIHZhciB0cmFuc2Zvcm0gPSBjaWVsYWJGb3J3YXJkVHJhbnNmb3JtOwogIHZhciB0cmFuc2Zvcm1lZFkgPSB0cmFuc2Zvcm0oeHl6WzFdKTsKICByZXR1cm4gWzExNiAqIHRyYW5zZm9ybWVkWSAtIDE2LCA1MDAgKiAodHJhbnNmb3JtKHh5elswXSAvIDAuOTUwNDcpIC0gdHJhbnNmb3JtZWRZKSwgMjAwICogKHRyYW5zZm9ybWVkWSAtIHRyYW5zZm9ybSh4eXpbMl0gLyAxLjA4ODgzKSldOwp9CmV4cG9ydCBmdW5jdGlvbiB0b1hZWihsYWIpIHsKICB2YXIgdHJhbnNmb3JtID0gY2llbGFiUmV2ZXJzZVRyYW5zZm9ybTsKICB2YXIgTG4gPSAobGFiWzBdICsgMTYpIC8gMTE2OwogIHJldHVybiBbdHJhbnNmb3JtKExuICsgbGFiWzFdIC8gNTAwKSAqIDAuOTUwNDcsIHRyYW5zZm9ybShMbiksIHRyYW5zZm9ybShMbiAtIGxhYlsyXSAvIDIwMCkgKiAxLjA4ODgzXTsKfQ=="}, {"version": 3, "names": ["delta", "cielabForwardTransform", "t", "Math", "pow", "_Math$cbrt", "cielabReverseTransform", "fromXYZ", "xyz", "transform", "transformedY", "toXYZ", "lab", "Ln"], "sources": ["../../../src/util/color/transformCIELAB.ts"], "sourcesContent": ["import { XYZ, LAB } from '../colorUtils'\n\nconst delta = 0.20689655172413793 // 6÷29\n\nconst cielabForwardTransform = (t: number): number => (\n  t > delta ** 3\n    ? Math.cbrt(t)\n    : (t / (3 * delta ** 2)) + 4 / 29\n)\n\nconst cielabReverseTransform = (t: number): number => (\n  t > delta\n    ? t ** 3\n    : (3 * delta ** 2) * (t - 4 / 29)\n)\n\nexport function fromXYZ (xyz: XYZ): LAB {\n  const transform = cielabForwardTransform\n  const transformedY = transform(xyz[1])\n\n  return [\n    116 * transformedY - 16,\n    500 * (transform(xyz[0] / 0.95047) - transformedY),\n    200 * (transformedY - transform(xyz[2] / 1.08883)),\n  ]\n}\n\nexport function toXYZ (lab: LAB): XYZ {\n  const transform = cielabReverseTransform\n  const Ln = (lab[0] + 16) / 116\n  return [\n    transform(Ln + lab[1] / 500) * 0.95047,\n    transform(Ln),\n    transform(Ln - lab[2] / 200) * 1.08883,\n  ]\n}\n"], "mappings": ";AAEA,IAAMA,KAAK,GAAG,mBAAd,C,CAAkC;;AAElC,IAAMC,sBAAsB,GAAI,SAA1BA,sBAAsBA,CAAIC,CAAD;EAAA,OAC7BA,CAAC,GAAAC,IAAA,CAAAC,GAAA,CAAGJ,KAAK,EAAI,CAAb,IACIK,UAAA,CAAUH,CAAV,CADJ,GAEKA,CAAC,IAAI,IAAAC,IAAA,CAAAC,GAAA,CAAIJ,KAAK,EAAI,CAAjB,EAAF,GAAyB,IAAI,EAHnC;AAAA;AAMA,IAAMM,sBAAsB,GAAI,SAA1BA,sBAAsBA,CAAIJ,CAAD;EAAA,OAC7BA,CAAC,GAAGF,KAAJ,GAAAG,IAAA,CAAAC,GAAA,CACIF,CAAC,EAAI,CADT,IAEK,IAAAC,IAAA,CAAAC,GAAA,CAAIJ,KAAK,EAAI,CAAd,KAAoBE,CAAC,GAAG,IAAI,EAA5B,CAHN;AAAA;AAMA,OAAM,SAAUK,OAAVA,CAAmBC,GAAnB,EAA2B;EAC/B,IAAMC,SAAS,GAAGR,sBAAlB;EACA,IAAMS,YAAY,GAAGD,SAAS,CAACD,GAAG,CAAC,CAAD,CAAJ,CAA9B;EAEA,OAAO,CACL,MAAME,YAAN,GAAqB,EADhB,EAEL,OAAOD,SAAS,CAACD,GAAG,CAAC,CAAD,CAAH,GAAS,OAAV,CAAT,GAA8BE,YAArC,CAFK,EAGL,OAAOA,YAAY,GAAGD,SAAS,CAACD,GAAG,CAAC,CAAD,CAAH,GAAS,OAAV,CAA/B,CAHK,CAAP;AAKD;AAED,OAAM,SAAUG,KAAVA,CAAiBC,GAAjB,EAAyB;EAC7B,IAAMH,SAAS,GAAGH,sBAAlB;EACA,IAAMO,EAAE,GAAG,CAACD,GAAG,CAAC,CAAD,CAAH,GAAS,EAAV,IAAgB,GAA3B;EACA,OAAO,CACLH,SAAS,CAACI,EAAE,GAAGD,GAAG,CAAC,CAAD,CAAH,GAAS,GAAf,CAAT,GAA+B,OAD1B,EAELH,SAAS,CAACI,EAAD,CAFJ,EAGLJ,SAAS,CAACI,EAAE,GAAGD,GAAG,CAAC,CAAD,CAAH,GAAS,GAAf,CAAT,GAA+B,OAH1B,CAAP;AAKD", "ignoreList": []}]}