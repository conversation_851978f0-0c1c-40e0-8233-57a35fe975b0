{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VImg/VImg.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VImg/VImg.js", "mtime": 1757335238308}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["intersect", "VResponsive", "Themeable", "mixins", "mergeData", "console<PERSON>arn", "getSlot", "hasIntersect", "window", "extend", "name", "directives", "props", "alt", "String", "contain", "Boolean", "eager", "gradient", "lazySrc", "options", "type", "Object", "default", "root", "undefined", "rootMargin", "threshold", "position", "sizes", "src", "srcset", "transition", "data", "currentSrc", "image", "isLoading", "calculatedAspectRatio", "naturalWidth", "<PERSON><PERSON><PERSON><PERSON>", "computed", "computedAspectRatio", "Number", "normalisedSrc", "aspect", "_typeof", "aspectRatio", "__cachedImage", "backgroundImage", "push", "concat", "$createElement", "staticClass", "style", "join", "backgroundPosition", "key", "attrs", "mode", "watch", "init", "loadImage", "mounted", "methods", "entries", "observer", "isIntersecting", "lazyImg", "Image", "pollForSize", "onLoad", "_context", "_context2", "getSrc", "$emit", "_endsWithInstanceProperty", "call", "_startsWithInstanceProperty", "naturalHeight", "onError", "_this", "onload", "decode", "err", "message", "then", "onerror", "img", "_this2", "timeout", "arguments", "length", "poll", "complete", "_setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "_b", "width", "__genPlaceholder", "slot", "placeholder", "appear", "render", "h", "node", "role", "themeClasses", "modifiers", "once", "value", "handler", "children", "__cachedSizer", "tag"], "sources": ["../../../src/components/VImg/VImg.ts"], "sourcesContent": ["// Styles\nimport './VImg.sass'\n\n// Directives\nimport intersect from '../../directives/intersect'\n\n// Types\nimport { VNode } from 'vue'\nimport { PropValidator } from 'vue/types/options'\n\n// Components\nimport VResponsive from '../VResponsive'\n\n// Mixins\nimport Themeable from '../../mixins/themeable'\n\n// Utils\nimport mixins from '../../util/mixins'\nimport mergeData from '../../util/mergeData'\nimport { consoleWarn } from '../../util/console'\nimport { getSlot } from '../../util/helpers'\n\n// not intended for public use, this is passed in by vuetify-loader\nexport interface srcObject {\n  src: string\n  srcset?: string\n  lazySrc: string\n  aspect: number\n}\n\nconst hasIntersect = typeof window !== 'undefined' && 'IntersectionObserver' in window\n\n/* @vue/component */\nexport default mixins(\n  VResponsive,\n  Themeable,\n).extend({\n  name: 'v-img',\n\n  directives: { intersect },\n\n  props: {\n    alt: String,\n    contain: Boolean,\n    eager: Boolean,\n    gradient: String,\n    lazySrc: String,\n    options: {\n      type: Object,\n      // For more information on types, navigate to:\n      // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API\n      default: () => ({\n        root: undefined,\n        rootMargin: undefined,\n        threshold: undefined,\n      }),\n    } as PropValidator<IntersectionObserverInit>,\n    position: {\n      type: String,\n      default: 'center center',\n    },\n    sizes: String,\n    src: {\n      type: [String, Object],\n      default: '',\n    } as PropValidator<string | srcObject>,\n    srcset: String,\n    transition: {\n      type: [Boolean, String],\n      default: 'fade-transition',\n    },\n  },\n\n  data () {\n    return {\n      currentSrc: '', // Set from srcset\n      image: null as HTMLImageElement | null,\n      isLoading: true,\n      calculatedAspectRatio: undefined as number | undefined,\n      naturalWidth: undefined as number | undefined,\n      hasError: false,\n    }\n  },\n\n  computed: {\n    computedAspectRatio (): number {\n      return Number(this.normalisedSrc.aspect || this.calculatedAspectRatio)\n    },\n    normalisedSrc (): srcObject {\n      return this.src && typeof this.src === 'object'\n        ? {\n          src: this.src.src,\n          srcset: this.srcset || this.src.srcset,\n          lazySrc: this.lazySrc || this.src.lazySrc,\n          aspect: Number(this.aspectRatio || this.src.aspect),\n        } : {\n          src: this.src,\n          srcset: this.srcset,\n          lazySrc: this.lazySrc,\n          aspect: Number(this.aspectRatio || 0),\n        }\n    },\n    __cachedImage (): VNode | [] {\n      if (!(this.normalisedSrc.src || this.normalisedSrc.lazySrc || this.gradient)) return []\n\n      const backgroundImage: string[] = []\n      const src = this.isLoading ? this.normalisedSrc.lazySrc : this.currentSrc\n\n      if (this.gradient) backgroundImage.push(`linear-gradient(${this.gradient})`)\n      if (src) backgroundImage.push(`url(\"${src}\")`)\n\n      const image = this.$createElement('div', {\n        staticClass: 'v-image__image',\n        class: {\n          'v-image__image--preload': this.isLoading,\n          'v-image__image--contain': this.contain,\n          'v-image__image--cover': !this.contain,\n        },\n        style: {\n          backgroundImage: backgroundImage.join(', '),\n          backgroundPosition: this.position,\n        },\n        key: +this.isLoading,\n      })\n\n      /* istanbul ignore if */\n      if (!this.transition) return image\n\n      return this.$createElement('transition', {\n        attrs: {\n          name: this.transition,\n          mode: 'in-out',\n        },\n      }, [image])\n    },\n  },\n\n  watch: {\n    src () {\n      // Force re-init when src changes\n      if (!this.isLoading) this.init(undefined, undefined, true)\n      else this.loadImage()\n    },\n    '$vuetify.breakpoint.width': 'getSrc',\n  },\n\n  mounted () {\n    this.init()\n  },\n\n  methods: {\n    init (\n      entries?: IntersectionObserverEntry[],\n      observer?: IntersectionObserver,\n      isIntersecting?: boolean\n    ) {\n      // If the current browser supports the intersection\n      // observer api, the image is not observable, and\n      // the eager prop isn't being used, do not load\n      if (\n        hasIntersect &&\n        !isIntersecting &&\n        !this.eager\n      ) return\n\n      if (this.normalisedSrc.lazySrc) {\n        const lazyImg = new Image()\n        lazyImg.src = this.normalisedSrc.lazySrc\n        this.pollForSize(lazyImg, null)\n      }\n      /* istanbul ignore else */\n      if (this.normalisedSrc.src) this.loadImage()\n    },\n    onLoad () {\n      this.getSrc()\n      this.isLoading = false\n      this.$emit('load', this.src)\n\n      if (\n        this.image &&\n        (this.normalisedSrc.src.endsWith('.svg') || this.normalisedSrc.src.startsWith('data:image/svg+xml'))\n      ) {\n        if (this.image.naturalHeight && this.image.naturalWidth) {\n          this.naturalWidth = this.image.naturalWidth\n          this.calculatedAspectRatio = this.image.naturalWidth / this.image.naturalHeight\n        } else {\n          this.calculatedAspectRatio = 1\n        }\n      }\n    },\n    onError () {\n      this.hasError = true\n      this.$emit('error', this.src)\n    },\n    getSrc () {\n      /* istanbul ignore else */\n      if (this.image) this.currentSrc = this.image.currentSrc || this.image.src\n    },\n    loadImage () {\n      const image = new Image()\n      this.image = image\n\n      image.onload = () => {\n        /* istanbul ignore if */\n        if (image.decode) {\n          image.decode().catch((err: DOMException) => {\n            consoleWarn(\n              `Failed to decode image, trying to render anyway\\n\\n` +\n              `src: ${this.normalisedSrc.src}` +\n              (err.message ? `\\nOriginal error: ${err.message}` : ''),\n              this\n            )\n          }).then(this.onLoad)\n        } else {\n          this.onLoad()\n        }\n      }\n      image.onerror = this.onError\n\n      this.hasError = false\n      this.sizes && (image.sizes = this.sizes)\n      this.normalisedSrc.srcset && (image.srcset = this.normalisedSrc.srcset)\n      image.src = this.normalisedSrc.src\n      this.$emit('loadstart', this.normalisedSrc.src)\n\n      this.aspectRatio || this.pollForSize(image)\n      this.getSrc()\n    },\n    pollForSize (img: HTMLImageElement, timeout: number | null = 100) {\n      const poll = () => {\n        const { naturalHeight, naturalWidth } = img\n\n        if (naturalHeight || naturalWidth) {\n          this.naturalWidth = naturalWidth\n          this.calculatedAspectRatio = naturalWidth / naturalHeight\n        } else if (!img.complete && this.isLoading && !this.hasError && timeout != null) {\n          setTimeout(poll, timeout)\n        }\n      }\n\n      poll()\n    },\n    genContent () {\n      const content: VNode = VResponsive.options.methods.genContent.call(this)\n      if (this.naturalWidth) {\n        this._b(content.data!, 'div', {\n          style: { width: `${this.naturalWidth}px` },\n        })\n      }\n\n      return content\n    },\n    __genPlaceholder (): VNode | void {\n      const slot = getSlot(this, 'placeholder')\n      if (slot) {\n        const placeholder = this.isLoading\n          ? [this.$createElement('div', {\n            staticClass: 'v-image__placeholder',\n          }, slot)]\n          : []\n\n        if (!this.transition) return placeholder[0]\n\n        return this.$createElement('transition', {\n          props: {\n            appear: true,\n            name: this.transition,\n          },\n        }, placeholder)\n      }\n    },\n  },\n\n  render (h): VNode {\n    const node = VResponsive.options.render.call(this, h)\n\n    const data = mergeData(node.data!, {\n      staticClass: 'v-image',\n      attrs: {\n        'aria-label': this.alt,\n        role: this.alt ? 'img' : undefined,\n      },\n      class: this.themeClasses,\n      // Only load intersect directive if it\n      // will work in the current browser.\n      directives: hasIntersect\n        ? [{\n          name: 'intersect',\n          modifiers: { once: true },\n          value: {\n            handler: this.init,\n            options: this.options,\n          },\n        }]\n        : undefined,\n    })\n\n    node.children = [\n      this.__cachedSizer,\n      this.__cachedImage,\n      this.__genPlaceholder(),\n      this.genContent(),\n    ] as VNode[]\n\n    return h(node.tag, data, node.children)\n  },\n})\n"], "mappings": ";;;;;;;AAAA;AACA,OAAO,wCAAP,C,CAEA;;AACA,OAAOA,SAAP,MAAsB,4BAAtB,C,CAMA;;AACA,OAAOC,WAAP,MAAwB,gBAAxB,C,CAEA;;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,OAAOC,SAAP,MAAsB,sBAAtB;AACA,SAASC,WAAT,QAA4B,oBAA5B;AACA,SAASC,OAAT,QAAwB,oBAAxB;AAUA,IAAMC,YAAY,GAAG,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,0BAA0BA,MAAhF;AAEA;;AACA,eAAeL,MAAM,CACnBF,WADmB,EAEnBC,SAFmB,CAAN,CAGbO,MAHa,CAGN;EACPC,IAAI,EAAE,OADC;EAGPC,UAAU,EAAE;IAAEX,SAAA,EAAAA;EAAF,CAHL;EAKPY,KAAK,EAAE;IACLC,GAAG,EAAEC,MADA;IAELC,OAAO,EAAEC,OAFJ;IAGLC,KAAK,EAAED,OAHF;IAILE,QAAQ,EAAEJ,MAJL;IAKLK,OAAO,EAAEL,MALJ;IAMLM,OAAO,EAAE;MACPC,IAAI,EAAEC,MADC;MAEP;MACA;MACA,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAS;UACdC,IAAI,EAAEC,SADQ;UAEdC,UAAU,EAAED,SAFE;UAGdE,SAAS,EAAEF;QAHG,CAAP;MAAA;IAJF,CANJ;IAgBLG,QAAQ,EAAE;MACRP,IAAI,EAAEP,MADE;MAER,WAAS;IAFD,CAhBL;IAoBLe,KAAK,EAAEf,MApBF;IAqBLgB,GAAG,EAAE;MACHT,IAAI,EAAE,CAACP,MAAD,EAASQ,MAAT,CADH;MAEH,WAAS;IAFN,CArBA;IAyBLS,MAAM,EAAEjB,MAzBH;IA0BLkB,UAAU,EAAE;MACVX,IAAI,EAAE,CAACL,OAAD,EAAUF,MAAV,CADI;MAEV,WAAS;IAFC;EA1BP,CALA;EAqCPmB,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,UAAU,EAAE,EADP;MAELC,KAAK,EAAE,IAFF;MAGLC,SAAS,EAAE,IAHN;MAILC,qBAAqB,EAAEZ,SAJlB;MAKLa,YAAY,EAAEb,SALT;MAMLc,QAAQ,EAAE;IANL,CAAP;EAQD,CA9CM;EAgDPC,QAAQ,EAAE;IACRC,mBAAmB,WAAnBA,mBAAmBA,CAAA;MACjB,OAAOC,MAAM,CAAC,KAAKC,aAAL,CAAmBC,MAAnB,IAA6B,KAAKP,qBAAnC,CAAb;IACD,CAHO;IAIRM,aAAa,WAAbA,aAAaA,CAAA;MACX,OAAO,KAAKb,GAAL,IAAYe,OAAA,CAAO,KAAKf,GAAZ,MAAoB,QAAhC,GACH;QACAA,GAAG,EAAE,KAAKA,GAAL,CAASA,GADd;QAEAC,MAAM,EAAE,KAAKA,MAAL,IAAe,KAAKD,GAAL,CAASC,MAFhC;QAGAZ,OAAO,EAAE,KAAKA,OAAL,IAAgB,KAAKW,GAAL,CAASX,OAHlC;QAIAyB,MAAM,EAAEF,MAAM,CAAC,KAAKI,WAAL,IAAoB,KAAKhB,GAAL,CAASc,MAA9B;MAJd,CADG,GAMD;QACFd,GAAG,EAAE,KAAKA,GADR;QAEFC,MAAM,EAAE,KAAKA,MAFX;QAGFZ,OAAO,EAAE,KAAKA,OAHZ;QAIFyB,MAAM,EAAEF,MAAM,CAAC,KAAKI,WAAL,IAAoB,CAArB;MAJZ,CANN;IAYD,CAjBO;IAkBRC,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAI,EAAE,KAAKJ,aAAL,CAAmBb,GAAnB,IAA0B,KAAKa,aAAL,CAAmBxB,OAA7C,IAAwD,KAAKD,QAA/D,CAAJ,EAA8E,OAAO,EAAP;MAE9E,IAAM8B,eAAe,GAAa,EAAlC;MACA,IAAMlB,GAAG,GAAG,KAAKM,SAAL,GAAiB,KAAKO,aAAL,CAAmBxB,OAApC,GAA8C,KAAKe,UAA/D;MAEA,IAAI,KAAKhB,QAAT,EAAmB8B,eAAe,CAACC,IAAhB,oBAAAC,MAAA,CAAwC,KAAKhC,QAAQ,MAArD;MACnB,IAAIY,GAAJ,EAASkB,eAAe,CAACC,IAAhB,UAAAC,MAAA,CAA6BpB,GAAG,QAAhC;MAET,IAAMK,KAAK,GAAG,KAAKgB,cAAL,CAAoB,KAApB,EAA2B;QACvCC,WAAW,EAAE,gBAD0B;QAEvC,SAAO;UACL,2BAA2B,KAAKhB,SAD3B;UAEL,2BAA2B,KAAKrB,OAF3B;UAGL,yBAAyB,CAAC,KAAKA;QAH1B,CAFgC;QAOvCsC,KAAK,EAAE;UACLL,eAAe,EAAEA,eAAe,CAACM,IAAhB,CAAqB,IAArB,CADZ;UAELC,kBAAkB,EAAE,KAAK3B;QAFpB,CAPgC;QAWvC4B,GAAG,EAAE,CAAC,KAAKpB;MAX4B,CAA3B,CAAd;MAcA;;MACA,IAAI,CAAC,KAAKJ,UAAV,EAAsB,OAAOG,KAAP;MAEtB,OAAO,KAAKgB,cAAL,CAAoB,YAApB,EAAkC;QACvCM,KAAK,EAAE;UACL/C,IAAI,EAAE,KAAKsB,UADN;UAEL0B,IAAI,EAAE;QAFD;MADgC,CAAlC,EAKJ,CAACvB,KAAD,CALI,CAAP;IAMD;EAlDO,CAhDH;EAqGPwB,KAAK,EAAE;IACL7B,GAAG,WAAHA,GAAGA,CAAA;MACD;MACA,IAAI,CAAC,KAAKM,SAAV,EAAqB,KAAKwB,IAAL,CAAUnC,SAAV,EAAqBA,SAArB,EAAgC,IAAhC,EAArB,KACK,KAAKoC,SAAL;IACN,CALI;IAML,6BAA6B;EANxB,CArGA;EA8GPC,OAAO,WAAPA,OAAOA,CAAA;IACL,KAAKF,IAAL;EACD,CAhHM;EAkHPG,OAAO,EAAE;IACPH,IAAI,WAAJA,IAAIA,CACFI,OADE,EAEFC,QAFE,EAGFC,cAHE,EAGsB;MAExB;MACA;MACA;MACA,IACE3D,YAAY,IACZ,CAAC2D,cADD,IAEA,CAAC,KAAKjD,KAHR,EAIE;MAEF,IAAI,KAAK0B,aAAL,CAAmBxB,OAAvB,EAAgC;QAC9B,IAAMgD,OAAO,GAAG,IAAIC,KAAJ,EAAhB;QACAD,OAAO,CAACrC,GAAR,GAAc,KAAKa,aAAL,CAAmBxB,OAAjC;QACA,KAAKkD,WAAL,CAAiBF,OAAjB,EAA0B,IAA1B;MACD;MACD;;MACA,IAAI,KAAKxB,aAAL,CAAmBb,GAAvB,EAA4B,KAAK+B,SAAL;IAC7B,CAtBM;IAuBPS,MAAM,WAANA,MAAMA,CAAA;MAAA,IAAAC,QAAA,EAAAC,SAAA;MACJ,KAAKC,MAAL;MACA,KAAKrC,SAAL,GAAiB,KAAjB;MACA,KAAKsC,KAAL,CAAW,MAAX,EAAmB,KAAK5C,GAAxB;MAEA,IACE,KAAKK,KAAL,KACCwC,yBAAA,CAAAJ,QAAA,QAAK5B,aAAL,CAAmBb,GAAnB,EAAA8C,IAAA,CAAAL,QAAA,EAAgC,MAAhC,KAA2CM,2BAAA,CAAAL,SAAA,QAAK7B,aAAL,CAAmBb,GAAnB,EAAA8C,IAAA,CAAAJ,SAAA,EAAkC,oBAAlC,CAD5C,CADF,EAGE;QACA,IAAI,KAAKrC,KAAL,CAAW2C,aAAX,IAA4B,KAAK3C,KAAL,CAAWG,YAA3C,EAAyD;UACvD,KAAKA,YAAL,GAAoB,KAAKH,KAAL,CAAWG,YAA/B;UACA,KAAKD,qBAAL,GAA6B,KAAKF,KAAL,CAAWG,YAAX,GAA0B,KAAKH,KAAL,CAAW2C,aAAlE;QACD,CAHD,MAGO;UACL,KAAKzC,qBAAL,GAA6B,CAA7B;QACD;MACF;IACF,CAvCM;IAwCP0C,OAAO,WAAPA,OAAOA,CAAA;MACL,KAAKxC,QAAL,GAAgB,IAAhB;MACA,KAAKmC,KAAL,CAAW,OAAX,EAAoB,KAAK5C,GAAzB;IACD,CA3CM;IA4CP2C,MAAM,WAANA,MAAMA,CAAA;MACJ;MACA,IAAI,KAAKtC,KAAT,EAAgB,KAAKD,UAAL,GAAkB,KAAKC,KAAL,CAAWD,UAAX,IAAyB,KAAKC,KAAL,CAAWL,GAAtD;IACjB,CA/CM;IAgDP+B,SAAS,WAATA,SAASA,CAAA;MAAA,IAAAmB,KAAA;MACP,IAAM7C,KAAK,GAAG,IAAIiC,KAAJ,EAAd;MACA,KAAKjC,KAAL,GAAaA,KAAb;MAEAA,KAAK,CAAC8C,MAAN,GAAe,YAAK;QAClB;QACA,IAAI9C,KAAK,CAAC+C,MAAV,EAAkB;UAChB/C,KAAK,CAAC+C,MAAN,YAAsB,UAAAC,GAAD,EAAsB;YACzC9E,WAAW,CACT,gEAAA6C,MAAA,CACQ8B,KAAA,CAAKrC,aAAL,CAAmBb,GAAG,CAD9B,IAECqD,GAAG,CAACC,OAAJ,wBAAAlC,MAAA,CAAmCiC,GAAG,CAACC,OAAO,IAAK,EAFpD,CADS,EAITJ,KAJS,CAAX;UAMD,CAPD,EAOGK,IAPH,CAOQL,KAAA,CAAKV,MAPb;QAQD,CATD,MASO;UACLU,KAAA,CAAKV,MAAL;QACD;MACF,CAdD;MAeAnC,KAAK,CAACmD,OAAN,GAAgB,KAAKP,OAArB;MAEA,KAAKxC,QAAL,GAAgB,KAAhB;MACA,KAAKV,KAAL,KAAeM,KAAK,CAACN,KAAN,GAAc,KAAKA,KAAlC;MACA,KAAKc,aAAL,CAAmBZ,MAAnB,KAA8BI,KAAK,CAACJ,MAAN,GAAe,KAAKY,aAAL,CAAmBZ,MAAhE;MACAI,KAAK,CAACL,GAAN,GAAY,KAAKa,aAAL,CAAmBb,GAA/B;MACA,KAAK4C,KAAL,CAAW,WAAX,EAAwB,KAAK/B,aAAL,CAAmBb,GAA3C;MAEA,KAAKgB,WAAL,IAAoB,KAAKuB,WAAL,CAAiBlC,KAAjB,CAApB;MACA,KAAKsC,MAAL;IACD,CA7EM;IA8EPJ,WAAW,WAAXA,WAAWA,CAAEkB,GAAF,EAAqD;MAAA,IAAAC,MAAA;MAAA,IAA5BC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAjE,SAAA,GAAAiE,SAAA,MAAyB,GAAlD;MACT,IAAME,KAAI,GAAG,SAAPA,IAAIA,CAAA,EAAQ;QAChB,IAAQd,aAAF,GAAkCS,GAAxC,CAAQT,aAAF;UAAiBxC,YAAA,GAAiBiD,GAAxC,CAAuBjD,YAAA;QAEvB,IAAIwC,aAAa,IAAIxC,YAArB,EAAmC;UACjCkD,MAAA,CAAKlD,YAAL,GAAoBA,YAApB;UACAkD,MAAA,CAAKnD,qBAAL,GAA6BC,YAAY,GAAGwC,aAA5C;QACD,CAHD,MAGO,IAAI,CAACS,GAAG,CAACM,QAAL,IAAiBL,MAAA,CAAKpD,SAAtB,IAAmC,CAACoD,MAAA,CAAKjD,QAAzC,IAAqDkD,OAAO,IAAI,IAApE,EAA0E;UAC/EK,WAAA,CAAWF,KAAD,EAAOH,OAAP,CAAV;QACD;MACF,CATD;MAWAG,KAAI;IACL,CA3FM;IA4FPG,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAMC,OAAO,GAAU/F,WAAW,CAACmB,OAAZ,CAAoB2C,OAApB,CAA4BgC,UAA5B,CAAuCnB,IAAvC,CAA4C,IAA5C,CAAvB;MACA,IAAI,KAAKtC,YAAT,EAAuB;QACrB,KAAK2D,EAAL,CAAQD,OAAO,CAAC/D,IAAhB,EAAuB,KAAvB,EAA8B;UAC5BoB,KAAK,EAAE;YAAE6C,KAAK,KAAAhD,MAAA,CAAK,KAAKZ,YAAY;UAA7B;QADqB,CAA9B;MAGD;MAED,OAAO0D,OAAP;IACD,CArGM;IAsGPG,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,IAAMC,IAAI,GAAG9F,OAAO,CAAC,IAAD,EAAO,aAAP,CAApB;MACA,IAAI8F,IAAJ,EAAU;QACR,IAAMC,WAAW,GAAG,KAAKjE,SAAL,GAChB,CAAC,KAAKe,cAAL,CAAoB,KAApB,EAA2B;UAC5BC,WAAW,EAAE;QADe,CAA3B,EAEAgD,IAFA,CAAD,CADgB,GAIhB,EAJJ;QAMA,IAAI,CAAC,KAAKpE,UAAV,EAAsB,OAAOqE,WAAW,CAAC,CAAD,CAAlB;QAEtB,OAAO,KAAKlD,cAAL,CAAoB,YAApB,EAAkC;UACvCvC,KAAK,EAAE;YACL0F,MAAM,EAAE,IADH;YAEL5F,IAAI,EAAE,KAAKsB;UAFN;QADgC,CAAlC,EAKJqE,WALI,CAAP;MAMD;IACF;EAxHM,CAlHF;EA6OPE,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMC,IAAI,GAAGxG,WAAW,CAACmB,OAAZ,CAAoBmF,MAApB,CAA2B3B,IAA3B,CAAgC,IAAhC,EAAsC4B,CAAtC,CAAb;IAEA,IAAMvE,IAAI,GAAG7B,SAAS,CAACqG,IAAI,CAACxE,IAAN,EAAa;MACjCmB,WAAW,EAAE,SADoB;MAEjCK,KAAK,EAAE;QACL,cAAc,KAAK5C,GADd;QAEL6F,IAAI,EAAE,KAAK7F,GAAL,GAAW,KAAX,GAAmBY;MAFpB,CAF0B;MAMjC,SAAO,KAAKkF,YANqB;MAOjC;MACA;MACAhG,UAAU,EAAEJ,YAAY,GACpB,CAAC;QACDG,IAAI,EAAE,WADL;QAEDkG,SAAS,EAAE;UAAEC,IAAI,EAAE;QAAR,CAFV;QAGDC,KAAK,EAAE;UACLC,OAAO,EAAE,KAAKnD,IADT;UAELxC,OAAO,EAAE,KAAKA;QAFT;MAHN,CAAD,CADoB,GASpBK;IAlB6B,CAAb,CAAtB;IAqBAgF,IAAI,CAACO,QAAL,GAAgB,CACd,KAAKC,aADS,EAEd,KAAKlE,aAFS,EAGd,KAAKoD,gBAAL,EAHc,EAId,KAAKJ,UAAL,EAJc,CAAhB;IAOA,OAAOS,CAAC,CAACC,IAAI,CAACS,GAAN,EAAWjF,IAAX,EAAiBwE,IAAI,CAACO,QAAtB,CAAR;EACD;AA7QM,CAHM,CAAf", "ignoreList": []}]}