{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VInput/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VInput/index.js", "mtime": 1757335236871}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZJbnB1dCBmcm9tICcuL1ZJbnB1dCc7CmV4cG9ydCB7IFZJbnB1dCB9OwpleHBvcnQgZGVmYXVsdCBWSW5wdXQ7"}, {"version": 3, "names": ["VInput"], "sources": ["../../../src/components/VInput/index.ts"], "sourcesContent": ["import VInput from './VInput'\n\nexport { VInput }\nexport default VInput\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,UAAnB;AAEA,SAASA,MAAT;AACA,eAAeA,MAAf", "ignoreList": []}]}