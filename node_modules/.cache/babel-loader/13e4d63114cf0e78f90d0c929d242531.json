{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/roundable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/roundable/index.js", "mtime": 1757335237225}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIGZyb20gIi9Vc2Vycy9odWFuZ2NvbmdxaWFuZy9Eb3dubG9hZHMvd2ViLWRlbW8vbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lLWNvcmVqczMvaGVscGVycy9lc20vY3JlYXRlRm9yT2ZJdGVyYXRvckhlbHBlci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwovKiBAdnVlL2NvbXBvbmVudCAqLwoKZXhwb3J0IGRlZmF1bHQgVnVlLmV4dGVuZCh7CiAgbmFtZTogJ3JvdW5kYWJsZScsCiAgcHJvcHM6IHsKICAgIHJvdW5kZWQ6IFtCb29sZWFuLCBTdHJpbmddLAogICAgdGlsZTogQm9vbGVhbgogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIHJvdW5kZWRDbGFzc2VzOiBmdW5jdGlvbiByb3VuZGVkQ2xhc3NlcygpIHsKICAgICAgdmFyIGNvbXBvc2l0ZSA9IFtdOwogICAgICB2YXIgcm91bmRlZCA9IHR5cGVvZiB0aGlzLnJvdW5kZWQgPT09ICdzdHJpbmcnID8gU3RyaW5nKHRoaXMucm91bmRlZCkgOiB0aGlzLnJvdW5kZWQgPT09IHRydWU7CiAgICAgIGlmICh0aGlzLnRpbGUpIHsKICAgICAgICBjb21wb3NpdGUucHVzaCgncm91bmRlZC0wJyk7CiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHJvdW5kZWQgPT09ICdzdHJpbmcnKSB7CiAgICAgICAgdmFyIHZhbHVlcyA9IHJvdW5kZWQuc3BsaXQoJyAnKTsKICAgICAgICB2YXIgX2l0ZXJhdG9yID0gX2NyZWF0ZUZvck9mSXRlcmF0b3JIZWxwZXIodmFsdWVzKSwKICAgICAgICAgIF9zdGVwOwogICAgICAgIHRyeSB7CiAgICAgICAgICBmb3IgKF9pdGVyYXRvci5zKCk7ICEoX3N0ZXAgPSBfaXRlcmF0b3IubigpKS5kb25lOykgewogICAgICAgICAgICB2YXIgdmFsdWUgPSBfc3RlcC52YWx1ZTsKICAgICAgICAgICAgY29tcG9zaXRlLnB1c2goInJvdW5kZWQtIi5jb25jYXQodmFsdWUpKTsKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlcnIpIHsKICAgICAgICAgIF9pdGVyYXRvci5lKGVycik7CiAgICAgICAgfSBmaW5hbGx5IHsKICAgICAgICAgIF9pdGVyYXRvci5mKCk7CiAgICAgICAgfQogICAgICB9IGVsc2UgaWYgKHJvdW5kZWQpIHsKICAgICAgICBjb21wb3NpdGUucHVzaCgncm91bmRlZCcpOwogICAgICB9CiAgICAgIHJldHVybiBjb21wb3NpdGUubGVuZ3RoID4gMCA/IF9kZWZpbmVQcm9wZXJ0eSh7fSwgY29tcG9zaXRlLmpvaW4oJyAnKSwgdHJ1ZSkgOiB7fTsKICAgIH0KICB9Cn0pOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "extend", "name", "props", "rounded", "Boolean", "String", "tile", "computed", "roundedClasses", "composite", "push", "values", "split", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "value", "concat", "err", "e", "f", "length", "_defineProperty", "join"], "sources": ["../../../src/mixins/roundable/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\n/* @vue/component */\nexport default Vue.extend({\n  name: 'roundable',\n\n  props: {\n    rounded: [<PERSON><PERSON><PERSON>, String],\n    tile: <PERSON>olean,\n  },\n\n  computed: {\n    roundedClasses (): Record<string, boolean> {\n      const composite = []\n      const rounded = typeof this.rounded === 'string'\n        ? String(this.rounded)\n        : this.rounded === true\n\n      if (this.tile) {\n        composite.push('rounded-0')\n      } else if (typeof rounded === 'string') {\n        const values = rounded.split(' ')\n\n        for (const value of values) {\n          composite.push(`rounded-${value}`)\n        }\n      } else if (rounded) {\n        composite.push('rounded')\n      }\n\n      return composite.length > 0 ? {\n        [composite.join(' ')]: true,\n      } : {}\n    },\n  },\n})\n"], "mappings": ";;;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA;;AACA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,WADkB;EAGxBC,KAAK,EAAE;IACLC,OAAO,EAAE,CAACC,OAAD,EAAUC,MAAV,CADJ;IAELC,IAAI,EAAEF;EAFD,CAHiB;EAQxBG,QAAQ,EAAE;IACRC,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAMC,SAAS,GAAG,EAAlB;MACA,IAAMN,OAAO,GAAG,OAAO,KAAKA,OAAZ,KAAwB,QAAxB,GACZE,MAAM,CAAC,KAAKF,OAAN,CADM,GAEZ,KAAKA,OAAL,KAAiB,IAFrB;MAIA,IAAI,KAAKG,IAAT,EAAe;QACbG,SAAS,CAACC,IAAV,CAAe,WAAf;MACD,CAFD,MAEO,IAAI,OAAOP,OAAP,KAAmB,QAAvB,EAAiC;QACtC,IAAMQ,MAAM,GAAGR,OAAO,CAACS,KAAR,CAAc,GAAd,CAAf;QAAA,IAAAC,SAAA,GAAAC,0BAAA,CAEoBH,MAApB;UAAAI,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAA4B;YAAA,IAAjBC,KAAX,GAAAJ,KAAA,CAAAI,KAAA;YACEV,SAAS,CAACC,IAAV,YAAAU,MAAA,CAA0BD,KAAK,CAA/B;UACD;QAAA,SAAAE,GAAA;UAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;QAAA;UAAAR,SAAA,CAAAU,CAAA;QAAA;MACF,CANM,MAMA,IAAIpB,OAAJ,EAAa;QAClBM,SAAS,CAACC,IAAV,CAAe,SAAf;MACD;MAED,OAAOD,SAAS,CAACe,MAAV,GAAmB,CAAnB,GAAAC,eAAA,KACJhB,SAAS,CAACiB,IAAV,CAAe,GAAf,CAAD,EAAuB,QACrB,EAFJ;IAGD;EAtBO;AARc,CAAX,CAAf", "ignoreList": []}]}