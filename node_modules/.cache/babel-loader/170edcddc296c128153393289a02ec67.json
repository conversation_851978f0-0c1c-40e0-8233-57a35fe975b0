{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/activatable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/activatable/index.js", "mtime": 1757335237039}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Delayable", "Toggleable", "mixins", "getSlot", "getSlotType", "consoleError", "baseMixins", "extend", "name", "props", "activator", "validator", "val", "_context", "_includesInstanceProperty", "call", "_typeof", "disabled", "Boolean", "internalActivator", "openOnClick", "type", "openOnHover", "openOnFocus", "data", "activatorElement", "activatorNode", "events", "listeners", "watch", "mounted", "_context2", "slotType", "addActivatorEvents", "<PERSON><PERSON><PERSON><PERSON>", "removeActivatorEvents", "methods", "getActivator", "genActivatorListeners", "keys", "_Object$keys", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "key", "value", "addEventListener", "err", "e", "f", "genActivator", "node", "_Object$assign", "getValueProxy", "on", "attrs", "genActivatorAttributes", "role", "undefined", "String", "isActive", "_this", "mouseenter", "runDelay", "mouseleave", "click", "focus", "stopPropagation", "target", "$el", "document", "querySelector", "length", "_context3", "vm", "componentInstance", "$options", "_someInstanceProperty", "m", "_context4", "options", "elm", "currentTarget", "nodeType", "Node", "ELEMENT_NODE", "getContentSlot", "self", "_iterator2", "_step2", "removeEventListener", "resetActivator"], "sources": ["../../../src/mixins/activatable/index.ts"], "sourcesContent": ["// Mixins\nimport Delayable from '../delayable'\nimport Toggleable from '../toggleable'\n\n// Utilities\nimport mixins from '../../util/mixins'\nimport { getSlot, getSlotType } from '../../util/helpers'\nimport { consoleError } from '../../util/console'\n\n// Types\nimport { VNode, PropType } from 'vue'\n\ntype Listeners = Dictionary<(e: MouseEvent & KeyboardEvent & FocusEvent) => void>\n\nconst baseMixins = mixins(\n  Delayable,\n  Toggleable\n)\n\n/* @vue/component */\nexport default baseMixins.extend({\n  name: 'activatable',\n\n  props: {\n    activator: {\n      default: null as unknown as PropType<string | HTMLElement | VNode | Element | null>,\n      validator: (val: string | object) => {\n        return ['string', 'object'].includes(typeof val)\n      },\n    },\n    disabled: Boolean,\n    internalActivator: Boolean,\n    openOnClick: {\n      type: Boolean,\n      default: true,\n    },\n    openOnHover: Boolean,\n    openOnFocus: Boolean,\n  },\n\n  data: () => ({\n    // Do not use this directly, call getActivator() instead\n    activatorElement: null as HTMLElement | null,\n    activatorNode: [] as VNode[],\n    events: ['click', 'mouseenter', 'mouseleave', 'focus'],\n    listeners: {} as Listeners,\n  }),\n\n  watch: {\n    activator: 'resetActivator',\n    openOnFocus: 'resetActivator',\n    openOnHover: 'resetActivator',\n  },\n\n  mounted () {\n    const slotType = getSlotType(this, 'activator', true)\n\n    if (slotType && ['v-slot', 'normal'].includes(slotType)) {\n      consoleError(`The activator slot must be bound, try '<template v-slot:activator=\"{ on }\"><v-btn v-on=\"on\">'`, this)\n    }\n\n    this.addActivatorEvents()\n  },\n\n  beforeDestroy () {\n    this.removeActivatorEvents()\n  },\n\n  methods: {\n    addActivatorEvents () {\n      if (\n        !this.activator ||\n        this.disabled ||\n        !this.getActivator()\n      ) return\n\n      this.listeners = this.genActivatorListeners()\n      const keys = Object.keys(this.listeners)\n\n      for (const key of keys) {\n        this.getActivator()!.addEventListener(key, this.listeners[key] as any)\n      }\n    },\n    genActivator () {\n      const node = getSlot(this, 'activator', Object.assign(this.getValueProxy(), {\n        on: this.genActivatorListeners(),\n        attrs: this.genActivatorAttributes(),\n      })) || []\n\n      this.activatorNode = node\n\n      return node\n    },\n    genActivatorAttributes () {\n      return {\n        role: (this.openOnClick && !this.openOnHover) ? 'button' : undefined,\n        'aria-haspopup': true,\n        'aria-expanded': String(this.isActive),\n      }\n    },\n    genActivatorListeners () {\n      if (this.disabled) return {}\n\n      const listeners: Listeners = {}\n\n      if (this.openOnHover) {\n        listeners.mouseenter = (e: MouseEvent) => {\n          this.getActivator(e)\n          this.runDelay('open')\n        }\n        listeners.mouseleave = (e: MouseEvent) => {\n          this.getActivator(e)\n          this.runDelay('close')\n        }\n      } else if (this.openOnClick) {\n        listeners.click = (e: MouseEvent) => {\n          const activator = this.getActivator(e)\n          if (activator) activator.focus()\n\n          e.stopPropagation()\n\n          this.isActive = !this.isActive\n        }\n      }\n\n      if (this.openOnFocus) {\n        listeners.focus = (e: FocusEvent) => {\n          this.getActivator(e)\n\n          e.stopPropagation()\n\n          this.isActive = !this.isActive\n        }\n      }\n\n      return listeners\n    },\n    getActivator (e?: Event): HTMLElement | null {\n      // If we've already fetched the activator, re-use\n      if (this.activatorElement) return this.activatorElement\n\n      let activator = null\n\n      if (this.activator) {\n        const target = this.internalActivator ? this.$el : document\n\n        if (typeof this.activator === 'string') {\n          // Selector\n          activator = target.querySelector(this.activator)\n        } else if ((this.activator as any).$el) {\n          // Component (ref)\n          activator = (this.activator as any).$el\n        } else {\n          // HTMLElement | Element\n          activator = this.activator\n        }\n      } else if (this.activatorNode.length === 1 || (this.activatorNode.length && !e)) {\n        // Use the contents of the activator slot\n        // There's either only one element in it or we\n        // don't have a click event to use as a last resort\n        const vm = this.activatorNode[0].componentInstance\n        if (\n          vm &&\n          vm.$options.mixins && //                         Activatable is indirectly used via Menuable\n          vm.$options.mixins.some((m: any) => m.options && ['activatable', 'menuable'].includes(m.options.name))\n        ) {\n          // Activator is actually another activatible component, use its activator (#8846)\n          activator = (vm as any).getActivator()\n        } else {\n          activator = this.activatorNode[0].elm as HTMLElement\n        }\n      } else if (e) {\n        // Activated by a click or focus event\n        activator = (e.currentTarget || e.target) as HTMLElement\n      }\n\n      // The activator should only be a valid element (Ignore comments and text nodes)\n      this.activatorElement = activator?.nodeType === Node.ELEMENT_NODE ? activator : null\n\n      return this.activatorElement\n    },\n    getContentSlot () {\n      return getSlot(this, 'default', this.getValueProxy(), true)\n    },\n    getValueProxy (): object {\n      const self = this\n      return {\n        get value () {\n          return self.isActive\n        },\n        set value (isActive: boolean) {\n          self.isActive = isActive\n        },\n      }\n    },\n    removeActivatorEvents () {\n      if (\n        !this.activator ||\n        !this.activatorElement\n      ) return\n\n      const keys = Object.keys(this.listeners)\n\n      for (const key of keys) {\n        (this.activatorElement as any).removeEventListener(key, this.listeners[key])\n      }\n\n      this.listeners = {}\n    },\n    resetActivator () {\n      this.removeActivatorEvents()\n      this.activatorElement = null\n      this.getActivator()\n      this.addActivatorEvents()\n    },\n  },\n})\n"], "mappings": ";;;;;;;AAAA;AACA,OAAOA,SAAP,MAAsB,cAAtB;AACA,OAAOC,UAAP,MAAuB,eAAvB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,OAAT,EAAkBC,WAAlB,QAAqC,oBAArC;AACA,SAASC,YAAT,QAA6B,oBAA7B;AAOA,IAAMC,UAAU,GAAGJ,MAAM,CACvBF,SADuB,EAEvBC,UAFuB,CAAzB;AAKA;;AACA,eAAeK,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,aADyB;EAG/BC,KAAK,EAAE;IACLC,SAAS,EAAE;MACT,WAAS,IADA;MAETC,SAAS,EAAG,SAAZA,SAASA,CAAGC,GAAD,EAAyB;QAAA,IAAAC,QAAA;QAClC,OAAOC,yBAAA,CAAAD,QAAA,IAAC,QAAD,EAAW,QAAX,GAAAE,IAAA,CAAAF,QAAA,EAAAG,OAAA,CAAqCJ,GAArC,EAAP;MACD;IAJQ,CADN;IAOLK,QAAQ,EAAEC,OAPL;IAQLC,iBAAiB,EAAED,OARd;IASLE,WAAW,EAAE;MACXC,IAAI,EAAEH,OADK;MAEX,WAAS;IAFE,CATR;IAaLI,WAAW,EAAEJ,OAbR;IAcLK,WAAW,EAAEL;EAdR,CAHwB;EAoB/BM,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACX;MACAC,gBAAgB,EAAE,IAFP;MAGXC,aAAa,EAAE,EAHJ;MAIXC,MAAM,EAAE,CAAC,OAAD,EAAU,YAAV,EAAwB,YAAxB,EAAsC,OAAtC,CAJG;MAKXC,SAAS,EAAE;IALA,CAAP;EAAA,CApByB;EA4B/BC,KAAK,EAAE;IACLnB,SAAS,EAAE,gBADN;IAELa,WAAW,EAAE,gBAFR;IAGLD,WAAW,EAAE;EAHR,CA5BwB;EAkC/BQ,OAAO,WAAPA,OAAOA,CAAA;IAAA,IAAAC,SAAA;IACL,IAAMC,QAAQ,GAAG5B,WAAW,CAAC,IAAD,EAAO,WAAP,EAAoB,IAApB,CAA5B;IAEA,IAAI4B,QAAQ,IAAIlB,yBAAA,CAAAiB,SAAA,IAAC,QAAD,EAAW,QAAX,GAAAhB,IAAA,CAAAgB,SAAA,EAA8BC,QAA9B,CAAhB,EAAyD;MACvD3B,YAAY,sGAAkG,IAAlG,CAAZ;IACD;IAED,KAAK4B,kBAAL;EACD,CA1C8B;EA4C/BC,aAAa,WAAbA,aAAaA,CAAA;IACX,KAAKC,qBAAL;EACD,CA9C8B;EAgD/BC,OAAO,EAAE;IACPH,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IACE,CAAC,KAAKvB,SAAN,IACA,KAAKO,QADL,IAEA,CAAC,KAAKoB,YAAL,EAHH,EAIE;MAEF,KAAKT,SAAL,GAAiB,KAAKU,qBAAL,EAAjB;MACA,IAAMC,IAAI,GAAGC,YAAA,CAAY,KAAKZ,SAAjB,CAAb;MAAA,IAAAa,SAAA,GAAAC,0BAAA,CAEkBH,IAAlB;QAAAI,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAAwB;UAAA,IAAbC,GAAX,GAAAJ,KAAA,CAAAK,KAAA;UACE,KAAKX,YAAL,GAAqBY,gBAArB,CAAsCF,GAAtC,EAA2C,KAAKnB,SAAL,CAAemB,GAAf,CAA3C;QACD;MAAA,SAAAG,GAAA;QAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;MAAA;QAAAT,SAAA,CAAAW,CAAA;MAAA;IACF,CAdM;IAePC,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAMC,IAAI,GAAGnD,OAAO,CAAC,IAAD,EAAO,WAAP,EAAoBoD,cAAA,CAAc,KAAKC,aAAL,EAAd,EAAoC;QAC1EC,EAAE,EAAE,KAAKnB,qBAAL,EADsE;QAE1EoB,KAAK,EAAE,KAAKC,sBAAL;MAFmE,CAApC,CAApB,CAAP,IAGN,EAHP;MAKA,KAAKjC,aAAL,GAAqB4B,IAArB;MAEA,OAAOA,IAAP;IACD,CAxBM;IAyBPK,sBAAsB,WAAtBA,sBAAsBA,CAAA;MACpB,OAAO;QACLC,IAAI,EAAG,KAAKxC,WAAL,IAAoB,CAAC,KAAKE,WAA3B,GAA0C,QAA1C,GAAqDuC,SADtD;QAEL,iBAAiB,IAFZ;QAGL,iBAAiBC,MAAM,CAAC,KAAKC,QAAN;MAHlB,CAAP;IAKD,CA/BM;IAgCPzB,qBAAqB,WAArBA,qBAAqBA,CAAA;MAAA,IAAA0B,KAAA;MACnB,IAAI,KAAK/C,QAAT,EAAmB,OAAO,EAAP;MAEnB,IAAMW,SAAS,GAAc,EAA7B;MAEA,IAAI,KAAKN,WAAT,EAAsB;QACpBM,SAAS,CAACqC,UAAV,GAAwB,UAAAd,CAAD,EAAkB;UACvCa,KAAA,CAAK3B,YAAL,CAAkBc,CAAlB;UACAa,KAAA,CAAKE,QAAL,CAAc,MAAd;QACD,CAHD;QAIAtC,SAAS,CAACuC,UAAV,GAAwB,UAAAhB,CAAD,EAAkB;UACvCa,KAAA,CAAK3B,YAAL,CAAkBc,CAAlB;UACAa,KAAA,CAAKE,QAAL,CAAc,OAAd;QACD,CAHD;MAID,CATD,MASO,IAAI,KAAK9C,WAAT,EAAsB;QAC3BQ,SAAS,CAACwC,KAAV,GAAmB,UAAAjB,CAAD,EAAkB;UAClC,IAAMzC,SAAS,GAAGsD,KAAA,CAAK3B,YAAL,CAAkBc,CAAlB,CAAlB;UACA,IAAIzC,SAAJ,EAAeA,SAAS,CAAC2D,KAAV;UAEflB,CAAC,CAACmB,eAAF;UAEAN,KAAA,CAAKD,QAAL,GAAgB,CAACC,KAAA,CAAKD,QAAtB;QACD,CAPD;MAQD;MAED,IAAI,KAAKxC,WAAT,EAAsB;QACpBK,SAAS,CAACyC,KAAV,GAAmB,UAAAlB,CAAD,EAAkB;UAClCa,KAAA,CAAK3B,YAAL,CAAkBc,CAAlB;UAEAA,CAAC,CAACmB,eAAF;UAEAN,KAAA,CAAKD,QAAL,GAAgB,CAACC,KAAA,CAAKD,QAAtB;QACD,CAND;MAOD;MAED,OAAOnC,SAAP;IACD,CApEM;IAqEPS,YAAY,WAAZA,YAAYA,CAAEc,CAAF,EAAW;MACrB;MACA,IAAI,KAAK1B,gBAAT,EAA2B,OAAO,KAAKA,gBAAZ;MAE3B,IAAIf,SAAS,GAAG,IAAhB;MAEA,IAAI,KAAKA,SAAT,EAAoB;QAClB,IAAM6D,MAAM,GAAG,KAAKpD,iBAAL,GAAyB,KAAKqD,GAA9B,GAAoCC,QAAnD;QAEA,IAAI,OAAO,KAAK/D,SAAZ,KAA0B,QAA9B,EAAwC;UACtC;UACAA,SAAS,GAAG6D,MAAM,CAACG,aAAP,CAAqB,KAAKhE,SAA1B,CAAZ;QACD,CAHD,MAGO,IAAK,KAAKA,SAAL,CAAuB8D,GAA5B,EAAiC;UACtC;UACA9D,SAAS,GAAI,KAAKA,SAAL,CAAuB8D,GAApC;QACD,CAHM,MAGA;UACL;UACA9D,SAAS,GAAG,KAAKA,SAAjB;QACD;MACF,CAbD,MAaO,IAAI,KAAKgB,aAAL,CAAmBiD,MAAnB,KAA8B,CAA9B,IAAoC,KAAKjD,aAAL,CAAmBiD,MAAnB,IAA6B,CAACxB,CAAtE,EAA0E;QAAA,IAAAyB,SAAA;QAC/E;QACA;QACA;QACA,IAAMC,EAAE,GAAG,KAAKnD,aAAL,CAAmB,CAAnB,EAAsBoD,iBAAjC;QACA,IACED,EAAE,IACFA,EAAE,CAACE,QAAH,CAAY7E,MADZ;QACsB;QACtB8E,qBAAA,CAAAJ,SAAA,GAAAC,EAAE,CAACE,QAAH,CAAY7E,MAAZ,EAAAa,IAAA,CAAA6D,SAAA,EAAyB,UAAAK,CAAD;UAAA,IAAAC,SAAA;UAAA,OAAYD,CAAC,CAACE,OAAF,IAAarE,yBAAA,CAAAoE,SAAA,IAAC,aAAD,EAAgB,UAAhB,GAAAnE,IAAA,CAAAmE,SAAA,EAAqCD,CAAC,CAACE,OAAF,CAAU3E,IAA/C,CAAjD;QAAA,EAHF,EAIE;UACA;UACAE,SAAS,GAAImE,EAAU,CAACxC,YAAX,EAAb;QACD,CAPD,MAOO;UACL3B,SAAS,GAAG,KAAKgB,aAAL,CAAmB,CAAnB,EAAsB0D,GAAlC;QACD;MACF,CAfM,MAeA,IAAIjC,CAAJ,EAAO;QACZ;QACAzC,SAAS,GAAIyC,CAAC,CAACkC,aAAF,IAAmBlC,CAAC,CAACoB,MAAlC;MACD,CArCoB,CAuCrB;;MACA,KAAK9C,gBAAL,GAAwB,CAAAf,SAAS,SAAT,IAAAA,SAAS,WAAT,GAAS,MAAT,GAAAA,SAAS,CAAE4E,QAAX,MAAwBC,IAAI,CAACC,YAA7B,GAA4C9E,SAA5C,GAAwD,IAAhF;MAEA,OAAO,KAAKe,gBAAZ;IACD,CAhHM;IAiHPgE,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAOtF,OAAO,CAAC,IAAD,EAAO,SAAP,EAAkB,KAAKqD,aAAL,EAAlB,EAAwC,IAAxC,CAAd;IACD,CAnHM;IAoHPA,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAMkC,IAAI,GAAG,IAAb;MACA,OAAO;QACL,IAAI1C,KAAJA,CAAA,EAAS;UACP,OAAO0C,IAAI,CAAC3B,QAAZ;QACD,CAHI;QAIL,IAAIf,KAAJA,CAAWe,QAAX,EAA4B;UAC1B2B,IAAI,CAAC3B,QAAL,GAAgBA,QAAhB;QACD;MANI,CAAP;IAQD,CA9HM;IA+HP5B,qBAAqB,WAArBA,qBAAqBA,CAAA;MACnB,IACE,CAAC,KAAKzB,SAAN,IACA,CAAC,KAAKe,gBAFR,EAGE;MAEF,IAAMc,IAAI,GAAGC,YAAA,CAAY,KAAKZ,SAAjB,CAAb;MAAA,IAAA+D,UAAA,GAAAjD,0BAAA,CAEkBH,IAAlB;QAAAqD,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA/C,CAAA,MAAAgD,MAAA,GAAAD,UAAA,CAAA9C,CAAA,IAAAC,IAAA,GAAwB;UAAA,IAAbC,GAAX,GAAA6C,MAAA,CAAA5C,KAAA;UACG,KAAKvB,gBAAL,CAA8BoE,mBAA9B,CAAkD9C,GAAlD,EAAuD,KAAKnB,SAAL,CAAemB,GAAf,CAAvD;QACF;MAAA,SAAAG,GAAA;QAAAyC,UAAA,CAAAxC,CAAA,CAAAD,GAAA;MAAA;QAAAyC,UAAA,CAAAvC,CAAA;MAAA;MAED,KAAKxB,SAAL,GAAiB,EAAjB;IACD,CA5IM;IA6IPkE,cAAc,WAAdA,cAAcA,CAAA;MACZ,KAAK3D,qBAAL;MACA,KAAKV,gBAAL,GAAwB,IAAxB;MACA,KAAKY,YAAL;MACA,KAAKJ,kBAAL;IACD;EAlJM;AAhDsB,CAAlB,CAAf", "ignoreList": []}]}