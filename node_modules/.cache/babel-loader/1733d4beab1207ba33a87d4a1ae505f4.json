{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/registrable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/registrable/index.js", "mtime": 1757335237208}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF9jb25jYXRJbnN0YW5jZVByb3BlcnR5IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvaW5zdGFuY2UvY29uY2F0IjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgeyBjb25zb2xlV2FybiB9IGZyb20gJy4uLy4uL3V0aWwvY29uc29sZSc7CmZ1bmN0aW9uIGdlbmVyYXRlV2FybmluZyhjaGlsZCwgcGFyZW50KSB7CiAgcmV0dXJuIGZ1bmN0aW9uICgpIHsKICAgIHZhciBfY29udGV4dDsKICAgIHJldHVybiBjb25zb2xlV2FybihfY29uY2F0SW5zdGFuY2VQcm9wZXJ0eShfY29udGV4dCA9ICJUaGUgIi5jb25jYXQoY2hpbGQsICIgY29tcG9uZW50IG11c3QgYmUgdXNlZCBpbnNpZGUgYSAiKSkuY2FsbChfY29udGV4dCwgcGFyZW50KSk7CiAgfTsKfQpleHBvcnQgZnVuY3Rpb24gaW5qZWN0KG5hbWVzcGFjZSwgY2hpbGQsIHBhcmVudCkgewogIHZhciBkZWZhdWx0SW1wbCA9IGNoaWxkICYmIHBhcmVudCA/IHsKICAgIHJlZ2lzdGVyOiBnZW5lcmF0ZVdhcm5pbmcoY2hpbGQsIHBhcmVudCksCiAgICB1bnJlZ2lzdGVyOiBnZW5lcmF0ZVdhcm5pbmcoY2hpbGQsIHBhcmVudCkKICB9IDogbnVsbDsKICByZXR1cm4gVnVlLmV4dGVuZCh7CiAgICBuYW1lOiAncmVnaXN0cmFibGUtaW5qZWN0JywKICAgIGluamVjdDogX2RlZmluZVByb3BlcnR5KHt9LCBuYW1lc3BhY2UsIHsKICAgICAgImRlZmF1bHQiOiBkZWZhdWx0SW1wbAogICAgfSkKICB9KTsKfQpleHBvcnQgZnVuY3Rpb24gcHJvdmlkZShuYW1lc3BhY2UpIHsKICB2YXIgc2VsZiA9IGFyZ3VtZW50cy5sZW5ndGggPiAxICYmIGFyZ3VtZW50c1sxXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzFdIDogZmFsc2U7CiAgcmV0dXJuIFZ1ZS5leHRlbmQoewogICAgbmFtZTogJ3JlZ2lzdHJhYmxlLXByb3ZpZGUnLAogICAgcHJvdmlkZTogZnVuY3Rpb24gcHJvdmlkZSgpIHsKICAgICAgcmV0dXJuIF9kZWZpbmVQcm9wZXJ0eSh7fSwgbmFtZXNwYWNlLCBzZWxmID8gdGhpcyA6IHsKICAgICAgICByZWdpc3RlcjogdGhpcy5yZWdpc3RlciwKICAgICAgICB1bnJlZ2lzdGVyOiB0aGlzLnVucmVnaXN0ZXIKICAgICAgfSk7CiAgICB9CiAgfSk7Cn0="}, {"version": 3, "names": ["<PERSON><PERSON>", "console<PERSON>arn", "generateWarning", "child", "parent", "_context", "_concatInstanceProperty", "concat", "call", "inject", "namespace", "defaultImpl", "register", "unregister", "extend", "name", "_defineProperty", "provide", "self", "arguments", "length", "undefined"], "sources": ["../../../src/mixins/registrable/index.ts"], "sourcesContent": ["import Vue from 'vue'\nimport { VueConstructor } from 'vue/types/vue'\nimport { consoleWarn } from '../../util/console'\n\nfunction generateWarning (child: string, parent: string) {\n  return () => consoleWarn(`The ${child} component must be used inside a ${parent}`)\n}\n\nexport type Registrable<T extends string, C extends VueConstructor | null = null> = VueConstructor<Vue & {\n  [K in T]: C extends VueConstructor ? InstanceType<C> : {\n    register (...props: any[]): void\n    unregister (self: any): void\n  }\n}>\n\nexport function inject<\n  T extends string, C extends VueConstructor | null = null\n> (namespace: T, child?: string, parent?: string): Registrable<T, C> {\n  const defaultImpl = child && parent ? {\n    register: generateWarning(child, parent),\n    unregister: generateWarning(child, parent),\n  } : null\n\n  return Vue.extend({\n    name: 'registrable-inject',\n\n    inject: {\n      [namespace]: {\n        default: defaultImpl,\n      },\n    },\n  })\n}\n\nexport function provide (namespace: string, self = false) {\n  return Vue.extend({\n    name: 'registrable-provide',\n\n    provide (): object {\n      return {\n        [namespace]: self ? this : {\n          register: (this as any).register,\n          unregister: (this as any).unregister,\n        },\n      }\n    },\n  })\n}\n"], "mappings": ";;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA,SAASC,WAAT,QAA4B,oBAA5B;AAEA,SAASC,eAATA,CAA0BC,KAA1B,EAAyCC,MAAzC,EAAuD;EACrD,OAAO;IAAA,IAAAC,QAAA;IAAA,OAAMJ,WAAW,CAAAK,uBAAA,CAAAD,QAAA,UAAAE,MAAA,CAAQJ,KAAK,wCAAAK,IAAA,CAAAH,QAAA,EAAoCD,MAAM,CAAvD,CAAxB;EAAA;AACD;AASD,OAAM,SAAUK,MAAVA,CAEHC,SAFG,EAEWP,KAFX,EAE2BC,MAF3B,EAE0C;EAC9C,IAAMO,WAAW,GAAGR,KAAK,IAAIC,MAAT,GAAkB;IACpCQ,QAAQ,EAAEV,eAAe,CAACC,KAAD,EAAQC,MAAR,CADW;IAEpCS,UAAU,EAAEX,eAAe,CAACC,KAAD,EAAQC,MAAR;EAFS,CAAlB,GAGhB,IAHJ;EAKA,OAAOJ,GAAG,CAACc,MAAJ,CAAW;IAChBC,IAAI,EAAE,oBADU;IAGhBN,MAAM,EAAAO,eAAA,KACHN,SAAD,EAAa;MACX,WAASC;IADE;EAJC,CAAX,CAAP;AASD;AAED,OAAM,SAAUM,OAAVA,CAAmBP,SAAnB,EAAkD;EAAA,IAAZQ,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAA7C;EACJ,OAAOnB,GAAG,CAACc,MAAJ,CAAW;IAChBC,IAAI,EAAE,qBADU;IAGhBE,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAD,eAAA,KACGN,SAAD,EAAaQ,IAAI,GAAG,IAAH,GAAU;QACzBN,QAAQ,EAAG,KAAaA,QADC;QAEzBC,UAAU,EAAG,KAAaA;MAFD;IAK9B;EAVe,CAAX,CAAP;AAYD", "ignoreList": []}]}