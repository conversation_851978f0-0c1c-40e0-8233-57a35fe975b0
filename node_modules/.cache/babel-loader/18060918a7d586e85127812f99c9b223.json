{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/groupable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/groupable/index.js", "mtime": 1757335237115}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKLy8gTWl4aW5zCmltcG9ydCB7IGluamVjdCBhcyBSZWdpc3RyYWJsZUluamVjdCB9IGZyb20gJy4uL3JlZ2lzdHJhYmxlJzsKZXhwb3J0IGZ1bmN0aW9uIGZhY3RvcnkobmFtZXNwYWNlLCBjaGlsZCwgcGFyZW50KSB7CiAgcmV0dXJuIFJlZ2lzdHJhYmxlSW5qZWN0KG5hbWVzcGFjZSwgY2hpbGQsIHBhcmVudCkuZXh0ZW5kKHsKICAgIG5hbWU6ICdncm91cGFibGUnLAogICAgcHJvcHM6IHsKICAgICAgYWN0aXZlQ2xhc3M6IHsKICAgICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICAgImRlZmF1bHQiOiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICAgIGlmICghdGhpc1tuYW1lc3BhY2VdKSByZXR1cm4gdW5kZWZpbmVkOwogICAgICAgICAgcmV0dXJuIHRoaXNbbmFtZXNwYWNlXS5hY3RpdmVDbGFzczsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGRpc2FibGVkOiBCb29sZWFuCiAgICB9LAogICAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBpc0FjdGl2ZTogZmFsc2UKICAgICAgfTsKICAgIH0sCiAgICBjb21wdXRlZDogewogICAgICBncm91cENsYXNzZXM6IGZ1bmN0aW9uIGdyb3VwQ2xhc3NlcygpIHsKICAgICAgICBpZiAoIXRoaXMuYWN0aXZlQ2xhc3MpIHJldHVybiB7fTsKICAgICAgICByZXR1cm4gX2RlZmluZVByb3BlcnR5KHt9LCB0aGlzLmFjdGl2ZUNsYXNzLCB0aGlzLmlzQWN0aXZlKTsKICAgICAgfQogICAgfSwKICAgIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICAgIHRoaXNbbmFtZXNwYWNlXSAmJiB0aGlzW25hbWVzcGFjZV0ucmVnaXN0ZXIodGhpcyk7CiAgICB9LAogICAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgICAgdGhpc1tuYW1lc3BhY2VdICYmIHRoaXNbbmFtZXNwYWNlXS51bnJlZ2lzdGVyKHRoaXMpOwogICAgfSwKICAgIG1ldGhvZHM6IHsKICAgICAgdG9nZ2xlOiBmdW5jdGlvbiB0b2dnbGUoKSB7CiAgICAgICAgdGhpcy4kZW1pdCgnY2hhbmdlJyk7CiAgICAgIH0KICAgIH0KICB9KTsKfQovKiBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXJlZGVjbGFyZSAqLwoKdmFyIEdyb3VwYWJsZSA9IGZhY3RvcnkoJ2l0ZW1Hcm91cCcpOwpleHBvcnQgZGVmYXVsdCBHcm91cGFibGU7"}, {"version": 3, "names": ["inject", "RegistrableInject", "factory", "namespace", "child", "parent", "extend", "name", "props", "activeClass", "type", "String", "default", "undefined", "disabled", "Boolean", "data", "isActive", "computed", "groupClasses", "_defineProperty", "created", "register", "<PERSON><PERSON><PERSON><PERSON>", "unregister", "methods", "toggle", "$emit", "Groupable"], "sources": ["../../../src/mixins/groupable/index.ts"], "sourcesContent": ["// Mixins\nimport { Registrable, inject as RegistrableInject } from '../registrable'\n\n// Utilities\nimport { ExtractVue } from '../../util/mixins'\nimport { VueConstructor } from 'vue'\nimport { PropValidator } from 'vue/types/options'\n\nexport type Groupable<T extends string, C extends VueConstructor | null = null> = VueConstructor<ExtractVue<Registrable<T, C>> & {\n  activeClass: string\n  isActive: boolean\n  disabled: boolean\n  groupClasses: object\n  toggle (): void\n}>\n\nexport function factory<T extends string, C extends VueConstructor | null = null> (\n  namespace: T,\n  child?: string,\n  parent?: string\n): Groupable<T, C> {\n  return RegistrableInject<T, C>(namespace, child, parent).extend({\n    name: 'groupable',\n\n    props: {\n      activeClass: {\n        type: String,\n        default (): string | undefined {\n          if (!this[namespace]) return undefined\n\n          return this[namespace].activeClass\n        },\n      } as any as PropValidator<string>,\n      disabled: <PERSON>olean,\n    },\n\n    data () {\n      return {\n        isActive: false,\n      }\n    },\n\n    computed: {\n      groupClasses (): object {\n        if (!this.activeClass) return {}\n\n        return {\n          [this.activeClass]: this.isActive,\n        }\n      },\n    },\n\n    created () {\n      this[namespace] && (this[namespace] as any).register(this)\n    },\n\n    beforeDestroy () {\n      this[namespace] && (this[namespace] as any).unregister(this)\n    },\n\n    methods: {\n      toggle () {\n        this.$emit('change')\n      },\n    },\n  })\n}\n\n/* eslint-disable-next-line @typescript-eslint/no-redeclare */\nconst Groupable = factory('itemGroup')\n\nexport default Groupable\n"], "mappings": ";AAAA;AACA,SAAsBA,MAAM,IAAIC,iBAAhC,QAAyD,gBAAzD;AAeA,OAAM,SAAUC,OAAVA,CACJC,SADI,EAEJC,KAFI,EAGJC,MAHI,EAGW;EAEf,OAAOJ,iBAAiB,CAAOE,SAAP,EAAkBC,KAAlB,EAAyBC,MAAzB,CAAjB,CAAkDC,MAAlD,CAAyD;IAC9DC,IAAI,EAAE,WADwD;IAG9DC,KAAK,EAAE;MACLC,WAAW,EAAE;QACXC,IAAI,EAAEC,MADK;QAAA,oBAEXC,QAAOA,CAAA;UACL,IAAI,CAAC,KAAKT,SAAL,CAAL,EAAsB,OAAOU,SAAP;UAEtB,OAAO,KAAKV,SAAL,EAAgBM,WAAvB;QACD;MANU,CADR;MASLK,QAAQ,EAAEC;IATL,CAHuD;IAe9DC,IAAI,WAAJA,IAAIA,CAAA;MACF,OAAO;QACLC,QAAQ,EAAE;MADL,CAAP;IAGD,CAnB6D;IAqB9DC,QAAQ,EAAE;MACRC,YAAY,WAAZA,YAAYA,CAAA;QACV,IAAI,CAAC,KAAKV,WAAV,EAAuB,OAAO,EAAP;QAEvB,OAAAW,eAAA,KACG,KAAKX,WAAN,EAAoB,KAAKQ,QAAA;MAE5B;IAPO,CArBoD;IA+B9DI,OAAO,WAAPA,OAAOA,CAAA;MACL,KAAKlB,SAAL,KAAoB,KAAKA,SAAL,EAAwBmB,QAAxB,CAAiC,IAAjC,CAApB;IACD,CAjC6D;IAmC9DC,aAAa,WAAbA,aAAaA,CAAA;MACX,KAAKpB,SAAL,KAAoB,KAAKA,SAAL,EAAwBqB,UAAxB,CAAmC,IAAnC,CAApB;IACD,CArC6D;IAuC9DC,OAAO,EAAE;MACPC,MAAM,WAANA,MAAMA,CAAA;QACJ,KAAKC,KAAL,CAAW,QAAX;MACD;IAHM;EAvCqD,CAAzD,CAAP;AA6CD;AAED;;AACA,IAAMC,SAAS,GAAG1B,OAAO,CAAC,WAAD,CAAzB;AAEA,eAAe0B,SAAf", "ignoreList": []}]}