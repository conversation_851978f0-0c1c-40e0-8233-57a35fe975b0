{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/goto/easing-patterns.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/goto/easing-patterns.js", "mtime": 1757335235486}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gbGluZWFyCmV4cG9ydCB2YXIgbGluZWFyID0gZnVuY3Rpb24gbGluZWFyKHQpIHsKICByZXR1cm4gdDsKfTsgLy8gYWNjZWxlcmF0aW5nIGZyb20gemVybyB2ZWxvY2l0eQoKZXhwb3J0IHZhciBlYXNlSW5RdWFkID0gZnVuY3Rpb24gZWFzZUluUXVhZCh0KSB7CiAgcmV0dXJuIE1hdGgucG93KHQsIDIpOwp9OyAvLyBkZWNlbGVyYXRpbmcgdG8gemVybyB2ZWxvY2l0eQoKZXhwb3J0IHZhciBlYXNlT3V0UXVhZCA9IGZ1bmN0aW9uIGVhc2VPdXRRdWFkKHQpIHsKICByZXR1cm4gdCAqICgyIC0gdCk7Cn07IC8vIGFjY2VsZXJhdGlvbiB1bnRpbCBoYWxmd2F5LCB0aGVuIGRlY2VsZXJhdGlvbgoKZXhwb3J0IHZhciBlYXNlSW5PdXRRdWFkID0gZnVuY3Rpb24gZWFzZUluT3V0UXVhZCh0KSB7CiAgcmV0dXJuIHQgPCAwLjUgPyAyICogTWF0aC5wb3codCwgMikgOiAtMSArICg0IC0gMiAqIHQpICogdDsKfTsgLy8gYWNjZWxlcmF0aW5nIGZyb20gemVybyB2ZWxvY2l0eQoKZXhwb3J0IHZhciBlYXNlSW5DdWJpYyA9IGZ1bmN0aW9uIGVhc2VJbkN1YmljKHQpIHsKICByZXR1cm4gTWF0aC5wb3codCwgMyk7Cn07IC8vIGRlY2VsZXJhdGluZyB0byB6ZXJvIHZlbG9jaXR5CgpleHBvcnQgdmFyIGVhc2VPdXRDdWJpYyA9IGZ1bmN0aW9uIGVhc2VPdXRDdWJpYyh0KSB7CiAgcmV0dXJuIE1hdGgucG93KC0tdCwgMykgKyAxOwp9OyAvLyBhY2NlbGVyYXRpb24gdW50aWwgaGFsZndheSwgdGhlbiBkZWNlbGVyYXRpb24KCmV4cG9ydCB2YXIgZWFzZUluT3V0Q3ViaWMgPSBmdW5jdGlvbiBlYXNlSW5PdXRDdWJpYyh0KSB7CiAgcmV0dXJuIHQgPCAwLjUgPyA0ICogTWF0aC5wb3codCwgMykgOiAodCAtIDEpICogKDIgKiB0IC0gMikgKiAoMiAqIHQgLSAyKSArIDE7Cn07IC8vIGFjY2VsZXJhdGluZyBmcm9tIHplcm8gdmVsb2NpdHkKCmV4cG9ydCB2YXIgZWFzZUluUXVhcnQgPSBmdW5jdGlvbiBlYXNlSW5RdWFydCh0KSB7CiAgcmV0dXJuIE1hdGgucG93KHQsIDQpOwp9OyAvLyBkZWNlbGVyYXRpbmcgdG8gemVybyB2ZWxvY2l0eQoKZXhwb3J0IHZhciBlYXNlT3V0UXVhcnQgPSBmdW5jdGlvbiBlYXNlT3V0UXVhcnQodCkgewogIHJldHVybiAxIC0gTWF0aC5wb3coLS10LCA0KTsKfTsgLy8gYWNjZWxlcmF0aW9uIHVudGlsIGhhbGZ3YXksIHRoZW4gZGVjZWxlcmF0aW9uCgpleHBvcnQgdmFyIGVhc2VJbk91dFF1YXJ0ID0gZnVuY3Rpb24gZWFzZUluT3V0UXVhcnQodCkgewogIHJldHVybiB0IDwgMC41ID8gOCAqIHQgKiB0ICogdCAqIHQgOiAxIC0gOCAqIC0tdCAqIHQgKiB0ICogdDsKfTsgLy8gYWNjZWxlcmF0aW5nIGZyb20gemVybyB2ZWxvY2l0eQoKZXhwb3J0IHZhciBlYXNlSW5RdWludCA9IGZ1bmN0aW9uIGVhc2VJblF1aW50KHQpIHsKICByZXR1cm4gTWF0aC5wb3codCwgNSk7Cn07IC8vIGRlY2VsZXJhdGluZyB0byB6ZXJvIHZlbG9jaXR5CgpleHBvcnQgdmFyIGVhc2VPdXRRdWludCA9IGZ1bmN0aW9uIGVhc2VPdXRRdWludCh0KSB7CiAgcmV0dXJuIDEgKyBNYXRoLnBvdygtLXQsIDUpOwp9OyAvLyBhY2NlbGVyYXRpb24gdW50aWwgaGFsZndheSwgdGhlbiBkZWNlbGVyYXRpb24KCmV4cG9ydCB2YXIgZWFzZUluT3V0UXVpbnQgPSBmdW5jdGlvbiBlYXNlSW5PdXRRdWludCh0KSB7CiAgcmV0dXJuIHQgPCAwLjUgPyAxNiAqIE1hdGgucG93KHQsIDUpIDogMSArIDE2ICogTWF0aC5wb3coLS10LCA1KTsKfTs="}, {"version": 3, "names": ["linear", "t", "easeInQuad", "Math", "pow", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint"], "sources": ["../../../src/services/goto/easing-patterns.ts"], "sourcesContent": ["export type EasingFunction = (t: number) => number\n\n// linear\nexport const linear = (t: number) => t\n// accelerating from zero velocity\nexport const easeInQuad = (t: number) => t ** 2\n// decelerating to zero velocity\nexport const easeOutQuad = (t: number) => t * (2 - t)\n// acceleration until halfway, then deceleration\nexport const easeInOutQuad = (t: number) => (t < 0.5 ? 2 * t ** 2 : -1 + (4 - 2 * t) * t)\n// accelerating from zero velocity\nexport const easeInCubic = (t: number) => t ** 3\n// decelerating to zero velocity\nexport const easeOutCubic = (t: number) => --t ** 3 + 1\n// acceleration until halfway, then deceleration\nexport const easeInOutCubic = (t: number) => t < 0.5 ? 4 * t ** 3 : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1\n// accelerating from zero velocity\nexport const easeInQuart = (t: number) => t ** 4\n// decelerating to zero velocity\nexport const easeOutQuart = (t: number) => 1 - --t ** 4\n// acceleration until halfway, then deceleration\nexport const easeInOutQuart = (t: number) => (t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t)\n// accelerating from zero velocity\nexport const easeInQuint = (t: number) => t ** 5\n// decelerating to zero velocity\nexport const easeOutQuint = (t: number) => 1 + --t ** 5\n// acceleration until halfway, then deceleration\nexport const easeInOutQuint = (t: number) => t < 0.5 ? 16 * t ** 5 : 1 + 16 * --t ** 5\n"], "mappings": "AAEA;AACA,OAAO,IAAMA,MAAM,GAAI,SAAVA,MAAMA,CAAIC,CAAD;EAAA,OAAeA,CAA9B;AAAA,E,CACP;;AACA,OAAO,IAAMC,UAAU,GAAI,SAAdA,UAAUA,CAAID,CAAD;EAAA,OAAAE,IAAA,CAAAC,GAAA,CAAeH,CAAC,EAAI,CAAvC;AAAA,E,CACP;;AACA,OAAO,IAAMI,WAAW,GAAI,SAAfA,WAAWA,CAAIJ,CAAD;EAAA,OAAeA,CAAC,IAAI,IAAIA,CAAR,CAApC;AAAA,E,CACP;;AACA,OAAO,IAAMK,aAAa,GAAI,SAAjBA,aAAaA,CAAIL,CAAD;EAAA,OAAgBA,CAAC,GAAG,GAAJ,GAAU,IAAAE,IAAA,CAAAC,GAAA,CAAIH,CAAC,EAAI,CAAnB,IAAuB,CAAC,CAAD,GAAK,CAAC,IAAI,IAAIA,CAAT,IAAcA,CAAhF;AAAA,E,CACP;;AACA,OAAO,IAAMM,WAAW,GAAI,SAAfA,WAAWA,CAAIN,CAAD;EAAA,OAAAE,IAAA,CAAAC,GAAA,CAAeH,CAAC,EAAI,CAAxC;AAAA,E,CACP;;AACA,OAAO,IAAMO,YAAY,GAAI,SAAhBA,YAAYA,CAAIP,CAAD;EAAA,OAAeE,IAAA,CAAAC,GAAA,GAAEH,CAAF,EAAO,CAAP,IAAW,CAA/C;AAAA,E,CACP;;AACA,OAAO,IAAMQ,cAAc,GAAI,SAAlBA,cAAcA,CAAIR,CAAD;EAAA,OAAeA,CAAC,GAAG,GAAJ,GAAU,IAAAE,IAAA,CAAAC,GAAA,CAAIH,CAAC,EAAI,CAAnB,IAAuB,CAACA,CAAC,GAAG,CAAL,KAAW,IAAIA,CAAJ,GAAQ,CAAnB,KAAyB,IAAIA,CAAJ,GAAQ,CAAjC,IAAsC,CAAnG;AAAA,E,CACP;;AACA,OAAO,IAAMS,WAAW,GAAI,SAAfA,WAAWA,CAAIT,CAAD;EAAA,OAAAE,IAAA,CAAAC,GAAA,CAAeH,CAAC,EAAI,CAAxC;AAAA,E,CACP;;AACA,OAAO,IAAMU,YAAY,GAAI,SAAhBA,YAAYA,CAAIV,CAAD;EAAA,OAAe,IAAAE,IAAA,CAAAC,GAAA,CAAI,EAAEH,CAAF,EAAO,CAA/C;AAAA,E,CACP;;AACA,OAAO,IAAMW,cAAc,GAAI,SAAlBA,cAAcA,CAAIX,CAAD;EAAA,OAAgBA,CAAC,GAAG,GAAJ,GAAU,IAAIA,CAAJ,GAAQA,CAAR,GAAYA,CAAZ,GAAgBA,CAA1B,GAA8B,IAAI,IAAI,EAAEA,CAAN,GAAUA,CAAV,GAAcA,CAAd,GAAkBA,CAA3F;AAAA,E,CACP;;AACA,OAAO,IAAMY,WAAW,GAAI,SAAfA,WAAWA,CAAIZ,CAAD;EAAA,OAAAE,IAAA,CAAAC,GAAA,CAAeH,CAAC,EAAI,CAAxC;AAAA,E,CACP;;AACA,OAAO,IAAMa,YAAY,GAAI,SAAhBA,YAAYA,CAAIb,CAAD;EAAA,OAAe,IAAAE,IAAA,CAAAC,GAAA,CAAI,EAAEH,CAAF,EAAO,CAA/C;AAAA,E,CACP;;AACA,OAAO,IAAMc,cAAc,GAAI,SAAlBA,cAAcA,CAAId,CAAD;EAAA,OAAeA,CAAC,GAAG,GAAJ,GAAU,KAAAE,IAAA,CAAAC,GAAA,CAAKH,CAAC,EAAI,CAApB,IAAwB,IAAI,KAAAE,IAAA,CAAAC,GAAA,CAAK,EAAEH,CAAF,EAAO,CAA9E;AAAA", "ignoreList": []}]}