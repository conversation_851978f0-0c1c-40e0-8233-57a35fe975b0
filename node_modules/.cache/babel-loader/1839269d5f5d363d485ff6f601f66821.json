{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/filterable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/filterable/index.js", "mtime": 1757335237112}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwovKiBAdnVlL2NvbXBvbmVudCAqLwoKZXhwb3J0IGRlZmF1bHQgVnVlLmV4dGVuZCh7CiAgbmFtZTogJ2ZpbHRlcmFibGUnLAogIHByb3BzOiB7CiAgICBub0RhdGFUZXh0OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgImRlZmF1bHQiOiAnJHZ1ZXRpZnkubm9EYXRhVGV4dCcKICAgIH0KICB9Cn0pOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "extend", "name", "props", "noDataText", "type", "String"], "sources": ["../../../src/mixins/filterable/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\n/* @vue/component */\nexport default Vue.extend({\n  name: 'filterable',\n\n  props: {\n    noDataText: {\n      type: String,\n      default: '$vuetify.noDataText',\n    },\n  },\n})\n"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA;;AACA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,YADkB;EAGxBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,MADI;MAEV,WAAS;IAFC;EADP;AAHiB,CAAX,CAAf", "ignoreList": []}]}