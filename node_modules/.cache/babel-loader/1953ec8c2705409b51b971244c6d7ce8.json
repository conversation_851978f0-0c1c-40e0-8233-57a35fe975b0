{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/vi.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/vi.js", "mtime": 1757335238290}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/vi.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON> hiệu',\n  close: '<PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: '<PERSON>hông tìm thấy kết quả nào',\n    loadingText: '<PERSON>ang tải...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Số hàng mỗi trang:',\n    ariaLabel: {\n      sortDescending: 'Sắp xếp giảm dần.',\n      sortAscending: 'Sắp xếp tăng dần.',\n      sortNone: 'Không sắp xếp.',\n      activateNone: '<PERSON>ích hoạt để bỏ sắp xếp.',\n      activateDescending: '<PERSON><PERSON><PERSON> hoạt để sắp xếp giảm dần.',\n      activateAscending: '<PERSON>ích hoạt để sắp xếp tăng dần.',\n    },\n    sortBy: 'Sắp xếp',\n  },\n  dataFooter: {\n    itemsPerPageText: 'S<PERSON> mục mỗi trang:',\n    itemsPerPageAll: 'Toàn bộ',\n    nextPage: 'Trang tiếp theo',\n    prevPage: 'Trang trước',\n    firstPage: 'Trang đầu',\n    lastPage: 'Trang cuối',\n    pageText: '{0}-{1} trên {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} được chọn',\n    nextMonthAriaLabel: 'Tháng sau',\n    nextYearAriaLabel: 'Năm sau',\n    prevMonthAriaLabel: 'Tháng trước',\n    prevYearAriaLabel: 'Năm trước',\n  },\n  noDataText: 'Không có dữ liệu',\n  carousel: {\n    prev: 'Ảnh tiếp theo',\n    next: 'Ảnh trước',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} trên {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} nữa',\n  },\n  fileInput: {\n    counter: '{0} tệp',\n    counterSize: '{0} tệp (tổng cộng {1})',\n  },\n  timePicker: {\n    am: 'SA',\n    pm: 'CH',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Điều hướng phân trang',\n      next: 'Trang tiếp theo',\n      previous: 'Trang trước',\n      page: 'Đến trang {0}',\n      currentPage: 'Trang hiện tại, Trang {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Đánh giá {0} trên {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,UADM;EAEbC,KAAK,EAAE,MAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,4BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,oBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,mBADP;MAETC,aAAa,EAAE,mBAFN;MAGTC,QAAQ,EAAE,gBAHD;MAITC,YAAY,EAAE,0BAJL;MAKTC,kBAAkB,EAAE,gCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,mBADR;IAEVU,eAAe,EAAE,SAFP;IAGVC,QAAQ,EAAE,iBAHA;IAIVC,QAAQ,EAAE,aAJA;IAKVC,SAAS,EAAE,WALD;IAMVC,QAAQ,EAAE,YANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,eADL;IAEVC,kBAAkB,EAAE,WAFV;IAGVC,iBAAiB,EAAE,SAHT;IAIVC,kBAAkB,EAAE,aAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,kBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,eADE;IAERC,IAAI,EAAE,WAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,SADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,iBAFG;MAGTY,QAAQ,EAAE,aAHD;MAITC,IAAI,EAAE,eAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}