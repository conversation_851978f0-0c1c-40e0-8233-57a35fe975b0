{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMessages/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMessages/index.js", "mtime": 1757335236897}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZNZXNzYWdlcyBmcm9tICcuL1ZNZXNzYWdlcyc7CmV4cG9ydCB7IFZNZXNzYWdlcyB9OwpleHBvcnQgZGVmYXVsdCBWTWVzc2FnZXM7"}, {"version": 3, "names": ["VMessages"], "sources": ["../../../src/components/VMessages/index.ts"], "sourcesContent": ["import VMessages from './VMessages'\n\nexport { VMessages }\nexport default VMessages\n"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,aAAtB;AAEA,SAASA,SAAT;AACA,eAAeA,SAAf", "ignoreList": []}]}