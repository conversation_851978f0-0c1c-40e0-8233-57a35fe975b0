{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VRadioGroup/VRadioGroup.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VRadioGroup/VRadioGroup.js", "mtime": 1757335238831}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKLy8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL3N0eWxlcy9jb21wb25lbnRzL19zZWxlY3Rpb24tY29udHJvbHMuc2FzcyI7CmltcG9ydCAiLi4vLi4vLi4vc3JjL2NvbXBvbmVudHMvVlJhZGlvR3JvdXAvVlJhZGlvR3JvdXAuc2FzcyI7IC8vIEV4dGVuc2lvbnMKCmltcG9ydCBWSW5wdXQgZnJvbSAnLi4vVklucHV0JzsKaW1wb3J0IHsgQmFzZUl0ZW1Hcm91cCB9IGZyb20gJy4uL1ZJdGVtR3JvdXAvVkl0ZW1Hcm91cCc7IC8vIFR5cGVzCgppbXBvcnQgbWl4aW5zIGZyb20gJy4uLy4uL3V0aWwvbWl4aW5zJzsKdmFyIGJhc2VNaXhpbnMgPSBtaXhpbnMoQmFzZUl0ZW1Hcm91cCwgVklucHV0KTsKLyogQHZ1ZS9jb21wb25lbnQgKi8KCmV4cG9ydCBkZWZhdWx0IGJhc2VNaXhpbnMuZXh0ZW5kKHsKICBuYW1lOiAndi1yYWRpby1ncm91cCcsCiAgcHJvdmlkZTogZnVuY3Rpb24gcHJvdmlkZSgpIHsKICAgIHJldHVybiB7CiAgICAgIHJhZGlvR3JvdXA6IHRoaXMKICAgIH07CiAgfSwKICBwcm9wczogewogICAgY29sdW1uOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgICJkZWZhdWx0IjogdHJ1ZQogICAgfSwKICAgIGhlaWdodDogewogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6ICdhdXRvJwogICAgfSwKICAgIG5hbWU6IFN0cmluZywKICAgIHJvdzogQm9vbGVhbiwKICAgIC8vIElmIG5vIHZhbHVlIHNldCBvbiBWUmFkaW8KICAgIC8vIHdpbGwgbWF0Y2ggdmFsdWVDb21wYXJhdG9yCiAgICAvLyBmb3JjZSBkZWZhdWx0IHRvIG51bGwKICAgIHZhbHVlOiBudWxsCiAgfSwKICBjb21wdXRlZDogewogICAgY2xhc3NlczogZnVuY3Rpb24gY2xhc3NlcygpIHsKICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgVklucHV0Lm9wdGlvbnMuY29tcHV0ZWQuY2xhc3Nlcy5jYWxsKHRoaXMpKSwge30sIHsKICAgICAgICAndi1pbnB1dC0tc2VsZWN0aW9uLWNvbnRyb2xzIHYtaW5wdXQtLXJhZGlvLWdyb3VwJzogdHJ1ZSwKICAgICAgICAndi1pbnB1dC0tcmFkaW8tZ3JvdXAtLWNvbHVtbic6IHRoaXMuY29sdW1uICYmICF0aGlzLnJvdywKICAgICAgICAndi1pbnB1dC0tcmFkaW8tZ3JvdXAtLXJvdyc6IHRoaXMucm93CiAgICAgIH0pOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2VuRGVmYXVsdFNsb3Q6IGZ1bmN0aW9uIGdlbkRlZmF1bHRTbG90KCkgewogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1pbnB1dC0tcmFkaW8tZ3JvdXBfX2lucHV0JywKICAgICAgICBhdHRyczogewogICAgICAgICAgaWQ6IHRoaXMuaWQsCiAgICAgICAgICByb2xlOiAncmFkaW9ncm91cCcsCiAgICAgICAgICAnYXJpYS1sYWJlbGxlZGJ5JzogdGhpcy5jb21wdXRlZElkCiAgICAgICAgfQogICAgICB9LCBWSW5wdXQub3B0aW9ucy5tZXRob2RzLmdlbkRlZmF1bHRTbG90LmNhbGwodGhpcykpOwogICAgfSwKICAgIGdlbklucHV0U2xvdDogZnVuY3Rpb24gZ2VuSW5wdXRTbG90KCkgewogICAgICB2YXIgcmVuZGVyID0gVklucHV0Lm9wdGlvbnMubWV0aG9kcy5nZW5JbnB1dFNsb3QuY2FsbCh0aGlzKTsKICAgICAgZGVsZXRlIHJlbmRlci5kYXRhLm9uLmNsaWNrOwogICAgICByZXR1cm4gcmVuZGVyOwogICAgfSwKICAgIGdlbkxhYmVsOiBmdW5jdGlvbiBnZW5MYWJlbCgpIHsKICAgICAgdmFyIGxhYmVsID0gVklucHV0Lm9wdGlvbnMubWV0aG9kcy5nZW5MYWJlbC5jYWxsKHRoaXMpOwogICAgICBpZiAoIWxhYmVsKSByZXR1cm4gbnVsbDsKICAgICAgbGFiZWwuZGF0YS5hdHRycy5pZCA9IHRoaXMuY29tcHV0ZWRJZDsgLy8gV0FJIGNvbnNpZGVycyB0aGlzIGFuIG9ycGhhbmVkIGxhYmVsCgogICAgICBkZWxldGUgbGFiZWwuZGF0YS5hdHRyc1siZm9yIl07CiAgICAgIGxhYmVsLnRhZyA9ICdsZWdlbmQnOwogICAgICByZXR1cm4gbGFiZWw7CiAgICB9LAogICAgb25DbGljazogQmFzZUl0ZW1Hcm91cC5vcHRpb25zLm1ldGhvZHMub25DbGljawogIH0sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCkgewogICAgdmFyIHZub2RlID0gVklucHV0Lm9wdGlvbnMucmVuZGVyLmNhbGwodGhpcywgaCk7CiAgICB0aGlzLl9iKHZub2RlLmRhdGEsICdkaXYnLCB0aGlzLmF0dHJzJCk7CiAgICByZXR1cm4gdm5vZGU7CiAgfQp9KTs="}, {"version": 3, "names": ["VInput", "BaseItemGroup", "mixins", "baseMixins", "extend", "name", "provide", "radioGroup", "props", "column", "type", "Boolean", "height", "Number", "String", "row", "value", "computed", "classes", "_objectSpread", "options", "call", "methods", "genDefaultSlot", "$createElement", "staticClass", "attrs", "id", "role", "computedId", "genInputSlot", "render", "data", "on", "click", "gen<PERSON><PERSON><PERSON>", "label", "tag", "onClick", "h", "vnode", "_b", "attrs$"], "sources": ["../../../src/components/VRadioGroup/VRadioGroup.ts"], "sourcesContent": ["// Styles\nimport '../../styles/components/_selection-controls.sass'\nimport './VRadioGroup.sass'\n\n// Extensions\nimport VInput from '../VInput'\nimport { BaseItemGroup } from '../VItemGroup/VItemGroup'\n\n// Types\nimport mixins from '../../util/mixins'\nimport { PropType } from 'vue'\n\nconst baseMixins = mixins(\n  BaseItemGroup,\n  VInput\n)\n\n/* @vue/component */\nexport default baseMixins.extend({\n  name: 'v-radio-group',\n\n  provide () {\n    return {\n      radioGroup: this,\n    }\n  },\n\n  props: {\n    column: {\n      type: Boolean,\n      default: true,\n    },\n    height: {\n      type: [Number, String],\n      default: 'auto',\n    },\n    name: String,\n    row: Boolean,\n    // If no value set on VRadio\n    // will match valueComparator\n    // force default to null\n    value: null as unknown as PropType<any>,\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...VInput.options.computed.classes.call(this),\n        'v-input--selection-controls v-input--radio-group': true,\n        'v-input--radio-group--column': this.column && !this.row,\n        'v-input--radio-group--row': this.row,\n      }\n    },\n  },\n\n  methods: {\n    genDefaultSlot () {\n      return this.$createElement('div', {\n        staticClass: 'v-input--radio-group__input',\n        attrs: {\n          id: this.id,\n          role: 'radiogroup',\n          'aria-labelledby': this.computedId,\n        },\n      }, VInput.options.methods.genDefaultSlot.call(this))\n    },\n    genInputSlot () {\n      const render = VInput.options.methods.genInputSlot.call(this)\n\n      delete render.data!.on!.click\n\n      return render\n    },\n    genLabel () {\n      const label = VInput.options.methods.genLabel.call(this)\n\n      if (!label) return null\n\n      label.data!.attrs!.id = this.computedId\n      // WAI considers this an orphaned label\n      delete label.data!.attrs!.for\n      label.tag = 'legend'\n\n      return label\n    },\n    onClick: BaseItemGroup.options.methods.onClick,\n  },\n\n  render (h) {\n    const vnode = VInput.options.render.call(this, h)\n\n    this._b(vnode.data!, 'div', this.attrs$)\n\n    return vnode\n  },\n})\n"], "mappings": ";;AAAA;AACA,OAAO,yDAAP;AACA,OAAO,sDAAP,C,CAEA;;AACA,OAAOA,MAAP,MAAmB,WAAnB;AACA,SAASC,aAAT,QAA8B,0BAA9B,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAGA,IAAMC,UAAU,GAAGD,MAAM,CACvBD,aADuB,EAEvBD,MAFuB,CAAzB;AAKA;;AACA,eAAeG,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,eADyB;EAG/BC,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,UAAU,EAAE;IADP,CAAP;EAGD,CAP8B;EAS/BC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,OADA;MAEN,WAAS;IAFH,CADH;IAKLC,MAAM,EAAE;MACNF,IAAI,EAAE,CAACG,MAAD,EAASC,MAAT,CADA;MAEN,WAAS;IAFH,CALH;IASLT,IAAI,EAAES,MATD;IAULC,GAAG,EAAEJ,OAVA;IAWL;IACA;IACA;IACAK,KAAK,EAAE;EAdF,CATwB;EA0B/BC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKnB,MAAM,CAACoB,OAAP,CAAeH,QAAf,CAAwBC,OAAxB,CAAgCG,IAAhC,CAAqC,IAArC,CADE;QAEL,oDAAoD,IAF/C;QAGL,gCAAgC,KAAKZ,MAAL,IAAe,CAAC,KAAKM,GAHhD;QAIL,6BAA6B,KAAKA;MAAA;IAErC;EARO,CA1BqB;EAqC/BO,OAAO,EAAE;IACPC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,6BADmB;QAEhCC,KAAK,EAAE;UACLC,EAAE,EAAE,KAAKA,EADJ;UAELC,IAAI,EAAE,YAFD;UAGL,mBAAmB,KAAKC;QAHnB;MAFyB,CAA3B,EAOJ7B,MAAM,CAACoB,OAAP,CAAeE,OAAf,CAAuBC,cAAvB,CAAsCF,IAAtC,CAA2C,IAA3C,CAPI,CAAP;IAQD,CAVM;IAWPS,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAMC,MAAM,GAAG/B,MAAM,CAACoB,OAAP,CAAeE,OAAf,CAAuBQ,YAAvB,CAAoCT,IAApC,CAAyC,IAAzC,CAAf;MAEA,OAAOU,MAAM,CAACC,IAAP,CAAaC,EAAb,CAAiBC,KAAxB;MAEA,OAAOH,MAAP;IACD,CAjBM;IAkBPI,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAMC,KAAK,GAAGpC,MAAM,CAACoB,OAAP,CAAeE,OAAf,CAAuBa,QAAvB,CAAgCd,IAAhC,CAAqC,IAArC,CAAd;MAEA,IAAI,CAACe,KAAL,EAAY,OAAO,IAAP;MAEZA,KAAK,CAACJ,IAAN,CAAYN,KAAZ,CAAmBC,EAAnB,GAAwB,KAAKE,UAA7B,CALM,CAMN;;MACA,OAAOO,KAAK,CAACJ,IAAN,CAAYN,KAAZ,OAAP;MACAU,KAAK,CAACC,GAAN,GAAY,QAAZ;MAEA,OAAOD,KAAP;IACD,CA7BM;IA8BPE,OAAO,EAAErC,aAAa,CAACmB,OAAd,CAAsBE,OAAtB,CAA8BgB;EA9BhC,CArCsB;EAsE/BP,MAAM,WAANA,MAAMA,CAAEQ,CAAF,EAAG;IACP,IAAMC,KAAK,GAAGxC,MAAM,CAACoB,OAAP,CAAeW,MAAf,CAAsBV,IAAtB,CAA2B,IAA3B,EAAiCkB,CAAjC,CAAd;IAEA,KAAKE,EAAL,CAAQD,KAAK,CAACR,IAAd,EAAqB,KAArB,EAA4B,KAAKU,MAAjC;IAEA,OAAOF,KAAP;EACD;AA5E8B,CAAlB,CAAf", "ignoreList": []}]}