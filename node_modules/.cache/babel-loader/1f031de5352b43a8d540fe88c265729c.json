{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/lv.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/lv.js", "mtime": 1757335237324}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGJhZGdlOiAnxb1ldG9ucycsCiAgY2xvc2U6ICdBaXp2xJNydCcsCiAgZGF0YUl0ZXJhdG9yOiB7CiAgICBub1Jlc3VsdHNUZXh0OiAnTmVrYXMgbmV0aWthIGF0cmFzdHMnLAogICAgbG9hZGluZ1RleHQ6ICdJZWzEgWTEky4uLicKICB9LAogIGRhdGFUYWJsZTogewogICAgaXRlbXNQZXJQYWdlVGV4dDogJ1LEgWTEq3QgbGFwxIE6JywKICAgIGFyaWFMYWJlbDogewogICAgICBzb3J0RGVzY2VuZGluZzogJ1Nha8SBcnRvdHMgZGlsc3RvxaHEgSBzZWPEq2LEgS4nLAogICAgICBzb3J0QXNjZW5kaW5nOiAnU2FrxIFydG90cyBhdWdvxaHEgSBzZWPEq2LEgS4nLAogICAgICBzb3J0Tm9uZTogJ05hdiBzYWvEgXJ0b3RzLicsCiAgICAgIGFjdGl2YXRlTm9uZTogJ0FrdGl2aXrEk3QsIGxhaSBub8WGZW10dSBrxIFydG/FoWFudS4nLAogICAgICBhY3RpdmF0ZURlc2NlbmRpbmc6ICdBa3Rpdml6xJN0LCBsYWkgc2FrxIFydG90dSBkaWxzdG/FocSBIHNlY8SrYsSBLicsCiAgICAgIGFjdGl2YXRlQXNjZW5kaW5nOiAnQWt0aXZpesSTdCwgbGFpIHNha8SBcnRvdHUgYXVnb8WhxIEgc2VjxKtixIEuJwogICAgfSwKICAgIHNvcnRCeTogJ1NvcnQgYnknCiAgfSwKICBkYXRhRm9vdGVyOiB7CiAgICBpdGVtc1BlclBhZ2VUZXh0OiAnUsSBZMSrdCBsYXDEgTonLAogICAgaXRlbXNQZXJQYWdlQWxsOiAnVmlzdScsCiAgICBuZXh0UGFnZTogJ07EgWthbcSBIGxhcGEnLAogICAgcHJldlBhZ2U6ICdJZXByaWVrxaHEk2rEgSBsYXBhJywKICAgIGZpcnN0UGFnZTogJ1Bpcm3EgSBsYXBhJywKICAgIGxhc3RQYWdlOiAnUMSTZMSTasSBIGxhcGEnLAogICAgcGFnZVRleHQ6ICd7MH0tezF9IG5vIHsyfScKICB9LAogIGRhdGVQaWNrZXI6IHsKICAgIGl0ZW1zU2VsZWN0ZWQ6ICd7MH0gaXp2xJNsxJN0cycsCiAgICBuZXh0TW9udGhBcmlhTGFiZWw6ICdOxIFrYW1txJNuZXMnLAogICAgbmV4dFllYXJBcmlhTGFiZWw6ICdOxIFrYW1nYWQnLAogICAgcHJldk1vbnRoQXJpYUxhYmVsOiAnSWVwcmlla8WhxJNqYWlzIG3Ek25lc2lzJywKICAgIHByZXZZZWFyQXJpYUxhYmVsOiAnSWVwcmlla8WhxJNqYWlzIGdhZHMnCiAgfSwKICBub0RhdGFUZXh0OiAnTmF2IHBpZWVqYW11IGRhdHUnLAogIGNhcm91c2VsOiB7CiAgICBwcmV2OiAnSWVwcmlla8WhxJNqYWlzIHNsYWlkcycsCiAgICBuZXh0OiAnTsSBa2FtYWlzIHNsYWlkcycsCiAgICBhcmlhTGFiZWw6IHsKICAgICAgZGVsaW1pdGVyOiAnQ2Fyb3VzZWwgc2xpZGUgezB9IG9mIHsxfScKICAgIH0KICB9LAogIGNhbGVuZGFyOiB7CiAgICBtb3JlRXZlbnRzOiAnVsSTbCB7MH0nCiAgfSwKICBmaWxlSW5wdXQ6IHsKICAgIGNvdW50ZXI6ICd7MH0gZmlsZXMnLAogICAgY291bnRlclNpemU6ICd7MH0gZmlsZXMgKHsxfSBpbiB0b3RhbCknCiAgfSwKICB0aW1lUGlja2VyOiB7CiAgICBhbTogJ0FNJywKICAgIHBtOiAnUE0nCiAgfSwKICBwYWdpbmF0aW9uOiB7CiAgICBhcmlhTGFiZWw6IHsKICAgICAgd3JhcHBlcjogJ05hdmlnxIFjaWphIHBhZ2luYXRpb27EgScsCiAgICAgIG5leHQ6ICdOxIFrYW3EgSBsYXBhJywKICAgICAgcHJldmlvdXM6ICdJZXByaWVrxaHEk2rEgSBsYXBhJywKICAgICAgcGFnZTogJ0lldCB1eiBsYXB1IHswfScsCiAgICAgIGN1cnJlbnRQYWdlOiAnUGHFoXJlaXrEk2rEgSBsYXBhLCBsYXBhIHswfScKICAgIH0KICB9LAogIHJhdGluZzogewogICAgYXJpYUxhYmVsOiB7CiAgICAgIGljb246ICdSYXRpbmcgezB9IG9mIHsxfScKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/lv.ts"], "sourcesContent": ["export default {\n  badge: 'Žetons',\n  close: 'Aizvērt',\n  dataIterator: {\n    noResultsText: 'Nekas netika atrasts',\n    loadingText: 'Ielādē...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Rād<PERSON>t lapā:',\n    ariaLabel: {\n      sortDescending: '<PERSON>k<PERSON>rtots dilstošā secībā.',\n      sortAscending: 'Sak<PERSON>rtots augošā secībā.',\n      sortNone: 'Nav sakārtots.',\n      activateNone: 'Aktivizēt, lai noņemtu kārtošanu.',\n      activateDescending: 'Aktivizēt, lai sakārtotu dilstošā secībā.',\n      activateAscending: 'Aktivizēt, lai sakārtotu augošā secībā.',\n    },\n    sortBy: 'Sort by',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Rādīt lapā:',\n    itemsPerPageAll: 'Visu',\n    nextPage: 'Nāka<PERSON>ā lapa',\n    prevPage: 'Iepriekšējā lapa',\n    firstPage: '<PERSON><PERSON><PERSON> lapa',\n    lastPage: 'Pēdējā lapa',\n    pageText: '{0}-{1} no {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} izvēlēts',\n    nextMonthAriaLabel: 'Nākammēnes',\n    nextYearAriaLabel: 'Nākamgad',\n    prevMonthAriaLabel: 'Iepriekšējais mēnesis',\n    prevYearAriaLabel: 'Iepriekšējais gads',\n  },\n  noDataText: 'Nav pieejamu datu',\n  carousel: {\n    prev: 'Iepriekšējais slaids',\n    next: 'Nākamais slaids',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: 'Vēl {0}',\n  },\n  fileInput: {\n    counter: '{0} files',\n    counterSize: '{0} files ({1} in total)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigācija paginationā',\n      next: 'Nākamā lapa',\n      previous: 'Iepriekšējā lapa',\n      page: 'Iet uz lapu {0}',\n      currentPage: 'Pašreizējā lapa, lapa {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,sBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,aADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,4BADP;MAETC,aAAa,EAAE,0BAFN;MAGTC,QAAQ,EAAE,gBAHD;MAITC,YAAY,EAAE,mCAJL;MAKTC,kBAAkB,EAAE,2CALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,aADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,aAHA;IAIVC,QAAQ,EAAE,kBAJA;IAKVC,SAAS,EAAE,YALD;IAMVC,QAAQ,EAAE,aANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,cADL;IAEVC,kBAAkB,EAAE,YAFV;IAGVC,iBAAiB,EAAE,UAHT;IAIVC,kBAAkB,EAAE,uBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,mBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,sBADE;IAERC,IAAI,EAAE,iBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,wBADA;MAETX,IAAI,EAAE,aAFG;MAGTY,QAAQ,EAAE,kBAHD;MAITC,IAAI,EAAE,iBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}