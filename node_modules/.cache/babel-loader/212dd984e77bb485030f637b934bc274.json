{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VFileInput/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VFileInput/index.js", "mtime": 1757335236857}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZGaWxlSW5wdXQgZnJvbSAnLi9WRmlsZUlucHV0JzsKZXhwb3J0IHsgVkZpbGVJbnB1dCB9OwpleHBvcnQgZGVmYXVsdCBWRmlsZUlucHV0Ow=="}, {"version": 3, "names": ["VFileInput"], "sources": ["../../../src/components/VFileInput/index.ts"], "sourcesContent": ["import VFileInput from './VFileInput'\n\nexport { VFileInput }\nexport default VFileInput\n"], "mappings": "AAAA,OAAOA,UAAP,MAAuB,cAAvB;AAEA,SAASA,UAAT;AACA,eAAeA,UAAf", "ignoreList": []}]}