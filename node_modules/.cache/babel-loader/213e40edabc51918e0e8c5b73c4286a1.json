{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ckb.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ckb.js", "mtime": 1757335235059}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/ckb.ts"], "sourcesContent": ["export default {\n  badge: 'باج',\n  close: 'داخستن',\n  dataIterator: {\n    noResultsText: 'هیچ تۆمارێکی هاوتا نەدۆزرایەوە',\n    loadingText: 'بارکردنی ئایتمەکان...',\n  },\n  dataTable: {\n    itemsPerPageText: 'ڕیزەکان بۆ هەر پەڕەیەک:',\n    ariaLabel: {\n      sortDescending: '.سەر بەرەو خوار ڕیزکراوە',\n      sortAscending: '.سەر بەرەو ژوور ڕیزکراوە',\n      sortNone: 'ڕیزنەکراوە.',\n      activateNone: 'چالاککردن بۆ لابردنی ڕیزکردن.',\n      activateDescending: 'چالاککردن بۆ ڕیزکردنی سەربەرەوخوار.',\n      activateAscending: 'چالاککردن بۆ ڕیزکردنی سەر بەرەو ژوور.',\n    },\n    sortBy: 'ڕیزکردن بەپێی',\n  },\n  dataFooter: {\n    itemsPerPageText: 'ئایتمەکان بۆ هەر پەڕەیەک:',\n    itemsPerPageAll: 'هەمووی',\n    nextPage: 'پەڕەی دواتر',\n    prevPage: 'پەڕەی پێشوو',\n    firstPage: 'پەڕەی یەکەم',\n    lastPage: 'پەڕەی کۆتایی',\n    pageText: '{0}-{1} لە {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} دەسنیشانکراوە',\n    nextMonthAriaLabel: 'مانگی داهاتوو',\n    nextYearAriaLabel: 'ساڵی داهاتوو',\n    prevMonthAriaLabel: 'مانگی پێشوو',\n    prevYearAriaLabel: 'ساڵی پێشوو',\n  },\n  noDataText: 'هیچ داتایەک بەردەست نیە',\n  carousel: {\n    prev: 'بینراوی پێشوو',\n    next: 'بینراوی داهاتوو',\n    ariaLabel: {\n      delimiter: 'سلایدی کارۆسێل {0} لە {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} زیاتر',\n  },\n  fileInput: {\n    counter: '{0} فایل',\n    counterSize: '{0} فایل ({1} لە کۆی گشتی)',\n  },\n  timePicker: {\n    am: 'پێش نیوەڕۆژ',\n    pm: 'دوای نیوەڕۆژ',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'ڕێنیشاندەری پەڕەگۆڕکێ',\n      next: 'پەڕەی دواتر',\n      previous: 'پەڕەی پێشوو',\n      page: 'بڕۆ بۆ پەڕەی {0}',\n      currentPage: 'پەڕەی ئێستا، پەڕە{0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,KADM;EAEbC,KAAK,EAAE,QAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,gCADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,yBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,0BADP;MAETC,aAAa,EAAE,0BAFN;MAGTC,QAAQ,EAAE,aAHD;MAITC,YAAY,EAAE,+BAJL;MAKTC,kBAAkB,EAAE,qCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,2BADR;IAEVU,eAAe,EAAE,QAFP;IAGVC,QAAQ,EAAE,aAHA;IAIVC,QAAQ,EAAE,aAJA;IAKVC,SAAS,EAAE,aALD;IAMVC,QAAQ,EAAE,cANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,mBADL;IAEVC,kBAAkB,EAAE,eAFV;IAGVC,iBAAiB,EAAE,cAHT;IAIVC,kBAAkB,EAAE,aAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,yBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,eADE;IAERC,IAAI,EAAE,iBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,UADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,aADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,aAFG;MAGTY,QAAQ,EAAE,aAHD;MAITC,IAAI,EAAE,kBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}