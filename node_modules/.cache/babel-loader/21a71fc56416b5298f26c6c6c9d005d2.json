{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sr-Latn.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sr-Latn.js", "mtime": 1757335237521}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/sr-Latn.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: '<PERSON><PERSON><PERSON> zap<PERSON> nije pronađen',\n    loadingText: 'Učitavanje stavke...',\n  },\n  dataTable: {\n    itemsPerPageText: '<PERSON><PERSON> po stranici:',\n    ariaLabel: {\n      sortDescending: 'Sortirano opadajuće.',\n      sortAscending: 'Sortirano rastuće.',\n      sortNone: '<PERSON><PERSON> sortirano.',\n      activateNone: 'Klikni da ukloniš sortiranje.',\n      activateDescending: '<PERSON><PERSON><PERSON> da sortiraš opadajuće.',\n      activateAscending: '<PERSON><PERSON><PERSON> da sortiraš rastuće.',\n    },\n    sortBy: 'Sortiraj po',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Stavki po stranici:',\n    itemsPerPageAll: 'Sve',\n    nextPage: 'Sledeća stranica',\n    prevPage: 'Prethodna stranica',\n    firstPage: 'Prva stranica',\n    lastPage: 'Poslednja stranica',\n    pageText: '{0}-{1} od {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} odabrano',\n    nextMonthAriaLabel: 'Sledećeg meseca',\n    nextYearAriaLabel: 'Sledeće godine',\n    prevMonthAriaLabel: 'Prethodni mesec',\n    prevYearAriaLabel: 'Prethodna godina',\n  },\n  noDataText: 'Nema dostupnih podataka',\n  carousel: {\n    prev: 'Prethodna slika',\n    next: 'Sledeća slika',\n    ariaLabel: {\n      delimiter: 'Slika {0} od {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} više',\n  },\n  fileInput: {\n    counter: '{0} fajlova',\n    counterSize: '{0} fajlova ({1} ukupno)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigacija stranicama',\n      next: 'Sledeća stranica',\n      previous: 'Prethodna stranica',\n      page: 'Idi na stranu {0}',\n      currentPage: 'Trenutna stranica, stranica {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Ocena {0} od {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,6BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,qBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,sBADP;MAETC,aAAa,EAAE,oBAFN;MAGTC,QAAQ,EAAE,iBAHD;MAITC,YAAY,EAAE,+BAJL;MAKTC,kBAAkB,EAAE,+BALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,kBAHA;IAIVC,QAAQ,EAAE,oBAJA;IAKVC,SAAS,EAAE,eALD;IAMVC,QAAQ,EAAE,oBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,cADL;IAEVC,kBAAkB,EAAE,iBAFV;IAGVC,iBAAiB,EAAE,gBAHT;IAIVC,kBAAkB,EAAE,iBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,yBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,kBAFG;MAGTY,QAAQ,EAAE,oBAHD;MAITC,IAAI,EAAE,mBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}