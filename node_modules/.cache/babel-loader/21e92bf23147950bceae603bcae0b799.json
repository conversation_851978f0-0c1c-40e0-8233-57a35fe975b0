{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/ElementDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/ElementDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "showDialog", "elementParams", "imageElement", "title", "file", "left", "right", "h5Element", "mathBoard", "opts", "axisColor", "width", "height", "mathGraph", "mathBoardId", "expression", "color", "selectedColor", "mathGraphType", "type", "formula", "erasable", "top", "fontSize", "fontColor", "elementTab", "mathBoardIds", "mathGraphTypes", "label", "value", "TEduBoard", "TEduBoardMathGraphType", "NONE", "POINT", "LINE", "LINE_SEGMENT", "RAY", "CIRCLE", "ANGLE", "POLYGON", "VECTOR", "ELLIPSE", "CUBE", "CYLINDER", "CIRCULAR_CONE", "formulaEditorDialog", "loadingEditor", "watch", "handler", "_context", "_context2", "_mapInstanceProperty", "_filterInstanceProperty", "window", "teduBoard", "getBoardElementList", "call", "i", "TEduBoardElementType", "TEDU_BOARD_ELEMENT_MATH_BOARD", "elementId", "deep", "val", "_context3", "_context4", "mounted", "_this", "addEventListener", "event", "recvData", "isString", "Object", "prototype", "toString", "JSON", "parse", "cmd", "e", "methods", "show", "loanMinRule", "min", "concat", "loanMaxRule", "max", "addElement", "opt", "deg", "TEDU_BOARD_ELEMENT_H5", "TEDU_BOARD_ELEMENT_IMAGE", "TEDU_BOARD_ELEMENT_MATH_GRAPH", "$refs", "formulaForm", "validate", "TEDU_BOARD_ELEMENT_FORMULA", "setMathGraphType", "formulaEditorOnload", "contentWindow", "postMessage", "module"], "sources": ["src/components/ToolbarDialog/ElementDialog.vue"], "sourcesContent": ["<template>\n  <div>\n    <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n      <v-card>\n        <v-card-title class=\"headline lighten-2\"> 白板元素 </v-card-title>\n        <v-card-text>\n          <v-tabs v-model=\"elementTab\">\n            <v-tab>图片元素</v-tab>\n            <v-tab>H5元素</v-tab>\n            <v-tab>数学画板元素</v-tab>\n            <v-tab>数学函数图像元素</v-tab>\n            <v-tab>几何图形</v-tab>\n            <v-tab>学科公式</v-tab>\n          </v-tabs>\n          <v-row>\n            <v-col cols=\"12\" md=\"12\">\n              <v-tabs-items v-model=\"elementTab\">\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"8\">\n                      <v-text-field\n                        v-model=\"elementParams['imageElement'].file\"\n                        label=\"图片元素URL\"\n                        prepend-icon=\"mdi-image\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['imageElement'].left\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['imageElement'].top\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"6\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].file\"\n                        label=\"H5元素URL\"\n                        prepend-icon=\"mdi-language-html5\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].left\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].top\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].deg\"\n                        label=\"旋转角度\"\n                        suffix=\"°\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"3\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].opts.width\"\n                        label=\"宽度\"\n                        suffix=\"px\"\n                        prepend-icon=\"mdi-cog-outline\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].opts.height\"\n                        label=\"高度\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].opts.axisColor\"\n                        label=\"颜色\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].left\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].top\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"4\">\n                      <v-select\n                        v-model=\"elementParams['mathGraph'].opts.mathBoardId\"\n                        :items=\"mathBoardIds\"\n                        label=\"函数画板ID\"\n                      ></v-select>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"4\">\n                      <v-text-field\n                        v-model=\"elementParams['mathGraph'].opts.expression\"\n                        label=\"函数表达式\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathGraph'].opts.color\"\n                        label=\"函数图像颜色\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathGraph'].opts.selectedColor\"\n                        label=\"选中高亮颜色\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"4\">\n                      <v-select\n                        v-model=\"elementParams['mathGraphType'].type\"\n                        :items=\"mathGraphTypes\"\n                        item-text=\"label\"\n                        item-value=\"value\"\n                        label=\"几何图像类型\"\n                      ></v-select>\n                    </v-col>\n                    <v-col cols=\"6\" md=\"4\">\n                      <v-btn\n                        rounded\n                        color=\"primary\"\n                        dark\n                        small\n                        @click=\"setMathGraphType\"\n                        >设置</v-btn\n                      >\n                    </v-col>\n                    <v-col cols=\"18\" md=\"4\">\n                      使用此功能，需要先在白板上添加数学画板，然后在数学画板上进行点击操作，则会绘制出对应的图形\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <!-- 数学公式 -->\n                  <v-form ref=\"formulaForm\">\n                    <v-row>\n                      <v-col cols=\"12\" md=\"6\">\n                        <v-text-field\n                          v-model=\"elementParams['formula'].expression\"\n                          :rules=\"[(value) => !!value || '必填']\"\n                          label=\"latex公式\"\n                          prepend-icon=\"mdi-function-variant\"\n                          readonly\n                          @click=\"\n                            (formulaEditorDialog = true), (loadingEditor = true)\n                          \"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"12\" md=\"2\">\n                        <v-text-field\n                          v-model.number=\"elementParams['formula'].left\"\n                          :rules=\"[\n                            loanMinRule(elementParams['formula'].left, 0),\n                            loanMaxRule(elementParams['formula'].left, 99),\n                          ]\"\n                          label=\"左边距\"\n                          suffix=\"%\"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"12\" md=\"2\">\n                        <v-text-field\n                          v-model.number=\"elementParams['formula'].top\"\n                          :rules=\"[\n                            loanMinRule(elementParams['formula'].top, 0),\n                            loanMaxRule(elementParams['formula'].top, 99),\n                          ]\"\n                          label=\"上边距\"\n                          suffix=\"%\"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"12\" md=\"2\">\n                        <v-switch\n                          v-model=\"elementParams['formula'].erasable\"\n                          label=\"是否可以擦除\"\n                        ></v-switch>\n                      </v-col>\n                    </v-row>\n                    <v-row>\n                      <v-col cols=\"2\">\n                        <v-text-field\n                          v-model.number=\"elementParams['formula'].fontSize\"\n                          label=\"字号\"\n                          suffix=\"px\"\n                          :rules=\"[\n                            loanMinRule(elementParams['formula'].fontSize, 12),\n                            loanMaxRule(elementParams['formula'].fontSize, 48),\n                          ]\"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"4\">\n                        <v-color-picker\n                          v-model=\"elementParams['formula'].fontColor\"\n                          canvas-height=\"60\"\n                          width=\"300\"\n                          hide-inputs\n                          mode=\"hexa\"\n                          flat\n                        ></v-color-picker>\n                      </v-col>\n                    </v-row>\n                  </v-form>\n                </v-tab-item>\n              </v-tabs-items>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-divider></v-divider>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            v-if=\"elementTab !== 4\"\n            color=\"primary\"\n            text\n            @click=\"addElement\"\n            >添加</v-btn\n          >\n          <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <v-dialog\n      v-if=\"formulaEditorDialog\"\n      v-model=\"formulaEditorDialog\"\n      max-width=\"1050\"\n    >\n      <v-card>\n        <v-card-title>\n          公式编辑器\n          <a\n            class=\"ml-4 primary--text text-subtitle-2\"\n            style=\"position: absolute; right: 32px\"\n            @click=\"\n              window.open(\n                'https://demo.qcloudtiw.com/web/latest/res/tiw-formula-editor.zip'\n              )\n            \"\n          >\n            点击此处可下载源码\n          </a>\n        </v-card-title>\n        <v-card-text>\n          <v-progress-circular\n            :size=\"50\"\n            color=\"primary\"\n            indeterminate\n            v-if=\"loadingEditor\"\n            style=\"\n              position: absolute;\n              left: 50%;\n              top: 50%;\n              transform: translate(-50%, -50%);\n              z-index: 1000;\n            \"\n          ></v-progress-circular>\n          <iframe\n            src=\"https://demo.qcloudtiw.com/web/latest/formula-editor/examples/basic-usage.html\"\n            width=\"1000\"\n            height=\"500\"\n            style=\"border: none\"\n            allow=\"clipboard-read; clipboard-write\"\n            @load=\"formulaEditorOnload\"\n            v-if=\"formulaEditorDialog\"\n            ref=\"formual-editor-ref\"\n          />\n        </v-card-text>\n      </v-card>\n    </v-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      elementParams: {\n        imageElement: {\n          title: '图片元素',\n          file: '',\n          left: '',\n          right: '',\n        },\n        h5Element: {\n          title: 'H5元素',\n          file: '',\n          left: '',\n          right: '',\n        },\n        mathBoard: {\n          title: '数学画板元素',\n          opts: {\n            axisColor: '#AEAEAE',\n            width: '600',\n            height: '600',\n          },\n          left: '',\n          right: '',\n        },\n        mathGraph: {\n          title: '数学函数图像元素',\n          opts: {\n            mathBoardId: '',\n            expression: '',\n            color: '#ff0000',\n            selectedColor: '#00ff00',\n          },\n          left: '',\n          right: '',\n        },\n        mathGraphType: {\n          title: '几何图形',\n          type: null,\n        },\n        formula: {\n          title: '学科公式',\n          expression: '',\n          erasable: true, // 是否可以擦除\n          left: '10',\n          top: '10',\n          fontSize: 16,\n          fontColor: '#000000FF',\n        },\n      },\n      elementTab: null,\n      mathBoardIds: [],\n      mathGraphTypes: [\n        {\n          label: '选中',\n          value: TEduBoard.TEduBoardMathGraphType.NONE,\n        },\n        {\n          label: '点',\n          value: TEduBoard.TEduBoardMathGraphType.POINT,\n        },\n        {\n          label: '直线',\n          value: TEduBoard.TEduBoardMathGraphType.LINE,\n        },\n        {\n          label: '线段',\n          value: TEduBoard.TEduBoardMathGraphType.LINE_SEGMENT,\n        },\n        {\n          label: '射线',\n          value: TEduBoard.TEduBoardMathGraphType.RAY,\n        },\n        {\n          label: '圆',\n          value: TEduBoard.TEduBoardMathGraphType.CIRCLE,\n        },\n        {\n          label: '角',\n          value: TEduBoard.TEduBoardMathGraphType.ANGLE,\n        },\n        {\n          label: '多边形',\n          value: TEduBoard.TEduBoardMathGraphType.POLYGON,\n        },\n        {\n          label: '向量',\n          value: TEduBoard.TEduBoardMathGraphType.VECTOR,\n        },\n        {\n          label: '椭圆',\n          value: TEduBoard.TEduBoardMathGraphType.ELLIPSE,\n        },\n        {\n          label: '立方体',\n          value: TEduBoard.TEduBoardMathGraphType.CUBE,\n        },\n        {\n          label: '圆柱体',\n          value: TEduBoard.TEduBoardMathGraphType.CYLINDER,\n        },\n        {\n          label: '圆锥体',\n          value: TEduBoard.TEduBoardMathGraphType.CIRCULAR_CONE,\n        },\n      ],\n      formulaEditorDialog: false,\n      loadingEditor: true,\n    };\n  },\n\n  watch: {\n    '$store.state.current.fileInfo': {\n      handler() {\n        this.mathBoardIds = window.teduBoard\n          .getBoardElementList()\n          .filter(\n            (i) =>\n              i.type ===\n              TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_BOARD\n          )\n          .map((i) => i.elementId);\n      },\n      deep: true,\n    },\n    elementTab(val) {\n      if (val === 3) {\n        this.mathBoardIds = window.teduBoard\n          .getBoardElementList()\n          .filter(\n            (i) =>\n              i.type ===\n              TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_BOARD\n          )\n          .map((i) => i.elementId);\n      }\n    },\n  },\n\n  mounted() {\n    window.addEventListener('message', (event) => {\n      try {\n        const recvData = event.data;\n        const isString =\n          Object.prototype.toString.call(recvData) === '[object String]';\n        const data = isString ? JSON.parse(recvData) : recvData;\n        if (data.cmd === 'tedu-formula-data') {\n          this.elementParams.formula.expression = data.data;\n          this.formulaEditorDialog = false;\n          this.loadingEditor = true;\n        }\n      } catch (e) {\n        // console.error(e);\n      }\n    });\n  },\n\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n    loanMinRule(value, min) {\n      return (value || '') >= min || `不能小于最小值 ${min}`;\n    },\n    loanMaxRule(value, max) {\n      return (value || '') <= max || `不能大于最大值 ${max}`;\n    },\n    addElement() {\n      let type = '';\n      if (this.elementTab === 0) {\n        type = 'imageElement';\n      } else if (this.elementTab === 1) {\n        type = 'h5Element';\n      } else if (this.elementTab === 2) {\n        type = 'mathBoard';\n      } else if (this.elementTab === 3) {\n        type = 'mathGraph';\n      } else if (this.elementTab === 5) {\n        type = 'formula';\n      }\n\n      switch (type) {\n        case 'h5Element': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          const opt = {};\n          if (this.elementParams.h5Element.left) {\n            opt.left = this.elementParams.h5Element.left;\n          }\n          if (this.elementParams.h5Element.top) {\n            opt.top = this.elementParams.h5Element.top;\n          }\n          if (this.elementParams.h5Element.deg) {\n            opt.deg = this.elementParams.h5Element.deg;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_H5,\n            this.elementParams.h5Element.file,\n            opt\n          );\n          this.elementParams.h5Element.file = null;\n          this.elementParams.h5Element.left = '';\n          this.elementParams.h5Element.top = '';\n          break;\n        }\n        case 'imageElement': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          const opt = {};\n          if (this.elementParams.imageElement.left) {\n            opt.left = this.elementParams.imageElement.left;\n          }\n          if (this.elementParams.imageElement.top) {\n            opt.top = this.elementParams.imageElement.top;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_IMAGE,\n            this.elementParams.imageElement.file,\n            opt\n          );\n          this.elementParams.imageElement.file = null;\n          this.elementParams.imageElement.left = '';\n          this.elementParams.imageElement.top = '';\n          break;\n        }\n        case 'mathBoard': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          const opt = {};\n          if (this.elementParams.mathBoard.left) {\n            opt.left = this.elementParams.mathBoard.left;\n          }\n          if (this.elementParams.mathBoard.top) {\n            opt.top = this.elementParams.mathBoard.top;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_BOARD,\n            this.elementParams.mathBoard.opts,\n            opt\n          );\n          this.elementParams.mathBoard.opts = {\n            axisColor: '#AEAEAE',\n            width: '600',\n            height: '400',\n          };\n          this.elementParams.mathBoard.left = '';\n          this.elementParams.mathBoard.top = '';\n          break;\n        }\n        case 'mathGraph': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_GRAPH,\n            this.elementParams.mathGraph.opts\n          );\n          this.elementParams.mathGraph.opts = {\n            mathBoardId: '',\n            expression: '',\n            color: '#ff0000',\n            selectedColor: '#00ff00',\n          };\n          this.elementParams.mathGraph.left = '';\n          this.elementParams.mathGraph.top = '';\n          break;\n        }\n\n        case 'formula': {\n          if (!this.$refs.formulaForm.validate()) {\n            return;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_FORMULA,\n            {\n              expression: this.elementParams.formula.expression,\n            },\n            {\n              erasable: this.elementParams.formula.erasable,\n              left: `${this.elementParams.formula.left}%`,\n              top: `${this.elementParams.formula.top}%`,\n              fontSize: `${this.elementParams.formula.fontSize}px`,\n              fontColor: `${this.elementParams.formula.fontColor}`,\n            }\n          );\n          this.elementParams.formula = {\n            title: '学科公式',\n            expression: '',\n            erasable: true, // 是否可以擦除\n            left: 10,\n            top: 10,\n            fontSize: 16,\n            fontColor: '#000000FF',\n          };\n          break;\n        }\n      }\n      this.showDialog = false;\n    },\n    // 设置数学工具\n    setMathGraphType() {\n      window.teduBoard.setMathGraphType(\n        this.elementParams.mathGraphType.type,\n        true\n      );\n    },\n\n    formulaEditorOnload() {\n      this.loadingEditor = false;\n      const expression = this.elementParams.formula.expression;\n      if (expression) {\n        this.$refs['formual-editor-ref'].contentWindow.postMessage(\n          {\n            module: 'tiw',\n            expression: expression,\n          },\n          'https://demo.qcloudtiw.com/web/latest/formula-editor/examples/basic-usage.html'\n        );\n      }\n    },\n  },\n};\n</script>\n"], "mappings": ";;;;;;AAsTA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,aAAA;QACAC,YAAA;UACAC,KAAA;UACAC,IAAA;UACAC,IAAA;UACAC,KAAA;QACA;QACAC,SAAA;UACAJ,KAAA;UACAC,IAAA;UACAC,IAAA;UACAC,KAAA;QACA;QACAE,SAAA;UACAL,KAAA;UACAM,IAAA;YACAC,SAAA;YACAC,KAAA;YACAC,MAAA;UACA;UACAP,IAAA;UACAC,KAAA;QACA;QACAO,SAAA;UACAV,KAAA;UACAM,IAAA;YACAK,WAAA;YACAC,UAAA;YACAC,KAAA;YACAC,aAAA;UACA;UACAZ,IAAA;UACAC,KAAA;QACA;QACAY,aAAA;UACAf,KAAA;UACAgB,IAAA;QACA;QACAC,OAAA;UACAjB,KAAA;UACAY,UAAA;UACAM,QAAA;UAAA;UACAhB,IAAA;UACAiB,GAAA;UACAC,QAAA;UACAC,SAAA;QACA;MACA;MACAC,UAAA;MACAC,YAAA;MACAC,cAAA,GACA;QACAC,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAC;MACA,GACA;QACAJ,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAE;MACA,GACA;QACAL,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAG;MACA,GACA;QACAN,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAI;MACA,GACA;QACAP,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAK;MACA,GACA;QACAR,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAM;MACA,GACA;QACAT,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAO;MACA,GACA;QACAV,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAQ;MACA,GACA;QACAX,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAS;MACA,GACA;QACAZ,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAU;MACA,GACA;QACAb,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAW;MACA,GACA;QACAd,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAY;MACA,GACA;QACAf,KAAA;QACAC,KAAA,EAAAC,SAAA,CAAAC,sBAAA,CAAAa;MACA,EACA;MACAC,mBAAA;MACAC,aAAA;IACA;EACA;EAEAC,KAAA;IACA;MACAC,OAAA,WAAAA,QAAA;QAAA,IAAAC,QAAA,EAAAC,SAAA;QACA,KAAAxB,YAAA,GAAAyB,oBAAA,CAAAF,QAAA,GAAAG,uBAAA,CAAAF,SAAA,GAAAG,MAAA,CAAAC,SAAA,CACAC,mBAAA,IAAAC,IAAA,CAAAN,SAAA,EAEA,UAAAO,CAAA;UAAA,OACAA,CAAA,CAAAtC,IAAA,KACAW,SAAA,CAAA4B,oBAAA,CAAAC,6BAAA;QAAA,CACA,GAAAH,IAAA,CAAAP,QAAA,EACA,UAAAQ,CAAA;UAAA,OAAAA,CAAA,CAAAG,SAAA;QAAA;MACA;MACAC,IAAA;IACA;IACApC,UAAA,WAAAA,WAAAqC,GAAA;MACA,IAAAA,GAAA;QAAA,IAAAC,SAAA,EAAAC,SAAA;QACA,KAAAtC,YAAA,GAAAyB,oBAAA,CAAAY,SAAA,GAAAX,uBAAA,CAAAY,SAAA,GAAAX,MAAA,CAAAC,SAAA,CACAC,mBAAA,IAAAC,IAAA,CAAAQ,SAAA,EAEA,UAAAP,CAAA;UAAA,OACAA,CAAA,CAAAtC,IAAA,KACAW,SAAA,CAAA4B,oBAAA,CAAAC,6BAAA;QAAA,CACA,GAAAH,IAAA,CAAAO,SAAA,EACA,UAAAN,CAAA;UAAA,OAAAA,CAAA,CAAAG,SAAA;QAAA;MACA;IACA;EACA;EAEAK,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAb,MAAA,CAAAc,gBAAA,sBAAAC,KAAA;MACA;QACA,IAAAC,QAAA,GAAAD,KAAA,CAAArE,IAAA;QACA,IAAAuE,QAAA,GACAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAAjB,IAAA,CAAAa,QAAA;QACA,IAAAtE,IAAA,GAAAuE,QAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAN,QAAA,IAAAA,QAAA;QACA,IAAAtE,IAAA,CAAA6E,GAAA;UACAV,KAAA,CAAAjE,aAAA,CAAAmB,OAAA,CAAAL,UAAA,GAAAhB,IAAA,CAAAA,IAAA;UACAmE,KAAA,CAAArB,mBAAA;UACAqB,KAAA,CAAApB,aAAA;QACA;MACA,SAAA+B,CAAA;QACA;MAAA;IAEA;EACA;EAEAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAA/E,UAAA;IACA;IACAgF,WAAA,WAAAA,YAAAnD,KAAA,EAAAoD,GAAA;MACA,QAAApD,KAAA,WAAAoD,GAAA,kDAAAC,MAAA,CAAAD,GAAA;IACA;IACAE,WAAA,WAAAA,YAAAtD,KAAA,EAAAuD,GAAA;MACA,QAAAvD,KAAA,WAAAuD,GAAA,kDAAAF,MAAA,CAAAE,GAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAlE,IAAA;MACA,SAAAM,UAAA;QACAN,IAAA;MACA,gBAAAM,UAAA;QACAN,IAAA;MACA,gBAAAM,UAAA;QACAN,IAAA;MACA,gBAAAM,UAAA;QACAN,IAAA;MACA,gBAAAM,UAAA;QACAN,IAAA;MACA;MAEA,QAAAA,IAAA;QACA;UAAA;YACA;YACA,IAAAmE,GAAA;YACA,SAAArF,aAAA,CAAAM,SAAA,CAAAF,IAAA;cACAiF,GAAA,CAAAjF,IAAA,QAAAJ,aAAA,CAAAM,SAAA,CAAAF,IAAA;YACA;YACA,SAAAJ,aAAA,CAAAM,SAAA,CAAAe,GAAA;cACAgE,GAAA,CAAAhE,GAAA,QAAArB,aAAA,CAAAM,SAAA,CAAAe,GAAA;YACA;YACA,SAAArB,aAAA,CAAAM,SAAA,CAAAgF,GAAA;cACAD,GAAA,CAAAC,GAAA,QAAAtF,aAAA,CAAAM,SAAA,CAAAgF,GAAA;YACA;YACAlC,MAAA,CAAAC,SAAA,CAAA+B,UAAA,CACAvD,SAAA,CAAA4B,oBAAA,CAAA8B,qBAAA,EACA,KAAAvF,aAAA,CAAAM,SAAA,CAAAH,IAAA,EACAkF,GACA;YACA,KAAArF,aAAA,CAAAM,SAAA,CAAAH,IAAA;YACA,KAAAH,aAAA,CAAAM,SAAA,CAAAF,IAAA;YACA,KAAAJ,aAAA,CAAAM,SAAA,CAAAe,GAAA;YACA;UACA;QACA;UAAA;YACA;YACA,IAAAgE,IAAA;YACA,SAAArF,aAAA,CAAAC,YAAA,CAAAG,IAAA;cACAiF,IAAA,CAAAjF,IAAA,QAAAJ,aAAA,CAAAC,YAAA,CAAAG,IAAA;YACA;YACA,SAAAJ,aAAA,CAAAC,YAAA,CAAAoB,GAAA;cACAgE,IAAA,CAAAhE,GAAA,QAAArB,aAAA,CAAAC,YAAA,CAAAoB,GAAA;YACA;YACA+B,MAAA,CAAAC,SAAA,CAAA+B,UAAA,CACAvD,SAAA,CAAA4B,oBAAA,CAAA+B,wBAAA,EACA,KAAAxF,aAAA,CAAAC,YAAA,CAAAE,IAAA,EACAkF,IACA;YACA,KAAArF,aAAA,CAAAC,YAAA,CAAAE,IAAA;YACA,KAAAH,aAAA,CAAAC,YAAA,CAAAG,IAAA;YACA,KAAAJ,aAAA,CAAAC,YAAA,CAAAoB,GAAA;YACA;UACA;QACA;UAAA;YACA;YACA,IAAAgE,KAAA;YACA,SAAArF,aAAA,CAAAO,SAAA,CAAAH,IAAA;cACAiF,KAAA,CAAAjF,IAAA,QAAAJ,aAAA,CAAAO,SAAA,CAAAH,IAAA;YACA;YACA,SAAAJ,aAAA,CAAAO,SAAA,CAAAc,GAAA;cACAgE,KAAA,CAAAhE,GAAA,QAAArB,aAAA,CAAAO,SAAA,CAAAc,GAAA;YACA;YACA+B,MAAA,CAAAC,SAAA,CAAA+B,UAAA,CACAvD,SAAA,CAAA4B,oBAAA,CAAAC,6BAAA,EACA,KAAA1D,aAAA,CAAAO,SAAA,CAAAC,IAAA,EACA6E,KACA;YACA,KAAArF,aAAA,CAAAO,SAAA,CAAAC,IAAA;cACAC,SAAA;cACAC,KAAA;cACAC,MAAA;YACA;YACA,KAAAX,aAAA,CAAAO,SAAA,CAAAH,IAAA;YACA,KAAAJ,aAAA,CAAAO,SAAA,CAAAc,GAAA;YACA;UACA;QACA;UAAA;YACA;YACA+B,MAAA,CAAAC,SAAA,CAAA+B,UAAA,CACAvD,SAAA,CAAA4B,oBAAA,CAAAgC,6BAAA,EACA,KAAAzF,aAAA,CAAAY,SAAA,CAAAJ,IACA;YACA,KAAAR,aAAA,CAAAY,SAAA,CAAAJ,IAAA;cACAK,WAAA;cACAC,UAAA;cACAC,KAAA;cACAC,aAAA;YACA;YACA,KAAAhB,aAAA,CAAAY,SAAA,CAAAR,IAAA;YACA,KAAAJ,aAAA,CAAAY,SAAA,CAAAS,GAAA;YACA;UACA;QAEA;UAAA;YACA,UAAAqE,KAAA,CAAAC,WAAA,CAAAC,QAAA;cACA;YACA;YACAxC,MAAA,CAAAC,SAAA,CAAA+B,UAAA,CACAvD,SAAA,CAAA4B,oBAAA,CAAAoC,0BAAA,EACA;cACA/E,UAAA,OAAAd,aAAA,CAAAmB,OAAA,CAAAL;YACA,GACA;cACAM,QAAA,OAAApB,aAAA,CAAAmB,OAAA,CAAAC,QAAA;cACAhB,IAAA,KAAA6E,MAAA,MAAAjF,aAAA,CAAAmB,OAAA,CAAAf,IAAA;cACAiB,GAAA,KAAA4D,MAAA,MAAAjF,aAAA,CAAAmB,OAAA,CAAAE,GAAA;cACAC,QAAA,KAAA2D,MAAA,MAAAjF,aAAA,CAAAmB,OAAA,CAAAG,QAAA;cACAC,SAAA,KAAA0D,MAAA,MAAAjF,aAAA,CAAAmB,OAAA,CAAAI,SAAA;YACA,CACA;YACA,KAAAvB,aAAA,CAAAmB,OAAA;cACAjB,KAAA;cACAY,UAAA;cACAM,QAAA;cAAA;cACAhB,IAAA;cACAiB,GAAA;cACAC,QAAA;cACAC,SAAA;YACA;YACA;UACA;MACA;MACA,KAAAxB,UAAA;IACA;IACA;IACA+F,gBAAA,WAAAA,iBAAA;MACA1C,MAAA,CAAAC,SAAA,CAAAyC,gBAAA,CACA,KAAA9F,aAAA,CAAAiB,aAAA,CAAAC,IAAA,EACA,IACA;IACA;IAEA6E,mBAAA,WAAAA,oBAAA;MACA,KAAAlD,aAAA;MACA,IAAA/B,UAAA,QAAAd,aAAA,CAAAmB,OAAA,CAAAL,UAAA;MACA,IAAAA,UAAA;QACA,KAAA4E,KAAA,uBAAAM,aAAA,CAAAC,WAAA,CACA;UACAC,MAAA;UACApF,UAAA,EAAAA;QACA,GACA,gFACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}