{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/pl.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/pl.js", "mtime": 1757335237476}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/pl.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Nie znaleziono danych odpowiadających wyszukiwaniu',\n    loadingText: 'Wczytywanie danych...',\n  },\n  dataTable: {\n    itemsPerPageText: '<PERSON><PERSON><PERSON> na stronie:',\n    ariaLabel: {\n      sortDescending: 'Sortowanie malejąco. Kliknij aby zmienić.',\n      sortAscending: 'Sortowanie rosnąco. Kliknij aby zmienić.',\n      sortNone: 'Bez sortowania. Kliknij aby posortować rosnąco.',\n      activateNone: 'Kliknij aby usunąć sortowanie.',\n      activateDescending: 'Kliknij aby posortować malejąco.',\n      activateAscending: 'Kliknij aby posortować rosnąco.',\n    },\n    sortBy: 'Sortuj według',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Pozycji na stronie:',\n    itemsPerPageAll: 'Wszystkie',\n    nextPage: 'Na<PERSON><PERSON><PERSON>na strona',\n    prevPage: 'Poprzednia strona',\n    firstPage: '<PERSON><PERSON><PERSON> strona',\n    lastPage: 'Ostatnia strona',\n    pageText: '{0}-{1} z {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} dat(y)',\n    nextMonthAriaLabel: 'Następny miesiąc',\n    nextYearAriaLabel: 'Następny rok',\n    prevMonthAriaLabel: 'Poprzedni miesiąc',\n    prevYearAriaLabel: 'Poprzedni rok',\n  },\n  noDataText: 'Brak danych',\n  carousel: {\n    prev: 'Poprzedni obraz',\n    next: 'Następny obraz',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} więcej',\n  },\n  fileInput: {\n    counter: 'Liczba plików: {0}',\n    counterSize: 'Liczba plików: {0} (łącznie {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Nawigacja paginacyjna',\n      next: 'Następna strona',\n      previous: 'Poprzednia strona',\n      page: 'Idź do strony {0}',\n      currentPage: 'Bieżąca strona, strona {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,SADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,oDADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,qBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,2CADP;MAETC,aAAa,EAAE,0CAFN;MAGTC,QAAQ,EAAE,iDAHD;MAITC,YAAY,EAAE,gCAJL;MAKTC,kBAAkB,EAAE,kCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,WAFP;IAGVC,QAAQ,EAAE,iBAHA;IAIVC,QAAQ,EAAE,mBAJA;IAKVC,SAAS,EAAE,iBALD;IAMVC,QAAQ,EAAE,iBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,YADL;IAEVC,kBAAkB,EAAE,kBAFV;IAGVC,iBAAiB,EAAE,cAHT;IAIVC,kBAAkB,EAAE,mBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,aAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,gBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,oBADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,iBAFG;MAGTY,QAAQ,EAAE,mBAHD;MAITC,IAAI,EAAE,mBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}