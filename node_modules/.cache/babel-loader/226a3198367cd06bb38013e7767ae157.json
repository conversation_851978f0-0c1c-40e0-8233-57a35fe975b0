{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/dom.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/dom.js", "mtime": 1757335235447}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:LyoqCiAqIFJldHVybnM6CiAqICAtICdudWxsJyBpZiB0aGUgbm9kZSBpcyBub3QgYXR0YWNoZWQgdG8gdGhlIERPTQogKiAgLSB0aGUgcm9vdCBub2RlIChIVE1MRG9jdW1lbnQgfCBTaGFkb3dSb290KSBvdGhlcndpc2UKICovCmV4cG9ydCBmdW5jdGlvbiBhdHRhY2hlZFJvb3Qobm9kZSkgewogIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovCiAgaWYgKHR5cGVvZiBub2RlLmdldFJvb3ROb2RlICE9PSAnZnVuY3Rpb24nKSB7CiAgICAvLyBTaGFkb3cgRE9NIG5vdCBzdXBwb3J0ZWQgKElFMTEpLCBsZXRzIGZpbmQgdGhlIHJvb3Qgb2YgdGhpcyBub2RlCiAgICB3aGlsZSAobm9kZS5wYXJlbnROb2RlKSBub2RlID0gbm9kZS5wYXJlbnROb2RlOyAvLyBUaGUgcm9vdCBwYXJlbnQgaXMgdGhlIGRvY3VtZW50IGlmIHRoZSBub2RlIGlzIGF0dGFjaGVkIHRvIHRoZSBET00KCiAgICBpZiAobm9kZSAhPT0gZG9jdW1lbnQpIHJldHVybiBudWxsOwogICAgcmV0dXJuIGRvY3VtZW50OwogIH0KICB2YXIgcm9vdCA9IG5vZGUuZ2V0Um9vdE5vZGUoKTsgLy8gVGhlIGNvbXBvc2VkIHJvb3Qgbm9kZSBpcyB0aGUgZG9jdW1lbnQgaWYgdGhlIG5vZGUgaXMgYXR0YWNoZWQgdG8gdGhlIERPTQoKICBpZiAocm9vdCAhPT0gZG9jdW1lbnQgJiYgcm9vdC5nZXRSb290Tm9kZSh7CiAgICBjb21wb3NlZDogdHJ1ZQogIH0pICE9PSBkb2N1bWVudCkgcmV0dXJuIG51bGw7CiAgcmV0dXJuIHJvb3Q7Cn0="}, {"version": 3, "names": ["attachedRoot", "node", "getRootNode", "parentNode", "document", "root", "composed"], "sources": ["../../src/util/dom.ts"], "sourcesContent": ["/**\n * Returns:\n *  - 'null' if the node is not attached to the DOM\n *  - the root node (HTMLDocument | ShadowRoot) otherwise\n */\nexport function attachedRoot (node: Node): null | HTMLDocument | ShadowRoot {\n  /* istanbul ignore next */\n  if (typeof node.getRootNode !== 'function') {\n    // Shadow DOM not supported (IE11), lets find the root of this node\n    while (node.parentNode) node = node.parentNode\n\n    // The root parent is the document if the node is attached to the DOM\n    if (node !== document) return null\n\n    return document\n  }\n\n  const root = node.getRootNode()\n\n  // The composed root node is the document if the node is attached to the DOM\n  if (root !== document && root.getRootNode({ composed: true }) !== document) return null\n\n  return root as HTMLDocument | ShadowRoot\n}\n"], "mappings": "AAAA;;;;AAIG;AACH,OAAM,SAAUA,YAAVA,CAAwBC,IAAxB,EAAkC;EACtC;EACA,IAAI,OAAOA,IAAI,CAACC,WAAZ,KAA4B,UAAhC,EAA4C;IAC1C;IACA,OAAOD,IAAI,CAACE,UAAZ,EAAwBF,IAAI,GAAGA,IAAI,CAACE,UAAZ,CAFkB,CAI1C;;IACA,IAAIF,IAAI,KAAKG,QAAb,EAAuB,OAAO,IAAP;IAEvB,OAAOA,QAAP;EACD;EAED,IAAMC,IAAI,GAAGJ,IAAI,CAACC,WAAL,EAAb,CAZsC,CActC;;EACA,IAAIG,IAAI,KAAKD,QAAT,IAAqBC,IAAI,CAACH,WAAL,CAAiB;IAAEI,QAAQ,EAAE;EAAZ,CAAjB,MAAyCF,QAAlE,EAA4E,OAAO,IAAP;EAE5E,OAAOC,IAAP;AACD", "ignoreList": []}]}