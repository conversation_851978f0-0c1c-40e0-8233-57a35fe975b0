{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/VFlex.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/VFlex.js", "mtime": 1757335238274}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICIuLi8uLi8uLi9zcmMvY29tcG9uZW50cy9WR3JpZC9fZ3JpZC5zYXNzIjsKaW1wb3J0IEdyaWQgZnJvbSAnLi9ncmlkJzsKZXhwb3J0IGRlZmF1bHQgR3JpZCgnZmxleCcpOw=="}, {"version": 3, "names": ["Grid"], "sources": ["../../../src/components/VGrid/VFlex.ts"], "sourcesContent": ["import './_grid.sass'\n\nimport Grid from './grid'\n\nexport default Grid('flex')\n"], "mappings": "AAAA,OAAO,0CAAP;AAEA,OAAOA,IAAP,MAAiB,QAAjB;AAEA,eAAeA,IAAI,CAAC,MAAD,CAAnB", "ignoreList": []}]}