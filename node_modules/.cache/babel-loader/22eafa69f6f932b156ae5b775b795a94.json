{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSlider/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSlider/index.js", "mtime": 1757335236951}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZTbGlkZXIgZnJvbSAnLi9WU2xpZGVyJzsKZXhwb3J0IHsgVlNsaWRlciB9OwpleHBvcnQgZGVmYXVsdCBWU2xpZGVyOw=="}, {"version": 3, "names": ["VSlider"], "sources": ["../../../src/components/VSlider/index.ts"], "sourcesContent": ["import VSlider from './VSlider'\n\nexport { VSlider }\nexport default VSlider\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,WAApB;AAEA,SAASA,OAAT;AACA,eAAeA,OAAf", "ignoreList": []}]}