{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/fr.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/fr.js", "mtime": 1757335235818}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/fr.ts"], "sourcesContent": ["export default {\n  badge: 'Badge',\n  close: 'Ferm<PERSON>',\n  dataIterator: {\n    noResultsText: 'Aucun enregistrement correspondant trouvé',\n    loadingText: `Chargement de l'élément...`,\n  },\n  dataTable: {\n    itemsPerPageText: 'Lignes par page :',\n    ariaLabel: {\n      sortDescending: 'Tri décroissant.',\n      sortAscending: 'Tri croissant.',\n      sortNone: 'Non trié.',\n      activateNone: 'Activer pour supprimer le tri.',\n      activateDescending: 'Activer pour trier par ordre décroissant.',\n      activateAscending: 'Activer pour trier par ordre croissant.',\n    },\n    sortBy: 'Trier par',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Élements par page :',\n    itemsPerPageAll: 'Tous',\n    nextPage: 'Page suivante',\n    prevPage: 'Page précédente',\n    firstPage: 'Première page',\n    lastPage: 'Dernière page',\n    pageText: '{0}-{1} de {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} sélectionné(s)',\n    nextMonthAriaLabel: 'Le mois prochain',\n    nextYearAriaLabel: `L'année prochaine`,\n    prevMonthAriaLabel: 'Le mois précédent',\n    prevYearAriaLabel: 'Année précédente',\n  },\n  noDataText: 'Aucune donnée disponible',\n  carousel: {\n    prev: 'Visuel précédent',\n    next: 'Visuel suivant',\n    ariaLabel: {\n      delimiter: 'Diapositive {0} de {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} de plus',\n  },\n  fileInput: {\n    counter: '{0} fichier(s)',\n    counterSize: '{0} fichier(s) ({1} au total)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigation de pagination',\n      next: 'Page suivante',\n      previous: 'Page précédente',\n      page: 'Aller à la page {0}',\n      currentPage: 'Page actuelle, Page {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Note de {0} sur {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,OADM;EAEbC,KAAK,EAAE,QAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,2CADH;IAEZC,WAAW;EAFC,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,mBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,kBADP;MAETC,aAAa,EAAE,gBAFN;MAGTC,QAAQ,EAAE,WAHD;MAITC,YAAY,EAAE,gCAJL;MAKTC,kBAAkB,EAAE,2CALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,eAHA;IAIVC,QAAQ,EAAE,iBAJA;IAKVC,SAAS,EAAE,eALD;IAMVC,QAAQ,EAAE,eANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,oBADL;IAEVC,kBAAkB,EAAE,kBAFV;IAGVC,iBAAiB,wBAHP;IAIVC,kBAAkB,EAAE,mBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,0BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,kBADE;IAERC,IAAI,EAAE,gBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,gBADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,0BADA;MAETX,IAAI,EAAE,eAFG;MAGTY,QAAQ,EAAE,iBAHD;MAITC,IAAI,EAAE,qBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}