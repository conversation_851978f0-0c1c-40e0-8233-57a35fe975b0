{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/dependent/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/dependent/index.js", "mtime": 1757335237083}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mixins", "searchC<PERSON><PERSON>n", "children", "results", "index", "length", "child", "isActive", "isDependent", "push", "apply", "_toConsumableArray", "$children", "extend", "name", "data", "closeDependents", "watch", "val", "openDependents", "getOpenDependents", "methods", "getOpenDependentElements", "result", "getClickableDependentElements", "$el", "$refs", "content", "overlay"], "sources": ["../../../src/mixins/dependent/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\nimport mixins from '../../util/mixins'\nimport { VOverlay } from '../../components/VOverlay'\n\ninterface options {\n  $el: HTMLElement\n  $refs: {\n    content?: HTMLElement\n  }\n  overlay?: InstanceType<typeof VOverlay>\n}\n\ninterface DependentInstance extends Vue {\n  isActive?: boolean\n  isDependent?: boolean\n}\n\nfunction searchChildren (children: Vue[]): DependentInstance[] {\n  const results = []\n  for (let index = 0; index < children.length; index++) {\n    const child = children[index] as DependentInstance\n    if (child.isActive && child.isDependent) {\n      results.push(child)\n    } else {\n      results.push(...searchChildren(child.$children))\n    }\n  }\n\n  return results\n}\n\n/* @vue/component */\nexport default mixins<Vue & options>().extend({\n  name: 'dependent',\n\n  data () {\n    return {\n      closeDependents: true,\n      isActive: false,\n      isDependent: true,\n    }\n  },\n\n  watch: {\n    isActive (val) {\n      if (val) return\n\n      const openDependents = this.getOpenDependents()\n      for (let index = 0; index < openDependents.length; index++) {\n        openDependents[index].isActive = false\n      }\n    },\n  },\n\n  methods: {\n    getOpenDependents (): any[] {\n      if (this.closeDependents) return searchChildren(this.$children)\n\n      return []\n    },\n    getOpenDependentElements (): HTMLElement[] {\n      const result = []\n      const openDependents = this.getOpenDependents()\n\n      for (let index = 0; index < openDependents.length; index++) {\n        result.push(...openDependents[index].getClickableDependentElements())\n      }\n\n      return result\n    },\n    getClickableDependentElements (): HTMLElement[] {\n      const result = [this.$el]\n      if (this.$refs.content) result.push(this.$refs.content)\n      if (this.overlay) result.push(this.overlay.$el as HTMLElement)\n      result.push(...this.getOpenDependentElements())\n\n      return result\n    },\n  },\n})\n"], "mappings": ";;AAEA,OAAOA,MAAP,MAAmB,mBAAnB;AAgBA,SAASC,cAATA,CAAyBC,QAAzB,EAAwC;EACtC,IAAMC,OAAO,GAAG,EAAhB;EACA,KAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,QAAQ,CAACG,MAArC,EAA6CD,KAAK,EAAlD,EAAsD;IACpD,IAAME,KAAK,GAAGJ,QAAQ,CAACE,KAAD,CAAtB;IACA,IAAIE,KAAK,CAACC,QAAN,IAAkBD,KAAK,CAACE,WAA5B,EAAyC;MACvCL,OAAO,CAACM,IAAR,CAAaH,KAAb;IACD,CAFD,MAEO;MACLH,OAAO,CAACM,IAAR,CAAAC,KAAA,CAAAP,OAAO,EAAAQ,kBAAA,CAASV,cAAc,CAACK,KAAK,CAACM,SAAP,CAA9B;IACD;EACF;EAED,OAAOT,OAAP;AACD;AAED;;AACA,eAAeH,MAAM,GAAkBa,MAAxB,CAA+B;EAC5CC,IAAI,EAAE,WADsC;EAG5CC,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,eAAe,EAAE,IADZ;MAELT,QAAQ,EAAE,KAFL;MAGLC,WAAW,EAAE;IAHR,CAAP;EAKD,CAT2C;EAW5CS,KAAK,EAAE;IACLV,QAAQ,WAARA,QAAQA,CAAEW,GAAF,EAAK;MACX,IAAIA,GAAJ,EAAS;MAET,IAAMC,cAAc,GAAG,KAAKC,iBAAL,EAAvB;MACA,KAAK,IAAIhB,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGe,cAAc,CAACd,MAA3C,EAAmDD,KAAK,EAAxD,EAA4D;QAC1De,cAAc,CAACf,KAAD,CAAd,CAAsBG,QAAtB,GAAiC,KAAjC;MACD;IACF;EARI,CAXqC;EAsB5Cc,OAAO,EAAE;IACPD,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IAAI,KAAKJ,eAAT,EAA0B,OAAOf,cAAc,CAAC,KAAKW,SAAN,CAArB;MAE1B,OAAO,EAAP;IACD,CALM;IAMPU,wBAAwB,WAAxBA,wBAAwBA,CAAA;MACtB,IAAMC,MAAM,GAAG,EAAf;MACA,IAAMJ,cAAc,GAAG,KAAKC,iBAAL,EAAvB;MAEA,KAAK,IAAIhB,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGe,cAAc,CAACd,MAA3C,EAAmDD,KAAK,EAAxD,EAA4D;QAC1DmB,MAAM,CAACd,IAAP,CAAAC,KAAA,CAAAa,MAAM,EAAAZ,kBAAA,CAASQ,cAAc,CAACf,KAAD,CAAd,CAAsBoB,6BAAtB,EAAf;MACD;MAED,OAAOD,MAAP;IACD,CAfM;IAgBPC,6BAA6B,WAA7BA,6BAA6BA,CAAA;MAC3B,IAAMD,MAAM,GAAG,CAAC,KAAKE,GAAN,CAAf;MACA,IAAI,KAAKC,KAAL,CAAWC,OAAf,EAAwBJ,MAAM,CAACd,IAAP,CAAY,KAAKiB,KAAL,CAAWC,OAAvB;MACxB,IAAI,KAAKC,OAAT,EAAkBL,MAAM,CAACd,IAAP,CAAY,KAAKmB,OAAL,CAAaH,GAAzB;MAClBF,MAAM,CAACd,IAAP,CAAAC,KAAA,CAAAa,MAAM,EAAAZ,kBAAA,CAAS,KAAKW,wBAAL,EAAf;MAEA,OAAOC,MAAP;IACD;EAvBM;AAtBmC,CAA/B,CAAf", "ignoreList": []}]}