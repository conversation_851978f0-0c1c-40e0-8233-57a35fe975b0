{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/routable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/routable/index.js", "mtime": 1757335237229}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getObjectValueByPath", "extend", "name", "directives", "props", "activeClass", "String", "append", "Boolean", "disabled", "exact", "type", "undefined", "exactPath", "exactActiveClass", "link", "href", "Object", "to", "nuxt", "replace", "ripple", "tag", "target", "data", "isActive", "proxyClass", "computed", "classes", "computedRipple", "_a", "isClickable", "isLink", "$listeners", "click", "$attrs", "tabindex", "styles", "watch", "$route", "mounted", "onRouteChange", "methods", "generateRouteLink", "_defineProperty", "attrs", "style", "value", "_objectSpread", "path", "_context", "_context2", "_context3", "_context4", "_trimInstanceProperty", "_concatInstanceProperty", "concat", "call", "_Object$assign", "_context5", "_context6", "_context7", "_context8", "_this", "$refs", "$nextTick", "toggle"], "sources": ["../../../src/mixins/routable/index.ts"], "sourcesContent": ["import Vue, { VNodeData, PropType } from 'vue'\n\n// Directives\nimport R<PERSON>ple, { RippleOptions } from '../../directives/ripple'\n\n// Utilities\nimport { getObjectValueByPath } from '../../util/helpers'\n\nexport default Vue.extend({\n  name: 'routable',\n\n  directives: {\n    Ripple,\n  },\n\n  props: {\n    activeClass: String,\n    append: <PERSON>olean,\n    disabled: Boolean,\n    exact: {\n      type: Boolean as PropType<boolean | undefined>,\n      default: undefined,\n    },\n    exactPath: Boolean,\n    exactActiveClass: String,\n    link: Boolean,\n    href: [String, Object],\n    to: [String, Object],\n    nuxt: Boolean,\n    replace: Boolean,\n    ripple: {\n      type: [Boolean, Object],\n      default: null,\n    },\n    tag: String,\n    target: String,\n  },\n\n  data: () => ({\n    isActive: false,\n    proxyClass: '',\n  }),\n\n  computed: {\n    classes (): object {\n      const classes: Record<string, boolean> = {}\n\n      if (this.to) return classes\n\n      if (this.activeClass) classes[this.activeClass] = this.isActive\n      if (this.proxyClass) classes[this.proxyClass] = this.isActive\n\n      return classes\n    },\n    computedRipple (): RippleOptions | boolean {\n      return this.ripple ?? (!this.disabled && this.isClickable)\n    },\n    isClickable (): boolean {\n      if (this.disabled) return false\n\n      return Boolean(\n        this.isLink ||\n        this.$listeners.click ||\n        this.$listeners['!click'] ||\n        this.$attrs.tabindex\n      )\n    },\n    isLink (): boolean {\n      return this.to || this.href || this.link\n    },\n    styles: () => ({}),\n  },\n\n  watch: {\n    $route: 'onRouteChange',\n  },\n\n  mounted () {\n    this.onRouteChange()\n  },\n\n  methods: {\n    generateRouteLink () {\n      let exact = this.exact\n      let tag\n\n      const data: VNodeData = {\n        attrs: {\n          tabindex: 'tabindex' in this.$attrs ? this.$attrs.tabindex : undefined,\n        },\n        class: this.classes,\n        style: this.styles,\n        props: {},\n        directives: [{\n          name: 'ripple',\n          value: this.computedRipple,\n        }],\n        [this.to ? 'nativeOn' : 'on']: {\n          ...this.$listeners,\n          ...('click' in this ? { click: (this as any).click } : undefined), // #14447\n        },\n        ref: 'link',\n      }\n\n      if (typeof this.exact === 'undefined') {\n        exact = this.to === '/' ||\n          (this.to === Object(this.to) && this.to.path === '/')\n      }\n\n      if (this.to) {\n        // Add a special activeClass hook\n        // for component level styles\n        let activeClass = this.activeClass\n        let exactActiveClass = this.exactActiveClass || activeClass\n\n        if (this.proxyClass) {\n          activeClass = `${activeClass} ${this.proxyClass}`.trim()\n          exactActiveClass = `${exactActiveClass} ${this.proxyClass}`.trim()\n        }\n\n        tag = this.nuxt ? 'nuxt-link' : 'router-link'\n        Object.assign(data.props, {\n          to: this.to,\n          exact,\n          exactPath: this.exactPath,\n          activeClass,\n          exactActiveClass,\n          append: this.append,\n          replace: this.replace,\n        })\n      } else {\n        tag = (this.href && 'a') || this.tag || 'div'\n\n        if (tag === 'a' && this.href) data.attrs!.href = this.href\n      }\n\n      if (this.target) data.attrs!.target = this.target\n\n      return { tag, data }\n    },\n    onRouteChange () {\n      if (!this.to || !this.$refs.link || !this.$route) return\n      const activeClass = `${this.activeClass || ''} ${this.proxyClass || ''}`.trim()\n      const exactActiveClass = `${this.exactActiveClass || ''} ${this.proxyClass || ''}`.trim() || activeClass\n\n      const path = '_vnode.data.class.' + (this.exact ? exactActiveClass : activeClass)\n\n      this.$nextTick(() => {\n        /* istanbul ignore else */\n        if (!getObjectValueByPath(this.$refs.link, path) === this.isActive) {\n          this.toggle()\n        }\n      })\n    },\n    toggle () {\n      this.isActive = !this.isActive\n    },\n  },\n})\n"], "mappings": ";;;;;;;;AAAA,OAAOA,GAAP,MAAyC,KAAzC,C,CAEA;;AACA,OAAOC,MAAP,MAAsC,yBAAtC,C,CAEA;;AACA,SAASC,oBAAT,QAAqC,oBAArC;AAEA,eAAeF,GAAG,CAACG,MAAJ,CAAW;EACxBC,IAAI,EAAE,UADkB;EAGxBC,UAAU,EAAE;IACVJ,MAAA,EAAAA;EADU,CAHY;EAOxBK,KAAK,EAAE;IACLC,WAAW,EAAEC,MADR;IAELC,MAAM,EAAEC,OAFH;IAGLC,QAAQ,EAAED,OAHL;IAILE,KAAK,EAAE;MACLC,IAAI,EAAEH,OADD;MAEL,WAASI;IAFJ,CAJF;IAQLC,SAAS,EAAEL,OARN;IASLM,gBAAgB,EAAER,MATb;IAULS,IAAI,EAAEP,OAVD;IAWLQ,IAAI,EAAE,CAACV,MAAD,EAASW,MAAT,CAXD;IAYLC,EAAE,EAAE,CAACZ,MAAD,EAASW,MAAT,CAZC;IAaLE,IAAI,EAAEX,OAbD;IAcLY,OAAO,EAAEZ,OAdJ;IAeLa,MAAM,EAAE;MACNV,IAAI,EAAE,CAACH,OAAD,EAAUS,MAAV,CADA;MAEN,WAAS;IAFH,CAfH;IAmBLK,GAAG,EAAEhB,MAnBA;IAoBLiB,MAAM,EAAEjB;EApBH,CAPiB;EA8BxBkB,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,QAAQ,EAAE,KADC;MAEXC,UAAU,EAAE;IAFD,CAAP;EAAA,CA9BkB;EAmCxBC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,IAAMA,OAAO,GAA4B,EAAzC;MAEA,IAAI,KAAKV,EAAT,EAAa,OAAOU,OAAP;MAEb,IAAI,KAAKvB,WAAT,EAAsBuB,OAAO,CAAC,KAAKvB,WAAN,CAAP,GAA4B,KAAKoB,QAAjC;MACtB,IAAI,KAAKC,UAAT,EAAqBE,OAAO,CAAC,KAAKF,UAAN,CAAP,GAA2B,KAAKD,QAAhC;MAErB,OAAOG,OAAP;IACD,CAVO;IAWRC,cAAc,WAAdA,cAAcA,CAAA;;MACZ,OAAO,CAAAC,EAAA,QAAKT,MAAL,MAAW,IAAX,IAAWS,EAAA,WAAX,GAAWA,EAAX,GAAgB,CAAC,KAAKrB,QAAN,IAAkB,KAAKsB,WAA9C;IACD,CAbO;IAcRA,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAI,KAAKtB,QAAT,EAAmB,OAAO,KAAP;MAEnB,OAAOD,OAAO,CACZ,KAAKwB,MAAL,IACA,KAAKC,UAAL,CAAgBC,KADhB,IAEA,KAAKD,UAAL,CAAgB,QAAhB,CAFA,IAGA,KAAKE,MAAL,CAAYC,QAJA,CAAd;IAMD,CAvBO;IAwBRJ,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO,KAAKd,EAAL,IAAW,KAAKF,IAAhB,IAAwB,KAAKD,IAApC;IACD,CA1BO;IA2BRsB,MAAM,EAAE,SAARA,MAAMA,CAAA;MAAA,OAAS,EAAP;IAAA;EA3BA,CAnCc;EAiExBC,KAAK,EAAE;IACLC,MAAM,EAAE;EADH,CAjEiB;EAqExBC,OAAO,WAAPA,OAAOA,CAAA;IACL,KAAKC,aAAL;EACD,CAvEuB;EAyExBC,OAAO,EAAE;IACPC,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IAAIjC,KAAK,GAAG,KAAKA,KAAjB;MACA,IAAIY,GAAJ;MAEA,IAAME,IAAI,GAAAoB,eAAA,CAAAA,eAAA;QACRC,KAAK,EAAE;UACLT,QAAQ,EAAE,cAAc,KAAKD,MAAnB,GAA4B,KAAKA,MAAL,CAAYC,QAAxC,GAAmDxB;QADxD,CADe;QAItB,SAAO,KAAKgB,OAJU;QAKtBkB,KAAK,EAAE,KAAKT,MALU;QAMtBjC,KAAK,EAAE,EANe;QAOtBD,UAAU,EAAE,CAAC;UACXD,IAAI,EAAE,QADK;UAEX6C,KAAK,EAAE,KAAKlB;QAFD,CAAD;MAPU,GAWrB,KAAKX,EAAL,GAAU,UAAV,GAAuB,IAAxB,EAAA8B,aAAA,CAAAA,aAAA,KACK,KAAKf,UADqB,GAEzB,WAAW,IAAX,GAAkB;QAAEC,KAAK,EAAG,KAAaA;MAAvB,CAAlB,GAAmDtB,SAAvD,WAEG,OAfP;MAkBA,IAAI,OAAO,KAAKF,KAAZ,KAAsB,WAA1B,EAAuC;QACrCA,KAAK,GAAG,KAAKQ,EAAL,KAAY,GAAZ,IACL,KAAKA,EAAL,KAAYD,MAAM,CAAC,KAAKC,EAAN,CAAlB,IAA+B,KAAKA,EAAL,CAAQ+B,IAAR,KAAiB,GADnD;MAED;MAED,IAAI,KAAK/B,EAAT,EAAa;QACX;QACA;QACA,IAAIb,WAAW,GAAG,KAAKA,WAAvB;QACA,IAAIS,gBAAgB,GAAG,KAAKA,gBAAL,IAAyBT,WAAhD;QAEA,IAAI,KAAKqB,UAAT,EAAqB;UAAA,IAAAwB,QAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA;UACnBhD,WAAW,GAAGiD,qBAAA,CAAAJ,QAAA,GAAAK,uBAAA,CAAAJ,SAAA,MAAAK,MAAA,CAAGnD,WAAW,QAAAoD,IAAA,CAAAN,SAAA,EAAI,KAAKzB,UAAU,GAAA+B,IAAA,CAAAP,QAAjC,CAAd;UACApC,gBAAgB,GAAGwC,qBAAA,CAAAF,SAAA,GAAAG,uBAAA,CAAAF,SAAA,MAAAG,MAAA,CAAG1C,gBAAgB,QAAA2C,IAAA,CAAAJ,SAAA,EAAI,KAAK3B,UAAU,GAAA+B,IAAA,CAAAL,SAAtC,CAAnB;QACD;QAED9B,GAAG,GAAG,KAAKH,IAAL,GAAY,WAAZ,GAA0B,aAAhC;QACAuC,cAAA,CAAclC,IAAI,CAACpB,KAAnB,EAA0B;UACxBc,EAAE,EAAE,KAAKA,EADe;UAExBR,KAFwB,EAExBA,KAFwB;UAGxBG,SAAS,EAAE,KAAKA,SAHQ;UAIxBR,WAJwB,EAIxBA,WAJwB;UAKxBS,gBALwB,EAKxBA,gBALwB;UAMxBP,MAAM,EAAE,KAAKA,MANW;UAOxBa,OAAO,EAAE,KAAKA;QAPU,CAA1B;MASD,CArBD,MAqBO;QACLE,GAAG,GAAI,KAAKN,IAAL,IAAa,GAAd,IAAsB,KAAKM,GAA3B,IAAkC,KAAxC;QAEA,IAAIA,GAAG,KAAK,GAAR,IAAe,KAAKN,IAAxB,EAA8BQ,IAAI,CAACqB,KAAL,CAAY7B,IAAZ,GAAmB,KAAKA,IAAxB;MAC/B;MAED,IAAI,KAAKO,MAAT,EAAiBC,IAAI,CAACqB,KAAL,CAAYtB,MAAZ,GAAqB,KAAKA,MAA1B;MAEjB,OAAO;QAAED,GAAF,EAAEA,GAAF;QAAOE,IAAA,EAAAA;MAAP,CAAP;IACD,CA1DM;IA2DPiB,aAAa,WAAbA,aAAaA,CAAA;MAAA,IAAAkB,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,SAAA;QAAAC,KAAA;MACX,IAAI,CAAC,KAAK7C,EAAN,IAAY,CAAC,KAAK8C,KAAL,CAAWjD,IAAxB,IAAgC,CAAC,KAAKwB,MAA1C,EAAkD;MAClD,IAAMlC,WAAW,GAAGiD,qBAAA,CAAAK,SAAA,GAAAJ,uBAAA,CAAAK,SAAA,MAAAJ,MAAA,CAAG,KAAKnD,WAAL,IAAoB,EAAE,QAAAoD,IAAA,CAAAG,SAAA,EAAI,KAAKlC,UAAL,IAAmB,EAAE,GAAA+B,IAAA,CAAAE,SAAlD,CAApB;MACA,IAAM7C,gBAAgB,GAAGwC,qBAAA,CAAAO,SAAA,GAAAN,uBAAA,CAAAO,SAAA,MAAAN,MAAA,CAAG,KAAK1C,gBAAL,IAAyB,EAAE,QAAA2C,IAAA,CAAAK,SAAA,EAAI,KAAKpC,UAAL,IAAmB,EAAE,GAAA+B,IAAA,CAAAI,SAAvD,KAAoExD,WAA7F;MAEA,IAAM4C,IAAI,GAAG,wBAAwB,KAAKvC,KAAL,GAAaI,gBAAb,GAAgCT,WAAxD,CAAb;MAEA,KAAK4D,SAAL,CAAe,YAAK;QAClB;QACA,IAAI,CAACjE,oBAAoB,CAAC+D,KAAA,CAAKC,KAAL,CAAWjD,IAAZ,EAAkBkC,IAAlB,CAArB,KAAiDc,KAAA,CAAKtC,QAA1D,EAAoE;UAClEsC,KAAA,CAAKG,MAAL;QACD;MACF,CALD;IAMD,CAxEM;IAyEPA,MAAM,WAANA,MAAMA,CAAA;MACJ,KAAKzC,QAAL,GAAgB,CAAC,KAAKA,QAAtB;IACD;EA3EM;AAzEe,CAAX,CAAf", "ignoreList": []}]}