{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/th.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/th.js", "mtime": 1757335237539}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/th.ts"], "sourcesContent": ["export default {\n  badge: 'สัญลักษณ์',\n  close: 'ปิด',\n  dataIterator: {\n    noResultsText: 'ไม่พบข้อมูลที่ค้นหา',\n    loadingText: 'กำลังโหลดข้อมูล...',\n  },\n  dataTable: {\n    itemsPerPageText: 'แถวต่อหน้า:',\n    ariaLabel: {\n      sortDescending: 'เรียงจากมากไปน้อยอยู่',\n      sortAscending: 'เรียงจากน้อยไปมากอยู่',\n      sortNone: 'ไม่ได้เรียงลำดับ',\n      activateNone: 'กดเพื่อปิดการเรียงลำดับ',\n      activateDescending: 'กดเพื่อเรียงจากมากไปน้อย',\n      activateAscending: 'กดเพื่อเรียงจากน้อยไปมาก',\n    },\n    sortBy: 'เรียงตาม',\n  },\n  dataFooter: {\n    itemsPerPageText: 'รายการต่อหน้า:',\n    itemsPerPageAll: 'ทั้งหมด',\n    nextPage: 'หน้าต่อไป',\n    prevPage: 'หน้าที่แล้ว',\n    firstPage: 'หน้าแรก',\n    lastPage: 'หน้าสุดท้าย',\n    pageText: '{0}-{1} จาก {2}',\n  },\n  datePicker: {\n    itemsSelected: 'เลือก {0} วัน',\n    nextMonthAriaLabel: 'เดือนถัดไป',\n    nextYearAriaLabel: 'ปีถัดไป',\n    prevMonthAriaLabel: 'เดือนก่อนหน้า',\n    prevYearAriaLabel: 'ปีก่อนหน้า',\n  },\n  noDataText: 'ไม่มีข้อมูล',\n  carousel: {\n    prev: 'ภาพก่อนหน้า',\n    next: 'ภาพถัดไป',\n    ariaLabel: {\n      delimiter: 'ภาพสไลด์ที่ {0} จาก {1}',\n    },\n  },\n  calendar: {\n    moreEvents: 'มีอีก {0}',\n  },\n  fileInput: {\n    counter: '{0} ไฟล์',\n    counterSize: '{0} ไฟล์ (รวม {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'การนำทางไปยังหน้า',\n      next: 'หน้าต่อไป',\n      previous: 'หน้าที่แล้ว',\n      page: 'ไปที่หน้า {0}',\n      currentPage: 'หน้าปัจจุบัน (หน้า {0})',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,WADM;EAEbC,KAAK,EAAE,KAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,qBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,aADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,uBADP;MAETC,aAAa,EAAE,uBAFN;MAGTC,QAAQ,EAAE,kBAHD;MAITC,YAAY,EAAE,yBAJL;MAKTC,kBAAkB,EAAE,0BALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,gBADR;IAEVU,eAAe,EAAE,SAFP;IAGVC,QAAQ,EAAE,WAHA;IAIVC,QAAQ,EAAE,aAJA;IAKVC,SAAS,EAAE,SALD;IAMVC,QAAQ,EAAE,aANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,eADL;IAEVC,kBAAkB,EAAE,YAFV;IAGVC,iBAAiB,EAAE,SAHT;IAIVC,kBAAkB,EAAE,eAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,aAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,aADE;IAERC,IAAI,EAAE,UAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,UADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,mBADA;MAETX,IAAI,EAAE,WAFG;MAGTY,QAAQ,EAAE,aAHD;MAITC,IAAI,EAAE,eAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}