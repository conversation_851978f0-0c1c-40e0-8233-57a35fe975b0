{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/pt.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/pt.js", "mtime": 1757335237489}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/pt.ts"], "sourcesContent": ["export default {\n  badge: 'Distint<PERSON>',\n  close: '<PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Nenhum dado encontrado',\n    loadingText: 'Carregando itens...',\n  },\n  dataTable: {\n    itemsPerPageText: '<PERSON>has por página:',\n    ariaLabel: {\n      sortDescending: 'Ordenado decrescente.',\n      sortAscending: 'Ordenado crescente.',\n      sortNone: 'Não ordenado.',\n      activateNone: 'Ative para remover a ordenação.',\n      activateDescending: 'Ative para ordenar decrescente.',\n      activateAscending: 'Ative para ordenar crescente.',\n    },\n    sortBy: 'Ordenar por',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Itens por página:',\n    itemsPerPageAll: 'Todos',\n    nextPage: 'Próxima página',\n    prevPage: 'Página anterior',\n    firstPage: 'Primeira página',\n    lastPage: 'Última página',\n    pageText: '{0}-{1} de {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} selecionado(s)',\n    nextMonthAriaLabel: 'Próximo mês',\n    nextYearAriaLabel: 'Próximo ano',\n    prevMonthAriaLabel: 'Mês anterior',\n    prevYearAriaLabel: 'Ano anterior',\n  },\n  noDataText: 'Não há dados disponíveis',\n  carousel: {\n    prev: 'Visão anterior',\n    next: 'Próxima visão',\n    ariaLabel: {\n      delimiter: 'Slide {0} de {1} do carrossel',\n    },\n  },\n  calendar: {\n    moreEvents: 'Mais {0}',\n  },\n  fileInput: {\n    counter: '{0} arquivo(s)',\n    counterSize: '{0} arquivo(s) ({1} no total)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navegação de paginação',\n      next: 'Próxima página',\n      previous: 'Página anterior',\n      page: 'Ir à página {0}',\n      currentPage: 'Página atual, página {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,YADM;EAEbC,KAAK,EAAE,QAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,wBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,oBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,uBADP;MAETC,aAAa,EAAE,qBAFN;MAGTC,QAAQ,EAAE,eAHD;MAITC,YAAY,EAAE,iCAJL;MAKTC,kBAAkB,EAAE,iCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,mBADR;IAEVU,eAAe,EAAE,OAFP;IAGVC,QAAQ,EAAE,gBAHA;IAIVC,QAAQ,EAAE,iBAJA;IAKVC,SAAS,EAAE,iBALD;IAMVC,QAAQ,EAAE,eANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,oBADL;IAEVC,kBAAkB,EAAE,aAFV;IAGVC,iBAAiB,EAAE,aAHT;IAIVC,kBAAkB,EAAE,cAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,0BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,gBADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,gBADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,wBADA;MAETX,IAAI,EAAE,gBAFG;MAGTY,QAAQ,EAAE,iBAHD;MAITC,IAAI,EAAE,iBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}