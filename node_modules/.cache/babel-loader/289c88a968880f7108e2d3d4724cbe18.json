{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabs.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabs.js", "mtime": 1757335239361}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VTabsBar", "VTabsItems", "VTabsSlider", "Colorable", "Proxyable", "Themeable", "Resize", "convertToUnit", "mixins", "baseMixins", "extend", "name", "directives", "props", "activeClass", "type", "String", "alignWithTitle", "Boolean", "backgroundColor", "centerActive", "centered", "fixedTabs", "grow", "height", "Number", "undefined", "hideSlider", "iconsAndText", "mobileBreakpoint", "nextIcon", "optional", "prevIcon", "right", "showArrows", "sliderColor", "sliderSize", "vertical", "data", "resizeTimeout", "slider", "left", "top", "width", "transitionTime", "computed", "classes", "_objectSpread", "themeClasses", "isReversed", "$vuetify", "rtl", "sliderStyles", "transition", "computedColor", "color", "isDark", "appIsDark", "watch", "mounted", "_this", "ResizeObserver", "obs", "onResize", "observe", "$el", "$on", "disconnect", "$nextTick", "_setTimeout", "callSlider", "methods", "_this2", "$refs", "items", "selectedItems", "length", "activeTab", "el", "scrollHeight", "offsetLeft", "offsetWidth", "offsetTop", "scrollWidth", "genBar", "_this3", "style", "dark", "light", "mandatory", "value", "internalValue", "on", "change", "val", "ref", "setTextColor", "setBackgroundColor", "$createElement", "genSlider", "genItems", "item", "_this4", "staticClass", "_isDestroyed", "clearTimeout", "parseNodes", "tab", "slot", "$slots", "i", "vnode", "componentOptions", "Ctor", "options", "push", "render", "h", "_this$parseNodes", "modifiers", "quiet"], "sources": ["../../../src/components/VTabs/VTabs.ts"], "sourcesContent": ["// Styles\nimport './VTabs.sass'\n\n// Components\nimport VTabsBar from './VTabsBar'\nimport VTabsItems from './VTabsItems'\nimport VTabsSlider from './VTabsSlider'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport Proxyable from '../../mixins/proxyable'\nimport Themeable from '../../mixins/themeable'\n\n// Directives\nimport Resize from '../../directives/resize'\n\n// Utilities\nimport { convertToUnit } from '../../util/helpers'\nimport { ExtractVue } from './../../util/mixins'\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue/types'\n\nconst baseMixins = mixins(\n  Colorable,\n  Proxyable,\n  Themeable\n)\n\ninterface options extends ExtractVue<typeof baseMixins> {\n  $refs: {\n    items: InstanceType<typeof VTabsBar>\n  }\n}\n\nexport default baseMixins.extend<options>().extend({\n  name: 'v-tabs',\n\n  directives: {\n    Resize,\n  },\n\n  props: {\n    activeClass: {\n      type: String,\n      default: '',\n    },\n    alignWithTitle: Boolean,\n    backgroundColor: String,\n    centerActive: Boolean,\n    centered: Boolean,\n    fixedTabs: Boolean,\n    grow: Boolean,\n    height: {\n      type: [Number, String],\n      default: undefined,\n    },\n    hideSlider: Boolean,\n    iconsAndText: Boolean,\n    mobileBreakpoint: [String, Number],\n    nextIcon: {\n      type: String,\n      default: '$next',\n    },\n    optional: Boolean,\n    prevIcon: {\n      type: String,\n      default: '$prev',\n    },\n    right: Boolean,\n    showArrows: [Boolean, String],\n    sliderColor: String,\n    sliderSize: {\n      type: [Number, String],\n      default: 2,\n    },\n    vertical: Boolean,\n  },\n\n  data () {\n    return {\n      resizeTimeout: 0,\n      slider: {\n        height: null as null | number,\n        left: null as null | number,\n        right: null as null | number,\n        top: null as null | number,\n        width: null as null | number,\n      },\n      transitionTime: 300,\n    }\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        'v-tabs--align-with-title': this.alignWithTitle,\n        'v-tabs--centered': this.centered,\n        'v-tabs--fixed-tabs': this.fixedTabs,\n        'v-tabs--grow': this.grow,\n        'v-tabs--icons-and-text': this.iconsAndText,\n        'v-tabs--right': this.right,\n        'v-tabs--vertical': this.vertical,\n        ...this.themeClasses,\n      }\n    },\n    isReversed (): boolean {\n      return this.$vuetify.rtl && this.vertical\n    },\n    sliderStyles (): object {\n      return {\n        height: convertToUnit(this.slider.height),\n        left: this.isReversed ? undefined : convertToUnit(this.slider.left),\n        right: this.isReversed ? convertToUnit(this.slider.right) : undefined,\n        top: this.vertical ? convertToUnit(this.slider.top) : undefined,\n        transition: this.slider.left != null ? null : 'none',\n        width: convertToUnit(this.slider.width),\n      }\n    },\n    computedColor (): string {\n      if (this.color) return this.color\n      else if (this.isDark && !this.appIsDark) return 'white'\n      else return 'primary'\n    },\n  },\n\n  watch: {\n    alignWithTitle: 'callSlider',\n    centered: 'callSlider',\n    centerActive: 'callSlider',\n    fixedTabs: 'callSlider',\n    grow: 'callSlider',\n    iconsAndText: 'callSlider',\n    right: 'callSlider',\n    showArrows: 'callSlider',\n    vertical: 'callSlider',\n    '$vuetify.application.left': 'onResize',\n    '$vuetify.application.right': 'onResize',\n    '$vuetify.rtl': 'onResize',\n  },\n\n  mounted () {\n    if (typeof ResizeObserver !== 'undefined') {\n      const obs = new ResizeObserver(() => {\n        this.onResize()\n      })\n      obs.observe(this.$el)\n      this.$on('hook:destroyed', () => {\n        obs.disconnect()\n      })\n    }\n\n    this.$nextTick(() => {\n      window.setTimeout(this.callSlider, 30)\n    })\n  },\n\n  methods: {\n    callSlider () {\n      if (\n        this.hideSlider ||\n        !this.$refs.items ||\n        !this.$refs.items.selectedItems.length\n      ) {\n        this.slider.width = 0\n        return false\n      }\n\n      this.$nextTick(() => {\n        // Give screen time to paint\n        const activeTab = this.$refs.items.selectedItems[0]\n        /* istanbul ignore if */\n        if (!activeTab || !activeTab.$el) {\n          this.slider.width = 0\n          this.slider.left = 0\n          return\n        }\n        const el = activeTab.$el as HTMLElement\n\n        this.slider = {\n          height: !this.vertical ? Number(this.sliderSize) : el.scrollHeight,\n          left: this.vertical ? 0 : el.offsetLeft,\n          right: this.vertical ? 0 : el.offsetLeft + el.offsetWidth,\n          top: el.offsetTop,\n          width: this.vertical ? Number(this.sliderSize) : el.scrollWidth,\n        }\n      })\n\n      return true\n    },\n    genBar (items: VNode[], slider: VNode | null) {\n      const data = {\n        style: {\n          height: convertToUnit(this.height),\n        },\n        props: {\n          activeClass: this.activeClass,\n          centerActive: this.centerActive,\n          dark: this.dark,\n          light: this.light,\n          mandatory: !this.optional,\n          mobileBreakpoint: this.mobileBreakpoint,\n          nextIcon: this.nextIcon,\n          prevIcon: this.prevIcon,\n          showArrows: this.showArrows,\n          value: this.internalValue,\n        },\n        on: {\n          'call:slider': this.callSlider,\n          change: (val: any) => {\n            this.internalValue = val\n          },\n        },\n        ref: 'items',\n      }\n\n      this.setTextColor(this.computedColor, data)\n      this.setBackgroundColor(this.backgroundColor, data)\n\n      return this.$createElement(VTabsBar, data, [\n        this.genSlider(slider),\n        items,\n      ])\n    },\n    genItems (items: VNode | null, item: VNode[]) {\n      // If user provides items\n      // opt to use theirs\n      if (items) return items\n\n      // If no tabs are provided\n      // render nothing\n      if (!item.length) return null\n\n      return this.$createElement(VTabsItems, {\n        props: {\n          value: this.internalValue,\n        },\n        on: {\n          change: (val: any) => {\n            this.internalValue = val\n          },\n        },\n      }, item)\n    },\n    genSlider (slider: VNode | null) {\n      if (this.hideSlider) return null\n\n      if (!slider) {\n        slider = this.$createElement(VTabsSlider, {\n          props: { color: this.sliderColor },\n        })\n      }\n\n      return this.$createElement('div', {\n        staticClass: 'v-tabs-slider-wrapper',\n        style: this.sliderStyles,\n      }, [slider])\n    },\n    onResize () {\n      if (this._isDestroyed) return\n\n      clearTimeout(this.resizeTimeout)\n      this.resizeTimeout = window.setTimeout(this.callSlider, 0)\n    },\n    parseNodes () {\n      let items = null\n      let slider = null\n      const item = []\n      const tab = []\n      const slot = this.$slots.default || []\n      const length = slot.length\n\n      for (let i = 0; i < length; i++) {\n        const vnode = slot[i]\n\n        if (vnode.componentOptions) {\n          switch (vnode.componentOptions.Ctor.options.name) {\n            case 'v-tabs-slider': slider = vnode\n              break\n            case 'v-tabs-items': items = vnode\n              break\n            case 'v-tab-item': item.push(vnode)\n              break\n            // case 'v-tab' - intentionally omitted\n            default: tab.push(vnode)\n          }\n        } else {\n          tab.push(vnode)\n        }\n      }\n\n      /**\n       * tab: array of `v-tab`\n       * slider: single `v-tabs-slider`\n       * items: single `v-tabs-items`\n       * item: array of `v-tab-item`\n       */\n      return { tab, slider, items, item }\n    },\n  },\n\n  render (h): VNode {\n    const { tab, slider, items, item } = this.parseNodes()\n\n    return h('div', {\n      staticClass: 'v-tabs',\n      class: this.classes,\n      directives: [{\n        name: 'resize',\n        modifiers: { quiet: true },\n        value: this.onResize,\n      }],\n    }, [\n      this.genBar(tab, slider),\n      this.genItems(items, item),\n    ])\n  },\n})\n"], "mappings": ";;;;;AAAA;AACA,OAAO,0CAAP,C,CAEA;;AACA,OAAOA,QAAP,MAAqB,YAArB;AACA,OAAOC,UAAP,MAAuB,cAAvB;AACA,OAAOC,WAAP,MAAwB,eAAxB,C,CAEA;;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,yBAAnB,C,CAEA;;AACA,SAASC,aAAT,QAA8B,oBAA9B;AAEA,OAAOC,MAAP,MAAmB,mBAAnB;AAKA,IAAMC,UAAU,GAAGD,MAAM,CACvBL,SADuB,EAEvBC,SAFuB,EAGvBC,SAHuB,CAAzB;AAYA,eAAeI,UAAU,CAACC,MAAX,GAA6BA,MAA7B,CAAoC;EACjDC,IAAI,EAAE,QAD2C;EAGjDC,UAAU,EAAE;IACVN,MAAA,EAAAA;EADU,CAHqC;EAOjDO,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC,MADK;MAEX,WAAS;IAFE,CADR;IAKLC,cAAc,EAAEC,OALX;IAMLC,eAAe,EAAEH,MANZ;IAOLI,YAAY,EAAEF,OAPT;IAQLG,QAAQ,EAAEH,OARL;IASLI,SAAS,EAAEJ,OATN;IAULK,IAAI,EAAEL,OAVD;IAWLM,MAAM,EAAE;MACNT,IAAI,EAAE,CAACU,MAAD,EAAST,MAAT,CADA;MAEN,WAASU;IAFH,CAXH;IAeLC,UAAU,EAAET,OAfP;IAgBLU,YAAY,EAAEV,OAhBT;IAiBLW,gBAAgB,EAAE,CAACb,MAAD,EAASS,MAAT,CAjBb;IAkBLK,QAAQ,EAAE;MACRf,IAAI,EAAEC,MADE;MAER,WAAS;IAFD,CAlBL;IAsBLe,QAAQ,EAAEb,OAtBL;IAuBLc,QAAQ,EAAE;MACRjB,IAAI,EAAEC,MADE;MAER,WAAS;IAFD,CAvBL;IA2BLiB,KAAK,EAAEf,OA3BF;IA4BLgB,UAAU,EAAE,CAAChB,OAAD,EAAUF,MAAV,CA5BP;IA6BLmB,WAAW,EAAEnB,MA7BR;IA8BLoB,UAAU,EAAE;MACVrB,IAAI,EAAE,CAACU,MAAD,EAAST,MAAT,CADI;MAEV,WAAS;IAFC,CA9BP;IAkCLqB,QAAQ,EAAEnB;EAlCL,CAP0C;EA4CjDoB,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,aAAa,EAAE,CADV;MAELC,MAAM,EAAE;QACNhB,MAAM,EAAE,IADF;QAENiB,IAAI,EAAE,IAFA;QAGNR,KAAK,EAAE,IAHD;QAINS,GAAG,EAAE,IAJC;QAKNC,KAAK,EAAE;MALD,CAFH;MASLC,cAAc,EAAE;IATX,CAAP;EAWD,CAxDgD;EA0DjDC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA;QACE,4BAA4B,KAAK9B,cAD5B;QAEL,oBAAoB,KAAKI,QAFpB;QAGL,sBAAsB,KAAKC,SAHtB;QAIL,gBAAgB,KAAKC,IAJhB;QAKL,0BAA0B,KAAKK,YAL1B;QAML,iBAAiB,KAAKK,KANjB;QAOL,oBAAoB,KAAKI;MAPpB,GAQF,KAAKW,YAAA;IAEX,CAZO;IAaRC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKC,QAAL,CAAcC,GAAd,IAAqB,KAAKd,QAAjC;IACD,CAfO;IAgBRe,YAAY,WAAZA,YAAYA,CAAA;MACV,OAAO;QACL5B,MAAM,EAAEjB,aAAa,CAAC,KAAKiC,MAAL,CAAYhB,MAAb,CADhB;QAELiB,IAAI,EAAE,KAAKQ,UAAL,GAAkBvB,SAAlB,GAA8BnB,aAAa,CAAC,KAAKiC,MAAL,CAAYC,IAAb,CAF5C;QAGLR,KAAK,EAAE,KAAKgB,UAAL,GAAkB1C,aAAa,CAAC,KAAKiC,MAAL,CAAYP,KAAb,CAA/B,GAAqDP,SAHvD;QAILgB,GAAG,EAAE,KAAKL,QAAL,GAAgB9B,aAAa,CAAC,KAAKiC,MAAL,CAAYE,GAAb,CAA7B,GAAiDhB,SAJjD;QAKL2B,UAAU,EAAE,KAAKb,MAAL,CAAYC,IAAZ,IAAoB,IAApB,GAA2B,IAA3B,GAAkC,MALzC;QAMLE,KAAK,EAAEpC,aAAa,CAAC,KAAKiC,MAAL,CAAYG,KAAb;MANf,CAAP;IAQD,CAzBO;IA0BRW,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAI,KAAKC,KAAT,EAAgB,OAAO,KAAKA,KAAZ,CAAhB,KACK,IAAI,KAAKC,MAAL,IAAe,CAAC,KAAKC,SAAzB,EAAoC,OAAO,OAAP,CAApC,KACA,OAAO,SAAP;IACN;EA9BO,CA1DuC;EA2FjDC,KAAK,EAAE;IACLzC,cAAc,EAAE,YADX;IAELI,QAAQ,EAAE,YAFL;IAGLD,YAAY,EAAE,YAHT;IAILE,SAAS,EAAE,YAJN;IAKLC,IAAI,EAAE,YALD;IAMLK,YAAY,EAAE,YANT;IAOLK,KAAK,EAAE,YAPF;IAQLC,UAAU,EAAE,YARP;IASLG,QAAQ,EAAE,YATL;IAUL,6BAA6B,UAVxB;IAWL,8BAA8B,UAXzB;IAYL,gBAAgB;EAZX,CA3F0C;EA0GjDsB,OAAO,WAAPA,OAAOA,CAAA;IAAA,IAAAC,KAAA;IACL,IAAI,OAAOC,cAAP,KAA0B,WAA9B,EAA2C;MACzC,IAAMC,GAAG,GAAG,IAAID,cAAJ,CAAmB,YAAK;QAClCD,KAAA,CAAKG,QAAL;MACD,CAFW,CAAZ;MAGAD,GAAG,CAACE,OAAJ,CAAY,KAAKC,GAAjB;MACA,KAAKC,GAAL,CAAS,gBAAT,EAA2B,YAAK;QAC9BJ,GAAG,CAACK,UAAJ;MACD,CAFD;IAGD;IAED,KAAKC,SAAL,CAAe,YAAK;MAClBC,WAAA,CAAkBT,KAAA,CAAKU,UAAvB,EAAmC,EAAnC;IACD,CAFD;EAGD,CAxHgD;EA0HjDC,OAAO,EAAE;IACPD,UAAU,WAAVA,UAAUA,CAAA;MAAA,IAAAE,MAAA;MACR,IACE,KAAK7C,UAAL,IACA,CAAC,KAAK8C,KAAL,CAAWC,KADZ,IAEA,CAAC,KAAKD,KAAL,CAAWC,KAAX,CAAiBC,aAAjB,CAA+BC,MAHlC,EAIE;QACA,KAAKpC,MAAL,CAAYG,KAAZ,GAAoB,CAApB;QACA,OAAO,KAAP;MACD;MAED,KAAKyB,SAAL,CAAe,YAAK;QAClB;QACA,IAAMS,SAAS,GAAGL,MAAA,CAAKC,KAAL,CAAWC,KAAX,CAAiBC,aAAjB,CAA+B,CAA/B,CAAlB;QACA;;QACA,IAAI,CAACE,SAAD,IAAc,CAACA,SAAS,CAACZ,GAA7B,EAAkC;UAChCO,MAAA,CAAKhC,MAAL,CAAYG,KAAZ,GAAoB,CAApB;UACA6B,MAAA,CAAKhC,MAAL,CAAYC,IAAZ,GAAmB,CAAnB;UACA;QACD;QACD,IAAMqC,EAAE,GAAGD,SAAS,CAACZ,GAArB;QAEAO,MAAA,CAAKhC,MAAL,GAAc;UACZhB,MAAM,EAAE,CAACgD,MAAA,CAAKnC,QAAN,GAAiBZ,MAAM,CAAC+C,MAAA,CAAKpC,UAAN,CAAvB,GAA2C0C,EAAE,CAACC,YAD1C;UAEZtC,IAAI,EAAE+B,MAAA,CAAKnC,QAAL,GAAgB,CAAhB,GAAoByC,EAAE,CAACE,UAFjB;UAGZ/C,KAAK,EAAEuC,MAAA,CAAKnC,QAAL,GAAgB,CAAhB,GAAoByC,EAAE,CAACE,UAAH,GAAgBF,EAAE,CAACG,WAHlC;UAIZvC,GAAG,EAAEoC,EAAE,CAACI,SAJI;UAKZvC,KAAK,EAAE6B,MAAA,CAAKnC,QAAL,GAAgBZ,MAAM,CAAC+C,MAAA,CAAKpC,UAAN,CAAtB,GAA0C0C,EAAE,CAACK;QALxC,CAAd;MAOD,CAlBD;MAoBA,OAAO,IAAP;IACD,CAhCM;IAiCPC,MAAM,WAANA,MAAMA,CAAEV,KAAF,EAAkBlC,MAAlB,EAAsC;MAAA,IAAA6C,MAAA;MAC1C,IAAM/C,IAAI,GAAG;QACXgD,KAAK,EAAE;UACL9D,MAAM,EAAEjB,aAAa,CAAC,KAAKiB,MAAN;QADhB,CADI;QAIXX,KAAK,EAAE;UACLC,WAAW,EAAE,KAAKA,WADb;UAELM,YAAY,EAAE,KAAKA,YAFd;UAGLmE,IAAI,EAAE,KAAKA,IAHN;UAILC,KAAK,EAAE,KAAKA,KAJP;UAKLC,SAAS,EAAE,CAAC,KAAK1D,QALZ;UAMLF,gBAAgB,EAAE,KAAKA,gBANlB;UAOLC,QAAQ,EAAE,KAAKA,QAPV;UAQLE,QAAQ,EAAE,KAAKA,QARV;UASLE,UAAU,EAAE,KAAKA,UATZ;UAULwD,KAAK,EAAE,KAAKC;QAVP,CAJI;QAgBXC,EAAE,EAAE;UACF,eAAe,KAAKtB,UADlB;UAEFuB,MAAM,EAAG,SAATA,MAAMA,CAAGC,GAAD,EAAa;YACnBT,MAAA,CAAKM,aAAL,GAAqBG,GAArB;UACD;QAJC,CAhBO;QAsBXC,GAAG,EAAE;MAtBM,CAAb;MAyBA,KAAKC,YAAL,CAAkB,KAAK1C,aAAvB,EAAsChB,IAAtC;MACA,KAAK2D,kBAAL,CAAwB,KAAK9E,eAA7B,EAA8CmB,IAA9C;MAEA,OAAO,KAAK4D,cAAL,CAAoBlG,QAApB,EAA8BsC,IAA9B,EAAoC,CACzC,KAAK6D,SAAL,CAAe3D,MAAf,CADyC,EAEzCkC,KAFyC,CAApC,CAAP;IAID,CAlEM;IAmEP0B,QAAQ,WAARA,QAAQA,CAAE1B,KAAF,EAAuB2B,IAAvB,EAAoC;MAAA,IAAAC,MAAA;MAC1C;MACA;MACA,IAAI5B,KAAJ,EAAW,OAAOA,KAAP,CAH+B,CAK1C;MACA;;MACA,IAAI,CAAC2B,IAAI,CAACzB,MAAV,EAAkB,OAAO,IAAP;MAElB,OAAO,KAAKsB,cAAL,CAAoBjG,UAApB,EAAgC;QACrCY,KAAK,EAAE;UACL6E,KAAK,EAAE,KAAKC;QADP,CAD8B;QAIrCC,EAAE,EAAE;UACFC,MAAM,EAAG,SAATA,MAAMA,CAAGC,GAAD,EAAa;YACnBQ,MAAA,CAAKX,aAAL,GAAqBG,GAArB;UACD;QAHC;MAJiC,CAAhC,EASJO,IATI,CAAP;IAUD,CAtFM;IAuFPF,SAAS,WAATA,SAASA,CAAE3D,MAAF,EAAsB;MAC7B,IAAI,KAAKb,UAAT,EAAqB,OAAO,IAAP;MAErB,IAAI,CAACa,MAAL,EAAa;QACXA,MAAM,GAAG,KAAK0D,cAAL,CAAoBhG,WAApB,EAAiC;UACxCW,KAAK,EAAE;YAAE0C,KAAK,EAAE,KAAKpB;UAAd;QADiC,CAAjC,CAAT;MAGD;MAED,OAAO,KAAK+D,cAAL,CAAoB,KAApB,EAA2B;QAChCK,WAAW,EAAE,uBADmB;QAEhCjB,KAAK,EAAE,KAAKlC;MAFoB,CAA3B,EAGJ,CAACZ,MAAD,CAHI,CAAP;IAID,CApGM;IAqGPuB,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAI,KAAKyC,YAAT,EAAuB;MAEvBC,YAAY,CAAC,KAAKlE,aAAN,CAAZ;MACA,KAAKA,aAAL,GAAqB8B,WAAA,CAAkB,KAAKC,UAAvB,EAAmC,CAAnC,CAArB;IACD,CA1GM;IA2GPoC,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAIhC,KAAK,GAAG,IAAZ;MACA,IAAIlC,MAAM,GAAG,IAAb;MACA,IAAM6D,IAAI,GAAG,EAAb;MACA,IAAMM,GAAG,GAAG,EAAZ;MACA,IAAMC,IAAI,GAAG,KAAKC,MAAL,eAAuB,EAApC;MACA,IAAMjC,MAAM,GAAGgC,IAAI,CAAChC,MAApB;MAEA,KAAK,IAAIkC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlC,MAApB,EAA4BkC,CAAC,EAA7B,EAAiC;QAC/B,IAAMC,KAAK,GAAGH,IAAI,CAACE,CAAD,CAAlB;QAEA,IAAIC,KAAK,CAACC,gBAAV,EAA4B;UAC1B,QAAQD,KAAK,CAACC,gBAAN,CAAuBC,IAAvB,CAA4BC,OAA5B,CAAoCvG,IAA5C;YACE,KAAK,eAAL;cAAsB6B,MAAM,GAAGuE,KAAT;cACpB;YACF,KAAK,cAAL;cAAqBrC,KAAK,GAAGqC,KAAR;cACnB;YACF,KAAK,YAAL;cAAmBV,IAAI,CAACc,IAAL,CAAUJ,KAAV;cACjB;YACF;;YACA;cAASJ,GAAG,CAACQ,IAAJ,CAASJ,KAAT;UARX;QAUD,CAXD,MAWO;UACLJ,GAAG,CAACQ,IAAJ,CAASJ,KAAT;QACD;MACF;MAED;;;;;AAKG;;MACH,OAAO;QAAEJ,GAAF,EAAEA,GAAF;QAAOnE,MAAP,EAAOA,MAAP;QAAekC,KAAf,EAAeA,KAAf;QAAsB2B,IAAA,EAAAA;MAAtB,CAAP;IACD;EA7IM,CA1HwC;EA0QjDe,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAAC,gBAAA,GAAqC,KAAKZ,UAAL,EAArC;MAAQC,GAAF,GAAAW,gBAAA,CAAEX,GAAF;MAAOnE,MAAP,GAAA8E,gBAAA,CAAO9E,MAAP;MAAekC,KAAf,GAAA4C,gBAAA,CAAe5C,KAAf;MAAsB2B,IAAA,GAAAiB,gBAAA,CAAAjB,IAAA;IAE5B,OAAOgB,CAAC,CAAC,KAAD,EAAQ;MACdd,WAAW,EAAE,QADC;MAEd,SAAO,KAAKzD,OAFE;MAGdlC,UAAU,EAAE,CAAC;QACXD,IAAI,EAAE,QADK;QAEX4G,SAAS,EAAE;UAAEC,KAAK,EAAE;QAAT,CAFA;QAGX9B,KAAK,EAAE,KAAK3B;MAHD,CAAD;IAHE,CAAR,EAQL,CACD,KAAKqB,MAAL,CAAYuB,GAAZ,EAAiBnE,MAAjB,CADC,EAED,KAAK4D,QAAL,CAAc1B,KAAd,EAAqB2B,IAArB,CAFC,CARK,CAAR;EAYD;AAzRgD,CAApC,CAAf", "ignoreList": []}]}