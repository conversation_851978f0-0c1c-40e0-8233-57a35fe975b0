{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/util/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/util/index.js", "mtime": 1757335236822}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["HSVAtoRGBA", "HSVAtoHex", "RGBAtoHSVA", "HexToHSVA", "HSVAtoHSLA", "RGBAtoHex", "HSLAtoHSVA", "parseHex", "fromHSVA", "hsva", "_objectSpread", "hexa", "hsla", "rgba", "alpha", "a", "hex", "substr", "hue", "h", "fromHSLA", "fromRGBA", "fromHexa", "fromHex", "has", "obj", "key", "_everyInstanceProperty", "call", "k", "hasOwnProperty", "parseColor", "color", "oldColor", "r", "g", "b", "_typeof", "_parseFloat", "stripAlpha", "rest", "_objectWithoutProperties", "_excluded", "extractColor", "input", "length", "shouldStrip", "has<PERSON><PERSON><PERSON>"], "sources": ["../../../../src/components/VColorPicker/util/index.ts"], "sourcesContent": ["// Utilities\nimport {\n  HSVA,\n  HSVAtoRGBA,\n  HSVAtoHex,\n  RGBA,\n  Hex,\n  RGBAtoHSVA,\n  HexToHSVA,\n  HSLA,\n  HSVAtoHSLA,\n  RGBAtoHex,\n  HSLAtoHSVA,\n  parseHex,\n  Hexa,\n} from '../../../util/colorUtils'\n\nexport interface VColorPickerColor {\n  alpha: number\n  hex: Hex\n  hexa: Hexa\n  hsla: HSLA\n  hsva: HSVA\n  hue: number\n  rgba: RGBA\n}\n\nexport function fromHSVA (hsva: HSVA): VColorPickerColor {\n  hsva = { ...hsva }\n  const hexa = HSVAtoHex(hsva)\n  const hsla = HSVAtoHSLA(hsva)\n  const rgba = HSVAtoRGBA(hsva)\n  return {\n    alpha: hsva.a,\n    hex: hexa.substr(0, 7),\n    hexa,\n    hsla,\n    hsva,\n    hue: hsva.h,\n    rgba,\n  }\n}\n\nexport function fromHSLA (hsla: HSLA): VColorPickerColor {\n  const hsva = HSLAtoHSVA(hsla)\n  const hexa = HSVAtoHex(hsva)\n  const rgba = HSVAtoRGBA(hsva)\n  return {\n    alpha: hsva.a,\n    hex: hexa.substr(0, 7),\n    hexa,\n    hsla,\n    hsva,\n    hue: hsva.h,\n    rgba,\n  }\n}\n\nexport function fromRGBA (rgba: RGBA): VColorPickerColor {\n  const hsva = RGBAtoHSVA(rgba)\n  const hexa = RGBAtoHex(rgba)\n  const hsla = HSVAtoHSLA(hsva)\n  return {\n    alpha: hsva.a,\n    hex: hexa.substr(0, 7),\n    hexa,\n    hsla,\n    hsva,\n    hue: hsva.h,\n    rgba,\n  }\n}\n\nexport function fromHexa (hexa: Hexa): VColorPickerColor {\n  const hsva = HexToHSVA(hexa)\n  const hsla = HSVAtoHSLA(hsva)\n  const rgba = HSVAtoRGBA(hsva)\n  return {\n    alpha: hsva.a,\n    hex: hexa.substr(0, 7),\n    hexa,\n    hsla,\n    hsva,\n    hue: hsva.h,\n    rgba,\n  }\n}\n\nexport function fromHex (hex: Hex): VColorPickerColor {\n  return fromHexa(parseHex(hex))\n}\n\nfunction has (obj: object, key: string[]) {\n  return key.every(k => obj.hasOwnProperty(k))\n}\n\nexport function parseColor (color: any, oldColor: VColorPickerColor | null) {\n  if (!color) return fromRGBA({ r: 255, g: 0, b: 0, a: 1 })\n\n  if (typeof color === 'string') {\n    if (color === 'transparent') return fromHexa('#00000000')\n\n    const hex = parseHex(color)\n\n    if (oldColor && hex === oldColor.hexa) return oldColor\n    else return fromHexa(hex)\n  }\n\n  if (typeof color === 'object') {\n    if (color.hasOwnProperty('alpha')) return color\n\n    const a = color.hasOwnProperty('a') ? parseFloat(color.a) : 1\n\n    if (has(color, ['r', 'g', 'b'])) {\n      if (oldColor && color === oldColor.rgba) return oldColor\n      else return fromRGBA({ ...color, a })\n    } else if (has(color, ['h', 's', 'l'])) {\n      if (oldColor && color === oldColor.hsla) return oldColor\n      else return fromHSLA({ ...color, a })\n    } else if (has(color, ['h', 's', 'v'])) {\n      if (oldColor && color === oldColor.hsva) return oldColor\n      else return fromHSVA({ ...color, a })\n    }\n  }\n\n  return fromRGBA({ r: 255, g: 0, b: 0, a: 1 })\n}\n\nfunction stripAlpha (color: any, stripAlpha: boolean) {\n  if (stripAlpha) {\n    const { a, ...rest } = color\n\n    return rest\n  }\n\n  return color\n}\n\nexport function extractColor (color: VColorPickerColor, input: any) {\n  if (input == null) return color\n\n  if (typeof input === 'string') {\n    return input.length === 7 ? color.hex : color.hexa\n  }\n\n  if (typeof input === 'object') {\n    const shouldStrip = typeof input.a === 'number' && input.a === 0 ? !!input.a : !input.a\n    if (has(input, ['r', 'g', 'b'])) return stripAlpha(color.rgba, shouldStrip)\n    else if (has(input, ['h', 's', 'l'])) return stripAlpha(color.hsla, shouldStrip)\n    else if (has(input, ['h', 's', 'v'])) return stripAlpha(color.hsva, shouldStrip)\n  }\n\n  return color\n}\n\nexport function hasAlpha (color: any) {\n  if (!color) return false\n\n  if (typeof color === 'string') {\n    return color.length > 7\n  }\n\n  if (typeof color === 'object') {\n    return has(color, ['a']) || has(color, ['alpha'])\n  }\n\n  return false\n}\n"], "mappings": ";;;;;;AAAA;AACA,SAEEA,UAFF,EAGEC,SAHF,EAMEC,UANF,EAOEC,SAPF,EASEC,UATF,EAUEC,SAVF,EAWEC,UAXF,EAYEC,QAZF,QAcO,0BAdP;AA0BA,OAAM,SAAUC,QAAVA,CAAoBC,IAApB,EAA8B;EAClCA,IAAI,GAAAC,aAAA,KAAQD,IAAA,CAAZ;EACA,IAAME,IAAI,GAAGV,SAAS,CAACQ,IAAD,CAAtB;EACA,IAAMG,IAAI,GAAGR,UAAU,CAACK,IAAD,CAAvB;EACA,IAAMI,IAAI,GAAGb,UAAU,CAACS,IAAD,CAAvB;EACA,OAAO;IACLK,KAAK,EAAEL,IAAI,CAACM,CADP;IAELC,GAAG,EAAEL,IAAI,CAACM,MAAL,CAAY,CAAZ,EAAe,CAAf,CAFA;IAGLN,IAHK,EAGLA,IAHK;IAILC,IAJK,EAILA,IAJK;IAKLH,IALK,EAKLA,IALK;IAMLS,GAAG,EAAET,IAAI,CAACU,CANL;IAOLN,IAAA,EAAAA;EAPK,CAAP;AASD;AAED,OAAM,SAAUO,QAAVA,CAAoBR,IAApB,EAA8B;EAClC,IAAMH,IAAI,GAAGH,UAAU,CAACM,IAAD,CAAvB;EACA,IAAMD,IAAI,GAAGV,SAAS,CAACQ,IAAD,CAAtB;EACA,IAAMI,IAAI,GAAGb,UAAU,CAACS,IAAD,CAAvB;EACA,OAAO;IACLK,KAAK,EAAEL,IAAI,CAACM,CADP;IAELC,GAAG,EAAEL,IAAI,CAACM,MAAL,CAAY,CAAZ,EAAe,CAAf,CAFA;IAGLN,IAHK,EAGLA,IAHK;IAILC,IAJK,EAILA,IAJK;IAKLH,IALK,EAKLA,IALK;IAMLS,GAAG,EAAET,IAAI,CAACU,CANL;IAOLN,IAAA,EAAAA;EAPK,CAAP;AASD;AAED,OAAM,SAAUQ,QAAVA,CAAoBR,IAApB,EAA8B;EAClC,IAAMJ,IAAI,GAAGP,UAAU,CAACW,IAAD,CAAvB;EACA,IAAMF,IAAI,GAAGN,SAAS,CAACQ,IAAD,CAAtB;EACA,IAAMD,IAAI,GAAGR,UAAU,CAACK,IAAD,CAAvB;EACA,OAAO;IACLK,KAAK,EAAEL,IAAI,CAACM,CADP;IAELC,GAAG,EAAEL,IAAI,CAACM,MAAL,CAAY,CAAZ,EAAe,CAAf,CAFA;IAGLN,IAHK,EAGLA,IAHK;IAILC,IAJK,EAILA,IAJK;IAKLH,IALK,EAKLA,IALK;IAMLS,GAAG,EAAET,IAAI,CAACU,CANL;IAOLN,IAAA,EAAAA;EAPK,CAAP;AASD;AAED,OAAM,SAAUS,QAAVA,CAAoBX,IAApB,EAA8B;EAClC,IAAMF,IAAI,GAAGN,SAAS,CAACQ,IAAD,CAAtB;EACA,IAAMC,IAAI,GAAGR,UAAU,CAACK,IAAD,CAAvB;EACA,IAAMI,IAAI,GAAGb,UAAU,CAACS,IAAD,CAAvB;EACA,OAAO;IACLK,KAAK,EAAEL,IAAI,CAACM,CADP;IAELC,GAAG,EAAEL,IAAI,CAACM,MAAL,CAAY,CAAZ,EAAe,CAAf,CAFA;IAGLN,IAHK,EAGLA,IAHK;IAILC,IAJK,EAILA,IAJK;IAKLH,IALK,EAKLA,IALK;IAMLS,GAAG,EAAET,IAAI,CAACU,CANL;IAOLN,IAAA,EAAAA;EAPK,CAAP;AASD;AAED,OAAM,SAAUU,OAAVA,CAAmBP,GAAnB,EAA2B;EAC/B,OAAOM,QAAQ,CAACf,QAAQ,CAACS,GAAD,CAAT,CAAf;AACD;AAED,SAASQ,GAATA,CAAcC,GAAd,EAA2BC,GAA3B,EAAwC;EACtC,OAAOC,sBAAA,CAAAD,GAAG,EAAAE,IAAA,CAAHF,GAAG,EAAO,UAAAG,CAAC;IAAA,OAAIJ,GAAG,CAACK,cAAJ,CAAmBD,CAAnB,CAAf;EAAA,EAAP;AACD;AAED,OAAM,SAAUE,UAAVA,CAAsBC,KAAtB,EAAkCC,QAAlC,EAAoE;EACxE,IAAI,CAACD,KAAL,EAAY,OAAOX,QAAQ,CAAC;IAAEa,CAAC,EAAE,GAAL;IAAUC,CAAC,EAAE,CAAb;IAAgBC,CAAC,EAAE,CAAnB;IAAsBrB,CAAC,EAAE;EAAzB,CAAD,CAAf;EAEZ,IAAI,OAAOiB,KAAP,KAAiB,QAArB,EAA+B;IAC7B,IAAIA,KAAK,KAAK,aAAd,EAA6B,OAAOV,QAAQ,CAAC,WAAD,CAAf;IAE7B,IAAMN,GAAG,GAAGT,QAAQ,CAACyB,KAAD,CAApB;IAEA,IAAIC,QAAQ,IAAIjB,GAAG,KAAKiB,QAAQ,CAACtB,IAAjC,EAAuC,OAAOsB,QAAP,CAAvC,KACK,OAAOX,QAAQ,CAACN,GAAD,CAAf;EACN;EAED,IAAIqB,OAAA,CAAOL,KAAP,MAAiB,QAArB,EAA+B;IAC7B,IAAIA,KAAK,CAACF,cAAN,CAAqB,OAArB,CAAJ,EAAmC,OAAOE,KAAP;IAEnC,IAAMjB,CAAC,GAAGiB,KAAK,CAACF,cAAN,CAAqB,GAArB,IAA4BQ,WAAA,CAAWN,KAAK,CAACjB,CAAP,CAAtC,GAAkD,CAA5D;IAEA,IAAIS,GAAG,CAACQ,KAAD,EAAQ,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAR,CAAP,EAAiC;MAC/B,IAAIC,QAAQ,IAAID,KAAK,KAAKC,QAAQ,CAACpB,IAAnC,EAAyC,OAAOoB,QAAP,CAAzC,KACK,OAAOZ,QAAQ,CAAAX,aAAA,CAAAA,aAAA,KAAMsB,KAAL;QAAYjB,CAAA,EAAAA;MAAA,EAAb,CAAf;IACN,CAHD,MAGO,IAAIS,GAAG,CAACQ,KAAD,EAAQ,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAR,CAAP,EAAiC;MACtC,IAAIC,QAAQ,IAAID,KAAK,KAAKC,QAAQ,CAACrB,IAAnC,EAAyC,OAAOqB,QAAP,CAAzC,KACK,OAAOb,QAAQ,CAAAV,aAAA,CAAAA,aAAA,KAAMsB,KAAL;QAAYjB,CAAA,EAAAA;MAAA,EAAb,CAAf;IACN,CAHM,MAGA,IAAIS,GAAG,CAACQ,KAAD,EAAQ,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAR,CAAP,EAAiC;MACtC,IAAIC,QAAQ,IAAID,KAAK,KAAKC,QAAQ,CAACxB,IAAnC,EAAyC,OAAOwB,QAAP,CAAzC,KACK,OAAOzB,QAAQ,CAAAE,aAAA,CAAAA,aAAA,KAAMsB,KAAL;QAAYjB,CAAA,EAAAA;MAAA,EAAb,CAAf;IACN;EACF;EAED,OAAOM,QAAQ,CAAC;IAAEa,CAAC,EAAE,GAAL;IAAUC,CAAC,EAAE,CAAb;IAAgBC,CAAC,EAAE,CAAnB;IAAsBrB,CAAC,EAAE;EAAzB,CAAD,CAAf;AACD;AAED,SAASwB,UAATA,CAAqBP,KAArB,EAAiCO,UAAjC,EAAoD;EAClD,IAAIA,UAAJ,EAAgB;IACd,IAAQxB,CAAF,GAAiBiB,KAAvB,CAAQjB,CAAF;MAAQyB,IAAA,GAAAC,wBAAA,CAAST,KAAvB,EAAAU,SAAA;IAEA,OAAOF,IAAP;EACD;EAED,OAAOR,KAAP;AACD;AAED,OAAM,SAAUW,YAAVA,CAAwBX,KAAxB,EAAkDY,KAAlD,EAA4D;EAChE,IAAIA,KAAK,IAAI,IAAb,EAAmB,OAAOZ,KAAP;EAEnB,IAAI,OAAOY,KAAP,KAAiB,QAArB,EAA+B;IAC7B,OAAOA,KAAK,CAACC,MAAN,KAAiB,CAAjB,GAAqBb,KAAK,CAAChB,GAA3B,GAAiCgB,KAAK,CAACrB,IAA9C;EACD;EAED,IAAI0B,OAAA,CAAOO,KAAP,MAAiB,QAArB,EAA+B;IAC7B,IAAME,WAAW,GAAG,OAAOF,KAAK,CAAC7B,CAAb,KAAmB,QAAnB,IAA+B6B,KAAK,CAAC7B,CAAN,KAAY,CAA3C,GAA+C,CAAC,CAAC6B,KAAK,CAAC7B,CAAvD,GAA2D,CAAC6B,KAAK,CAAC7B,CAAtF;IACA,IAAIS,GAAG,CAACoB,KAAD,EAAQ,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAR,CAAP,EAAiC,OAAOL,UAAU,CAACP,KAAK,CAACnB,IAAP,EAAaiC,WAAb,CAAjB,CAAjC,KACK,IAAItB,GAAG,CAACoB,KAAD,EAAQ,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAR,CAAP,EAAiC,OAAOL,UAAU,CAACP,KAAK,CAACpB,IAAP,EAAakC,WAAb,CAAjB,CAAjC,KACA,IAAItB,GAAG,CAACoB,KAAD,EAAQ,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAR,CAAP,EAAiC,OAAOL,UAAU,CAACP,KAAK,CAACvB,IAAP,EAAaqC,WAAb,CAAjB;EACvC;EAED,OAAOd,KAAP;AACD;AAED,OAAM,SAAUe,QAAVA,CAAoBf,KAApB,EAA8B;EAClC,IAAI,CAACA,KAAL,EAAY,OAAO,KAAP;EAEZ,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IAC7B,OAAOA,KAAK,CAACa,MAAN,GAAe,CAAtB;EACD;EAED,IAAIR,OAAA,CAAOL,KAAP,MAAiB,QAArB,EAA+B;IAC7B,OAAOR,GAAG,CAACQ,KAAD,EAAQ,CAAC,GAAD,CAAR,CAAH,IAAqBR,GAAG,CAACQ,KAAD,EAAQ,CAAC,OAAD,CAAR,CAA/B;EACD;EAED,OAAO,KAAP;AACD", "ignoreList": []}]}