{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/isNativeReflectConstruct.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/isNativeReflectConstruct.js", "mtime": 1757335237643}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9SZWZsZWN0JGNvbnN0cnVjdCBmcm9tICJjb3JlLWpzLXB1cmUvZmVhdHVyZXMvcmVmbGVjdC9jb25zdHJ1Y3QuanMiOwpmdW5jdGlvbiBfaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0KCkgewogIHRyeSB7CiAgICB2YXIgdCA9ICFCb29sZWFuLnByb3RvdHlwZS52YWx1ZU9mLmNhbGwoX1JlZmxlY3QkY29uc3RydWN0KEJvb2xlYW4sIFtdLCBmdW5jdGlvbiAoKSB7fSkpOwogIH0gY2F0Y2ggKHQpIHt9CiAgcmV0dXJuIChfaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0ID0gZnVuY3Rpb24gX2lzTmF0aXZlUmVmbGVjdENvbnN0cnVjdCgpIHsKICAgIHJldHVybiAhIXQ7CiAgfSkoKTsKfQpleHBvcnQgeyBfaXNOYXRpdmVSZWZsZWN0Q29uc3RydWN0IGFzIGRlZmF1bHQgfTs="}, {"version": 3, "names": ["_Reflect$construct", "_isNativeReflectConstruct", "t", "Boolean", "prototype", "valueOf", "call", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/isNativeReflectConstruct.js"], "sourcesContent": ["import _Reflect$construct from \"core-js-pure/features/reflect/construct.js\";\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(_Reflect$construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,4CAA4C;AAC3E,SAASC,yBAAyBA,CAAA,EAAG;EACnC,IAAI;IACF,IAAIC,CAAC,GAAG,CAACC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACN,kBAAkB,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAC1F,CAAC,CAAC,OAAOD,CAAC,EAAE,CAAC;EACb,OAAO,CAACD,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IACvE,OAAO,CAAC,CAACC,CAAC;EACZ,CAAC,EAAE,CAAC;AACN;AACA,SAASD,yBAAyB,IAAIM,OAAO", "ignoreList": []}]}