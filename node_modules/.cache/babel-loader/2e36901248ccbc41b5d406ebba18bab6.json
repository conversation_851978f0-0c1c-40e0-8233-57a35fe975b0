{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/presets/default/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/presets/default/index.js", "mtime": 1757335237280}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL3N0eWxlcy9tYWluLnNhc3MiOyAvLyBMb2NhbGUKCmltcG9ydCB7IGVuIH0gZnJvbSAnLi4vLi4vbG9jYWxlJzsKZXhwb3J0IHZhciBwcmVzZXQgPSB7CiAgYnJlYWtwb2ludDogewogICAgLy8gVE9ETzogdXBkYXRlIHRvIE1EMiBzcGVjIGluIHYzIC0gMTI4MAogICAgbW9iaWxlQnJlYWtwb2ludDogMTI2NCwKICAgIHNjcm9sbEJhcldpZHRoOiAxNiwKICAgIHRocmVzaG9sZHM6IHsKICAgICAgeHM6IDYwMCwKICAgICAgc206IDk2MCwKICAgICAgbWQ6IDEyODAsCiAgICAgIGxnOiAxOTIwCiAgICB9CiAgfSwKICBpY29uczogewogICAgLy8gVE9ETzogcmVtb3ZlIHYzCiAgICBpY29uZm9udDogJ21kaScsCiAgICB2YWx1ZXM6IHt9CiAgfSwKICBsYW5nOiB7CiAgICBjdXJyZW50OiAnZW4nLAogICAgbG9jYWxlczogewogICAgICBlbjogZW4KICAgIH0sCiAgICAvLyBEZWZhdWx0IHRyYW5zbGF0b3IgZXhpc3RzIGluIGxhbmcgc2VydmljZQogICAgdDogdW5kZWZpbmVkCiAgfSwKICBydGw6IGZhbHNlLAogIHRoZW1lOiB7CiAgICBkYXJrOiBmYWxzZSwKICAgICJkZWZhdWx0IjogJ2xpZ2h0JywKICAgIGRpc2FibGU6IGZhbHNlLAogICAgb3B0aW9uczogewogICAgICBjc3BOb25jZTogdW5kZWZpbmVkLAogICAgICBjdXN0b21Qcm9wZXJ0aWVzOiB1bmRlZmluZWQsCiAgICAgIG1pbmlmeVRoZW1lOiB1bmRlZmluZWQsCiAgICAgIHRoZW1lQ2FjaGU6IHVuZGVmaW5lZCwKICAgICAgdmFyaWF0aW9uczogdHJ1ZQogICAgfSwKICAgIHRoZW1lczogewogICAgICBsaWdodDogewogICAgICAgIHByaW1hcnk6ICcjMTk3NkQyJywKICAgICAgICBzZWNvbmRhcnk6ICcjNDI0MjQyJywKICAgICAgICBhY2NlbnQ6ICcjODJCMUZGJywKICAgICAgICBlcnJvcjogJyNGRjUyNTInLAogICAgICAgIGluZm86ICcjMjE5NkYzJywKICAgICAgICBzdWNjZXNzOiAnIzRDQUY1MCcsCiAgICAgICAgd2FybmluZzogJyNGQjhDMDAnCiAgICAgIH0sCiAgICAgIGRhcms6IHsKICAgICAgICBwcmltYXJ5OiAnIzIxOTZGMycsCiAgICAgICAgc2Vjb25kYXJ5OiAnIzQyNDI0MicsCiAgICAgICAgYWNjZW50OiAnI0ZGNDA4MScsCiAgICAgICAgZXJyb3I6ICcjRkY1MjUyJywKICAgICAgICBpbmZvOiAnIzIxOTZGMycsCiAgICAgICAgc3VjY2VzczogJyM0Q0FGNTAnLAogICAgICAgIHdhcm5pbmc6ICcjRkI4QzAwJwogICAgICB9CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["en", "preset", "breakpoint", "mobileBreakpoint", "scrollBarWidth", "thresholds", "xs", "sm", "md", "lg", "icons", "iconfont", "values", "lang", "current", "locales", "t", "undefined", "rtl", "theme", "dark", "disable", "options", "cspNonce", "customProperties", "minifyTheme", "themeCache", "variations", "themes", "light", "primary", "secondary", "accent", "error", "info", "success", "warning"], "sources": ["../../../src/presets/default/index.ts"], "sourcesContent": ["// Styles\nimport '../../styles/main.sass'\n\n// Locale\nimport { en } from '../../locale'\n\n// Icons\n// TODO: Enable for v3\n// import mdi from '../../services/icons/presets/mdi'\n\n// Types\nimport { VuetifyPreset } from 'vuetify/types/services/presets'\n\nexport const preset: VuetifyPreset = {\n  breakpoint: {\n    // TODO: update to MD2 spec in v3 - 1280\n    mobileBreakpoint: 1264,\n    scrollBarWidth: 16,\n    thresholds: {\n      xs: 600,\n      sm: 960,\n      md: 1280,\n      lg: 1920,\n    },\n  },\n  icons: {\n    // TODO: remove v3\n    iconfont: 'mdi',\n    values: {},\n  },\n  lang: {\n    current: 'en',\n    locales: { en },\n    // Default translator exists in lang service\n    t: undefined as any,\n  },\n  rtl: false,\n  theme: {\n    dark: false,\n    default: 'light',\n    disable: false,\n    options: {\n      cspNonce: undefined,\n      customProperties: undefined,\n      minifyTheme: undefined,\n      themeCache: undefined,\n      variations: true,\n    },\n    themes: {\n      light: {\n        primary: '#1976D2',\n        secondary: '#424242',\n        accent: '#82B1FF',\n        error: '#FF5252',\n        info: '#2196F3',\n        success: '#4CAF50',\n        warning: '#FB8C00',\n      },\n      dark: {\n        primary: '#2196F3',\n        secondary: '#424242',\n        accent: '#FF4081',\n        error: '#FF5252',\n        info: '#2196F3',\n        success: '#4CAF50',\n        warning: '#FB8C00',\n      },\n    },\n  },\n}\n"], "mappings": "AAAA;AACA,OAAO,+BAAP,C,CAEA;;AACA,SAASA,EAAT,QAAmB,cAAnB;AASA,OAAO,IAAMC,MAAM,GAAkB;EACnCC,UAAU,EAAE;IACV;IACAC,gBAAgB,EAAE,IAFR;IAGVC,cAAc,EAAE,EAHN;IAIVC,UAAU,EAAE;MACVC,EAAE,EAAE,GADM;MAEVC,EAAE,EAAE,GAFM;MAGVC,EAAE,EAAE,IAHM;MAIVC,EAAE,EAAE;IAJM;EAJF,CADuB;EAYnCC,KAAK,EAAE;IACL;IACAC,QAAQ,EAAE,KAFL;IAGLC,MAAM,EAAE;EAHH,CAZ4B;EAiBnCC,IAAI,EAAE;IACJC,OAAO,EAAE,IADL;IAEJC,OAAO,EAAE;MAAEf,EAAA,EAAAA;IAAF,CAFL;IAGJ;IACAgB,CAAC,EAAEC;EAJC,CAjB6B;EAuBnCC,GAAG,EAAE,KAvB8B;EAwBnCC,KAAK,EAAE;IACLC,IAAI,EAAE,KADD;IAEL,WAAS,OAFJ;IAGLC,OAAO,EAAE,KAHJ;IAILC,OAAO,EAAE;MACPC,QAAQ,EAAEN,SADH;MAEPO,gBAAgB,EAAEP,SAFX;MAGPQ,WAAW,EAAER,SAHN;MAIPS,UAAU,EAAET,SAJL;MAKPU,UAAU,EAAE;IALL,CAJJ;IAWLC,MAAM,EAAE;MACNC,KAAK,EAAE;QACLC,OAAO,EAAE,SADJ;QAELC,SAAS,EAAE,SAFN;QAGLC,MAAM,EAAE,SAHH;QAILC,KAAK,EAAE,SAJF;QAKLC,IAAI,EAAE,SALD;QAMLC,OAAO,EAAE,SANJ;QAOLC,OAAO,EAAE;MAPJ,CADD;MAUNhB,IAAI,EAAE;QACJU,OAAO,EAAE,SADL;QAEJC,SAAS,EAAE,SAFP;QAGJC,MAAM,EAAE,SAHJ;QAIJC,KAAK,EAAE,SAJH;QAKJC,IAAI,EAAE,SALF;QAMJC,OAAO,EAAE,SANL;QAOJC,OAAO,EAAE;MAPL;IAVA;EAXH;AAxB4B,CAA9B", "ignoreList": []}]}