{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VWindow/VWindow.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VWindow/VWindow.js", "mtime": 1757335245284}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Touch", "VBtn", "VIcon", "BaseItemGroup", "extend", "name", "directives", "provide", "windowGroup", "props", "activeClass", "type", "String", "continuous", "Boolean", "mandatory", "nextIcon", "prevIcon", "reverse", "showArrows", "showArrowsOnHover", "touch", "Object", "touchless", "value", "required", "vertical", "data", "changedByDelimiters", "internalHeight", "undefined", "transitionHeight", "transitionCount", "isBooted", "isReverse", "computed", "isActive", "classes", "_objectSpread", "options", "call", "computedTransition", "_context", "axis", "internalReverse", "direction", "_concatInstanceProperty", "concat", "hasActiveItems", "_context2", "_findInstanceProperty", "items", "item", "disabled", "hasNext", "internalIndex", "length", "has<PERSON>rev", "_context3", "_this", "_findIndexInstanceProperty", "i", "internalValue", "getValue", "$vuetify", "rtl", "_reverseInstanceProperty", "watch", "val", "oldVal", "updateReverse", "mounted", "_this2", "window", "requestAnimationFrame", "methods", "genDefaultSlot", "$slots", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "push", "genControlIcons", "$createElement", "staticClass", "style", "height", "genIcon", "icon", "click", "_this3", "on", "e", "stopPropagation", "attrs", "lang", "t", "_c", "_b", "_a", "$scopedSlots", "large", "icons", "prev", "next", "getNextIndex", "index", "nextIndex", "getPrevIndex", "prevIndex", "lastIndex", "itemsLength", "render", "h", "_this4", "left", "right", "end", "start"], "sources": ["../../../src/components/VWindow/VWindow.ts"], "sourcesContent": ["// Styles\nimport './VWindow.sass'\n\n// Types\nimport { VNode, VNodeDirective } from 'vue/types/vnode'\nimport { PropType } from 'vue'\nimport { TouchHandlers } from 'vuetify/types'\n\n// Directives\nimport Touch from '../../directives/touch'\n\n// Components\nimport VBtn from '../VBtn'\nimport VIcon from '../VIcon'\nimport { BaseItemGroup } from '../VItemGroup/VItemGroup'\n\n/* @vue/component */\nexport default BaseItemGroup.extend({\n  name: 'v-window',\n\n  directives: { Touch },\n\n  provide (): object {\n    return {\n      windowGroup: this,\n    }\n  },\n\n  props: {\n    activeClass: {\n      type: String,\n      default: 'v-window-item--active',\n    },\n    continuous: Boolean,\n    mandatory: {\n      type: Boolean,\n      default: true,\n    },\n    nextIcon: {\n      type: [Boolean, String],\n      default: '$next',\n    },\n    prevIcon: {\n      type: [Boolean, String],\n      default: '$prev',\n    },\n    reverse: <PERSON>olean,\n    showArrows: <PERSON>olean,\n    showArrowsOnHover: <PERSON><PERSON><PERSON>,\n    touch: Object as PropType<TouchHandlers>,\n    touchless: <PERSON>ole<PERSON>,\n    value: {\n      required: false,\n    },\n    vertical: Boolean,\n  },\n\n  data () {\n    return {\n      changedByDelimiters: false,\n      internalHeight: undefined as undefined | string, // This can be fixed by child class.\n      transitionHeight: undefined as undefined | string, // Intermediate height during transition.\n      transitionCount: 0, // Number of windows in transition state.\n      isBooted: false,\n      isReverse: false,\n    }\n  },\n\n  computed: {\n    isActive (): boolean {\n      return this.transitionCount > 0\n    },\n    classes (): object {\n      return {\n        ...BaseItemGroup.options.computed.classes.call(this),\n        'v-window--show-arrows-on-hover': this.showArrowsOnHover,\n      }\n    },\n    computedTransition (): string {\n      if (!this.isBooted) return ''\n\n      const axis = this.vertical ? 'y' : 'x'\n      const reverse = this.internalReverse ? !this.isReverse : this.isReverse\n      const direction = reverse ? '-reverse' : ''\n\n      return `v-window-${axis}${direction}-transition`\n    },\n    hasActiveItems (): boolean {\n      return Boolean(\n        this.items.find(item => !item.disabled)\n      )\n    },\n    hasNext (): boolean {\n      return this.continuous || this.internalIndex < this.items.length - 1\n    },\n    hasPrev (): boolean {\n      return this.continuous || this.internalIndex > 0\n    },\n    internalIndex (): number {\n      return this.items.findIndex((item, i) => {\n        return this.internalValue === this.getValue(item, i)\n      })\n    },\n    internalReverse (): boolean {\n      return this.$vuetify.rtl ? !this.reverse : this.reverse\n    },\n  },\n\n  watch: {\n    internalIndex (val, oldVal) {\n      this.isReverse = this.updateReverse(val, oldVal)\n    },\n  },\n\n  mounted () {\n    window.requestAnimationFrame(() => (this.isBooted = true))\n  },\n\n  methods: {\n    genDefaultSlot () {\n      return this.$slots.default\n    },\n    genContainer (): VNode {\n      const children = [this.genDefaultSlot()]\n\n      if (this.showArrows) {\n        children.push(this.genControlIcons())\n      }\n\n      return this.$createElement('div', {\n        staticClass: 'v-window__container',\n        class: {\n          'v-window__container--is-active': this.isActive,\n        },\n        style: {\n          height: this.internalHeight || this.transitionHeight,\n        },\n      }, children)\n    },\n    genIcon (\n      direction: 'prev' | 'next',\n      icon: string,\n      click: () => void\n    ) {\n      const on = {\n        click: (e: Event) => {\n          e.stopPropagation()\n          this.changedByDelimiters = true\n          click()\n        },\n      }\n      const attrs = {\n        'aria-label': this.$vuetify.lang.t(`$vuetify.carousel.${direction}`),\n      }\n      const children = this.$scopedSlots[direction]?.({\n        on,\n        attrs,\n      }) ?? [this.$createElement(VBtn, {\n        props: { icon: true },\n        attrs,\n        on,\n      }, [\n        this.$createElement(VIcon, {\n          props: { large: true },\n        }, icon),\n      ])]\n\n      return this.$createElement('div', {\n        staticClass: `v-window__${direction}`,\n      }, children)\n    },\n    genControlIcons () {\n      const icons = []\n\n      const prevIcon = this.$vuetify.rtl\n        ? this.nextIcon\n        : this.prevIcon\n\n      /* istanbul ignore else */\n      if (\n        this.hasPrev &&\n        prevIcon &&\n        typeof prevIcon === 'string'\n      ) {\n        const icon = this.genIcon('prev', prevIcon, this.prev)\n        icon && icons.push(icon)\n      }\n\n      const nextIcon = this.$vuetify.rtl\n        ? this.prevIcon\n        : this.nextIcon\n\n      /* istanbul ignore else */\n      if (\n        this.hasNext &&\n        nextIcon &&\n        typeof nextIcon === 'string'\n      ) {\n        const icon = this.genIcon('next', nextIcon, this.next)\n        icon && icons.push(icon)\n      }\n\n      return icons\n    },\n    getNextIndex (index: number): number {\n      const nextIndex = (index + 1) % this.items.length\n      const item = this.items[nextIndex]\n\n      if (item.disabled) return this.getNextIndex(nextIndex)\n\n      return nextIndex\n    },\n    getPrevIndex (index: number): number {\n      const prevIndex = (index + this.items.length - 1) % this.items.length\n      const item = this.items[prevIndex]\n\n      if (item.disabled) return this.getPrevIndex(prevIndex)\n\n      return prevIndex\n    },\n    next () {\n      /* istanbul ignore if */\n      if (!this.hasActiveItems || !this.hasNext) return\n\n      const nextIndex = this.getNextIndex(this.internalIndex)\n      const item = this.items[nextIndex]\n\n      this.internalValue = this.getValue(item, nextIndex)\n    },\n    prev () {\n      /* istanbul ignore if */\n      if (!this.hasActiveItems || !this.hasPrev) return\n\n      const lastIndex = this.getPrevIndex(this.internalIndex)\n      const item = this.items[lastIndex]\n\n      this.internalValue = this.getValue(item, lastIndex)\n    },\n    updateReverse (val: number, oldVal: number) {\n      const itemsLength = this.items.length\n      const lastIndex = itemsLength - 1\n\n      if (itemsLength <= 2) return val < oldVal\n\n      if (val === lastIndex && oldVal === 0) {\n        return true\n      } else if (val === 0 && oldVal === lastIndex) {\n        return false\n      } else {\n        return val < oldVal\n      }\n    },\n  },\n\n  render (h): VNode {\n    const data = {\n      staticClass: 'v-window',\n      class: this.classes,\n      directives: [] as VNodeDirective[],\n    }\n\n    if (!this.touchless) {\n      const value = this.touch || {\n        left: () => {\n          this.$vuetify.rtl ? this.prev() : this.next()\n        },\n        right: () => {\n          this.$vuetify.rtl ? this.next() : this.prev()\n        },\n        end: (e: TouchEvent) => {\n          e.stopPropagation()\n        },\n        start: (e: TouchEvent) => {\n          e.stopPropagation()\n        },\n      }\n\n      data.directives.push({\n        name: 'touch',\n        value,\n      })\n    }\n\n    return h('div', data, [this.genContainer()])\n  },\n})\n"], "mappings": ";;;;;;AAAA;AACA,OAAO,8CAAP,C,CAOA;;AACA,OAAOA,KAAP,MAAkB,wBAAlB,C,CAEA;;AACA,OAAOC,IAAP,MAAiB,SAAjB;AACA,OAAOC,KAAP,MAAkB,UAAlB;AACA,SAASC,aAAT,QAA8B,0BAA9B;AAEA;;AACA,eAAeA,aAAa,CAACC,MAAd,CAAqB;EAClCC,IAAI,EAAE,UAD4B;EAGlCC,UAAU,EAAE;IAAEN,KAAA,EAAAA;EAAF,CAHsB;EAKlCO,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,WAAW,EAAE;IADR,CAAP;EAGD,CATiC;EAWlCC,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC,MADK;MAEX,WAAS;IAFE,CADR;IAKLC,UAAU,EAAEC,OALP;IAMLC,SAAS,EAAE;MACTJ,IAAI,EAAEG,OADG;MAET,WAAS;IAFA,CANN;IAULE,QAAQ,EAAE;MACRL,IAAI,EAAE,CAACG,OAAD,EAAUF,MAAV,CADE;MAER,WAAS;IAFD,CAVL;IAcLK,QAAQ,EAAE;MACRN,IAAI,EAAE,CAACG,OAAD,EAAUF,MAAV,CADE;MAER,WAAS;IAFD,CAdL;IAkBLM,OAAO,EAAEJ,OAlBJ;IAmBLK,UAAU,EAAEL,OAnBP;IAoBLM,iBAAiB,EAAEN,OApBd;IAqBLO,KAAK,EAAEC,MArBF;IAsBLC,SAAS,EAAET,OAtBN;IAuBLU,KAAK,EAAE;MACLC,QAAQ,EAAE;IADL,CAvBF;IA0BLC,QAAQ,EAAEZ;EA1BL,CAX2B;EAwClCa,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,mBAAmB,EAAE,KADhB;MAELC,cAAc,EAAEC,SAFX;MAGLC,gBAAgB,EAAED,SAHb;MAILE,eAAe,EAAE,CAJZ;MAKLC,QAAQ,EAAE,KALL;MAMLC,SAAS,EAAE;IANN,CAAP;EAQD,CAjDiC;EAmDlCC,QAAQ,EAAE;IACRC,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAO,KAAKJ,eAAL,GAAuB,CAA9B;IACD,CAHO;IAIRK,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKnC,aAAa,CAACoC,OAAd,CAAsBJ,QAAtB,CAA+BE,OAA/B,CAAuCG,IAAvC,CAA4C,IAA5C,CADE;QAEL,kCAAkC,KAAKpB;MAAA;IAE1C,CATO;IAURqB,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAAA,IAAAC,QAAA;MAChB,IAAI,CAAC,KAAKT,QAAV,EAAoB,OAAO,EAAP;MAEpB,IAAMU,IAAI,GAAG,KAAKjB,QAAL,GAAgB,GAAhB,GAAsB,GAAnC;MACA,IAAMR,OAAO,GAAG,KAAK0B,eAAL,GAAuB,CAAC,KAAKV,SAA7B,GAAyC,KAAKA,SAA9D;MACA,IAAMW,SAAS,GAAG3B,OAAO,GAAG,UAAH,GAAgB,EAAzC;MAEA,OAAA4B,uBAAA,CAAAJ,QAAA,eAAAK,MAAA,CAAmBJ,IAAI,GAAAH,IAAA,CAAAE,QAAA,EAAGG,SAAS;IACpC,CAlBO;IAmBRG,cAAc,WAAdA,cAAcA,CAAA;MAAA,IAAAC,SAAA;MACZ,OAAOnC,OAAO,CACZoC,qBAAA,CAAAD,SAAA,QAAKE,KAAL,EAAAX,IAAA,CAAAS,SAAA,EAAgB,UAAAG,IAAI;QAAA,OAAI,CAACA,IAAI,CAACC,QAA9B;MAAA,EADY,CAAd;IAGD,CAvBO;IAwBRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAKzC,UAAL,IAAmB,KAAK0C,aAAL,GAAqB,KAAKJ,KAAL,CAAWK,MAAX,GAAoB,CAAnE;IACD,CA1BO;IA2BRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAK5C,UAAL,IAAmB,KAAK0C,aAAL,GAAqB,CAA/C;IACD,CA7BO;IA8BRA,aAAa,WAAbA,aAAaA,CAAA;MAAA,IAAAG,SAAA;QAAAC,KAAA;MACX,OAAOC,0BAAA,CAAAF,SAAA,QAAKP,KAAL,EAAAX,IAAA,CAAAkB,SAAA,EAAqB,UAACN,IAAD,EAAOS,CAAP,EAAY;QACtC,OAAOF,KAAA,CAAKG,aAAL,KAAuBH,KAAA,CAAKI,QAAL,CAAcX,IAAd,EAAoBS,CAApB,CAA9B;MACD,CAFM,CAAP;IAGD,CAlCO;IAmCRjB,eAAe,WAAfA,eAAeA,CAAA;MACb,OAAO,KAAKoB,QAAL,CAAcC,GAAd,GAAoB,CAAAC,wBAAA,CAAC,KAArB,GAAAA,wBAAA,CAAoC,KAA3C;IACD;EArCO,CAnDwB;EA2FlCC,KAAK,EAAE;IACLZ,aAAa,WAAbA,aAAaA,CAAEa,GAAF,EAAOC,MAAP,EAAa;MACxB,KAAKnC,SAAL,GAAiB,KAAKoC,aAAL,CAAmBF,GAAnB,EAAwBC,MAAxB,CAAjB;IACD;EAHI,CA3F2B;EAiGlCE,OAAO,WAAPA,OAAOA,CAAA;IAAA,IAAAC,MAAA;IACLC,MAAM,CAACC,qBAAP,CAA6B;MAAA,OAAOF,MAAA,CAAKvC,QAAL,GAAgB,IAApD;IAAA;EACD,CAnGiC;EAqGlC0C,OAAO,EAAE;IACPC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,KAAKC,MAAL,WAAP;IACD,CAHM;IAIPC,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAMC,QAAQ,GAAG,CAAC,KAAKH,cAAL,EAAD,CAAjB;MAEA,IAAI,KAAKzD,UAAT,EAAqB;QACnB4D,QAAQ,CAACC,IAAT,CAAc,KAAKC,eAAL,EAAd;MACD;MAED,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,qBADmB;QAEhC,SAAO;UACL,kCAAkC,KAAK/C;QADlC,CAFyB;QAKhCgD,KAAK,EAAE;UACLC,MAAM,EAAE,KAAKxD,cAAL,IAAuB,KAAKE;QAD/B;MALyB,CAA3B,EAQJgD,QARI,CAAP;IASD,CApBM;IAqBPO,OAAO,WAAPA,OAAOA,CACLzC,SADK,EAEL0C,IAFK,EAGLC,MAHK,EAGY;MAAA,IAAAC,MAAA;;MAEjB,IAAMC,EAAE,GAAG;QACTF,KAAK,EAAG,SAARA,KAAKA,CAAGG,CAAD,EAAa;UAClBA,CAAC,CAACC,eAAF;UACAH,MAAA,CAAK7D,mBAAL,GAA2B,IAA3B;UACA4D,MAAK;QACN;MALQ,CAAX;MAOA,IAAMK,KAAK,GAAG;QACZ,cAAc,KAAK7B,QAAL,CAAc8B,IAAd,CAAmBC,CAAnB,sBAAAhD,MAAA,CAA0CF,SAAS,CAAnD;MADF,CAAd;MAGA,IAAMkC,QAAQ,GAAG,CAAAiB,EAAA,IAAAC,EAAA,IAAAC,EAAA,QAAKC,YAAL,EAAkBtD,SAAlB,OAA4B,IAA5B,IAA4BoD,EAAA,WAA5B,GAA4B,MAA5B,GAA4BA,EAAA,CAAAzD,IAAA,CAAA0D,EAAA,EAAG;QAC9CR,EAD8C,EAC9CA,EAD8C;QAE9CG,KAAA,EAAAA;MAF8C,CAAH,CAA5B,MAGf,IAHe,IAGfG,EAAA,WAHe,GAGfA,EAHe,GAGX,CAAC,KAAKd,cAAL,CAAoBjF,IAApB,EAA0B;QAC/BQ,KAAK,EAAE;UAAE8E,IAAI,EAAE;QAAR,CADwB;QAE/BM,KAF+B,EAE/BA,KAF+B;QAG/BH,EAAA,EAAAA;MAH+B,CAA1B,EAIJ,CACD,KAAKR,cAAL,CAAoBhF,KAApB,EAA2B;QACzBO,KAAK,EAAE;UAAE2F,KAAK,EAAE;QAAT;MADkB,CAA3B,EAEGb,IAFH,CADC,CAJI,CAAD,CAHN;MAaA,OAAO,KAAKL,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,eAAApC,MAAA,CAAeF,SAAS;MADH,CAA3B,EAEJkC,QAFI,CAAP;IAGD,CApDM;IAqDPE,eAAe,WAAfA,eAAeA,CAAA;MACb,IAAMoB,KAAK,GAAG,EAAd;MAEA,IAAMpF,QAAQ,GAAG,KAAK+C,QAAL,CAAcC,GAAd,GACb,KAAKjD,QADQ,GAEb,KAAKC,QAFT;MAIA;;MACA,IACE,KAAKwC,OAAL,IACAxC,QADA,IAEA,OAAOA,QAAP,KAAoB,QAHtB,EAIE;QACA,IAAMsE,IAAI,GAAG,KAAKD,OAAL,CAAa,MAAb,EAAqBrE,QAArB,EAA+B,KAAKqF,IAApC,CAAb;QACAf,IAAI,IAAIc,KAAK,CAACrB,IAAN,CAAWO,IAAX,CAAR;MACD;MAED,IAAMvE,QAAQ,GAAG,KAAKgD,QAAL,CAAcC,GAAd,GACb,KAAKhD,QADQ,GAEb,KAAKD,QAFT;MAIA;;MACA,IACE,KAAKsC,OAAL,IACAtC,QADA,IAEA,OAAOA,QAAP,KAAoB,QAHtB,EAIE;QACA,IAAMuE,KAAI,GAAG,KAAKD,OAAL,CAAa,MAAb,EAAqBtE,QAArB,EAA+B,KAAKuF,IAApC,CAAb;QACAhB,KAAI,IAAIc,KAAK,CAACrB,IAAN,CAAWO,KAAX,CAAR;MACD;MAED,OAAOc,KAAP;IACD,CArFM;IAsFPG,YAAY,WAAZA,YAAYA,CAAEC,KAAF,EAAe;MACzB,IAAMC,SAAS,GAAG,CAACD,KAAK,GAAG,CAAT,IAAc,KAAKtD,KAAL,CAAWK,MAA3C;MACA,IAAMJ,IAAI,GAAG,KAAKD,KAAL,CAAWuD,SAAX,CAAb;MAEA,IAAItD,IAAI,CAACC,QAAT,EAAmB,OAAO,KAAKmD,YAAL,CAAkBE,SAAlB,CAAP;MAEnB,OAAOA,SAAP;IACD,CA7FM;IA8FPC,YAAY,WAAZA,YAAYA,CAAEF,KAAF,EAAe;MACzB,IAAMG,SAAS,GAAG,CAACH,KAAK,GAAG,KAAKtD,KAAL,CAAWK,MAAnB,GAA4B,CAA7B,IAAkC,KAAKL,KAAL,CAAWK,MAA/D;MACA,IAAMJ,IAAI,GAAG,KAAKD,KAAL,CAAWyD,SAAX,CAAb;MAEA,IAAIxD,IAAI,CAACC,QAAT,EAAmB,OAAO,KAAKsD,YAAL,CAAkBC,SAAlB,CAAP;MAEnB,OAAOA,SAAP;IACD,CArGM;IAsGPL,IAAI,WAAJA,IAAIA,CAAA;MACF;MACA,IAAI,CAAC,KAAKvD,cAAN,IAAwB,CAAC,KAAKM,OAAlC,EAA2C;MAE3C,IAAMoD,SAAS,GAAG,KAAKF,YAAL,CAAkB,KAAKjD,aAAvB,CAAlB;MACA,IAAMH,IAAI,GAAG,KAAKD,KAAL,CAAWuD,SAAX,CAAb;MAEA,KAAK5C,aAAL,GAAqB,KAAKC,QAAL,CAAcX,IAAd,EAAoBsD,SAApB,CAArB;IACD,CA9GM;IA+GPJ,IAAI,WAAJA,IAAIA,CAAA;MACF;MACA,IAAI,CAAC,KAAKtD,cAAN,IAAwB,CAAC,KAAKS,OAAlC,EAA2C;MAE3C,IAAMoD,SAAS,GAAG,KAAKF,YAAL,CAAkB,KAAKpD,aAAvB,CAAlB;MACA,IAAMH,IAAI,GAAG,KAAKD,KAAL,CAAW0D,SAAX,CAAb;MAEA,KAAK/C,aAAL,GAAqB,KAAKC,QAAL,CAAcX,IAAd,EAAoByD,SAApB,CAArB;IACD,CAvHM;IAwHPvC,aAAa,WAAbA,aAAaA,CAAEF,GAAF,EAAeC,MAAf,EAA6B;MACxC,IAAMyC,WAAW,GAAG,KAAK3D,KAAL,CAAWK,MAA/B;MACA,IAAMqD,SAAS,GAAGC,WAAW,GAAG,CAAhC;MAEA,IAAIA,WAAW,IAAI,CAAnB,EAAsB,OAAO1C,GAAG,GAAGC,MAAb;MAEtB,IAAID,GAAG,KAAKyC,SAAR,IAAqBxC,MAAM,KAAK,CAApC,EAAuC;QACrC,OAAO,IAAP;MACD,CAFD,MAEO,IAAID,GAAG,KAAK,CAAR,IAAaC,MAAM,KAAKwC,SAA5B,EAAuC;QAC5C,OAAO,KAAP;MACD,CAFM,MAEA;QACL,OAAOzC,GAAG,GAAGC,MAAb;MACD;IACF;EArIM,CArGyB;EA6OlC0C,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IAAA,IAAAC,MAAA;IACP,IAAMtF,IAAI,GAAG;MACXwD,WAAW,EAAE,UADF;MAEX,SAAO,KAAK9C,OAFD;MAGX/B,UAAU,EAAE;IAHD,CAAb;IAMA,IAAI,CAAC,KAAKiB,SAAV,EAAqB;MACnB,IAAMC,KAAK,GAAG,KAAKH,KAAL,IAAc;QAC1B6F,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAO;UACTD,MAAA,CAAKjD,QAAL,CAAcC,GAAd,GAAoBgD,MAAA,CAAKX,IAAL,EAApB,GAAkCW,MAAA,CAAKV,IAAL,EAAlC;QACD,CAHyB;QAI1BY,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAO;UACVF,MAAA,CAAKjD,QAAL,CAAcC,GAAd,GAAoBgD,MAAA,CAAKV,IAAL,EAApB,GAAkCU,MAAA,CAAKX,IAAL,EAAlC;QACD,CANyB;QAO1Bc,GAAG,EAAG,SAANA,GAAGA,CAAGzB,CAAD,EAAkB;UACrBA,CAAC,CAACC,eAAF;QACD,CATyB;QAU1ByB,KAAK,EAAG,SAARA,KAAKA,CAAG1B,CAAD,EAAkB;UACvBA,CAAC,CAACC,eAAF;QACD;MAZyB,CAA5B;MAeAjE,IAAI,CAACrB,UAAL,CAAgB0E,IAAhB,CAAqB;QACnB3E,IAAI,EAAE,OADa;QAEnBmB,KAAA,EAAAA;MAFmB,CAArB;IAID;IAED,OAAOwF,CAAC,CAAC,KAAD,EAAQrF,IAAR,EAAc,CAAC,KAAKmD,YAAL,EAAD,CAAd,CAAR;EACD;AA3QiC,CAArB,CAAf", "ignoreList": []}]}