{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/elevatable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/elevatable/index.js", "mtime": 1757335237107}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF9wYXJzZUludCBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL3BhcnNlLWludCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm51bWJlci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCBWdWUgZnJvbSAndnVlJzsKZXhwb3J0IGRlZmF1bHQgVnVlLmV4dGVuZCh7CiAgbmFtZTogJ2VsZXZhdGFibGUnLAogIHByb3BzOiB7CiAgICBlbGV2YXRpb246IFtOdW1iZXIsIFN0cmluZ10KICB9LAogIGNvbXB1dGVkOiB7CiAgICBjb21wdXRlZEVsZXZhdGlvbjogZnVuY3Rpb24gY29tcHV0ZWRFbGV2YXRpb24oKSB7CiAgICAgIHJldHVybiB0aGlzLmVsZXZhdGlvbjsKICAgIH0sCiAgICBlbGV2YXRpb25DbGFzc2VzOiBmdW5jdGlvbiBlbGV2YXRpb25DbGFzc2VzKCkgewogICAgICB2YXIgZWxldmF0aW9uID0gdGhpcy5jb21wdXRlZEVsZXZhdGlvbjsKICAgICAgaWYgKGVsZXZhdGlvbiA9PSBudWxsKSByZXR1cm4ge307CiAgICAgIGlmIChpc05hTihfcGFyc2VJbnQoZWxldmF0aW9uKSkpIHJldHVybiB7fTsKICAgICAgcmV0dXJuIF9kZWZpbmVQcm9wZXJ0eSh7fSwgImVsZXZhdGlvbi0iLmNvbmNhdCh0aGlzLmVsZXZhdGlvbiksIHRydWUpOwogICAgfQogIH0KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "extend", "name", "props", "elevation", "Number", "String", "computed", "computedElevation", "elevationClasses", "isNaN", "_parseInt", "_defineProperty", "concat"], "sources": ["../../../src/mixins/elevatable/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\nexport default Vue.extend({\n  name: 'elevatable',\n\n  props: {\n    elevation: [Number, String],\n  },\n\n  computed: {\n    computedElevation (): string | number | undefined {\n      return this.elevation\n    },\n    elevationClasses (): Record<string, boolean> {\n      const elevation = this.computedElevation\n\n      if (elevation == null) return {}\n      if (isNaN(parseInt(elevation))) return {}\n      return { [`elevation-${this.elevation}`]: true }\n    },\n  },\n})\n"], "mappings": ";;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,YADkB;EAGxBC,KAAK,EAAE;IACLC,SAAS,EAAE,CAACC,MAAD,EAASC,MAAT;EADN,CAHiB;EAOxBC,QAAQ,EAAE;IACRC,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,OAAO,KAAKJ,SAAZ;IACD,CAHO;IAIRK,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,IAAML,SAAS,GAAG,KAAKI,iBAAvB;MAEA,IAAIJ,SAAS,IAAI,IAAjB,EAAuB,OAAO,EAAP;MACvB,IAAIM,KAAK,CAACC,SAAA,CAASP,SAAD,CAAT,CAAT,EAAgC,OAAO,EAAP;MAChC,OAAAQ,eAAA,kBAAAC,MAAA,CAAuB,KAAKT,SAAS,GAAK;IAC3C;EAVO;AAPc,CAAX,CAAf", "ignoreList": []}]}