{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VList.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VList.js", "mtime": 1757335238374}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VSheet", "extend", "name", "provide", "isInList", "list", "inject", "isInMenu", "isInNav", "props", "dense", "Boolean", "disabled", "expand", "flat", "nav", "rounded", "subheader", "threeLine", "twoLine", "data", "groups", "computed", "classes", "_objectSpread", "options", "call", "_flatInstanceProperty", "methods", "register", "content", "push", "unregister", "_context", "_context2", "index", "_findIndexInstanceProperty", "g", "_uid", "_spliceInstanceProperty", "listClick", "uid", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "group", "value", "toggle", "err", "e", "f", "render", "h", "staticClass", "style", "styles", "attrs", "role", "undefined", "attrs$", "tag", "setBackgroundColor", "color", "$slots"], "sources": ["../../../src/components/VList/VList.ts"], "sourcesContent": ["// Styles\nimport './VList.sass'\nimport VListGroup from './VListGroup'\n\n// Components\nimport VSheet from '../VSheet/VSheet'\n\n// Types\nimport { VNode } from 'vue'\n\ntype VListGroupInstance = InstanceType<typeof VListGroup>\n\ninterface options extends InstanceType<typeof VSheet> {\n  isInMenu: boolean\n  isInNav: boolean\n}\n\n/* @vue/component */\nexport default VSheet.extend<options>().extend({\n  name: 'v-list',\n\n  provide (): object {\n    return {\n      isInList: true,\n      list: this,\n    }\n  },\n\n  inject: {\n    isInMenu: {\n      default: false,\n    },\n    isInNav: {\n      default: false,\n    },\n  },\n\n  props: {\n    dense: Boolean,\n    disabled: Boolean,\n    expand: Boolean,\n    flat: Boolean,\n    nav: Boolean,\n    rounded: Boolean,\n    subheader: Boolean,\n    threeLine: Boolean,\n    twoLine: Boolean,\n  },\n\n  data: () => ({\n    groups: [] as VListGroupInstance[],\n  }),\n\n  computed: {\n    classes (): object {\n      return {\n        ...VSheet.options.computed.classes.call(this),\n        'v-list--dense': this.dense,\n        'v-list--disabled': this.disabled,\n        'v-list--flat': this.flat,\n        'v-list--nav': this.nav,\n        'v-list--rounded': this.rounded,\n        'v-list--subheader': this.subheader,\n        'v-list--two-line': this.twoLine,\n        'v-list--three-line': this.threeLine,\n      }\n    },\n  },\n\n  methods: {\n    register (content: VListGroupInstance) {\n      this.groups.push(content)\n    },\n    unregister (content: VListGroupInstance) {\n      const index = this.groups.findIndex(g => g._uid === content._uid)\n\n      if (index > -1) this.groups.splice(index, 1)\n    },\n    listClick (uid: number) {\n      if (this.expand) return\n\n      for (const group of this.groups) {\n        group.toggle(uid)\n      }\n    },\n  },\n\n  render (h): VNode {\n    const data = {\n      staticClass: 'v-list',\n      class: this.classes,\n      style: this.styles,\n      attrs: {\n        role: this.isInNav || this.isInMenu ? undefined : 'list',\n        ...this.attrs$,\n      },\n    }\n\n    return h(this.tag, this.setBackgroundColor(this.color, data), [this.$slots.default])\n  },\n})\n"], "mappings": ";;;;;;AAAA;AACA,OAAO,0CAAP,C,CAGA;;AACA,OAAOA,MAAP,MAAmB,kBAAnB;AAYA;;AACA,eAAeA,MAAM,CAACC,MAAP,GAAyBA,MAAzB,CAAgC;EAC7CC,IAAI,EAAE,QADuC;EAG7CC,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,QAAQ,EAAE,IADL;MAELC,IAAI,EAAE;IAFD,CAAP;EAID,CAR4C;EAU7CC,MAAM,EAAE;IACNC,QAAQ,EAAE;MACR,WAAS;IADD,CADJ;IAINC,OAAO,EAAE;MACP,WAAS;IADF;EAJH,CAVqC;EAmB7CC,KAAK,EAAE;IACLC,KAAK,EAAEC,OADF;IAELC,QAAQ,EAAED,OAFL;IAGLE,MAAM,EAAEF,OAHH;IAILG,IAAI,EAAEH,OAJD;IAKLI,GAAG,EAAEJ,OALA;IAMLK,OAAO,EAAEL,OANJ;IAOLM,SAAS,EAAEN,OAPN;IAQLO,SAAS,EAAEP,OARN;IASLQ,OAAO,EAAER;EATJ,CAnBsC;EA+B7CS,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,MAAM,EAAE;IADG,CAAP;EAAA,CA/BuC;EAmC7CC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKxB,MAAM,CAACyB,OAAP,CAAeH,QAAf,CAAwBC,OAAxB,CAAgCG,IAAhC,CAAqC,IAArC,CADE;QAEL,iBAAiB,KAAKhB,KAFjB;QAGL,oBAAoB,KAAKE,QAHpB;QAIL,gBAAAe,qBAAA,CAAgB,KAJX;QAKL,eAAe,KAAKZ,GALf;QAML,mBAAmB,KAAKC,OANnB;QAOL,qBAAqB,KAAKC,SAPrB;QAQL,oBAAoB,KAAKE,OARpB;QASL,sBAAsB,KAAKD;MAAA;IAE9B;EAbO,CAnCmC;EAmD7CU,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAEC,OAAF,EAA6B;MACnC,KAAKT,MAAL,CAAYU,IAAZ,CAAiBD,OAAjB;IACD,CAHM;IAIPE,UAAU,WAAVA,UAAUA,CAAEF,OAAF,EAA6B;MAAA,IAAAG,QAAA,EAAAC,SAAA;MACrC,IAAMC,KAAK,GAAGC,0BAAA,CAAAH,QAAA,QAAKZ,MAAL,EAAAK,IAAA,CAAAO,QAAA,EAAsB,UAAAI,CAAC;QAAA,OAAIA,CAAC,CAACC,IAAF,KAAWR,OAAO,CAACQ,IAA9C;MAAA,EAAd;MAEA,IAAIH,KAAK,GAAG,CAAC,CAAb,EAAgBI,uBAAA,CAAAL,SAAA,QAAKb,MAAL,EAAAK,IAAA,CAAAQ,SAAA,EAAmBC,KAAnB,EAA0B,CAA1B;IACjB,CARM;IASPK,SAAS,WAATA,SAASA,CAAEC,GAAF,EAAa;MACpB,IAAI,KAAK5B,MAAT,EAAiB;MAAA,IAAA6B,SAAA,GAAAC,0BAAA,CAEG,KAAKtB,MAAzB;QAAAuB,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAAiC;UAAA,IAAtBC,KAAX,GAAAJ,KAAA,CAAAK,KAAA;UACED,KAAK,CAACE,MAAN,CAAaT,GAAb;QACD;MAAA,SAAAU,GAAA;QAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;MAAA;QAAAT,SAAA,CAAAW,CAAA;MAAA;IACF;EAfM,CAnDoC;EAqE7CC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMnC,IAAI,GAAG;MACXoC,WAAW,EAAE,QADF;MAEX,SAAO,KAAKjC,OAFD;MAGXkC,KAAK,EAAE,KAAKC,MAHD;MAIXC,KAAK,EAAAnC,aAAA;QACHoC,IAAI,EAAE,KAAKpD,OAAL,IAAgB,KAAKD,QAArB,GAAgCsD,SAAhC,GAA4C;MAD7C,GAEF,KAAKC,MAAA;IANC,CAAb;IAUA,OAAOP,CAAC,CAAC,KAAKQ,GAAN,EAAW,KAAKC,kBAAL,CAAwB,KAAKC,KAA7B,EAAoC7C,IAApC,CAAX,EAAsD,CAAC,KAAK8C,MAAL,WAAD,CAAtD,CAAR;EACD;AAjF4C,CAAhC,CAAf", "ignoreList": []}]}