{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sr-Cyrl.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sr-Cyrl.js", "mtime": 1757335237517}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/sr-Cyrl.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  close: 'Затвори',\n  dataIterator: {\n    noResultsText: 'Ни један запис није пронађен',\n    loadingText: 'Учитавање ставке...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Редова по страници:',\n    ariaLabel: {\n      sortDescending: 'Сортирано опадајуће.',\n      sortAscending: 'Сортирано растуће.',\n      sortNone: 'Није сортирано.',\n      activateNone: 'Кликни да уклониш сортирање.',\n      activateDescending: 'Кликни да сортираш опадајуће.',\n      activateAscending: 'Кликни да сортираш растуће.',\n    },\n    sortBy: 'Сортирај по',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Ставки по страници:',\n    itemsPerPageAll: 'Све',\n    nextPage: 'Следећа страница',\n    prevPage: 'Претходна страница',\n    firstPage: 'Прва страница',\n    lastPage: 'Последња страница',\n    pageText: '{0}-{1} од {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} одабрано',\n    nextMonthAriaLabel: 'Следећег месеца',\n    nextYearAriaLabel: 'Следеће године',\n    prevMonthAriaLabel: 'Претходни месец',\n    prevYearAriaLabel: 'Претходна година',\n  },\n  noDataText: 'Нема доступних података',\n  carousel: {\n    prev: 'Претходна слика',\n    next: 'Следећа слика',\n    ariaLabel: {\n      delimiter: 'Слика {0} од {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} више',\n  },\n  fileInput: {\n    counter: '{0} фајлова',\n    counterSize: '{0} фајлова ({1} укупно)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Навигација страницама',\n      next: 'Следећа страница',\n      previous: 'Претходна страница',\n      page: 'Иди на страну {0}',\n      currentPage: 'Тренутна страница, страница {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Оцена {0} од {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,8BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,qBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,sBADP;MAETC,aAAa,EAAE,oBAFN;MAGTC,QAAQ,EAAE,iBAHD;MAITC,YAAY,EAAE,8BAJL;MAKTC,kBAAkB,EAAE,+BALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,kBAHA;IAIVC,QAAQ,EAAE,oBAJA;IAKVC,SAAS,EAAE,eALD;IAMVC,QAAQ,EAAE,mBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,cADL;IAEVC,kBAAkB,EAAE,iBAFV;IAGVC,iBAAiB,EAAE,gBAHT;IAIVC,kBAAkB,EAAE,iBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,yBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,kBAFG;MAGTY,QAAQ,EAAE,oBAHD;MAITC,IAAI,EAAE,mBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}