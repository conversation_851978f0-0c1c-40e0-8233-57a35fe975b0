{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/VColorPickerCanvas.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/VColorPickerCanvas.js", "mtime": 1757335237744}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["clamp", "convertToUnit", "fromHSVA", "fromRGBA", "<PERSON><PERSON>", "extend", "name", "props", "color", "type", "Object", "default", "r", "g", "b", "a", "disabled", "Boolean", "dotSize", "Number", "String", "height", "width", "data", "boundingRect", "left", "top", "computed", "dot", "x", "y", "hsva", "s", "_parseInt", "v", "watch", "mounted", "updateCanvas", "methods", "emitColor", "_this$boundingRect", "$emit", "h", "hue", "alpha", "canvas", "$refs", "ctx", "getContext", "saturationGradient", "createLinearGradient", "addColorStop", "concat", "fillStyle", "fillRect", "valueGradient", "handleClick", "e", "$el", "getBoundingClientRect", "clientX", "clientY", "handleMouseDown", "preventDefault", "window", "addEventListener", "handleMouseMove", "handleMouseUp", "removeEventListener", "gen<PERSON><PERSON><PERSON>", "$createElement", "ref", "attrs", "genDot", "_context", "radius", "staticClass", "style", "transform", "_concatInstanceProperty", "call", "render", "on", "click", "mousedown"], "sources": ["../../../src/components/VColorPicker/VColorPickerCanvas.ts"], "sourcesContent": ["// Styles\nimport './VColorPickerCanvas.sass'\n\n// Helpers\nimport { clamp, convertToUnit } from '../../util/helpers'\nimport { fromHSVA, VColorPickerColor, fromRGBA } from './util'\n\n// Types\nimport Vue, { VNode, PropType } from 'vue'\n\nexport default Vue.extend({\n  name: 'v-color-picker-canvas',\n\n  props: {\n    color: {\n      type: Object as PropType<VColorPickerColor>,\n      default: () => fromRGBA({ r: 255, g: 0, b: 0, a: 1 }),\n    },\n    disabled: Boolean,\n    dotSize: {\n      type: [Number, String],\n      default: 10,\n    },\n    height: {\n      type: [Number, String],\n      default: 150,\n    },\n    width: {\n      type: [Number, String],\n      default: 300,\n    },\n  },\n\n  data () {\n    return {\n      boundingRect: {\n        width: 0,\n        height: 0,\n        left: 0,\n        top: 0,\n      } as ClientRect,\n    }\n  },\n\n  computed: {\n    dot (): { x: number, y: number} {\n      if (!this.color) return { x: 0, y: 0 }\n\n      return {\n        x: this.color.hsva.s * parseInt(this.width, 10),\n        y: (1 - this.color.hsva.v) * parseInt(this.height, 10),\n      }\n    },\n  },\n\n  watch: {\n    'color.hue': 'updateCanvas',\n  },\n\n  mounted () {\n    this.updateCanvas()\n  },\n\n  methods: {\n    emitColor (x: number, y: number) {\n      const { left, top, width, height } = this.boundingRect\n\n      this.$emit('update:color', fromHSVA({\n        h: this.color.hue,\n        s: clamp(x - left, 0, width) / width,\n        v: 1 - clamp(y - top, 0, height) / height,\n        a: this.color.alpha,\n      }))\n    },\n    updateCanvas () {\n      if (!this.color) return\n\n      const canvas = this.$refs.canvas as HTMLCanvasElement\n      const ctx = canvas.getContext('2d')\n\n      if (!ctx) return\n\n      const saturationGradient = ctx.createLinearGradient(0, 0, canvas.width, 0)\n      saturationGradient.addColorStop(0, 'hsla(0, 0%, 100%, 1)') // white\n      saturationGradient.addColorStop(1, `hsla(${this.color.hue}, 100%, 50%, 1)`)\n      ctx.fillStyle = saturationGradient\n      ctx.fillRect(0, 0, canvas.width, canvas.height)\n\n      const valueGradient = ctx.createLinearGradient(0, 0, 0, canvas.height)\n      valueGradient.addColorStop(0, 'hsla(0, 0%, 100%, 0)') // transparent\n      valueGradient.addColorStop(1, 'hsla(0, 0%, 0%, 1)') // black\n      ctx.fillStyle = valueGradient\n      ctx.fillRect(0, 0, canvas.width, canvas.height)\n    },\n    handleClick (e: MouseEvent) {\n      if (this.disabled) return\n\n      this.boundingRect = this.$el.getBoundingClientRect()\n      this.emitColor(e.clientX, e.clientY)\n    },\n    handleMouseDown (e: MouseEvent) {\n      // To prevent selection while moving cursor\n      e.preventDefault()\n\n      if (this.disabled) return\n\n      this.boundingRect = this.$el.getBoundingClientRect()\n\n      window.addEventListener('mousemove', this.handleMouseMove)\n      window.addEventListener('mouseup', this.handleMouseUp)\n    },\n    handleMouseMove (e: MouseEvent) {\n      if (this.disabled) return\n\n      this.emitColor(e.clientX, e.clientY)\n    },\n    handleMouseUp () {\n      window.removeEventListener('mousemove', this.handleMouseMove)\n      window.removeEventListener('mouseup', this.handleMouseUp)\n    },\n    genCanvas (): VNode {\n      return this.$createElement('canvas', {\n        ref: 'canvas',\n        attrs: {\n          width: this.width,\n          height: this.height,\n        },\n      })\n    },\n    genDot (): VNode {\n      const radius = parseInt(this.dotSize, 10) / 2\n      const x = convertToUnit(this.dot.x - radius)\n      const y = convertToUnit(this.dot.y - radius)\n\n      return this.$createElement('div', {\n        staticClass: 'v-color-picker__canvas-dot',\n        class: {\n          'v-color-picker__canvas-dot--disabled': this.disabled,\n        },\n        style: {\n          width: convertToUnit(this.dotSize),\n          height: convertToUnit(this.dotSize),\n          transform: `translate(${x}, ${y})`,\n        },\n      })\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-color-picker__canvas',\n      style: {\n        width: convertToUnit(this.width),\n        height: convertToUnit(this.height),\n      },\n      on: {\n        click: this.handleClick,\n        mousedown: this.handleMouseDown,\n      },\n    }, [\n      this.genCanvas(),\n      this.genDot(),\n    ])\n  },\n})\n"], "mappings": ";;;AAAA;AACA,OAAO,8DAAP,C,CAEA;;AACA,SAASA,KAAT,EAAgBC,aAAhB,QAAqC,oBAArC;AACA,SAASC,QAAT,EAAsCC,QAAtC,QAAsD,QAAtD,C,CAEA;;AACA,OAAOC,GAAP,MAAqC,KAArC;AAEA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,uBADkB;EAGxBC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,MADD;MAEL,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQR,QAAQ,CAAC;UAAES,CAAC,EAAE,GAAL;UAAUC,CAAC,EAAE,CAAb;UAAgBC,CAAC,EAAE,CAAnB;UAAsBC,CAAC,EAAE;QAAzB,CAAD;MAAA;IAFlB,CADF;IAKLC,QAAQ,EAAEC,OALL;IAMLC,OAAO,EAAE;MACPT,IAAI,EAAE,CAACU,MAAD,EAASC,MAAT,CADC;MAEP,WAAS;IAFF,CANJ;IAULC,MAAM,EAAE;MACNZ,IAAI,EAAE,CAACU,MAAD,EAASC,MAAT,CADA;MAEN,WAAS;IAFH,CAVH;IAcLE,KAAK,EAAE;MACLb,IAAI,EAAE,CAACU,MAAD,EAASC,MAAT,CADD;MAEL,WAAS;IAFJ;EAdF,CAHiB;EAuBxBG,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,YAAY,EAAE;QACZF,KAAK,EAAE,CADK;QAEZD,MAAM,EAAE,CAFI;QAGZI,IAAI,EAAE,CAHM;QAIZC,GAAG,EAAE;MAJO;IADT,CAAP;EAQD,CAhCuB;EAkCxBC,QAAQ,EAAE;IACRC,GAAG,WAAHA,GAAGA,CAAA;MACD,IAAI,CAAC,KAAKpB,KAAV,EAAiB,OAAO;QAAEqB,CAAC,EAAE,CAAL;QAAQC,CAAC,EAAE;MAAX,CAAP;MAEjB,OAAO;QACLD,CAAC,EAAE,KAAKrB,KAAL,CAAWuB,IAAX,CAAgBC,CAAhB,GAAoBC,SAAA,CAAS,KAAKX,KAAN,EAAa,EAAb,CAD1B;QAELQ,CAAC,EAAE,CAAC,IAAI,KAAKtB,KAAL,CAAWuB,IAAX,CAAgBG,CAArB,IAA0BD,SAAA,CAAS,KAAKZ,MAAN,EAAc,EAAd;MAFhC,CAAP;IAID;EARO,CAlCc;EA6CxBc,KAAK,EAAE;IACL,aAAa;EADR,CA7CiB;EAiDxBC,OAAO,WAAPA,OAAOA,CAAA;IACL,KAAKC,YAAL;EACD,CAnDuB;EAqDxBC,OAAO,EAAE;IACPC,SAAS,WAATA,SAASA,CAAEV,CAAF,EAAaC,CAAb,EAAsB;MAC7B,IAAAU,kBAAA,GAAqC,KAAKhB,YAA1C;QAAQC,IAAF,GAAAe,kBAAA,CAAEf,IAAF;QAAQC,GAAR,GAAAc,kBAAA,CAAQd,GAAR;QAAaJ,KAAb,GAAAkB,kBAAA,CAAalB,KAAb;QAAoBD,MAAA,GAAAmB,kBAAA,CAAAnB,MAAA;MAE1B,KAAKoB,KAAL,CAAW,cAAX,EAA2BvC,QAAQ,CAAC;QAClCwC,CAAC,EAAE,KAAKlC,KAAL,CAAWmC,GADoB;QAElCX,CAAC,EAAEhC,KAAK,CAAC6B,CAAC,GAAGJ,IAAL,EAAW,CAAX,EAAcH,KAAd,CAAL,GAA4BA,KAFG;QAGlCY,CAAC,EAAE,IAAIlC,KAAK,CAAC8B,CAAC,GAAGJ,GAAL,EAAU,CAAV,EAAaL,MAAb,CAAL,GAA4BA,MAHD;QAIlCN,CAAC,EAAE,KAAKP,KAAL,CAAWoC;MAJoB,CAAD,CAAnC;IAMD,CAVM;IAWPP,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAI,CAAC,KAAK7B,KAAV,EAAiB;MAEjB,IAAMqC,MAAM,GAAG,KAAKC,KAAL,CAAWD,MAA1B;MACA,IAAME,GAAG,GAAGF,MAAM,CAACG,UAAP,CAAkB,IAAlB,CAAZ;MAEA,IAAI,CAACD,GAAL,EAAU;MAEV,IAAME,kBAAkB,GAAGF,GAAG,CAACG,oBAAJ,CAAyB,CAAzB,EAA4B,CAA5B,EAA+BL,MAAM,CAACvB,KAAtC,EAA6C,CAA7C,CAA3B;MACA2B,kBAAkB,CAACE,YAAnB,CAAgC,CAAhC,EAAmC,sBAAnC,EATU,CASiD;;MAC3DF,kBAAkB,CAACE,YAAnB,CAAgC,CAAhC,UAAAC,MAAA,CAA2C,KAAK5C,KAAL,CAAWmC,GAAG,oBAAzD;MACAI,GAAG,CAACM,SAAJ,GAAgBJ,kBAAhB;MACAF,GAAG,CAACO,QAAJ,CAAa,CAAb,EAAgB,CAAhB,EAAmBT,MAAM,CAACvB,KAA1B,EAAiCuB,MAAM,CAACxB,MAAxC;MAEA,IAAMkC,aAAa,GAAGR,GAAG,CAACG,oBAAJ,CAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkCL,MAAM,CAACxB,MAAzC,CAAtB;MACAkC,aAAa,CAACJ,YAAd,CAA2B,CAA3B,EAA8B,sBAA9B,EAfU,CAe4C;;MACtDI,aAAa,CAACJ,YAAd,CAA2B,CAA3B,EAA8B,oBAA9B,EAhBU,CAgB0C;;MACpDJ,GAAG,CAACM,SAAJ,GAAgBE,aAAhB;MACAR,GAAG,CAACO,QAAJ,CAAa,CAAb,EAAgB,CAAhB,EAAmBT,MAAM,CAACvB,KAA1B,EAAiCuB,MAAM,CAACxB,MAAxC;IACD,CA9BM;IA+BPmC,WAAW,WAAXA,WAAWA,CAAEC,CAAF,EAAe;MACxB,IAAI,KAAKzC,QAAT,EAAmB;MAEnB,KAAKQ,YAAL,GAAoB,KAAKkC,GAAL,CAASC,qBAAT,EAApB;MACA,KAAKpB,SAAL,CAAekB,CAAC,CAACG,OAAjB,EAA0BH,CAAC,CAACI,OAA5B;IACD,CApCM;IAqCPC,eAAe,WAAfA,eAAeA,CAAEL,CAAF,EAAe;MAC5B;MACAA,CAAC,CAACM,cAAF;MAEA,IAAI,KAAK/C,QAAT,EAAmB;MAEnB,KAAKQ,YAAL,GAAoB,KAAKkC,GAAL,CAASC,qBAAT,EAApB;MAEAK,MAAM,CAACC,gBAAP,CAAwB,WAAxB,EAAqC,KAAKC,eAA1C;MACAF,MAAM,CAACC,gBAAP,CAAwB,SAAxB,EAAmC,KAAKE,aAAxC;IACD,CA/CM;IAgDPD,eAAe,WAAfA,eAAeA,CAAET,CAAF,EAAe;MAC5B,IAAI,KAAKzC,QAAT,EAAmB;MAEnB,KAAKuB,SAAL,CAAekB,CAAC,CAACG,OAAjB,EAA0BH,CAAC,CAACI,OAA5B;IACD,CApDM;IAqDPM,aAAa,WAAbA,aAAaA,CAAA;MACXH,MAAM,CAACI,mBAAP,CAA2B,WAA3B,EAAwC,KAAKF,eAA7C;MACAF,MAAM,CAACI,mBAAP,CAA2B,SAA3B,EAAsC,KAAKD,aAA3C;IACD,CAxDM;IAyDPE,SAAS,WAATA,SAASA,CAAA;MACP,OAAO,KAAKC,cAAL,CAAoB,QAApB,EAA8B;QACnCC,GAAG,EAAE,QAD8B;QAEnCC,KAAK,EAAE;UACLlD,KAAK,EAAE,KAAKA,KADP;UAELD,MAAM,EAAE,KAAKA;QAFR;MAF4B,CAA9B,CAAP;IAOD,CAjEM;IAkEPoD,MAAM,WAANA,MAAMA,CAAA;MAAA,IAAAC,QAAA;MACJ,IAAMC,MAAM,GAAG1C,SAAA,CAAS,KAAKf,OAAN,EAAe,EAAf,CAAR,GAA6B,CAA5C;MACA,IAAMW,CAAC,GAAG5B,aAAa,CAAC,KAAK2B,GAAL,CAASC,CAAT,GAAa8C,MAAd,CAAvB;MACA,IAAM7C,CAAC,GAAG7B,aAAa,CAAC,KAAK2B,GAAL,CAASE,CAAT,GAAa6C,MAAd,CAAvB;MAEA,OAAO,KAAKL,cAAL,CAAoB,KAApB,EAA2B;QAChCM,WAAW,EAAE,4BADmB;QAEhC,SAAO;UACL,wCAAwC,KAAK5D;QADxC,CAFyB;QAKhC6D,KAAK,EAAE;UACLvD,KAAK,EAAErB,aAAa,CAAC,KAAKiB,OAAN,CADf;UAELG,MAAM,EAAEpB,aAAa,CAAC,KAAKiB,OAAN,CAFhB;UAGL4D,SAAS,EAAAC,uBAAA,CAAAL,QAAA,gBAAAtB,MAAA,CAAevB,CAAC,SAAAmD,IAAA,CAAAN,QAAA,EAAK5C,CAAC;QAH1B;MALyB,CAA3B,CAAP;IAWD;EAlFM,CArDe;EA0IxBmD,MAAM,WAANA,MAAMA,CAAEvC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ;MACdkC,WAAW,EAAE,wBADC;MAEdC,KAAK,EAAE;QACLvD,KAAK,EAAErB,aAAa,CAAC,KAAKqB,KAAN,CADf;QAELD,MAAM,EAAEpB,aAAa,CAAC,KAAKoB,MAAN;MAFhB,CAFO;MAMd6D,EAAE,EAAE;QACFC,KAAK,EAAE,KAAK3B,WADV;QAEF4B,SAAS,EAAE,KAAKtB;MAFd;IANU,CAAR,EAUL,CACD,KAAKO,SAAL,EADC,EAED,KAAKI,MAAL,EAFC,CAVK,CAAR;EAcD;AAzJuB,CAAX,CAAf", "ignoreList": []}]}