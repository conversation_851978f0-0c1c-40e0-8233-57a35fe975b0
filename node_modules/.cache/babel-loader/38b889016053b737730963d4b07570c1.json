{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VBtn/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VBtn/index.js", "mtime": 1757335236803}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZCdG4gZnJvbSAnLi9WQnRuJzsKZXhwb3J0IHsgVkJ0biB9OwpleHBvcnQgZGVmYXVsdCBWQnRuOw=="}, {"version": 3, "names": ["VBtn"], "sources": ["../../../src/components/VBtn/index.ts"], "sourcesContent": ["import VBtn from './VBtn'\n\nexport { VBtn }\nexport default VBtn\n"], "mappings": "AAAA,OAAOA,IAAP,MAAiB,QAAjB;AAEA,SAASA,IAAT;AACA,eAAeA,IAAf", "ignoreList": []}]}