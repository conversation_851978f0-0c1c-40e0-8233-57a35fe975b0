{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSheet/VSheet.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSheet/VSheet.js", "mtime": 1757335238982}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKLy8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL2NvbXBvbmVudHMvVlNoZWV0L1ZTaGVldC5zYXNzIjsgLy8gTWl4aW5zCgppbXBvcnQgQmluZHNBdHRycyBmcm9tICcuLi8uLi9taXhpbnMvYmluZHMtYXR0cnMnOwppbXBvcnQgQ29sb3JhYmxlIGZyb20gJy4uLy4uL21peGlucy9jb2xvcmFibGUnOwppbXBvcnQgRWxldmF0YWJsZSBmcm9tICcuLi8uLi9taXhpbnMvZWxldmF0YWJsZSc7CmltcG9ydCBNZWFzdXJhYmxlIGZyb20gJy4uLy4uL21peGlucy9tZWFzdXJhYmxlJzsKaW1wb3J0IFJvdW5kYWJsZSBmcm9tICcuLi8uLi9taXhpbnMvcm91bmRhYmxlJzsKaW1wb3J0IFRoZW1lYWJsZSBmcm9tICcuLi8uLi9taXhpbnMvdGhlbWVhYmxlJzsgLy8gSGVscGVycwoKaW1wb3J0IG1peGlucyBmcm9tICcuLi8uLi91dGlsL21peGlucyc7Ci8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBtaXhpbnMoQmluZHNBdHRycywgQ29sb3JhYmxlLCBFbGV2YXRhYmxlLCBNZWFzdXJhYmxlLCBSb3VuZGFibGUsIFRoZW1lYWJsZSkuZXh0ZW5kKHsKICBuYW1lOiAndi1zaGVldCcsCiAgcHJvcHM6IHsKICAgIG91dGxpbmVkOiBCb29sZWFuLAogICAgc2hhcGVkOiBCb29sZWFuLAogICAgdGFnOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgImRlZmF1bHQiOiAnZGl2JwogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNsYXNzZXM6IGZ1bmN0aW9uIGNsYXNzZXMoKSB7CiAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7CiAgICAgICAgJ3Ytc2hlZXQnOiB0cnVlLAogICAgICAgICd2LXNoZWV0LS1vdXRsaW5lZCc6IHRoaXMub3V0bGluZWQsCiAgICAgICAgJ3Ytc2hlZXQtLXNoYXBlZCc6IHRoaXMuc2hhcGVkCiAgICAgIH0sIHRoaXMudGhlbWVDbGFzc2VzKSwgdGhpcy5lbGV2YXRpb25DbGFzc2VzKSwgdGhpcy5yb3VuZGVkQ2xhc3Nlcyk7CiAgICB9LAogICAgc3R5bGVzOiBmdW5jdGlvbiBzdHlsZXMoKSB7CiAgICAgIHJldHVybiB0aGlzLm1lYXN1cmFibGVTdHlsZXM7CiAgICB9CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICB2YXIgZGF0YSA9IHsKICAgICAgImNsYXNzIjogdGhpcy5jbGFzc2VzLAogICAgICBzdHlsZTogdGhpcy5zdHlsZXMsCiAgICAgIG9uOiB0aGlzLmxpc3RlbmVycyQKICAgIH07CiAgICByZXR1cm4gaCh0aGlzLnRhZywgdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb2xvciwgZGF0YSksIHRoaXMuJHNsb3RzWyJkZWZhdWx0Il0pOwogIH0KfSk7"}, {"version": 3, "names": ["BindsAttrs", "Colorable", "Elevatable", "Measurable", "Roundable", "Themeable", "mixins", "extend", "name", "props", "outlined", "Boolean", "shaped", "tag", "type", "String", "computed", "classes", "_objectSpread", "themeClasses", "elevationClasses", "roundedClasses", "styles", "measurableStyles", "render", "h", "data", "style", "on", "listeners$", "setBackgroundColor", "color", "$slots"], "sources": ["../../../src/components/VSheet/VSheet.ts"], "sourcesContent": ["// Styles\nimport './VSheet.sass'\n\n// Mixins\nimport BindsAttrs from '../../mixins/binds-attrs'\nimport Colorable from '../../mixins/colorable'\nimport Elevatable from '../../mixins/elevatable'\nimport Measurable from '../../mixins/measurable'\nimport Roundable from '../../mixins/roundable'\nimport Themeable from '../../mixins/themeable'\n\n// Helpers\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\n\n/* @vue/component */\nexport default mixins(\n  BindsAttrs,\n  Colorable,\n  Elevatable,\n  Measurable,\n  Roundable,\n  Themeable\n).extend({\n  name: 'v-sheet',\n\n  props: {\n    outlined: Boolean,\n    shaped: Boolean,\n    tag: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        'v-sheet': true,\n        'v-sheet--outlined': this.outlined,\n        'v-sheet--shaped': this.shaped,\n        ...this.themeClasses,\n        ...this.elevationClasses,\n        ...this.roundedClasses,\n      }\n    },\n    styles (): object {\n      return this.measurableStyles\n    },\n  },\n\n  render (h): VNode {\n    const data = {\n      class: this.classes,\n      style: this.styles,\n      on: this.listeners$,\n    }\n\n    return h(\n      this.tag,\n      this.setBackgroundColor(this.color, data),\n      this.$slots.default\n    )\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAO,4CAAP,C,CAEA;;AACA,OAAOA,UAAP,MAAuB,0BAAvB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,UAAP,MAAuB,yBAAvB;AACA,OAAOC,UAAP,MAAuB,yBAAvB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAKA;;AACA,eAAeA,MAAM,CACnBN,UADmB,EAEnBC,SAFmB,EAGnBC,UAHmB,EAInBC,UAJmB,EAKnBC,SALmB,EAMnBC,SANmB,CAAN,CAObE,MAPa,CAON;EACPC,IAAI,EAAE,SADC;EAGPC,KAAK,EAAE;IACLC,QAAQ,EAAEC,OADL;IAELC,MAAM,EAAED,OAFH;IAGLE,GAAG,EAAE;MACHC,IAAI,EAAEC,MADH;MAEH,WAAS;IAFN;EAHA,CAHA;EAYPC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA;QACE,WAAW,IADN;QAEL,qBAAqB,KAAKR,QAFrB;QAGL,mBAAmB,KAAKE;MAHnB,GAIF,KAAKO,YAJH,GAKF,KAAKC,gBALH,GAMF,KAAKC,cAAA;IAEX,CAVO;IAWRC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO,KAAKC,gBAAZ;IACD;EAbO,CAZH;EA4BPC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMC,IAAI,GAAG;MACX,SAAO,KAAKT,OADD;MAEXU,KAAK,EAAE,KAAKL,MAFD;MAGXM,EAAE,EAAE,KAAKC;IAHE,CAAb;IAMA,OAAOJ,CAAC,CACN,KAAKZ,GADC,EAEN,KAAKiB,kBAAL,CAAwB,KAAKC,KAA7B,EAAoCL,IAApC,CAFM,EAGN,KAAKM,MAAL,WAHM,CAAR;EAKD;AAxCM,CAPM,CAAf", "ignoreList": []}]}