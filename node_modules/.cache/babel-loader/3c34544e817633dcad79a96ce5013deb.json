{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/assertThisInitialized.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/assertThisInitialized.js", "mtime": 1757335237114}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiOwpmdW5jdGlvbiBfYXNzZXJ0VGhpc0luaXRpYWxpemVkKGUpIHsKICBpZiAodm9pZCAwID09PSBlKSB0aHJvdyBuZXcgUmVmZXJlbmNlRXJyb3IoInRoaXMgaGFzbid0IGJlZW4gaW5pdGlhbGlzZWQgLSBzdXBlcigpIGhhc24ndCBiZWVuIGNhbGxlZCIpOwogIHJldHVybiBlOwp9CmV4cG9ydCB7IF9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQgYXMgZGVmYXVsdCB9Ow=="}, {"version": 3, "names": ["_assertThisInitialized", "e", "ReferenceError", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/assertThisInitialized.js"], "sourcesContent": ["function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };"], "mappings": ";AAAA,SAASA,sBAAsBA,CAACC,CAAC,EAAE;EACjC,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvG,OAAOD,CAAC;AACV;AACA,SAASD,sBAAsB,IAAIG,OAAO", "ignoreList": []}]}