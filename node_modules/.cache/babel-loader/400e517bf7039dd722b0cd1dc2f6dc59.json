{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VProgressLinear/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VProgressLinear/index.js", "mtime": 1757335236925}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZQcm9ncmVzc0xpbmVhciBmcm9tICcuL1ZQcm9ncmVzc0xpbmVhcic7CmV4cG9ydCB7IFZQcm9ncmVzc0xpbmVhciB9OwpleHBvcnQgZGVmYXVsdCBWUHJvZ3Jlc3NMaW5lYXI7"}, {"version": 3, "names": ["VProgressLinear"], "sources": ["../../../src/components/VProgressLinear/index.ts"], "sourcesContent": ["import VProgressLinear from './VProgressLinear'\n\nexport { VProgressLinear }\nexport default VProgressLinear\n"], "mappings": "AAAA,OAAOA,eAAP,MAA4B,mBAA5B;AAEA,SAASA,eAAT;AACA,eAAeA,eAAf", "ignoreList": []}]}