{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/id.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/id.js", "mtime": 1757335235910}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/id.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON>',\n  close: 'Tutu<PERSON>',\n  dataIterator: {\n    noResultsText: 'Tidak ditemukan catatan yang cocok',\n    loadingText: 'Memuat data...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Baris per halaman:',\n    ariaLabel: {\n      sortDescending: 'Diurutkan kebawah.',\n      sortAscending: 'Diurutkan keatas.',\n      sortNone: 'Tidak diurutkan.',\n      activateNone: 'Aktifkan untuk menghapus penyortiran.',\n      activateDescending: 'Aktifkan untuk mengurutkan kebawah.',\n      activateAscending: 'Aktifkan untuk mengurutkan keatas.',\n    },\n    sortBy: 'Urutkan berdasar',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Item per halaman:',\n    itemsPerPageAll: 'Semua',\n    nextPage: 'Halaman selanjutnya',\n    prevPage: 'Halaman sebelumnya',\n    firstPage: 'Halaman pertama',\n    lastPage: '<PERSON><PERSON> terakhir',\n    pageText: '{0}-{1} dari {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} dipilih',\n    nextMonthAriaLabel: 'Bulan depan',\n    nextYearAriaLabel: 'Tahun depan',\n    prevMonthAriaLabel: 'Bulan sebelumnya',\n    prevYearAriaLabel: 'Tahun sebelumnya',\n  },\n  noDataText: 'Tidak ada data tersedia',\n  carousel: {\n    prev: 'Visual sebelumnya',\n    next: 'Visual selanjutnya',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} lagi',\n  },\n  fileInput: {\n    counter: '{0} berkas',\n    counterSize: '{0} berkas (dari total {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigasi Pagination',\n      next: 'Halaman selanjutnya',\n      previous: 'Halaman sebelumnya',\n      page: 'Buka halaman {0}',\n      currentPage: 'Halaman Saat Ini, Halaman {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,SADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,oCADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,oBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,mBAFN;MAGTC,QAAQ,EAAE,kBAHD;MAITC,YAAY,EAAE,uCAJL;MAKTC,kBAAkB,EAAE,qCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,mBADR;IAEVU,eAAe,EAAE,OAFP;IAGVC,QAAQ,EAAE,qBAHA;IAIVC,QAAQ,EAAE,oBAJA;IAKVC,SAAS,EAAE,iBALD;IAMVC,QAAQ,EAAE,kBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,aADL;IAEVC,kBAAkB,EAAE,aAFV;IAGVC,iBAAiB,EAAE,aAHT;IAIVC,kBAAkB,EAAE,kBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,yBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,mBADE;IAERC,IAAI,EAAE,oBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,YADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,qBADA;MAETX,IAAI,EAAE,qBAFG;MAGTY,QAAQ,EAAE,oBAHD;MAITC,IAAI,EAAE,kBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}