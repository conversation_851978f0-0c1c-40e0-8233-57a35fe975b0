{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/Document.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/Document.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdEb2N1bWVudCcsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHdpbmRvdzogd2luZG93LAogICAgICBleGFtcGxlczogW3sKICAgICAgICB0aXRsZTogJ+WtpuenkeWFrOW8jycsCiAgICAgICAgdXJsOiAnaHR0cHM6Ly9kZW1vLnFjbG91ZHRpdy5jb20vd2ViL2xhdGVzdC9kb2N1bWVudC/lrabnp5HlhazlvI8uZ2lmJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfmlbDlrablh73mlbAnLAogICAgICAgIHVybDogJ2h0dHBzOi8vZGVtby5xY2xvdWR0aXcuY29tL3dlYi9sYXRlc3QvZG9jdW1lbnQv5pWw5a2m5Ye95pWwLmdpZicKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn6a2U5rOV56yUJywKICAgICAgICB1cmw6ICdodHRwczovL2RlbW8ucWNsb3VkdGl3LmNvbS93ZWIvbGF0ZXN0L2RvY3VtZW50L+mtlOazleeslC5naWYnCiAgICAgIH1dLAogICAgICBkb2NVcmxzOiBbewogICAgICAgIGljb246ICdtZGktY2xvdWQtZG93bmxvYWQnLAogICAgICAgIGNvbG9yOiAncHJpbWFyeScsCiAgICAgICAgdGl0bGU6ICdkZW1v5LiL6L29JywKICAgICAgICB1cmw6ICdodHRwczovL2RlbW8ucWNsb3VkdGl3LmNvbS93ZWIvbGF0ZXN0L3dlYi1kZW1vLnppcCcKICAgICAgfSwgewogICAgICAgIGljb246ICdtZGktZmlsZS1kb2N1bWVudCcsCiAgICAgICAgY29sb3I6ICdibHVlIGxpZ2h0ZW4tMScsCiAgICAgICAgdGl0bGU6ICdTREvmlofmoaMnLAogICAgICAgIHVybDogJ2h0dHBzOi8vZG9jLnFjbG91ZHRpdy5jb20vd2ViL2luZGV4Lmh0bWwnCiAgICAgIH0sIHsKICAgICAgICBpY29uOiAnbWRpLWNsb3VkJywKICAgICAgICBjb2xvcjogJ2dyZXkgZGFya2VuLTMnLAogICAgICAgIHRpdGxlOiAn5a6Y572R5Zyw5Z2AJywKICAgICAgICB1cmw6ICdodHRwczovL2Nsb3VkLnRlbmNlbnQuY29tL2RvY3VtZW50L3Byb2R1Y3QvMTEzNycKICAgICAgfV0KICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "data", "window", "examples", "title", "url", "doc<PERSON>rls", "icon", "color"], "sources": ["src/components/Document.vue"], "sourcesContent": ["<template>\n  <div>\n    <v-carousel\n      height=\"500\"\n      hide-delimiters\n      show-arrows-on-hover\n    >\n      <v-carousel-item\n        v-for=\"(example, i) in examples\"\n        :key=\"i\"\n      >\n        <v-sheet\n          tile\n        >\n          <v-row\n            align=\"center\"\n            justify=\"center\"\n          >\n            <v-col align=\"center\" class=\"pt-4 pb-1\" style=\"font-size: 20px; background: #616161;\">\n              {{ example.title }}\n            </v-col>\n          </v-row>\n          <v-row\n            class=\"fill-width\"\n            align=\"center\"\n            justify=\"center\"\n          >\n            <v-img\n              :src=\"example.url\"\n              contain\n            >\n              <template v-slot:placeholder>\n                <v-row\n                  class=\"fill-height\"\n                  align=\"center\"\n                  justify=\"center\"\n                >\n                  <v-progress-circular\n                    indeterminate\n                    color=\"grey lighten-5\"\n                  ></v-progress-circular>\n                </v-row>\n              </template>\n            </v-img>\n          </v-row>\n        </v-sheet>\n      </v-carousel-item>\n    </v-carousel>\n    <v-row>\n      <v-col v-for=\"item in docUrls\" :key=\"item.url\">\n        <v-btn\n          :outlined=\"item.outlined\"\n          :color=\"item.color\"\n          dark\n          @click=\"window.open(item.url)\"\n          block\n        >\n          <v-icon\n            left\n            dark\n          >\n            {{ item.icon }}\n          </v-icon>\n          {{ item.title }}\n        </v-btn>\n      </v-col>\n    </v-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Document',\n  data() {\n    return {\n      window,\n      examples: [\n        {\n          title: '学科公式',\n          url: 'https://demo.qcloudtiw.com/web/latest/document/学科公式.gif',\n        },\n        {\n          title: '数学函数',\n          url: 'https://demo.qcloudtiw.com/web/latest/document/数学函数.gif',\n        },\n        {\n          title: '魔法笔',\n          url: 'https://demo.qcloudtiw.com/web/latest/document/魔法笔.gif',\n        },\n      ],\n      docUrls: [\n        {\n          icon: 'mdi-cloud-download',\n          color: 'primary',\n          title: 'demo下载',\n          url: 'https://demo.qcloudtiw.com/web/latest/web-demo.zip',\n        },\n        {\n          icon: 'mdi-file-document',\n          color: 'blue lighten-1',\n          title: 'SDK文档',\n          url: 'https://doc.qcloudtiw.com/web/index.html',\n        },\n        {\n          icon: 'mdi-cloud',\n          color: 'grey darken-3',\n          title: '官网地址',\n          url: 'https://cloud.tencent.com/document/product/1137',\n        },\n      ],\n    };\n  },\n};\n</script>\n"], "mappings": "AAuEA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAA,MAAA;MACAC,QAAA,GACA;QACAC,KAAA;QACAC,GAAA;MACA,GACA;QACAD,KAAA;QACAC,GAAA;MACA,GACA;QACAD,KAAA;QACAC,GAAA;MACA,EACA;MACAC,OAAA,GACA;QACAC,IAAA;QACAC,KAAA;QACAJ,KAAA;QACAC,GAAA;MACA,GACA;QACAE,IAAA;QACAC,KAAA;QACAJ,KAAA;QACAC,GAAA;MACA,GACA;QACAE,IAAA;QACAC,KAAA;QACAJ,KAAA;QACAC,GAAA;MACA;IAEA;EACA;AACA", "ignoreList": []}]}