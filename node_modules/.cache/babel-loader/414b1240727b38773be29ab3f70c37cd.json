{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VStepper/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VStepper/index.js", "mtime": 1757335236964}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlU2ltcGxlRnVuY3Rpb25hbCB9IGZyb20gJy4uLy4uL3V0aWwvaGVscGVycyc7CmltcG9ydCBWU3RlcHBlciBmcm9tICcuL1ZTdGVwcGVyJzsKaW1wb3J0IFZTdGVwcGVyU3RlcCBmcm9tICcuL1ZTdGVwcGVyU3RlcCc7CmltcG9ydCBWU3RlcHBlckNvbnRlbnQgZnJvbSAnLi9WU3RlcHBlckNvbnRlbnQnOwp2YXIgVlN0ZXBwZXJIZWFkZXIgPSBjcmVhdGVTaW1wbGVGdW5jdGlvbmFsKCd2LXN0ZXBwZXJfX2hlYWRlcicpOwp2YXIgVlN0ZXBwZXJJdGVtcyA9IGNyZWF0ZVNpbXBsZUZ1bmN0aW9uYWwoJ3Ytc3RlcHBlcl9faXRlbXMnKTsKZXhwb3J0IHsgVlN0ZXBwZXIsIFZTdGVwcGVyQ29udGVudCwgVlN0ZXBwZXJTdGVwLCBWU3RlcHBlckhlYWRlciwgVlN0ZXBwZXJJdGVtcyB9OwpleHBvcnQgZGVmYXVsdCB7CiAgJF92dWV0aWZ5X3N1YmNvbXBvbmVudHM6IHsKICAgIFZTdGVwcGVyOiBWU3RlcHBlciwKICAgIFZTdGVwcGVyQ29udGVudDogVlN0ZXBwZXJDb250ZW50LAogICAgVlN0ZXBwZXJTdGVwOiBWU3RlcHBlclN0ZXAsCiAgICBWU3RlcHBlckhlYWRlcjogVlN0ZXBwZXJIZWFkZXIsCiAgICBWU3RlcHBlckl0ZW1zOiBWU3RlcHBlckl0ZW1zCiAgfQp9Ow=="}, {"version": 3, "names": ["createSimpleFunctional", "VStepper", "VStepperStep", "VStepperContent", "VStepperHeader", "VStepperItems", "$_vuetify_subcomponents"], "sources": ["../../../src/components/VStepper/index.ts"], "sourcesContent": ["import { createSimpleFunctional } from '../../util/helpers'\nimport VStepper from './VStepper'\nimport VStepperStep from './VStepperStep'\nimport VStepperContent from './VStepperContent'\n\nconst VStepperHeader = createSimpleFunctional('v-stepper__header')\nconst VStepperItems = createSimpleFunctional('v-stepper__items')\n\nexport {\n  VStepper,\n  VStepperContent,\n  VStepperStep,\n  VStepperHeader,\n  VStepperItems,\n}\n\nexport default {\n  $_vuetify_subcomponents: {\n    VStepper,\n    VStepperContent,\n    VStepperStep,\n    VStepperHeader,\n    VStepperItems,\n  },\n}\n"], "mappings": "AAAA,SAASA,sBAAT,QAAuC,oBAAvC;AACA,OAAOC,QAAP,MAAqB,YAArB;AACA,OAAOC,YAAP,MAAyB,gBAAzB;AACA,OAAOC,eAAP,MAA4B,mBAA5B;AAEA,IAAMC,cAAc,GAAGJ,sBAAsB,CAAC,mBAAD,CAA7C;AACA,IAAMK,aAAa,GAAGL,sBAAsB,CAAC,kBAAD,CAA5C;AAEA,SACEC,QADF,EAEEE,eAFF,EAGED,YAHF,EAIEE,cAJF,EAKEC,aALF;AAQA,eAAe;EACbC,uBAAuB,EAAE;IACvBL,QADuB,EACvBA,QADuB;IAEvBE,eAFuB,EAEvBA,eAFuB;IAGvBD,YAHuB,EAGvBA,YAHuB;IAIvBE,cAJuB,EAIvBA,cAJuB;IAKvBC,aAAA,EAAAA;EALuB;AADZ,CAAf", "ignoreList": []}]}