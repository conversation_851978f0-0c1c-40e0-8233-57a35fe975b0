{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCheckbox/VCheckbox.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCheckbox/VCheckbox.js", "mtime": 1757335237698}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VIcon", "VInput", "Selectable", "extend", "name", "props", "indeterminate", "Boolean", "indeterminateIcon", "type", "String", "offIcon", "onIcon", "data", "inputIndeterminate", "computed", "classes", "_objectSpread", "options", "call", "computedIcon", "isActive", "validationState", "isDisabled", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "shouldValidate", "hasSuccess", "hasColor", "computedColor", "watch", "val", "_this", "$nextTick", "$emit", "methods", "genCheckbox", "_this$attrs$", "attrs$", "title", "checkboxAttrs", "_objectWithoutProperties", "_excluded", "$createElement", "staticClass", "setTextColor", "dense", "dark", "light", "genInput", "toString", "gen<PERSON><PERSON><PERSON>", "rippleState", "genDefaultSlot", "gen<PERSON><PERSON><PERSON>"], "sources": ["../../../src/components/VCheckbox/VCheckbox.ts"], "sourcesContent": ["// Styles\nimport './VCheckbox.sass'\nimport '../../styles/components/_selection-controls.sass'\n\n// Components\nimport VIcon from '../VIcon'\nimport VInput from '../VInput'\n\n// Mixins\nimport Selectable from '../../mixins/selectable'\n\n/* @vue/component */\nexport default Selectable.extend({\n  name: 'v-checkbox',\n\n  props: {\n    indeterminate: Boolean,\n    indeterminateIcon: {\n      type: String,\n      default: '$checkboxIndeterminate',\n    },\n    offIcon: {\n      type: String,\n      default: '$checkboxOff',\n    },\n    onIcon: {\n      type: String,\n      default: '$checkboxOn',\n    },\n  },\n\n  data () {\n    return {\n      inputIndeterminate: this.indeterminate,\n    }\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...VInput.options.computed.classes.call(this),\n        'v-input--selection-controls': true,\n        'v-input--checkbox': true,\n        'v-input--indeterminate': this.inputIndeterminate,\n      }\n    },\n    computedIcon (): string {\n      if (this.inputIndeterminate) {\n        return this.indeterminateIcon\n      } else if (this.isActive) {\n        return this.onIcon\n      } else {\n        return this.offIcon\n      }\n    },\n    // Do not return undefined if disabled,\n    // according to spec, should still show\n    // a color when disabled and active\n    validationState (): string | undefined {\n      if (this.isDisabled && !this.inputIndeterminate) return undefined\n      if (this.hasError && this.shouldValidate) return 'error'\n      if (this.hasSuccess) return 'success'\n      if (this.hasColor !== null) return this.computedColor\n      return undefined\n    },\n  },\n\n  watch: {\n    indeterminate (val) {\n      // https://github.com/vuetifyjs/vuetify/issues/8270\n      this.$nextTick(() => (this.inputIndeterminate = val))\n    },\n    inputIndeterminate (val) {\n      this.$emit('update:indeterminate', val)\n    },\n    isActive () {\n      if (!this.indeterminate) return\n      this.inputIndeterminate = false\n    },\n  },\n\n  methods: {\n    genCheckbox () {\n      const { title, ...checkboxAttrs } = this.attrs$\n      return this.$createElement('div', {\n        staticClass: 'v-input--selection-controls__input',\n      }, [\n        this.$createElement(VIcon, this.setTextColor(this.validationState, {\n          props: {\n            dense: this.dense,\n            dark: this.dark,\n            light: this.light,\n          },\n        }), this.computedIcon),\n        this.genInput('checkbox', {\n          ...checkboxAttrs,\n          'aria-checked': this.inputIndeterminate\n            ? 'mixed'\n            : this.isActive.toString(),\n        }),\n        this.genRipple(this.setTextColor(this.rippleState)),\n      ])\n    },\n    genDefaultSlot () {\n      return [\n        this.genCheckbox(),\n        this.genLabel(),\n      ]\n    },\n  },\n})\n"], "mappings": ";;;;;;AAAA;AACA,OAAO,kDAAP;AACA,OAAO,yDAAP,C,CAEA;;AACA,OAAOA,KAAP,MAAkB,UAAlB;AACA,OAAOC,MAAP,MAAmB,WAAnB,C,CAEA;;AACA,OAAOC,UAAP,MAAuB,yBAAvB;AAEA;;AACA,eAAeA,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,YADyB;EAG/BC,KAAK,EAAE;IACLC,aAAa,EAAEC,OADV;IAELC,iBAAiB,EAAE;MACjBC,IAAI,EAAEC,MADW;MAEjB,WAAS;IAFQ,CAFd;IAMLC,OAAO,EAAE;MACPF,IAAI,EAAEC,MADC;MAEP,WAAS;IAFF,CANJ;IAULE,MAAM,EAAE;MACNH,IAAI,EAAEC,MADA;MAEN,WAAS;IAFH;EAVH,CAHwB;EAmB/BG,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,kBAAkB,EAAE,KAAKR;IADpB,CAAP;EAGD,CAvB8B;EAyB/BS,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKhB,MAAM,CAACiB,OAAP,CAAeH,QAAf,CAAwBC,OAAxB,CAAgCG,IAAhC,CAAqC,IAArC,CADE;QAEL,+BAA+B,IAF1B;QAGL,qBAAqB,IAHhB;QAIL,0BAA0B,KAAKL;MAAA;IAElC,CARO;IASRM,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAI,KAAKN,kBAAT,EAA6B;QAC3B,OAAO,KAAKN,iBAAZ;MACD,CAFD,MAEO,IAAI,KAAKa,QAAT,EAAmB;QACxB,OAAO,KAAKT,MAAZ;MACD,CAFM,MAEA;QACL,OAAO,KAAKD,OAAZ;MACD;IACF,CAjBO;IAkBR;IACA;IACA;IACAW,eAAe,WAAfA,eAAeA,CAAA;MACb,IAAI,KAAKC,UAAL,IAAmB,CAAC,KAAKT,kBAA7B,EAAiD,OAAOU,SAAP;MACjD,IAAI,KAAKC,QAAL,IAAiB,KAAKC,cAA1B,EAA0C,OAAO,OAAP;MAC1C,IAAI,KAAKC,UAAT,EAAqB,OAAO,SAAP;MACrB,IAAI,KAAKC,QAAL,KAAkB,IAAtB,EAA4B,OAAO,KAAKC,aAAZ;MAC5B,OAAOL,SAAP;IACD;EA3BO,CAzBqB;EAuD/BM,KAAK,EAAE;IACLxB,aAAa,WAAbA,aAAaA,CAAEyB,GAAF,EAAK;MAAA,IAAAC,KAAA;MAChB;MACA,KAAKC,SAAL,CAAe;QAAA,OAAOD,KAAA,CAAKlB,kBAAL,GAA0BiB,GAAhD;MAAA;IACD,CAJI;IAKLjB,kBAAkB,WAAlBA,kBAAkBA,CAAEiB,GAAF,EAAK;MACrB,KAAKG,KAAL,CAAW,sBAAX,EAAmCH,GAAnC;IACD,CAPI;IAQLV,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAI,CAAC,KAAKf,aAAV,EAAyB;MACzB,KAAKQ,kBAAL,GAA0B,KAA1B;IACD;EAXI,CAvDwB;EAqE/BqB,OAAO,EAAE;IACPC,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAAC,YAAA,GAAoC,KAAKC,MAAzC;QAAQC,KAAF,GAAAF,YAAA,CAAEE,KAAF;QAAYC,aAAA,GAAAC,wBAAA,CAAAJ,YAAA,EAAAK,SAAA;MAClB,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE;MADmB,CAA3B,EAEJ,CACD,KAAKD,cAAL,CAAoB3C,KAApB,EAA2B,KAAK6C,YAAL,CAAkB,KAAKvB,eAAvB,EAAwC;QACjEjB,KAAK,EAAE;UACLyC,KAAK,EAAE,KAAKA,KADP;UAELC,IAAI,EAAE,KAAKA,IAFN;UAGLC,KAAK,EAAE,KAAKA;QAHP;MAD0D,CAAxC,CAA3B,EAMI,KAAK5B,YANT,CADC,EAQD,KAAK6B,QAAL,CAAc,UAAd,EAAAhC,aAAA,CAAAA,aAAA,KACKuB,aADqB;QAExB,gBAAgB,KAAK1B,kBAAL,GACZ,OADY,GAEZ,KAAKO,QAAL,CAAc6B,QAAd;MAAA,EAJN,CARC,EAcD,KAAKC,SAAL,CAAe,KAAKN,YAAL,CAAkB,KAAKO,WAAvB,CAAf,CAdC,CAFI,CAAP;IAkBD,CArBM;IAsBPC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,CACL,KAAKjB,WAAL,EADK,EAEL,KAAKkB,QAAL,EAFK,CAAP;IAID;EA3BM;AArEsB,CAAlB,CAAf", "ignoreList": []}]}