{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VOverlay/VOverlay.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VOverlay/VOverlay.js", "mtime": 1757335238562}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Colorable", "Themeable", "Toggleable", "mixins", "extend", "name", "props", "absolute", "Boolean", "color", "type", "String", "dark", "opacity", "Number", "value", "zIndex", "computed", "__scrim", "data", "setBackgroundColor", "staticClass", "style", "computedOpacity", "$createElement", "classes", "_objectSpread", "isActive", "themeClasses", "styles", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$slots", "render", "h", "children", "push", "on", "$listeners"], "sources": ["../../../src/components/VOverlay/VOverlay.ts"], "sourcesContent": ["// Styles\nimport './VOverlay.sass'\n\n// Mixins\nimport Colorable from './../../mixins/colorable'\nimport Themeable from '../../mixins/themeable'\nimport Toggleable from './../../mixins/toggleable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\n\n/* @vue/component */\nexport default mixins(\n  Colorable,\n  Themeable,\n  Toggleable\n).extend({\n  name: 'v-overlay',\n\n  props: {\n    absolute: Boolean,\n    color: {\n      type: String,\n      default: '#212121',\n    },\n    dark: {\n      type: Boolean,\n      default: true,\n    },\n    opacity: {\n      type: [Number, String],\n      default: 0.46,\n    },\n    value: {\n      default: true,\n    },\n    zIndex: {\n      type: [Number, String],\n      default: 5,\n    },\n  },\n\n  computed: {\n    __scrim (): VNode {\n      const data = this.setBackgroundColor(this.color, {\n        staticClass: 'v-overlay__scrim',\n        style: {\n          opacity: this.computedOpacity,\n        },\n      })\n\n      return this.$createElement('div', data)\n    },\n    classes (): object {\n      return {\n        'v-overlay--absolute': this.absolute,\n        'v-overlay--active': this.isActive,\n        ...this.themeClasses,\n      }\n    },\n    computedOpacity (): number {\n      return Number(this.isActive ? this.opacity : 0)\n    },\n    styles (): object {\n      return {\n        zIndex: this.zIndex,\n      }\n    },\n  },\n\n  methods: {\n    genContent () {\n      return this.$createElement('div', {\n        staticClass: 'v-overlay__content',\n      }, this.$slots.default)\n    },\n  },\n\n  render (h): VNode {\n    const children = [this.__scrim]\n\n    if (this.isActive) children.push(this.genContent())\n\n    return h('div', {\n      staticClass: 'v-overlay',\n      on: this.$listeners,\n      class: this.classes,\n      style: this.styles,\n    }, children)\n  },\n})\n"], "mappings": ";;;AAAA;AACA,OAAO,gDAAP,C,CAEA;;AACA,OAAOA,SAAP,MAAsB,0BAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,UAAP,MAAuB,2BAAvB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAKA;;AACA,eAAeA,MAAM,CACnBH,SADmB,EAEnBC,SAFmB,EAGnBC,UAHmB,CAAN,CAIbE,MAJa,CAIN;EACPC,IAAI,EAAE,WADC;EAGPC,KAAK,EAAE;IACLC,QAAQ,EAAEC,OADL;IAELC,KAAK,EAAE;MACLC,IAAI,EAAEC,MADD;MAEL,WAAS;IAFJ,CAFF;IAMLC,IAAI,EAAE;MACJF,IAAI,EAAEF,OADF;MAEJ,WAAS;IAFL,CAND;IAULK,OAAO,EAAE;MACPH,IAAI,EAAE,CAACI,MAAD,EAASH,MAAT,CADC;MAEP,WAAS;IAFF,CAVJ;IAcLI,KAAK,EAAE;MACL,WAAS;IADJ,CAdF;IAiBLC,MAAM,EAAE;MACNN,IAAI,EAAE,CAACI,MAAD,EAASH,MAAT,CADA;MAEN,WAAS;IAFH;EAjBH,CAHA;EA0BPM,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,IAAMC,IAAI,GAAG,KAAKC,kBAAL,CAAwB,KAAKX,KAA7B,EAAoC;QAC/CY,WAAW,EAAE,kBADkC;QAE/CC,KAAK,EAAE;UACLT,OAAO,EAAE,KAAKU;QADT;MAFwC,CAApC,CAAb;MAOA,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2BL,IAA3B,CAAP;IACD,CAVO;IAWRM,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA;QACE,uBAAuB,KAAKnB,QADvB;QAEL,qBAAqB,KAAKoB;MAFrB,GAGF,KAAKC,YAAA;IAEX,CAjBO;IAkBRL,eAAe,WAAfA,eAAeA,CAAA;MACb,OAAOT,MAAM,CAAC,KAAKa,QAAL,GAAgB,KAAKd,OAArB,GAA+B,CAAhC,CAAb;IACD,CApBO;IAqBRgB,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO;QACLb,MAAM,EAAE,KAAKA;MADR,CAAP;IAGD;EAzBO,CA1BH;EAsDPc,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKP,cAAL,CAAoB,KAApB,EAA2B;QAChCH,WAAW,EAAE;MADmB,CAA3B,EAEJ,KAAKW,MAAL,WAFI,CAAP;IAGD;EALM,CAtDF;EA8DPC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMC,QAAQ,GAAG,CAAC,KAAKjB,OAAN,CAAjB;IAEA,IAAI,KAAKS,QAAT,EAAmBQ,QAAQ,CAACC,IAAT,CAAc,KAAKL,UAAL,EAAd;IAEnB,OAAOG,CAAC,CAAC,KAAD,EAAQ;MACdb,WAAW,EAAE,WADC;MAEdgB,EAAE,EAAE,KAAKC,UAFK;MAGd,SAAO,KAAKb,OAHE;MAIdH,KAAK,EAAE,KAAKO;IAJE,CAAR,EAKLM,QALK,CAAR;EAMD;AAzEM,CAJM,CAAf", "ignoreList": []}]}