{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/hr.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/hr.js", "mtime": 1757335235895}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/hr.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Nisu pronađene odgo<PERSON>aju<PERSON>e stavke',\n    loadingText: 'Učitavanje...',\n  },\n  dataTable: {\n    itemsPerPageText: '<PERSON>aka po stranici:',\n    ariaLabel: {\n      sortDescending: 'Sortirano silazno.',\n      sortAscending: 'Sortirano uzlazno.',\n      sortNone: '<PERSON><PERSON> sortirano.',\n      activateNone: 'Odaberite za uklanjanje sortiranja.',\n      activateDescending: 'Odaberite za silazno sortiranje.',\n      activateAscending: 'Odaberite za uzlazno sortiranje.',\n    },\n    sortBy: 'Sortirajte po',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Stavki po stranici:',\n    itemsPerPageAll: 'Sve',\n    nextPage: 'Sljedeća stranica',\n    prevPage: 'Prethodna stranica',\n    firstPage: 'Prva stranica',\n    lastPage: 'Posljednja stranica',\n    pageText: '{0}-{1} od {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} odabrano',\n    nextMonthAriaLabel: 'Sljedeći mjesec',\n    nextYearAriaLabel: 'Slijedeće godine',\n    prevMonthAriaLabel: 'Prethodni mjesec',\n    prevYearAriaLabel: 'Prošla godina',\n  },\n  noDataText: 'Nema dostupnih podataka',\n  carousel: {\n    prev: 'Prethodno',\n    next: 'Sljedeće',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: 'Još {0}',\n  },\n  fileInput: {\n    counter: 'Odabranih datoteka: {0}',\n    counterSize: 'Odabranih datoteka: {0} ({1} ukupno)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigacija stranicama',\n      next: 'Sljedeća stranica',\n      previous: 'Prethodna stranica',\n      page: 'Idi na stranicu {0}',\n      currentPage: 'Trenutna stranica, stranica {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,MADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,oCADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,qBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,oBAFN;MAGTC,QAAQ,EAAE,iBAHD;MAITC,YAAY,EAAE,qCAJL;MAKTC,kBAAkB,EAAE,kCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,mBAHA;IAIVC,QAAQ,EAAE,oBAJA;IAKVC,SAAS,EAAE,eALD;IAMVC,QAAQ,EAAE,qBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,cADL;IAEVC,kBAAkB,EAAE,iBAFV;IAGVC,iBAAiB,EAAE,kBAHT;IAIVC,kBAAkB,EAAE,kBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,yBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,WADE;IAERC,IAAI,EAAE,UAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,yBADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,mBAFG;MAGTY,QAAQ,EAAE,oBAHD;MAITC,IAAI,EAAE,qBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}