{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/az.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/az.js", "mtime": 1757335234945}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/az.ts"], "sourcesContent": ["export default {\n  badge: 'ni<PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: '<PERSON>yğ<PERSON> məlumat tapılmadı',\n    loadingText: 'Yüklənir... Zəhmət olmasa, gözləyin.',\n  },\n  dataTable: {\n    itemsPerPageText: 'Səhifə başı sətir sayı:',\n    ariaLabel: {\n      sortDescending: '<PERSON><PERSON><PERSON> sıra ilə düzülmüş.',\n      sortAscending: '<PERSON>an sıra ilə düzülmüş.',\n      sortNone: 'Sıralanmamışdır. ',\n      activateNone: 'Sıralamanı yığışdır.',\n      activateDescending: '<PERSON><PERSON><PERSON> sıra ilə düz.',\n      activateAscending: '<PERSON>an sıra ilə düz.',\n    },\n    sortBy: 'Sırala',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Səhifə başı sətir sayı:',\n    itemsPerPageAll: 'Hamısı',\n    nextPage: '<PERSON>öv<PERSON><PERSON>ti səhifə',\n    prevPage: 'Əvvəlk<PERSON> səhifə',\n    firstPage: 'İlk səhifə',\n    lastPage: 'Son səhifə',\n    pageText: '{0} - {1} arası, Cəmi: {2} qeydiyyat',\n  },\n  datePicker: {\n    itemsSelected: '{0} element seçildi',\n    nextMonthAriaLabel: 'Növbəti ay',\n    nextYearAriaLabel: 'Növbəti yıl',\n    prevMonthAriaLabel: 'Keçən ay',\n    prevYearAriaLabel: 'Keçən yıl',\n  },\n  noDataText: 'Bu görüntüdə məlumat yoxdur.',\n  carousel: {\n    prev: 'Əvvəlki görüntü',\n    next: 'Növbəti görüntü',\n    ariaLabel: {\n      delimiter: 'Galereya səhifə {0} / {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} ədad daha',\n  },\n  fileInput: {\n    counter: '{0} fayl',\n    counterSize: '{0} fayl (cəmi {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Səhifələmə Naviqasiyası',\n      next: 'Növbəti səhifə',\n      previous: 'Əvəvlki səhifə',\n      page: 'Səhifəyə get {0}',\n      currentPage: 'Cari səhifə, Səhifə {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,OADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,yBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,yBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,2BADP;MAETC,aAAa,EAAE,0BAFN;MAGTC,QAAQ,EAAE,mBAHD;MAITC,YAAY,EAAE,sBAJL;MAKTC,kBAAkB,EAAE,sBALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,yBADR;IAEVU,eAAe,EAAE,QAFP;IAGVC,QAAQ,EAAE,gBAHA;IAIVC,QAAQ,EAAE,gBAJA;IAKVC,SAAS,EAAE,YALD;IAMVC,QAAQ,EAAE,YANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,qBADL;IAEVC,kBAAkB,EAAE,YAFV;IAGVC,iBAAiB,EAAE,aAHT;IAIVC,kBAAkB,EAAE,UAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,8BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,iBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,UADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,yBADA;MAETX,IAAI,EAAE,gBAFG;MAGTY,QAAQ,EAAE,gBAHD;MAITC,IAAI,EAAE,kBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}