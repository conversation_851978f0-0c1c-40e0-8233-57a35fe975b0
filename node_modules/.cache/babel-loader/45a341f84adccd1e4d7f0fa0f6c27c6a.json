{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VDialog/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VDialog/index.js", "mtime": 1757335236849}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZEaWFsb2cgZnJvbSAnLi9WRGlhbG9nJzsKZXhwb3J0IHsgVkRpYWxvZyB9OwpleHBvcnQgZGVmYXVsdCBWRGlhbG9nOw=="}, {"version": 3, "names": ["VDialog"], "sources": ["../../../src/components/VDialog/index.ts"], "sourcesContent": ["import VDialog from './VDialog'\n\nexport { VDialog }\nexport default VDialog\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,WAApB;AAEA,SAASA,OAAT;AACA,eAAeA,OAAf", "ignoreList": []}]}