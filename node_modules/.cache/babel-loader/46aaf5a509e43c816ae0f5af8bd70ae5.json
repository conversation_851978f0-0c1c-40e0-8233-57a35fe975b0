{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/VColorPickerSwatches.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/VColorPickerSwatches.js", "mtime": 1757335237754}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VIcon", "colors", "fromHex", "parseColor", "convertToUnit", "deepEqual", "mixins", "Themeable", "contrastRatio", "parseDefaultColors", "_context", "_mapInstanceProperty", "_Object$keys", "call", "key", "color", "base", "darken4", "darken3", "darken2", "darken1", "lighten1", "lighten2", "lighten3", "lighten4", "lighten5", "black", "white", "transparent", "rgba", "extend", "name", "props", "swatches", "type", "Array", "default", "disabled", "Boolean", "Object", "max<PERSON><PERSON><PERSON>", "Number", "String", "maxHeight", "methods", "genColor", "_this", "content", "$createElement", "style", "background", "small", "dark", "alpha", "light", "staticClass", "on", "click", "$emit", "genSwatches", "_context2", "_this2", "swatch", "render", "h"], "sources": ["../../../src/components/VColorPicker/VColorPickerSwatches.ts"], "sourcesContent": ["// Styles\nimport './VColorPickerSwatches.sass'\n\n// Components\nimport VIcon from '../VIcon'\n\n// Helpers\nimport colors from '../../util/colors'\nimport { VColorPickerColor, fromHex, parseColor } from './util'\nimport { convertToUnit, deepEqual } from '../../util/helpers'\nimport mixins from '../../util/mixins'\nimport Themeable from '../../mixins/themeable'\n\n// Types\nimport { VNode, PropType } from 'vue'\nimport { contrastRatio } from '../../util/colorUtils'\n\nfunction parseDefaultColors (colors: Record<string, Record<string, string>>) {\n  return Object.keys(colors).map(key => {\n    const color = colors[key]\n    return color.base ? [\n      color.base,\n      color.darken4,\n      color.darken3,\n      color.darken2,\n      color.darken1,\n      color.lighten1,\n      color.lighten2,\n      color.lighten3,\n      color.lighten4,\n      color.lighten5,\n    ] : [\n      color.black,\n      color.white,\n      color.transparent,\n    ]\n  })\n}\n\nconst white = fromHex('#FFFFFF').rgba\nconst black = fromHex('#000000').rgba\n\nexport default mixins(Themeable).extend({\n  name: 'v-color-picker-swatches',\n\n  props: {\n    swatches: {\n      type: Array as PropType<string[][]>,\n      default: () => parseDefaultColors(colors),\n    },\n    disabled: Boolean,\n    color: Object as PropType<VColorPickerColor>,\n    maxWidth: [Number, String],\n    maxHeight: [Number, String],\n  },\n\n  methods: {\n    genColor (color: string) {\n      const content = this.$createElement('div', {\n        style: {\n          background: color,\n        },\n      }, [\n        deepEqual(this.color, parseColor(color, null)) && this.$createElement(VIcon, {\n          props: {\n            small: true,\n            dark: contrastRatio(this.color.rgba, white) > 2 && this.color.alpha > 0.5,\n            light: contrastRatio(this.color.rgba, black) > 2 && this.color.alpha > 0.5,\n          },\n        }, '$success'),\n      ])\n\n      return this.$createElement('div', {\n        staticClass: 'v-color-picker__color',\n        on: {\n          // TODO: Less hacky way of catching transparent\n          click: () => this.disabled || this.$emit('update:color', fromHex(color === 'transparent' ? '#00000000' : color)),\n        },\n      }, [content])\n    },\n    genSwatches () {\n      return this.swatches.map(swatch => {\n        const colors = swatch.map(this.genColor)\n\n        return this.$createElement('div', {\n          staticClass: 'v-color-picker__swatch',\n        }, colors)\n      })\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-color-picker__swatches',\n      style: {\n        maxWidth: convertToUnit(this.maxWidth),\n        maxHeight: convertToUnit(this.maxHeight),\n      },\n    }, [\n      this.$createElement('div', this.genSwatches()),\n    ])\n  },\n})\n"], "mappings": ";;;AAAA;AACA,OAAO,gEAAP,C,CAEA;;AACA,OAAOA,KAAP,MAAkB,UAAlB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAA4BC,OAA5B,EAAqCC,UAArC,QAAuD,QAAvD;AACA,SAASC,aAAT,EAAwBC,SAAxB,QAAyC,oBAAzC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AAIA,SAASC,aAAT,QAA8B,uBAA9B;AAEA,SAASC,kBAATA,CAA6BR,MAA7B,EAA2E;EAAA,IAAAS,QAAA;EACzE,OAAOC,oBAAA,CAAAD,QAAA,GAAAE,YAAA,CAAYX,MAAZ,GAAAY,IAAA,CAAAH,QAAA,EAAwB,UAAAI,GAAG,EAAG;IACnC,IAAMC,KAAK,GAAGd,MAAM,CAACa,GAAD,CAApB;IACA,OAAOC,KAAK,CAACC,IAAN,GAAa,CAClBD,KAAK,CAACC,IADY,EAElBD,KAAK,CAACE,OAFY,EAGlBF,KAAK,CAACG,OAHY,EAIlBH,KAAK,CAACI,OAJY,EAKlBJ,KAAK,CAACK,OALY,EAMlBL,KAAK,CAACM,QANY,EAOlBN,KAAK,CAACO,QAPY,EAQlBP,KAAK,CAACQ,QARY,EASlBR,KAAK,CAACS,QATY,EAUlBT,KAAK,CAACU,QAVY,CAAb,GAWH,CACFV,KAAK,CAACW,KADJ,EAEFX,KAAK,CAACY,KAFJ,EAGFZ,KAAK,CAACa,WAHJ,CAXJ;EAgBD,CAlBM,CAAP;AAmBD;AAED,IAAMD,KAAK,GAAGzB,OAAO,CAAC,SAAD,CAAP,CAAmB2B,IAAjC;AACA,IAAMH,KAAK,GAAGxB,OAAO,CAAC,SAAD,CAAP,CAAmB2B,IAAjC;AAEA,eAAevB,MAAM,CAACC,SAAD,CAAN,CAAkBuB,MAAlB,CAAyB;EACtCC,IAAI,EAAE,yBADgC;EAGtCC,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,KADE;MAER,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ3B,kBAAkB,CAACR,MAAD;MAAA;IAFzB,CADL;IAKLoC,QAAQ,EAAEC,OALL;IAMLvB,KAAK,EAAEwB,MANF;IAOLC,QAAQ,EAAE,CAACC,MAAD,EAASC,MAAT,CAPL;IAQLC,SAAS,EAAE,CAACF,MAAD,EAASC,MAAT;EARN,CAH+B;EActCE,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAE9B,KAAF,EAAe;MAAA,IAAA+B,KAAA;MACrB,IAAMC,OAAO,GAAG,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QACzCC,KAAK,EAAE;UACLC,UAAU,EAAEnC;QADP;MADkC,CAA3B,EAIb,CACDV,SAAS,CAAC,KAAKU,KAAN,EAAaZ,UAAU,CAACY,KAAD,EAAQ,IAAR,CAAvB,CAAT,IAAkD,KAAKiC,cAAL,CAAoBhD,KAApB,EAA2B;QAC3EgC,KAAK,EAAE;UACLmB,KAAK,EAAE,IADF;UAELC,IAAI,EAAE5C,aAAa,CAAC,KAAKO,KAAL,CAAWc,IAAZ,EAAkBF,KAAlB,CAAb,GAAwC,CAAxC,IAA6C,KAAKZ,KAAL,CAAWsC,KAAX,GAAmB,GAFjE;UAGLC,KAAK,EAAE9C,aAAa,CAAC,KAAKO,KAAL,CAAWc,IAAZ,EAAkBH,KAAlB,CAAb,GAAwC,CAAxC,IAA6C,KAAKX,KAAL,CAAWsC,KAAX,GAAmB;QAHlE;MADoE,CAA3B,EAM/C,UAN+C,CADjD,CAJa,CAAhB;MAcA,OAAO,KAAKL,cAAL,CAAoB,KAApB,EAA2B;QAChCO,WAAW,EAAE,uBADmB;QAEhCC,EAAE,EAAE;UACF;UACAC,KAAK,EAAE,SAAPA,KAAKA,CAAA;YAAA,OAAQX,KAAA,CAAKT,QAAL,IAAiBS,KAAA,CAAKY,KAAL,CAAW,cAAX,EAA2BxD,OAAO,CAACa,KAAK,KAAK,aAAV,GAA0B,WAA1B,GAAwCA,KAAzC,CAAlC;UAAA;QAF5B;MAF4B,CAA3B,EAMJ,CAACgC,OAAD,CANI,CAAP;IAOD,CAvBM;IAwBPY,WAAW,WAAXA,WAAWA,CAAA;MAAA,IAAAC,SAAA;QAAAC,MAAA;MACT,OAAOlD,oBAAA,CAAAiD,SAAA,QAAK3B,QAAL,EAAApB,IAAA,CAAA+C,SAAA,EAAkB,UAAAE,MAAM,EAAG;QAChC,IAAM7D,MAAM,GAAGU,oBAAA,CAAAmD,MAAM,EAAAjD,IAAA,CAANiD,MAAM,EAAKD,MAAA,CAAKhB,QAAhB,CAAf;QAEA,OAAOgB,MAAA,CAAKb,cAAL,CAAoB,KAApB,EAA2B;UAChCO,WAAW,EAAE;QADmB,CAA3B,EAEJtD,MAFI,CAAP;MAGD,CANM,CAAP;IAOD;EAhCM,CAd6B;EAiDtC8D,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ;MACdT,WAAW,EAAE,0BADC;MAEdN,KAAK,EAAE;QACLT,QAAQ,EAAEpC,aAAa,CAAC,KAAKoC,QAAN,CADlB;QAELG,SAAS,EAAEvC,aAAa,CAAC,KAAKuC,SAAN;MAFnB;IAFO,CAAR,EAML,CACD,KAAKK,cAAL,CAAoB,KAApB,EAA2B,KAAKW,WAAL,EAA3B,CADC,CANK,CAAR;EASD;AA3DqC,CAAzB,CAAf", "ignoreList": []}]}