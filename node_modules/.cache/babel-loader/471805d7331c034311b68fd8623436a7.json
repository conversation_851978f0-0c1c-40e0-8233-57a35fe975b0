{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/button-group/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/button-group/index.js", "mtime": 1757335237057}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gRXh0ZW5zaW9ucwppbXBvcnQgeyBCYXNlSXRlbUdyb3VwIH0gZnJvbSAnLi4vLi4vY29tcG9uZW50cy9WSXRlbUdyb3VwL1ZJdGVtR3JvdXAnOwovKiBAdnVlL2NvbXBvbmVudCAqLwoKZXhwb3J0IGRlZmF1bHQgQmFzZUl0ZW1Hcm91cC5leHRlbmQoewogIG5hbWU6ICdidXR0b24tZ3JvdXAnLAogIHByb3ZpZGU6IGZ1bmN0aW9uIHByb3ZpZGUoKSB7CiAgICByZXR1cm4gewogICAgICBidG5Ub2dnbGU6IHRoaXMKICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgY2xhc3NlczogZnVuY3Rpb24gY2xhc3NlcygpIHsKICAgICAgcmV0dXJuIEJhc2VJdGVtR3JvdXAub3B0aW9ucy5jb21wdXRlZC5jbGFzc2VzLmNhbGwodGhpcyk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyBJc24ndCBiZWluZyBwYXNzZWQgZG93biB0aHJvdWdoIHR5cGVzCiAgICBnZW5EYXRhOiBCYXNlSXRlbUdyb3VwLm9wdGlvbnMubWV0aG9kcy5nZW5EYXRhCiAgfQp9KTs="}, {"version": 3, "names": ["BaseItemGroup", "extend", "name", "provide", "btnToggle", "computed", "classes", "options", "call", "methods", "genData"], "sources": ["../../../src/mixins/button-group/index.ts"], "sourcesContent": ["// Extensions\nimport { BaseItemGroup } from '../../components/VItemGroup/VItemGroup'\n\n/* @vue/component */\nexport default BaseItemGroup.extend({\n  name: 'button-group',\n\n  provide (): object {\n    return {\n      btnToggle: this,\n    }\n  },\n\n  computed: {\n    classes (): object {\n      return BaseItemGroup.options.computed.classes.call(this)\n    },\n  },\n\n  methods: {\n    // Isn't being passed down through types\n    genData: BaseItemGroup.options.methods.genData,\n  },\n})\n"], "mappings": "AAAA;AACA,SAASA,aAAT,QAA8B,wCAA9B;AAEA;;AACA,eAAeA,aAAa,CAACC,MAAd,CAAqB;EAClCC,IAAI,EAAE,cAD4B;EAGlCC,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,SAAS,EAAE;IADN,CAAP;EAGD,CAPiC;EASlCC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAON,aAAa,CAACO,OAAd,CAAsBF,QAAtB,CAA+BC,OAA/B,CAAuCE,IAAvC,CAA4C,IAA5C,CAAP;IACD;EAHO,CATwB;EAelCC,OAAO,EAAE;IACP;IACAC,OAAO,EAAEV,aAAa,CAACO,OAAd,CAAsBE,OAAtB,CAA8BC;EAFhC;AAfyB,CAArB,CAAf", "ignoreList": []}]}