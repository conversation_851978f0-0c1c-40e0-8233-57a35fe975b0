{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/getPrototypeOf.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/getPrototypeOf.js", "mtime": 1757335237631}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9PYmplY3Qkc2V0UHJvdG90eXBlT2YgZnJvbSAiY29yZS1qcy1wdXJlL2ZlYXR1cmVzL29iamVjdC9zZXQtcHJvdG90eXBlLW9mLmpzIjsKaW1wb3J0IF9iaW5kSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJjb3JlLWpzLXB1cmUvZmVhdHVyZXMvaW5zdGFuY2UvYmluZC5qcyI7CmltcG9ydCBfT2JqZWN0JGdldFByb3RvdHlwZU9mIGZyb20gImNvcmUtanMtcHVyZS9mZWF0dXJlcy9vYmplY3QvZ2V0LXByb3RvdHlwZS1vZi5qcyI7CmZ1bmN0aW9uIF9nZXRQcm90b3R5cGVPZih0KSB7CiAgdmFyIF9jb250ZXh0OwogIHJldHVybiBfZ2V0UHJvdG90eXBlT2YgPSBfT2JqZWN0JHNldFByb3RvdHlwZU9mID8gX2JpbmRJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0ID0gX09iamVjdCRnZXRQcm90b3R5cGVPZikuY2FsbChfY29udGV4dCkgOiBmdW5jdGlvbiAodCkgewogICAgcmV0dXJuIHQuX19wcm90b19fIHx8IF9PYmplY3QkZ2V0UHJvdG90eXBlT2YodCk7CiAgfSwgX2dldFByb3RvdHlwZU9mKHQpOwp9CmV4cG9ydCB7IF9nZXRQcm90b3R5cGVPZiBhcyBkZWZhdWx0IH07"}, {"version": 3, "names": ["_Object$setPrototypeOf", "_bindInstanceProperty", "_Object$getPrototypeOf", "_getPrototypeOf", "t", "_context", "call", "__proto__", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/getPrototypeOf.js"], "sourcesContent": ["import _Object$setPrototypeOf from \"core-js-pure/features/object/set-prototype-of.js\";\nimport _bindInstanceProperty from \"core-js-pure/features/instance/bind.js\";\nimport _Object$getPrototypeOf from \"core-js-pure/features/object/get-prototype-of.js\";\nfunction _getPrototypeOf(t) {\n  var _context;\n  return _getPrototypeOf = _Object$setPrototypeOf ? _bindInstanceProperty(_context = _Object$getPrototypeOf).call(_context) : function (t) {\n    return t.__proto__ || _Object$getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,qBAAqB,MAAM,wCAAwC;AAC1E,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,SAASC,eAAeA,CAACC,CAAC,EAAE;EAC1B,IAAIC,QAAQ;EACZ,OAAOF,eAAe,GAAGH,sBAAsB,GAAGC,qBAAqB,CAACI,QAAQ,GAAGH,sBAAsB,CAAC,CAACI,IAAI,CAACD,QAAQ,CAAC,GAAG,UAAUD,CAAC,EAAE;IACvI,OAAOA,CAAC,CAACG,SAAS,IAAIL,sBAAsB,CAACE,CAAC,CAAC;EACjD,CAAC,EAAED,eAAe,CAACC,CAAC,CAAC;AACvB;AACA,SAASD,eAAe,IAAIK,OAAO", "ignoreList": []}]}