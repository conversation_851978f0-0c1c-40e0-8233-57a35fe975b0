{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "showDialog", "settingType", "settingTabs", "settingItems", "drawEnable", "handwritingEnable", "showRemoteCursor", "dataSyncEnable", "enableMultiTouch", "disablePointerEventResponding", "showCursorWhenDisabled", "graphicAutoSelect", "turnPage", "whiteBoard", "h5PPT", "imgPPT", "imgFile", "authorityCmd", "text", "value", "allowCmdList", "authorityStatus", "watch", "handler", "updateSetting", "deep", "immediate", "mounted", "_this", "window", "addEventListener", "e", "canvasWrap", "document", "querySelector", "pageY", "offsetTop", "clientHeight", "teduBoard", "setCursorPosition", "x", "pageX", "y", "methods", "show", "setDrawEnable", "setHandwritingEnable", "setDataSyncEnable", "setRemoteCursorVisible", "setEnableMultiTouch", "enable", "setSyncFps", "setMouseToolBehavior", "onAuthorityChange", "index", "allowCmds", "denyCmds", "totalCmds", "_forEachInstanceProperty", "call", "c", "_includesInstanceProperty", "push", "enablePermissionChecker", "identifier", "_context", "_mapInstanceProperty", "disablePer<PERSON><PERSON><PERSON><PERSON>", "onAuthorityCmdChange", "cmdList", "$emit", "setDisablePointerEventResponding"], "sources": ["src/components/ToolbarDialog/SettingDialog.vue"], "sourcesContent": ["<template>\n  <div class=\"setting-dialog__container\">\n    <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"600\">\n      <v-card>\n        <v-card-title class=\"headline lighten-2\">\n          <v-tabs v-model=\"settingType\">\n            <v-tab v-for=\"item in settingTabs\" :key=\"item\">\n              {{ item }}\n            </v-tab>\n          </v-tabs>\n        </v-card-title>\n        <v-card-text>\n          <v-tabs-items v-model=\"settingType\">\n            <v-tab-item>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].drawEnable\"\n                    label=\"允许涂鸦\"\n                    hide-details\n                    @change=\"setDrawEnable\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].dataSyncEnable\"\n                    label=\"开启数据同步\"\n                    hide-details\n                    @change=\"setDataSyncEnable\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].handwritingEnable\"\n                    label=\"开启笔锋\"\n                    hide-details\n                    @change=\"setHandwritingEnable\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].showRemoteCursor\"\n                    label=\"显示远端画笔\"\n                    hide-details\n                    @click=\"setRemoteCursorVisible\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <!-- TODO 2.9.2的测试 -->\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].disablePointerEventResponding\"\n                    label=\"禁用鼠标响应\"\n                    hide-details\n                    @change=\"setDisablePointerEventResponding\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].showCursorWhenDisabled\"\n                    label=\"禁用响应也显示光标\"\n                    hide-details\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].enableMultiTouch\"\n                    label=\"开启多点触控(只有触摸屏才有效)\"\n                    hide-details\n                    @click=\"setEnableMultiTouch\"\n                  ></v-switch>\n                </v-col>\n                 <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].graphicAutoSelect\"\n                    label=\"几何图形自动选中\"\n                    hide-details\n                    @click=\"setEnableMultiTouch\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n            </v-tab-item>\n            <v-tab-item>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.whiteBoard\"\n                    label=\"允许鼠标点击普通白板翻页\"\n                    hide-details\n                    @change=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.h5PPT\"\n                    label=\"允许鼠标点击H5PPT翻页\"\n                    hide-details\n                    @change=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.imgPPT\"\n                    label=\"允许鼠标点击静态PPT翻页\"\n                    hide-details\n                    @change=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.imgFile\"\n                    label=\"允许鼠标点击图片组文件翻页\"\n                    hide-details\n                    @click=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n            </v-tab-item>\n            <v-tab-item>\n              <v-sheet\n                v-for=\"(stat, index) in authorityStatus\"\n                :key=\"stat.text\"\n              >\n                <v-switch\n                  v-model=\"stat.value\"\n                  :label=\"stat.text\"\n                  @change=\"(e) => onAuthorityChange(index, e)\"\n                ></v-switch>\n                <v-sheet>\n                  <v-row>\n                    <v-col\n                      cols=\"3\"\n                      v-for=\"cmd in authorityCmd[index]\"\n                      :key=\"cmd.value\"\n                    >\n                      <v-checkbox\n                        v-model=\"allowCmdList[index]\"\n                        :label=\"cmd.text\"\n                        :value=\"cmd.value\"\n                        :disabled=\"!stat.value\"\n                        dense\n                        @change=\"(e) => onAuthorityCmdChange(index, e)\"\n                      ></v-checkbox>\n                    </v-col>\n                  </v-row>\n                </v-sheet>\n                <v-divider />\n              </v-sheet>\n            </v-tab-item>\n          </v-tabs-items>\n        </v-card-text>\n      </v-card>\n    </v-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      settingType: null,\n      settingTabs: ['基础设置', '鼠标行为设置', '权限校验设置'],\n      settingItems: [\n        {\n          drawEnable: true,\n          handwritingEnable: true,\n          showRemoteCursor: true,\n          dataSyncEnable: true,\n          enableMultiTouch: false,\n          disablePointerEventResponding: false,\n          showCursorWhenDisabled: false,\n          graphicAutoSelect: false,\n        },\n        {\n          turnPage: {\n            whiteBoard: true,\n            h5PPT: true,\n            imgPPT: true,\n            imgFile: true,\n          },\n        },\n      ],\n      authorityCmd: [\n        [\n          {\n            text: '设置背景颜色',\n            value: 'Background::Update::Color',\n          },\n          {\n            text: '设置背景Frame',\n            value: 'Background::Update::Frame',\n          },\n          {\n            text: '设置背景图片',\n            value: 'Background::Update::Image',\n          },\n        ],\n        [\n          {\n            text: '添加文件',\n            value: 'File::Add::*',\n          },\n          {\n            text: '删除文件',\n            value: 'File::Delete::*',\n          },\n          {\n            text: '清空文件',\n            value: 'File::Clear::*',\n          },\n          {\n            text: '切换文件',\n            value: 'File::Switch::*',\n          },\n          {\n            text: '更新文件',\n            value: 'File::Update::*',\n          },\n        ],\n        [\n          {\n            text: '添加白板',\n            value: 'Board::Add::*',\n          },\n          {\n            text: '清空白板',\n            value: 'Board::Clear::*',\n          },\n          {\n            text: '删除白板',\n            value: 'Board::Delete::*',\n          },\n          {\n            text: '缩放白板',\n            value: 'Board::Scale::*',\n          },\n          {\n            text: '切换白板',\n            value: 'Board::Switch::Page',\n          },\n          {\n            text: '切换动画步数',\n            value: 'Board::Switch::Step',\n          },\n          {\n            text: '更新白板内容填充模式',\n            value: 'Board::Update::ContentFitMode',\n          },\n          {\n            text: '更新白板比例',\n            value: 'Board::Update::Ratio',\n          },\n          {\n            text: '撤销',\n            value: 'Board::Undo::*',\n          },\n          {\n            text: '重做',\n            value: 'Board::Redo::*',\n          },\n        ],\n        [\n          {\n            text: '添加元素',\n            value: 'Element::Add::*',\n          },\n          {\n            text: '删除元素',\n            value: 'Element::Delete::*',\n          },\n          {\n            text: '更新元素',\n            value: 'Element::Update::*',\n          },\n          {\n            text: '选择元素',\n            value: 'Element::Select::*',\n          },\n          {\n            text: '移动元素',\n            value: 'Element::Move::*',\n          },\n          {\n            text: '旋转元素',\n            value: 'Element::Rotate::*',\n          },\n          {\n            text: '缩放元素',\n            value: 'Element::Scale::*',\n          },\n        ],\n        [\n          {\n            text: '重置白板',\n            value: 'Instance::Reset::*',\n          },\n        ],\n      ],\n\n      allowCmdList: [\n        [\n          'Background::Update::Color',\n          'Background::Update::Frame',\n          'Background::Update::Image',\n        ],\n        [\n          'File::Add::*',\n          'File::Delete::*',\n          'File::Clear::*',\n          'File::Switch::*',\n          'File::Update::*',\n        ],\n        [\n          'Board::Add::*',\n          'Board::Clear::*',\n          'Board::Delete::*',\n          'Board::Scale::*',\n          'Board::Switch::Page',\n          'Board::Switch::Step',\n          'Board::Update::ContentFitMode',\n          'Board::Update::Ratio',\n          'Board::Undo::*',\n          'Board::Redo::*',\n        ],\n        [\n          'Element::Add::*',\n          'Element::Delete::*',\n          'Element::Update::*',\n          'Element::Select::*',\n          'Element::Move::*',\n          'Element::Rotate::*',\n          'Element::Scale::*',\n        ],\n        ['Instance::Reset::*'],\n      ],\n\n      authorityStatus: [\n        {\n          text: '启用背景类权限校验',\n          value: false,\n        },\n        {\n          text: '启用文件类权限校验',\n          value: false,\n        },\n        {\n          text: '启用白板类权限校验',\n          value: false,\n        },\n        {\n          text: '启用元素类权限校验',\n          value: false,\n        },\n        {\n          text: '启用实例权限校验',\n          value: false,\n        },\n      ],\n    };\n  },\n  watch: {\n    settingItems: {\n      handler() {\n        this.updateSetting();\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n  mounted() {\n    // 2.9.2 测试设置鼠标位置\n    window.addEventListener('mousemove', (e) => {\n      const canvasWrap = document.querySelector('.tx_board_canvas_wrap');\n      if (this.settingItems[0].showCursorWhenDisabled) {\n        const pageY =\n          e.pageY - canvasWrap.offsetTop + canvasWrap.clientHeight / 2;\n        window.teduBoard.setCursorPosition({\n          x: e.pageX,\n          y: pageY,\n        });\n      }\n    });\n  },\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n    setDrawEnable() {\n      window.teduBoard.setDrawEnable(this.settingItems[0].drawEnable);\n    },\n    setHandwritingEnable() {\n      window.teduBoard.setHandwritingEnable(\n        this.settingItems[0].handwritingEnable\n      );\n    },\n\n    setDataSyncEnable() {\n      window.teduBoard.setDataSyncEnable(this.settingItems[0].dataSyncEnable);\n    },\n\n    setRemoteCursorVisible() {\n      window.teduBoard.setRemoteCursorVisible(\n        this.settingItems[0].showRemoteCursor\n      );\n    },\n\n    setEnableMultiTouch() {\n      const enable = this.settingItems[0].enableMultiTouch;\n      window.teduBoard.enableMultiTouch(enable);\n      window.teduBoard.setSyncFps(enable ? 10 : 5);\n    },\n\n    // 设置鼠标操作行为\n    setMouseToolBehavior() {\n      window.teduBoard.setMouseToolBehavior({\n        turnPage: this.settingItems[1].turnPage,\n      });\n    },\n\n    onAuthorityChange(index, enable) {\n      if (enable) {\n        const allowCmds = this.allowCmdList[index];\n        const denyCmds = [];\n        const totalCmds = this.authorityCmd[index];\n        totalCmds.forEach((c) => {\n          if (!allowCmds.includes(c.value)) {\n            denyCmds.push(c.value);\n          }\n        });\n        window.teduBoard.enablePermissionChecker(allowCmds, [\n          'operator/' + window.teduBoard.identifier,\n        ]);\n        window.teduBoard.enablePermissionChecker(denyCmds, ['operator/']);\n      } else {\n        const totalCmds = this.authorityCmd[index].map((c) => c.value);\n        window.teduBoard.disablePermissionChecker(totalCmds);\n      }\n    },\n\n    onAuthorityCmdChange(index, cmdList) {\n      const enable = this.authorityStatus[index].value;\n      if (enable) {\n        const allowCmds = cmdList;\n        const denyCmds = [];\n        const totalCmds = this.authorityCmd[index];\n        totalCmds.forEach((c) => {\n          if (!allowCmds.includes(c.value)) {\n            denyCmds.push(c.value);\n          }\n        });\n        window.teduBoard.enablePermissionChecker(allowCmds, [\n          'operator/' + window.teduBoard.identifier,\n        ]);\n        window.teduBoard.enablePermissionChecker(denyCmds, ['operator/']);\n      }\n    },\n\n    updateSetting() {\n      this.$emit('updateSetting', this.settingItems);\n    },\n\n    setDisablePointerEventResponding() {\n      window.teduBoard.disablePointerEventResponding(\n        this.settingItems[0].disablePointerEventResponding\n      );\n    },\n  },\n};\n</script>\n"], "mappings": ";;;;AAoKA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA,GACA;QACAC,UAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,6BAAA;QACAC,sBAAA;QACAC,iBAAA;MACA,GACA;QACAC,QAAA;UACAC,UAAA;UACAC,KAAA;UACAC,MAAA;UACAC,OAAA;QACA;MACA,EACA;MACAC,YAAA,GACA,CACA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,EACA,EACA,CACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,EACA,EACA,CACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,EACA,EACA,CACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,EACA,EACA,CACA;QACAD,IAAA;QACAC,KAAA;MACA,EACA,CACA;MAEAC,YAAA,GACA,CACA,6BACA,6BACA,4BACA,EACA,CACA,gBACA,mBACA,kBACA,mBACA,kBACA,EACA,CACA,iBACA,mBACA,oBACA,mBACA,uBACA,uBACA,iCACA,wBACA,kBACA,iBACA,EACA,CACA,mBACA,sBACA,sBACA,sBACA,oBACA,sBACA,oBACA,EACA,uBACA;MAEAC,eAAA,GACA;QACAH,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA,GACA;QACAD,IAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAG,KAAA;IACAnB,YAAA;MACAoB,OAAA,WAAAA,QAAA;QACA,KAAAC,aAAA;MACA;MACAC,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,wBAAAC,CAAA;MACA,IAAAC,UAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,IAAAN,KAAA,CAAAzB,YAAA,IAAAO,sBAAA;QACA,IAAAyB,KAAA,GACAJ,CAAA,CAAAI,KAAA,GAAAH,UAAA,CAAAI,SAAA,GAAAJ,UAAA,CAAAK,YAAA;QACAR,MAAA,CAAAS,SAAA,CAAAC,iBAAA;UACAC,CAAA,EAAAT,CAAA,CAAAU,KAAA;UACAC,CAAA,EAAAP;QACA;MACA;IACA;EACA;EACAQ,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAA5C,UAAA;IACA;IACA6C,aAAA,WAAAA,cAAA;MACAhB,MAAA,CAAAS,SAAA,CAAAO,aAAA,MAAA1C,YAAA,IAAAC,UAAA;IACA;IACA0C,oBAAA,WAAAA,qBAAA;MACAjB,MAAA,CAAAS,SAAA,CAAAQ,oBAAA,CACA,KAAA3C,YAAA,IAAAE,iBACA;IACA;IAEA0C,iBAAA,WAAAA,kBAAA;MACAlB,MAAA,CAAAS,SAAA,CAAAS,iBAAA,MAAA5C,YAAA,IAAAI,cAAA;IACA;IAEAyC,sBAAA,WAAAA,uBAAA;MACAnB,MAAA,CAAAS,SAAA,CAAAU,sBAAA,CACA,KAAA7C,YAAA,IAAAG,gBACA;IACA;IAEA2C,mBAAA,WAAAA,oBAAA;MACA,IAAAC,MAAA,QAAA/C,YAAA,IAAAK,gBAAA;MACAqB,MAAA,CAAAS,SAAA,CAAA9B,gBAAA,CAAA0C,MAAA;MACArB,MAAA,CAAAS,SAAA,CAAAa,UAAA,CAAAD,MAAA;IACA;IAEA;IACAE,oBAAA,WAAAA,qBAAA;MACAvB,MAAA,CAAAS,SAAA,CAAAc,oBAAA;QACAxC,QAAA,OAAAT,YAAA,IAAAS;MACA;IACA;IAEAyC,iBAAA,WAAAA,kBAAAC,KAAA,EAAAJ,MAAA;MACA,IAAAA,MAAA;QACA,IAAAK,SAAA,QAAAnC,YAAA,CAAAkC,KAAA;QACA,IAAAE,QAAA;QACA,IAAAC,SAAA,QAAAxC,YAAA,CAAAqC,KAAA;QACAI,wBAAA,CAAAD,SAAA,EAAAE,IAAA,CAAAF,SAAA,YAAAG,CAAA;UACA,KAAAC,yBAAA,CAAAN,SAAA,EAAAI,IAAA,CAAAJ,SAAA,EAAAK,CAAA,CAAAzC,KAAA;YACAqC,QAAA,CAAAM,IAAA,CAAAF,CAAA,CAAAzC,KAAA;UACA;QACA;QACAU,MAAA,CAAAS,SAAA,CAAAyB,uBAAA,CAAAR,SAAA,GACA,cAAA1B,MAAA,CAAAS,SAAA,CAAA0B,UAAA,CACA;QACAnC,MAAA,CAAAS,SAAA,CAAAyB,uBAAA,CAAAP,QAAA;MACA;QAAA,IAAAS,QAAA;QACA,IAAAR,UAAA,GAAAS,oBAAA,CAAAD,QAAA,QAAAhD,YAAA,CAAAqC,KAAA,GAAAK,IAAA,CAAAM,QAAA,YAAAL,CAAA;UAAA,OAAAA,CAAA,CAAAzC,KAAA;QAAA;QACAU,MAAA,CAAAS,SAAA,CAAA6B,wBAAA,CAAAV,UAAA;MACA;IACA;IAEAW,oBAAA,WAAAA,qBAAAd,KAAA,EAAAe,OAAA;MACA,IAAAnB,MAAA,QAAA7B,eAAA,CAAAiC,KAAA,EAAAnC,KAAA;MACA,IAAA+B,MAAA;QACA,IAAAK,SAAA,GAAAc,OAAA;QACA,IAAAb,QAAA;QACA,IAAAC,SAAA,QAAAxC,YAAA,CAAAqC,KAAA;QACAI,wBAAA,CAAAD,SAAA,EAAAE,IAAA,CAAAF,SAAA,YAAAG,CAAA;UACA,KAAAC,yBAAA,CAAAN,SAAA,EAAAI,IAAA,CAAAJ,SAAA,EAAAK,CAAA,CAAAzC,KAAA;YACAqC,QAAA,CAAAM,IAAA,CAAAF,CAAA,CAAAzC,KAAA;UACA;QACA;QACAU,MAAA,CAAAS,SAAA,CAAAyB,uBAAA,CAAAR,SAAA,GACA,cAAA1B,MAAA,CAAAS,SAAA,CAAA0B,UAAA,CACA;QACAnC,MAAA,CAAAS,SAAA,CAAAyB,uBAAA,CAAAP,QAAA;MACA;IACA;IAEAhC,aAAA,WAAAA,cAAA;MACA,KAAA8C,KAAA,uBAAAnE,YAAA;IACA;IAEAoE,gCAAA,WAAAA,iCAAA;MACA1C,MAAA,CAAAS,SAAA,CAAA7B,6BAAA,CACA,KAAAN,YAAA,IAAAM,6BACA;IACA;EACA;AACA", "ignoreList": []}]}