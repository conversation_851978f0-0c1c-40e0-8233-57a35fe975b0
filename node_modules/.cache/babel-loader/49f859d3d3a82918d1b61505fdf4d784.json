{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAlert/VAlert.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAlert/VAlert.js", "mtime": 1757335237598}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF9pbmNsdWRlc0luc3RhbmNlUHJvcGVydHkgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS9pbmNsdWRlcyI7Ci8vIFN0eWxlcwppbXBvcnQgIi4uLy4uLy4uL3NyYy9jb21wb25lbnRzL1ZBbGVydC9WQWxlcnQuc2FzcyI7IC8vIEV4dGVuc2lvbnMKCmltcG9ydCBWU2hlZXQgZnJvbSAnLi4vVlNoZWV0JzsgLy8gQ29tcG9uZW50cwoKaW1wb3J0IFZCdG4gZnJvbSAnLi4vVkJ0bic7CmltcG9ydCBWSWNvbiBmcm9tICcuLi9WSWNvbic7IC8vIE1peGlucwoKaW1wb3J0IFRvZ2dsZWFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL3RvZ2dsZWFibGUnOwppbXBvcnQgVGhlbWVhYmxlIGZyb20gJy4uLy4uL21peGlucy90aGVtZWFibGUnOwppbXBvcnQgVHJhbnNpdGlvbmFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL3RyYW5zaXRpb25hYmxlJzsgLy8gVXRpbGl0aWVzCgppbXBvcnQgbWl4aW5zIGZyb20gJy4uLy4uL3V0aWwvbWl4aW5zJzsKaW1wb3J0IHsgYnJlYWtpbmcgfSBmcm9tICcuLi8uLi91dGlsL2NvbnNvbGUnOwovKiBAdnVlL2NvbXBvbmVudCAqLwoKZXhwb3J0IGRlZmF1bHQgbWl4aW5zKFZTaGVldCwgVG9nZ2xlYWJsZSwgVHJhbnNpdGlvbmFibGUpLmV4dGVuZCh7CiAgbmFtZTogJ3YtYWxlcnQnLAogIHByb3BzOiB7CiAgICBib3JkZXI6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICB2YWxpZGF0b3I6IGZ1bmN0aW9uIHZhbGlkYXRvcih2YWwpIHsKICAgICAgICB2YXIgX2NvbnRleHQ7CiAgICAgICAgcmV0dXJuIF9pbmNsdWRlc0luc3RhbmNlUHJvcGVydHkoX2NvbnRleHQgPSBbJ3RvcCcsICdyaWdodCcsICdib3R0b20nLCAnbGVmdCddKS5jYWxsKF9jb250ZXh0LCB2YWwpOwogICAgICB9CiAgICB9LAogICAgY2xvc2VMYWJlbDogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICJkZWZhdWx0IjogJyR2dWV0aWZ5LmNsb3NlJwogICAgfSwKICAgIGNvbG9yZWRCb3JkZXI6IEJvb2xlYW4sCiAgICBkZW5zZTogQm9vbGVhbiwKICAgIGRpc21pc3NpYmxlOiBCb29sZWFuLAogICAgY2xvc2VJY29uOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgImRlZmF1bHQiOiAnJGNhbmNlbCcKICAgIH0sCiAgICBpY29uOiB7CiAgICAgICJkZWZhdWx0IjogJycsCiAgICAgIHR5cGU6IFtCb29sZWFuLCBTdHJpbmddLAogICAgICB2YWxpZGF0b3I6IGZ1bmN0aW9uIHZhbGlkYXRvcih2YWwpIHsKICAgICAgICByZXR1cm4gdHlwZW9mIHZhbCA9PT0gJ3N0cmluZycgfHwgdmFsID09PSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIG91dGxpbmVkOiBCb29sZWFuLAogICAgcHJvbWluZW50OiBCb29sZWFuLAogICAgdGV4dDogQm9vbGVhbiwKICAgIHR5cGU6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICB2YWxpZGF0b3I6IGZ1bmN0aW9uIHZhbGlkYXRvcih2YWwpIHsKICAgICAgICB2YXIgX2NvbnRleHQyOwogICAgICAgIHJldHVybiBfaW5jbHVkZXNJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0MiA9IFsnaW5mbycsICdlcnJvcicsICdzdWNjZXNzJywgJ3dhcm5pbmcnXSkuY2FsbChfY29udGV4dDIsIHZhbCk7CiAgICAgIH0KICAgIH0sCiAgICB2YWx1ZTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICAiZGVmYXVsdCI6IHRydWUKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBfX2NhY2hlZEJvcmRlcjogZnVuY3Rpb24gX19jYWNoZWRCb3JkZXIoKSB7CiAgICAgIGlmICghdGhpcy5ib3JkZXIpIHJldHVybiBudWxsOwogICAgICB2YXIgZGF0YSA9IHsKICAgICAgICBzdGF0aWNDbGFzczogJ3YtYWxlcnRfX2JvcmRlcicsCiAgICAgICAgImNsYXNzIjogX2RlZmluZVByb3BlcnR5KHt9LCAidi1hbGVydF9fYm9yZGVyLS0iLmNvbmNhdCh0aGlzLmJvcmRlciksIHRydWUpCiAgICAgIH07CiAgICAgIGlmICh0aGlzLmNvbG9yZWRCb3JkZXIpIHsKICAgICAgICBkYXRhID0gdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb21wdXRlZENvbG9yLCBkYXRhKTsKICAgICAgICBkYXRhWyJjbGFzcyJdWyd2LWFsZXJ0X19ib3JkZXItLWhhcy1jb2xvciddID0gdHJ1ZTsKICAgICAgfQogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgZGF0YSk7CiAgICB9LAogICAgX19jYWNoZWREaXNtaXNzaWJsZTogZnVuY3Rpb24gX19jYWNoZWREaXNtaXNzaWJsZSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgaWYgKCF0aGlzLmRpc21pc3NpYmxlKSByZXR1cm4gbnVsbDsKICAgICAgdmFyIGNvbG9yID0gdGhpcy5pY29uQ29sb3I7CiAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KFZCdG4sIHsKICAgICAgICBzdGF0aWNDbGFzczogJ3YtYWxlcnRfX2Rpc21pc3NpYmxlJywKICAgICAgICBwcm9wczogewogICAgICAgICAgY29sb3I6IGNvbG9yLAogICAgICAgICAgaWNvbjogdHJ1ZSwKICAgICAgICAgIHNtYWxsOiB0cnVlCiAgICAgICAgfSwKICAgICAgICBhdHRyczogewogICAgICAgICAgJ2FyaWEtbGFiZWwnOiB0aGlzLiR2dWV0aWZ5LmxhbmcudCh0aGlzLmNsb3NlTGFiZWwpCiAgICAgICAgfSwKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuaXNBY3RpdmUgPSBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIFt0aGlzLiRjcmVhdGVFbGVtZW50KFZJY29uLCB7CiAgICAgICAgcHJvcHM6IHsKICAgICAgICAgIGNvbG9yOiBjb2xvcgogICAgICAgIH0KICAgICAgfSwgdGhpcy5jbG9zZUljb24pXSk7CiAgICB9LAogICAgX19jYWNoZWRJY29uOiBmdW5jdGlvbiBfX2NhY2hlZEljb24oKSB7CiAgICAgIGlmICghdGhpcy5jb21wdXRlZEljb24pIHJldHVybiBudWxsOwogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudChWSWNvbiwgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1hbGVydF9faWNvbicsCiAgICAgICAgcHJvcHM6IHsKICAgICAgICAgIGNvbG9yOiB0aGlzLmljb25Db2xvcgogICAgICAgIH0KICAgICAgfSwgdGhpcy5jb21wdXRlZEljb24pOwogICAgfSwKICAgIGNsYXNzZXM6IGZ1bmN0aW9uIGNsYXNzZXMoKSB7CiAgICAgIHZhciBjbGFzc2VzID0gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBWU2hlZXQub3B0aW9ucy5jb21wdXRlZC5jbGFzc2VzLmNhbGwodGhpcykpLCB7fSwgewogICAgICAgICd2LWFsZXJ0LS1ib3JkZXInOiBCb29sZWFuKHRoaXMuYm9yZGVyKSwKICAgICAgICAndi1hbGVydC0tZGVuc2UnOiB0aGlzLmRlbnNlLAogICAgICAgICd2LWFsZXJ0LS1vdXRsaW5lZCc6IHRoaXMub3V0bGluZWQsCiAgICAgICAgJ3YtYWxlcnQtLXByb21pbmVudCc6IHRoaXMucHJvbWluZW50LAogICAgICAgICd2LWFsZXJ0LS10ZXh0JzogdGhpcy50ZXh0CiAgICAgIH0pOwogICAgICBpZiAodGhpcy5ib3JkZXIpIHsKICAgICAgICBjbGFzc2VzWyJ2LWFsZXJ0LS1ib3JkZXItIi5jb25jYXQodGhpcy5ib3JkZXIpXSA9IHRydWU7CiAgICAgIH0KICAgICAgcmV0dXJuIGNsYXNzZXM7CiAgICB9LAogICAgY29tcHV0ZWRDb2xvcjogZnVuY3Rpb24gY29tcHV0ZWRDb2xvcigpIHsKICAgICAgcmV0dXJuIHRoaXMuY29sb3IgfHwgdGhpcy50eXBlOwogICAgfSwKICAgIGNvbXB1dGVkSWNvbjogZnVuY3Rpb24gY29tcHV0ZWRJY29uKCkgewogICAgICB2YXIgX2NvbnRleHQzOwogICAgICBpZiAodGhpcy5pY29uID09PSBmYWxzZSkgcmV0dXJuIGZhbHNlOwogICAgICBpZiAodHlwZW9mIHRoaXMuaWNvbiA9PT0gJ3N0cmluZycgJiYgdGhpcy5pY29uKSByZXR1cm4gdGhpcy5pY29uOwogICAgICBpZiAoIV9pbmNsdWRlc0luc3RhbmNlUHJvcGVydHkoX2NvbnRleHQzID0gWydlcnJvcicsICdpbmZvJywgJ3N1Y2Nlc3MnLCAnd2FybmluZyddKS5jYWxsKF9jb250ZXh0MywgdGhpcy50eXBlKSkgcmV0dXJuIGZhbHNlOwogICAgICByZXR1cm4gIiQiLmNvbmNhdCh0aGlzLnR5cGUpOwogICAgfSwKICAgIGhhc0NvbG9yZWRJY29uOiBmdW5jdGlvbiBoYXNDb2xvcmVkSWNvbigpIHsKICAgICAgcmV0dXJuIHRoaXMuaGFzVGV4dCB8fCBCb29sZWFuKHRoaXMuYm9yZGVyKSAmJiB0aGlzLmNvbG9yZWRCb3JkZXI7CiAgICB9LAogICAgaGFzVGV4dDogZnVuY3Rpb24gaGFzVGV4dCgpIHsKICAgICAgcmV0dXJuIHRoaXMudGV4dCB8fCB0aGlzLm91dGxpbmVkOwogICAgfSwKICAgIGljb25Db2xvcjogZnVuY3Rpb24gaWNvbkNvbG9yKCkgewogICAgICByZXR1cm4gdGhpcy5oYXNDb2xvcmVkSWNvbiA/IHRoaXMuY29tcHV0ZWRDb2xvciA6IHVuZGVmaW5lZDsKICAgIH0sCiAgICBpc0Rhcms6IGZ1bmN0aW9uIGlzRGFyaygpIHsKICAgICAgaWYgKHRoaXMudHlwZSAmJiAhdGhpcy5jb2xvcmVkQm9yZGVyICYmICF0aGlzLm91dGxpbmVkKSByZXR1cm4gdHJ1ZTsKICAgICAgcmV0dXJuIFRoZW1lYWJsZS5vcHRpb25zLmNvbXB1dGVkLmlzRGFyay5jYWxsKHRoaXMpOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovCiAgICBpZiAodGhpcy4kYXR0cnMuaGFzT3duUHJvcGVydHkoJ291dGxpbmUnKSkgewogICAgICBicmVha2luZygnb3V0bGluZScsICdvdXRsaW5lZCcsIHRoaXMpOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2VuV3JhcHBlcjogZnVuY3Rpb24gZ2VuV3JhcHBlcigpIHsKICAgICAgdmFyIGNoaWxkcmVuID0gW3RoaXMuJHNsb3RzLnByZXBlbmQgfHwgdGhpcy5fX2NhY2hlZEljb24sIHRoaXMuZ2VuQ29udGVudCgpLCB0aGlzLl9fY2FjaGVkQm9yZGVyLCB0aGlzLiRzbG90cy5hcHBlbmQsIHRoaXMuJHNjb3BlZFNsb3RzLmNsb3NlID8gdGhpcy4kc2NvcGVkU2xvdHMuY2xvc2UoewogICAgICAgIHRvZ2dsZTogdGhpcy50b2dnbGUKICAgICAgfSkgOiB0aGlzLl9fY2FjaGVkRGlzbWlzc2libGVdOwogICAgICB2YXIgZGF0YSA9IHsKICAgICAgICBzdGF0aWNDbGFzczogJ3YtYWxlcnRfX3dyYXBwZXInCiAgICAgIH07CiAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KCdkaXYnLCBkYXRhLCBjaGlsZHJlbik7CiAgICB9LAogICAgZ2VuQ29udGVudDogZnVuY3Rpb24gZ2VuQ29udGVudCgpIHsKICAgICAgcmV0dXJuIHRoaXMuJGNyZWF0ZUVsZW1lbnQoJ2RpdicsIHsKICAgICAgICBzdGF0aWNDbGFzczogJ3YtYWxlcnRfX2NvbnRlbnQnCiAgICAgIH0sIHRoaXMuJHNsb3RzWyJkZWZhdWx0Il0pOwogICAgfSwKICAgIGdlbkFsZXJ0OiBmdW5jdGlvbiBnZW5BbGVydCgpIHsKICAgICAgdmFyIGRhdGEgPSB7CiAgICAgICAgc3RhdGljQ2xhc3M6ICd2LWFsZXJ0JywKICAgICAgICBhdHRyczogewogICAgICAgICAgcm9sZTogJ2FsZXJ0JwogICAgICAgIH0sCiAgICAgICAgb246IHRoaXMubGlzdGVuZXJzJCwKICAgICAgICAiY2xhc3MiOiB0aGlzLmNsYXNzZXMsCiAgICAgICAgc3R5bGU6IHRoaXMuc3R5bGVzLAogICAgICAgIGRpcmVjdGl2ZXM6IFt7CiAgICAgICAgICBuYW1lOiAnc2hvdycsCiAgICAgICAgICB2YWx1ZTogdGhpcy5pc0FjdGl2ZQogICAgICAgIH1dCiAgICAgIH07CiAgICAgIGlmICghdGhpcy5jb2xvcmVkQm9yZGVyKSB7CiAgICAgICAgdmFyIHNldENvbG9yID0gdGhpcy5oYXNUZXh0ID8gdGhpcy5zZXRUZXh0Q29sb3IgOiB0aGlzLnNldEJhY2tncm91bmRDb2xvcjsKICAgICAgICBkYXRhID0gc2V0Q29sb3IodGhpcy5jb21wdXRlZENvbG9yLCBkYXRhKTsKICAgICAgfQogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgZGF0YSwgW3RoaXMuZ2VuV3JhcHBlcigpXSk7CiAgICB9LAogICAgLyoqIEBwdWJsaWMgKi90b2dnbGU6IGZ1bmN0aW9uIHRvZ2dsZSgpIHsKICAgICAgdGhpcy5pc0FjdGl2ZSA9ICF0aGlzLmlzQWN0aXZlOwogICAgfQogIH0sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCkgewogICAgdmFyIHJlbmRlciA9IHRoaXMuZ2VuQWxlcnQoKTsKICAgIGlmICghdGhpcy50cmFuc2l0aW9uKSByZXR1cm4gcmVuZGVyOwogICAgcmV0dXJuIGgoJ3RyYW5zaXRpb24nLCB7CiAgICAgIHByb3BzOiB7CiAgICAgICAgbmFtZTogdGhpcy50cmFuc2l0aW9uLAogICAgICAgIG9yaWdpbjogdGhpcy5vcmlnaW4sCiAgICAgICAgbW9kZTogdGhpcy5tb2RlCiAgICAgIH0KICAgIH0sIFtyZW5kZXJdKTsKICB9Cn0pOw=="}, {"version": 3, "names": ["VSheet", "VBtn", "VIcon", "Toggleable", "Themeable", "Transitionable", "mixins", "breaking", "extend", "name", "props", "border", "type", "String", "validator", "val", "_context", "_includesInstanceProperty", "call", "<PERSON><PERSON><PERSON><PERSON>", "coloredBorder", "Boolean", "dense", "dismissible", "closeIcon", "icon", "outlined", "prominent", "text", "_context2", "value", "computed", "__cachedBorder", "data", "staticClass", "_defineProperty", "concat", "setBackgroundColor", "computedColor", "$createElement", "__cachedDismissible", "_this", "color", "iconColor", "small", "attrs", "$vuetify", "lang", "t", "on", "click", "isActive", "__cachedIcon", "computedIcon", "classes", "_objectSpread", "options", "_context3", "hasColoredIcon", "hasText", "undefined", "isDark", "created", "$attrs", "hasOwnProperty", "methods", "genWrapper", "children", "$slots", "prepend", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "append", "$scopedSlots", "close", "toggle", "gen<PERSON><PERSON><PERSON>", "role", "listeners$", "style", "styles", "directives", "setColor", "setTextColor", "render", "h", "transition", "origin", "mode"], "sources": ["../../../src/components/VAlert/VAlert.ts"], "sourcesContent": ["// Styles\nimport './VAlert.sass'\n\n// Extensions\nimport VSheet from '../VSheet'\n\n// Components\nimport VBtn from '../VBtn'\nimport VIcon from '../VIcon'\n\n// Mixins\nimport Toggleable from '../../mixins/toggleable'\nimport Themeable from '../../mixins/themeable'\nimport Transitionable from '../../mixins/transitionable'\n\n// Utilities\nimport mixins from '../../util/mixins'\nimport { breaking } from '../../util/console'\n\n// Types\nimport { VNodeData } from 'vue'\nimport { VNode } from 'vue/types'\n\n/* @vue/component */\nexport default mixins(\n  VSheet,\n  Toggleable,\n  Transitionable\n).extend({\n  name: 'v-alert',\n\n  props: {\n    border: {\n      type: String,\n      validator (val: string) {\n        return [\n          'top',\n          'right',\n          'bottom',\n          'left',\n        ].includes(val)\n      },\n    },\n    closeLabel: {\n      type: String,\n      default: '$vuetify.close',\n    },\n    coloredBorder: Boolean,\n    dense: Boolean,\n    dismissible: <PERSON>olean,\n    closeIcon: {\n      type: String,\n      default: '$cancel',\n    },\n    icon: {\n      default: '',\n      type: [Boolean, String],\n      validator (val: boolean | string) {\n        return typeof val === 'string' || val === false\n      },\n    },\n    outlined: Boolean,\n    prominent: Boolean,\n    text: Boolean,\n    type: {\n      type: String,\n      validator (val: string) {\n        return [\n          'info',\n          'error',\n          'success',\n          'warning',\n        ].includes(val)\n      },\n    },\n    value: {\n      type: Boolean,\n      default: true,\n    },\n  },\n\n  computed: {\n    __cachedBorder (): VNode | null {\n      if (!this.border) return null\n\n      let data: VNodeData = {\n        staticClass: 'v-alert__border',\n        class: {\n          [`v-alert__border--${this.border}`]: true,\n        },\n      }\n\n      if (this.coloredBorder) {\n        data = this.setBackgroundColor(this.computedColor, data)\n        data.class['v-alert__border--has-color'] = true\n      }\n\n      return this.$createElement('div', data)\n    },\n    __cachedDismissible (): VNode | null {\n      if (!this.dismissible) return null\n\n      const color = this.iconColor\n\n      return this.$createElement(VBtn, {\n        staticClass: 'v-alert__dismissible',\n        props: {\n          color,\n          icon: true,\n          small: true,\n        },\n        attrs: {\n          'aria-label': this.$vuetify.lang.t(this.closeLabel),\n        },\n        on: {\n          click: () => (this.isActive = false),\n        },\n      }, [\n        this.$createElement(VIcon, {\n          props: { color },\n        }, this.closeIcon),\n      ])\n    },\n    __cachedIcon (): VNode | null {\n      if (!this.computedIcon) return null\n\n      return this.$createElement(VIcon, {\n        staticClass: 'v-alert__icon',\n        props: { color: this.iconColor },\n      }, this.computedIcon)\n    },\n    classes (): object {\n      const classes: Record<string, boolean> = {\n        ...VSheet.options.computed.classes.call(this),\n        'v-alert--border': Boolean(this.border),\n        'v-alert--dense': this.dense,\n        'v-alert--outlined': this.outlined,\n        'v-alert--prominent': this.prominent,\n        'v-alert--text': this.text,\n      }\n\n      if (this.border) {\n        classes[`v-alert--border-${this.border}`] = true\n      }\n\n      return classes\n    },\n    computedColor (): string {\n      return this.color || this.type\n    },\n    computedIcon (): string | boolean {\n      if (this.icon === false) return false\n      if (typeof this.icon === 'string' && this.icon) return this.icon\n      if (!['error', 'info', 'success', 'warning'].includes(this.type)) return false\n\n      return `$${this.type}`\n    },\n    hasColoredIcon (): boolean {\n      return (\n        this.hasText ||\n        (Boolean(this.border) && this.coloredBorder)\n      )\n    },\n    hasText (): boolean {\n      return this.text || this.outlined\n    },\n    iconColor (): string | undefined {\n      return this.hasColoredIcon ? this.computedColor : undefined\n    },\n    isDark (): boolean {\n      if (\n        this.type &&\n        !this.coloredBorder &&\n        !this.outlined\n      ) return true\n\n      return Themeable.options.computed.isDark.call(this)\n    },\n  },\n\n  created () {\n    /* istanbul ignore next */\n    if (this.$attrs.hasOwnProperty('outline')) {\n      breaking('outline', 'outlined', this)\n    }\n  },\n\n  methods: {\n    genWrapper (): VNode {\n      const children = [\n        this.$slots.prepend || this.__cachedIcon,\n        this.genContent(),\n        this.__cachedBorder,\n        this.$slots.append,\n        this.$scopedSlots.close\n          ? this.$scopedSlots.close({ toggle: this.toggle })\n          : this.__cachedDismissible,\n      ]\n\n      const data: VNodeData = {\n        staticClass: 'v-alert__wrapper',\n      }\n\n      return this.$createElement('div', data, children)\n    },\n    genContent (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-alert__content',\n      }, this.$slots.default)\n    },\n    genAlert (): VNode {\n      let data: VNodeData = {\n        staticClass: 'v-alert',\n        attrs: {\n          role: 'alert',\n        },\n        on: this.listeners$,\n        class: this.classes,\n        style: this.styles,\n        directives: [{\n          name: 'show',\n          value: this.isActive,\n        }],\n      }\n\n      if (!this.coloredBorder) {\n        const setColor = this.hasText ? this.setTextColor : this.setBackgroundColor\n        data = setColor(this.computedColor, data)\n      }\n\n      return this.$createElement('div', data, [this.genWrapper()])\n    },\n    /** @public */\n    toggle () {\n      this.isActive = !this.isActive\n    },\n  },\n\n  render (h): VNode {\n    const render = this.genAlert()\n\n    if (!this.transition) return render\n\n    return h('transition', {\n      props: {\n        name: this.transition,\n        origin: this.origin,\n        mode: this.mode,\n      },\n    }, [render])\n  },\n})\n"], "mappings": ";;;AAAA;AACA,OAAO,4CAAP,C,CAEA;;AACA,OAAOA,MAAP,MAAmB,WAAnB,C,CAEA;;AACA,OAAOC,IAAP,MAAiB,SAAjB;AACA,OAAOC,KAAP,MAAkB,UAAlB,C,CAEA;;AACA,OAAOC,UAAP,MAAuB,yBAAvB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,cAAP,MAA2B,6BAA3B,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,QAAT,QAAyB,oBAAzB;AAMA;;AACA,eAAeD,MAAM,CACnBN,MADmB,EAEnBG,UAFmB,EAGnBE,cAHmB,CAAN,CAIbG,MAJa,CAIN;EACPC,IAAI,EAAE,SADC;EAGPC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,MADA;MAENC,SAAS,WAATA,SAASA,CAAEC,GAAF,EAAa;QAAA,IAAAC,QAAA;QACpB,OAAOC,yBAAA,CAAAD,QAAA,IACL,KADK,EAEL,OAFK,EAGL,QAHK,EAIL,MAJK,GAAAE,IAAA,CAAAF,QAAA,EAKID,GALJ,CAAP;MAMD;IATK,CADH;IAYLI,UAAU,EAAE;MACVP,IAAI,EAAEC,MADI;MAEV,WAAS;IAFC,CAZP;IAgBLO,aAAa,EAAEC,OAhBV;IAiBLC,KAAK,EAAED,OAjBF;IAkBLE,WAAW,EAAEF,OAlBR;IAmBLG,SAAS,EAAE;MACTZ,IAAI,EAAEC,MADG;MAET,WAAS;IAFA,CAnBN;IAuBLY,IAAI,EAAE;MACJ,WAAS,EADL;MAEJb,IAAI,EAAE,CAACS,OAAD,EAAUR,MAAV,CAFF;MAGJC,SAAS,WAATA,SAASA,CAAEC,GAAF,EAAuB;QAC9B,OAAO,OAAOA,GAAP,KAAe,QAAf,IAA2BA,GAAG,KAAK,KAA1C;MACD;IALG,CAvBD;IA8BLW,QAAQ,EAAEL,OA9BL;IA+BLM,SAAS,EAAEN,OA/BN;IAgCLO,IAAI,EAAEP,OAhCD;IAiCLT,IAAI,EAAE;MACJA,IAAI,EAAEC,MADF;MAEJC,SAAS,WAATA,SAASA,CAAEC,GAAF,EAAa;QAAA,IAAAc,SAAA;QACpB,OAAOZ,yBAAA,CAAAY,SAAA,IACL,MADK,EAEL,OAFK,EAGL,SAHK,EAIL,SAJK,GAAAX,IAAA,CAAAW,SAAA,EAKId,GALJ,CAAP;MAMD;IATG,CAjCD;IA4CLe,KAAK,EAAE;MACLlB,IAAI,EAAES,OADD;MAEL,WAAS;IAFJ;EA5CF,CAHA;EAqDPU,QAAQ,EAAE;IACRC,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAI,CAAC,KAAKrB,MAAV,EAAkB,OAAO,IAAP;MAElB,IAAIsB,IAAI,GAAc;QACpBC,WAAW,EAAE,iBADO;QAEpB,SAAAC,eAAA,yBAAAC,MAAA,CACuB,KAAKzB,MAAM,GAAK;MAHnB,CAAtB;MAOA,IAAI,KAAKS,aAAT,EAAwB;QACtBa,IAAI,GAAG,KAAKI,kBAAL,CAAwB,KAAKC,aAA7B,EAA4CL,IAA5C,CAAP;QACAA,IAAI,SAAJ,CAAW,4BAAX,IAA2C,IAA3C;MACD;MAED,OAAO,KAAKM,cAAL,CAAoB,KAApB,EAA2BN,IAA3B,CAAP;IACD,CAjBO;IAkBRO,mBAAmB,WAAnBA,mBAAmBA,CAAA;MAAA,IAAAC,KAAA;MACjB,IAAI,CAAC,KAAKlB,WAAV,EAAuB,OAAO,IAAP;MAEvB,IAAMmB,KAAK,GAAG,KAAKC,SAAnB;MAEA,OAAO,KAAKJ,cAAL,CAAoBtC,IAApB,EAA0B;QAC/BiC,WAAW,EAAE,sBADkB;QAE/BxB,KAAK,EAAE;UACLgC,KADK,EACLA,KADK;UAELjB,IAAI,EAAE,IAFD;UAGLmB,KAAK,EAAE;QAHF,CAFwB;QAO/BC,KAAK,EAAE;UACL,cAAc,KAAKC,QAAL,CAAcC,IAAd,CAAmBC,CAAnB,CAAqB,KAAK7B,UAA1B;QADT,CAPwB;QAU/B8B,EAAE,EAAE;UACFC,KAAK,EAAE,SAAPA,KAAKA,CAAA;YAAA,OAAST,KAAA,CAAKU,QAAL,GAAgB;UAAA;QAD5B;MAV2B,CAA1B,EAaJ,CACD,KAAKZ,cAAL,CAAoBrC,KAApB,EAA2B;QACzBQ,KAAK,EAAE;UAAEgC,KAAA,EAAAA;QAAF;MADkB,CAA3B,EAEG,KAAKlB,SAFR,CADC,CAbI,CAAP;IAkBD,CAzCO;IA0CR4B,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAI,CAAC,KAAKC,YAAV,EAAwB,OAAO,IAAP;MAExB,OAAO,KAAKd,cAAL,CAAoBrC,KAApB,EAA2B;QAChCgC,WAAW,EAAE,eADmB;QAEhCxB,KAAK,EAAE;UAAEgC,KAAK,EAAE,KAAKC;QAAd;MAFyB,CAA3B,EAGJ,KAAKU,YAHD,CAAP;IAID,CAjDO;IAkDRC,OAAO,WAAPA,OAAOA,CAAA;MACL,IAAMA,OAAO,GAAAC,aAAA,CAAAA,aAAA,KACRvD,MAAM,CAACwD,OAAP,CAAezB,QAAf,CAAwBuB,OAAxB,CAAgCpC,IAAhC,CAAqC,IAArC,CADoC;QAEvC,mBAAmBG,OAAO,CAAC,KAAKV,MAAN,CAFa;QAGvC,kBAAkB,KAAKW,KAHgB;QAIvC,qBAAqB,KAAKI,QAJa;QAKvC,sBAAsB,KAAKC,SALY;QAMvC,iBAAiB,KAAKC;MAAA,EANxB;MASA,IAAI,KAAKjB,MAAT,EAAiB;QACf2C,OAAO,oBAAAlB,MAAA,CAAoB,KAAKzB,MAAM,EAAtC,GAA4C,IAA5C;MACD;MAED,OAAO2C,OAAP;IACD,CAjEO;IAkERhB,aAAa,WAAbA,aAAaA,CAAA;MACX,OAAO,KAAKI,KAAL,IAAc,KAAK9B,IAA1B;IACD,CApEO;IAqERyC,YAAY,WAAZA,YAAYA,CAAA;MAAA,IAAAI,SAAA;MACV,IAAI,KAAKhC,IAAL,KAAc,KAAlB,EAAyB,OAAO,KAAP;MACzB,IAAI,OAAO,KAAKA,IAAZ,KAAqB,QAArB,IAAiC,KAAKA,IAA1C,EAAgD,OAAO,KAAKA,IAAZ;MAChD,IAAI,CAACR,yBAAA,CAAAwC,SAAA,IAAC,OAAD,EAAU,MAAV,EAAkB,SAAlB,EAA6B,SAA7B,GAAAvC,IAAA,CAAAuC,SAAA,EAAiD,KAAK7C,IAAtD,CAAL,EAAkE,OAAO,KAAP;MAElE,WAAAwB,MAAA,CAAW,KAAKxB,IAAI;IACrB,CA3EO;IA4ER8C,cAAc,WAAdA,cAAcA,CAAA;MACZ,OACE,KAAKC,OAAL,IACCtC,OAAO,CAAC,KAAKV,MAAN,CAAP,IAAwB,KAAKS,aAFhC;IAID,CAjFO;IAkFRuC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAK/B,IAAL,IAAa,KAAKF,QAAzB;IACD,CApFO;IAqFRiB,SAAS,WAATA,SAASA,CAAA;MACP,OAAO,KAAKe,cAAL,GAAsB,KAAKpB,aAA3B,GAA2CsB,SAAlD;IACD,CAvFO;IAwFRC,MAAM,WAANA,MAAMA,CAAA;MACJ,IACE,KAAKjD,IAAL,IACA,CAAC,KAAKQ,aADN,IAEA,CAAC,KAAKM,QAHR,EAIE,OAAO,IAAP;MAEF,OAAOtB,SAAS,CAACoD,OAAV,CAAkBzB,QAAlB,CAA2B8B,MAA3B,CAAkC3C,IAAlC,CAAuC,IAAvC,CAAP;IACD;EAhGO,CArDH;EAwJP4C,OAAO,WAAPA,OAAOA,CAAA;IACL;IACA,IAAI,KAAKC,MAAL,CAAYC,cAAZ,CAA2B,SAA3B,CAAJ,EAA2C;MACzCzD,QAAQ,CAAC,SAAD,EAAY,UAAZ,EAAwB,IAAxB,CAAR;IACD;EACF,CA7JM;EA+JP0D,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAMC,QAAQ,GAAG,CACf,KAAKC,MAAL,CAAYC,OAAZ,IAAuB,KAAKjB,YADb,EAEf,KAAKkB,UAAL,EAFe,EAGf,KAAKtC,cAHU,EAIf,KAAKoC,MAAL,CAAYG,MAJG,EAKf,KAAKC,YAAL,CAAkBC,KAAlB,GACI,KAAKD,YAAL,CAAkBC,KAAlB,CAAwB;QAAEC,MAAM,EAAE,KAAKA;MAAf,CAAxB,CADJ,GAEI,KAAKlC,mBAPM,CAAjB;MAUA,IAAMP,IAAI,GAAc;QACtBC,WAAW,EAAE;MADS,CAAxB;MAIA,OAAO,KAAKK,cAAL,CAAoB,KAApB,EAA2BN,IAA3B,EAAiCkC,QAAjC,CAAP;IACD,CAjBM;IAkBPG,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAK/B,cAAL,CAAoB,KAApB,EAA2B;QAChCL,WAAW,EAAE;MADmB,CAA3B,EAEJ,KAAKkC,MAAL,WAFI,CAAP;IAGD,CAtBM;IAuBPO,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAI1C,IAAI,GAAc;QACpBC,WAAW,EAAE,SADO;QAEpBW,KAAK,EAAE;UACL+B,IAAI,EAAE;QADD,CAFa;QAKpB3B,EAAE,EAAE,KAAK4B,UALW;QAMpB,SAAO,KAAKvB,OANQ;QAOpBwB,KAAK,EAAE,KAAKC,MAPQ;QAQpBC,UAAU,EAAE,CAAC;UACXvE,IAAI,EAAE,MADK;UAEXqB,KAAK,EAAE,KAAKqB;QAFD,CAAD;MARQ,CAAtB;MAcA,IAAI,CAAC,KAAK/B,aAAV,EAAyB;QACvB,IAAM6D,QAAQ,GAAG,KAAKtB,OAAL,GAAe,KAAKuB,YAApB,GAAmC,KAAK7C,kBAAzD;QACAJ,IAAI,GAAGgD,QAAQ,CAAC,KAAK3C,aAAN,EAAqBL,IAArB,CAAf;MACD;MAED,OAAO,KAAKM,cAAL,CAAoB,KAApB,EAA2BN,IAA3B,EAAiC,CAAC,KAAKiC,UAAL,EAAD,CAAjC,CAAP;IACD,CA5CM;IA6CP,cACAQ,MAAM,WAANA,MAAMA,CAAA;MACJ,KAAKvB,QAAL,GAAgB,CAAC,KAAKA,QAAtB;IACD;EAhDM,CA/JF;EAkNPgC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMD,MAAM,GAAG,KAAKR,QAAL,EAAf;IAEA,IAAI,CAAC,KAAKU,UAAV,EAAsB,OAAOF,MAAP;IAEtB,OAAOC,CAAC,CAAC,YAAD,EAAe;MACrB1E,KAAK,EAAE;QACLD,IAAI,EAAE,KAAK4E,UADN;QAELC,MAAM,EAAE,KAAKA,MAFR;QAGLC,IAAI,EAAE,KAAKA;MAHN;IADc,CAAf,EAML,CAACJ,MAAD,CANK,CAAR;EAOD;AA9NM,CAJM,CAAf", "ignoreList": []}]}