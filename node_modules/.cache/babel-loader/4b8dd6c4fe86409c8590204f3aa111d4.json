{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VResponsive/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VResponsive/index.js", "mtime": 1757335236936}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZSZXNwb25zaXZlIGZyb20gJy4vVlJlc3BvbnNpdmUnOwpleHBvcnQgeyBWUmVzcG9uc2l2ZSB9OwpleHBvcnQgZGVmYXVsdCBWUmVzcG9uc2l2ZTs="}, {"version": 3, "names": ["VResponsive"], "sources": ["../../../src/components/VResponsive/index.ts"], "sourcesContent": ["import VResponsive from './VResponsive'\n\nexport { VResponsive }\nexport default VResponsive\n"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,eAAxB;AAEA,SAASA,WAAT;AACA,eAAeA,WAAf", "ignoreList": []}]}