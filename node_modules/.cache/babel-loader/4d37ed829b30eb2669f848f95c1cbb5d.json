{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/grid.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/grid.js", "mtime": 1757335235847}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbi5qcyI7CmltcG9ydCBfdHJpbUluc3RhbmNlUHJvcGVydHkgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS90cmltIjsKaW1wb3J0IF9jb25jYXRJbnN0YW5jZVByb3BlcnR5IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvaW5zdGFuY2UvY29uY2F0IjsKaW1wb3J0IF9maWx0ZXJJbnN0YW5jZVByb3BlcnR5IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvaW5zdGFuY2UvZmlsdGVyIjsKaW1wb3J0IF9PYmplY3Qka2V5cyBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL29iamVjdC9rZXlzIjsKaW1wb3J0IF9zdGFydHNXaXRoSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL3N0YXJ0cy13aXRoIjsKLy8gVHlwZXMKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwpleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBWR3JpZChuYW1lKSB7CiAgLyogQHZ1ZS9jb21wb25lbnQgKi8KICByZXR1cm4gVnVlLmV4dGVuZCh7CiAgICBuYW1lOiAidi0iLmNvbmNhdChuYW1lKSwKICAgIGZ1bmN0aW9uYWw6IHRydWUsCiAgICBwcm9wczogewogICAgICBpZDogU3RyaW5nLAogICAgICB0YWc6IHsKICAgICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICAgImRlZmF1bHQiOiAnZGl2JwogICAgICB9CiAgICB9LAogICAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgX3JlZikgewogICAgICB2YXIgX2NvbnRleHQsIF9jb250ZXh0MjsKICAgICAgdmFyIHByb3BzID0gX3JlZi5wcm9wcywKICAgICAgICBkYXRhID0gX3JlZi5kYXRhLAogICAgICAgIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbjsKICAgICAgZGF0YS5zdGF0aWNDbGFzcyA9IF90cmltSW5zdGFuY2VQcm9wZXJ0eShfY29udGV4dCA9IF9jb25jYXRJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0MiA9ICIiLmNvbmNhdChuYW1lLCAiICIpKS5jYWxsKF9jb250ZXh0MiwgZGF0YS5zdGF0aWNDbGFzcyB8fCAnJykpLmNhbGwoX2NvbnRleHQpOwogICAgICB2YXIgYXR0cnMgPSBkYXRhLmF0dHJzOwogICAgICBpZiAoYXR0cnMpIHsKICAgICAgICB2YXIgX2NvbnRleHQzOwogICAgICAgIC8vIHJlc2V0IGF0dHJzIHRvIGV4dHJhY3QgdXRpbGl0eSBjbGFzZXMgbGlrZSBwYS0zCiAgICAgICAgZGF0YS5hdHRycyA9IHt9OwogICAgICAgIHZhciBjbGFzc2VzID0gX2ZpbHRlckluc3RhbmNlUHJvcGVydHkoX2NvbnRleHQzID0gX09iamVjdCRrZXlzKGF0dHJzKSkuY2FsbChfY29udGV4dDMsIGZ1bmN0aW9uIChrZXkpIHsKICAgICAgICAgIC8vIFRPRE86IFJlbW92ZSBvbmNlIHJlc29sdmVkCiAgICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vdnVlanMvdnVlL2lzc3Vlcy83ODQxCiAgICAgICAgICBpZiAoa2V5ID09PSAnc2xvdCcpIHJldHVybiBmYWxzZTsKICAgICAgICAgIHZhciB2YWx1ZSA9IGF0dHJzW2tleV07IC8vIGFkZCBiYWNrIGRhdGEgYXR0cmlidXRlcyBsaWtlIGRhdGEtdGVzdD0iZm9vIiBidXQgZG8gbm90CiAgICAgICAgICAvLyBhZGQgdGhlbSBhcyBjbGFzc2VzCgogICAgICAgICAgaWYgKF9zdGFydHNXaXRoSW5zdGFuY2VQcm9wZXJ0eShrZXkpLmNhbGwoa2V5LCAnZGF0YS0nKSkgewogICAgICAgICAgICBkYXRhLmF0dHJzW2tleV0gPSB2YWx1ZTsKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgICAgcmV0dXJuIHZhbHVlIHx8IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZyc7CiAgICAgICAgfSk7CiAgICAgICAgaWYgKGNsYXNzZXMubGVuZ3RoKSBkYXRhLnN0YXRpY0NsYXNzICs9ICIgIi5jb25jYXQoY2xhc3Nlcy5qb2luKCcgJykpOwogICAgICB9CiAgICAgIGlmIChwcm9wcy5pZCkgewogICAgICAgIGRhdGEuZG9tUHJvcHMgPSBkYXRhLmRvbVByb3BzIHx8IHt9OwogICAgICAgIGRhdGEuZG9tUHJvcHMuaWQgPSBwcm9wcy5pZDsKICAgICAgfQogICAgICByZXR1cm4gaChwcm9wcy50YWcsIGRhdGEsIGNoaWxkcmVuKTsKICAgIH0KICB9KTsKfQ=="}, {"version": 3, "names": ["<PERSON><PERSON>", "VGrid", "name", "extend", "concat", "functional", "props", "id", "String", "tag", "type", "render", "h", "_ref", "_context", "_context2", "data", "children", "staticClass", "_trimInstanceProperty", "_concatInstanceProperty", "call", "attrs", "_context3", "classes", "_filterInstanceProperty", "_Object$keys", "key", "value", "_startsWithInstanceProperty", "length", "join", "domProps"], "sources": ["../../../src/components/VGrid/grid.ts"], "sourcesContent": ["// Types\nimport Vue, { VNode } from 'vue'\n\nexport default function VGrid (name: string) {\n  /* @vue/component */\n  return Vue.extend({\n    name: `v-${name}`,\n\n    functional: true,\n\n    props: {\n      id: String,\n      tag: {\n        type: String,\n        default: 'div',\n      },\n    },\n\n    render (h, { props, data, children }): VNode {\n      data.staticClass = (`${name} ${data.staticClass || ''}`).trim()\n\n      const { attrs } = data\n      if (attrs) {\n        // reset attrs to extract utility clases like pa-3\n        data.attrs = {}\n        const classes = Object.keys(attrs).filter(key => {\n          // TODO: Remove once resolved\n          // https://github.com/vuejs/vue/issues/7841\n          if (key === 'slot') return false\n\n          const value = attrs[key]\n\n          // add back data attributes like data-test=\"foo\" but do not\n          // add them as classes\n          if (key.startsWith('data-')) {\n            data.attrs![key] = value\n            return false\n          }\n\n          return value || typeof value === 'string'\n        })\n\n        if (classes.length) data.staticClass += ` ${classes.join(' ')}`\n      }\n\n      if (props.id) {\n        data.domProps = data.domProps || {}\n        data.domProps.id = props.id\n      }\n\n      return h(props.tag, data, children)\n    },\n  })\n}\n"], "mappings": ";;;;;;AAAA;AACA,OAAOA,GAAP,MAA2B,KAA3B;AAEA,eAAc,SAAUC,KAAVA,CAAiBC,IAAjB,EAA6B;EACzC;EACA,OAAOF,GAAG,CAACG,MAAJ,CAAW;IAChBD,IAAI,OAAAE,MAAA,CAAOF,IAAI,CADC;IAGhBG,UAAU,EAAE,IAHI;IAKhBC,KAAK,EAAE;MACLC,EAAE,EAAEC,MADC;MAELC,GAAG,EAAE;QACHC,IAAI,EAAEF,MADH;QAEH,WAAS;MAFN;IAFA,CALS;IAahBG,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAAC,IAAA,EAA8B;MAAA,IAAAC,QAAA,EAAAC,SAAA;MAAA,IAAvBT,KAAF,GAAAO,IAAA,CAAEP,KAAF;QAASU,IAAT,GAAAH,IAAA,CAASG,IAAT;QAAeC,QAAA,GAAAJ,IAAA,CAAAI,QAAA;MACxBD,IAAI,CAACE,WAAL,GAAoBC,qBAAA,CAAAL,QAAA,GAAAM,uBAAA,CAAAL,SAAA,MAAAX,MAAA,CAAGF,IAAI,QAAAmB,IAAA,CAAAN,SAAA,EAAIC,IAAI,CAACE,WAAL,IAAoB,EAAE,GAAAG,IAAA,CAAAP,QAAlC,CAAnB;MAEA,IAAQQ,KAAA,GAAUN,IAAlB,CAAQM,KAAA;MACR,IAAIA,KAAJ,EAAW;QAAA,IAAAC,SAAA;QACT;QACAP,IAAI,CAACM,KAAL,GAAa,EAAb;QACA,IAAME,OAAO,GAAGC,uBAAA,CAAAF,SAAA,GAAAG,YAAA,CAAYJ,KAAZ,GAAAD,IAAA,CAAAE,SAAA,EAA0B,UAAAI,GAAG,EAAG;UAC9C;UACA;UACA,IAAIA,GAAG,KAAK,MAAZ,EAAoB,OAAO,KAAP;UAEpB,IAAMC,KAAK,GAAGN,KAAK,CAACK,GAAD,CAAnB,CAL8C,CAO9C;UACA;;UACA,IAAIE,2BAAA,CAAAF,GAAG,EAAAN,IAAA,CAAHM,GAAG,EAAY,OAAf,CAAJ,EAA6B;YAC3BX,IAAI,CAACM,KAAL,CAAYK,GAAZ,IAAmBC,KAAnB;YACA,OAAO,KAAP;UACD;UAED,OAAOA,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAAjC;QACD,CAfe,CAAhB;QAiBA,IAAIJ,OAAO,CAACM,MAAZ,EAAoBd,IAAI,CAACE,WAAL,QAAAd,MAAA,CAAwBoB,OAAO,CAACO,IAAR,CAAa,GAAb,CAAiB,CAAzC;MACrB;MAED,IAAIzB,KAAK,CAACC,EAAV,EAAc;QACZS,IAAI,CAACgB,QAAL,GAAgBhB,IAAI,CAACgB,QAAL,IAAiB,EAAjC;QACAhB,IAAI,CAACgB,QAAL,CAAczB,EAAd,GAAmBD,KAAK,CAACC,EAAzB;MACD;MAED,OAAOK,CAAC,CAACN,KAAK,CAACG,GAAP,EAAYO,IAAZ,EAAkBC,QAAlB,CAAR;IACD;EA9Ce,CAAX,CAAP;AAgDD", "ignoreList": []}]}