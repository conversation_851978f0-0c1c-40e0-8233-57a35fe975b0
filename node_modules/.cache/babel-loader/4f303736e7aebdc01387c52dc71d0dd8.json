{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sk.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sk.js", "mtime": 1757335237511}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/sk.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Neboli nájdené žiadne záznamy',\n    loadingText: 'Načítavam položky...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Počet riadkov na stránku:',\n    ariaLabel: {\n      sortDescending: 'Zoradené zostupne.',\n      sortAscending: 'Zoradené vzostupne.',\n      sortNone: 'Nezoradené.',\n      activateNone: 'Aktivujte na zrušenie triedenia.',\n      activateDescending: 'Aktivujte na zoradenie zostupne.',\n      activateAscending: 'Aktivujte na zoradenie vzostupne.',\n    },\n    sortBy: 'Zoradiť podľa',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Počet položiek na stránku:',\n    itemsPerPageAll: 'Všetko',\n    nextPage: 'Ďalšia stránka',\n    prevPage: 'Predch<PERSON>dza<PERSON><PERSON><PERSON> stránka',\n    firstPage: '<PERSON>rv<PERSON> stránka',\n    lastPage: 'Posledná stránka',\n    pageText: '{0}–{1} z {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} vybrané',\n    nextMonthAriaLabel: 'Ďalší mesiac',\n    nextYearAriaLabel: 'Ďalší rok',\n    prevMonthAriaLabel: 'Predchádzajúci mesiac',\n    prevYearAriaLabel: 'Predchádzajúci rok',\n  },\n  noDataText: 'Nie sú dostupné žiadne dáta',\n  carousel: {\n    prev: 'Predchádzajúci obrázok',\n    next: 'Další obrázok',\n    ariaLabel: {\n      delimiter: 'Snímka {0} z {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} ďalších',\n  },\n  fileInput: {\n    counter: '{0} súborov',\n    counterSize: '{0} súborov ({1} celkom)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigácia stránkovania',\n      next: 'Ďalšia stránka',\n      previous: 'Predchádzajúca stránka',\n      page: 'Ísť na stránku {0}',\n      currentPage: 'Aktuálna stránka, stránka {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Hodnotenie {0} z {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,+BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,2BADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,qBAFN;MAGTC,QAAQ,EAAE,aAHD;MAITC,YAAY,EAAE,kCAJL;MAKTC,kBAAkB,EAAE,kCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,4BADR;IAEVU,eAAe,EAAE,QAFP;IAGVC,QAAQ,EAAE,gBAHA;IAIVC,QAAQ,EAAE,wBAJA;IAKVC,SAAS,EAAE,cALD;IAMVC,QAAQ,EAAE,kBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,aADL;IAEVC,kBAAkB,EAAE,cAFV;IAGVC,iBAAiB,EAAE,WAHT;IAIVC,kBAAkB,EAAE,uBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,6BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,wBADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,wBADA;MAETX,IAAI,EAAE,gBAFG;MAGTY,QAAQ,EAAE,wBAHD;MAITC,IAAI,EAAE,oBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}