{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/fi.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/fi.js", "mtime": 1757335235784}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/fi.ts"], "sourcesContent": ["export default {\n  badge: 'Infopiste',\n  close: '<PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Ei osumia',\n    loadingText: '<PERSON><PERSON><PERSON> kohteita...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Rivejä sivulla:',\n    ariaLabel: {\n      sortDescending: ': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laskevasti. Poista järjestäminen aktivoimalla.',\n      sortAscending: ': Järjestetty nousevasti. Järjest<PERSON> laskevasti aktivoimalla.',\n      sortNone: ': <PERSON>i järjestetty. Järjestä nousevasti aktivoimalla.',\n      activateNone: 'Aktivoi lajittelun poistamiseksi.',\n      activateDescending: 'Aktivoi laskevien laskevien lajittelemiseksi.',\n      activateAscending: 'Aktivoi lajitella nouseva.',\n    },\n    sortBy: 'Järjestä',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Kohteita sivulla:',\n    itemsPerPageAll: '<PERSON>k<PERSON>',\n    nextPage: 'Se<PERSON><PERSON> sivu',\n    prevPage: 'Edellinen sivu',\n    firstPage: 'Ensimmäinen sivu',\n    lastPage: 'Viimeinen sivu',\n    pageText: '{0}-{1} ({2})',\n  },\n  datePicker: {\n    itemsSelected: '{0} valittu',\n    nextMonthAriaLabel: 'Seuraava kuukausi',\n    nextYearAriaLabel: 'Ensi vuosi',\n    prevMonthAriaLabel: 'Edellinen kuukausi',\n    prevYearAriaLabel: 'Edellinen vuosi',\n  },\n  noDataText: 'Ei dataa',\n  carousel: {\n    prev: 'Edellinen kuva',\n    next: 'Seuraava kuva',\n    ariaLabel: {\n      delimiter: 'Karusellin kuva {0}/{1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} lisää',\n  },\n  fileInput: {\n    counter: '{0} tiedostoa',\n    counterSize: '{0} tiedostoa ({1} yhteensä)',\n  },\n  timePicker: {\n    am: 'ap.',\n    pm: 'ip.',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Pagination Navigation',\n      next: 'Seuraava sivu',\n      previous: 'Edellinen sivu',\n      page: 'Mene sivulle {0}',\n      currentPage: 'Nykyinen sivu, Sivu {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Luokitus {0}/{1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,WADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,WADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,iBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,8DADP;MAETC,aAAa,EAAE,6DAFN;MAGTC,QAAQ,EAAE,qDAHD;MAITC,YAAY,EAAE,mCAJL;MAKTC,kBAAkB,EAAE,+CALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,mBADR;IAEVU,eAAe,EAAE,QAFP;IAGVC,QAAQ,EAAE,eAHA;IAIVC,QAAQ,EAAE,gBAJA;IAKVC,SAAS,EAAE,kBALD;IAMVC,QAAQ,EAAE,gBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,aADL;IAEVC,kBAAkB,EAAE,mBAFV;IAGVC,iBAAiB,EAAE,YAHT;IAIVC,kBAAkB,EAAE,oBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,UAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,gBADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,eADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,KADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,eAFG;MAGTY,QAAQ,EAAE,gBAHD;MAITC,IAAI,EAAE,kBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}