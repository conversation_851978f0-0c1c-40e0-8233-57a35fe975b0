{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/possibleConstructorReturn.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/possibleConstructorReturn.js", "mtime": 1757335237664}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiOwppbXBvcnQgX3R5cGVvZiBmcm9tICIuL3R5cGVvZi5qcyI7CmltcG9ydCBhc3NlcnRUaGlzSW5pdGlhbGl6ZWQgZnJvbSAiLi9hc3NlcnRUaGlzSW5pdGlhbGl6ZWQuanMiOwpmdW5jdGlvbiBfcG9zc2libGVDb25zdHJ1Y3RvclJldHVybih0LCBlKSB7CiAgaWYgKGUgJiYgKCJvYmplY3QiID09IF90eXBlb2YoZSkgfHwgImZ1bmN0aW9uIiA9PSB0eXBlb2YgZSkpIHJldHVybiBlOwogIGlmICh2b2lkIDAgIT09IGUpIHRocm93IG5ldyBUeXBlRXJyb3IoIkRlcml2ZWQgY29uc3RydWN0b3JzIG1heSBvbmx5IHJldHVybiBvYmplY3Qgb3IgdW5kZWZpbmVkIik7CiAgcmV0dXJuIGFzc2VydFRoaXNJbml0aWFsaXplZCh0KTsKfQpleHBvcnQgeyBfcG9zc2libGVDb25zdHJ1Y3RvclJldHVybiBhcyBkZWZhdWx0IH07"}, {"version": 3, "names": ["_typeof", "assertThisInitialized", "_possibleConstructorReturn", "t", "e", "TypeError", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/possibleConstructorReturn.js"], "sourcesContent": ["import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };"], "mappings": ";AAAA,OAAOA,OAAO,MAAM,aAAa;AACjC,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,0BAA0BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAIA,CAAC,KAAK,QAAQ,IAAIJ,OAAO,CAACI,CAAC,CAAC,IAAI,UAAU,IAAI,OAAOA,CAAC,CAAC,EAAE,OAAOA,CAAC;EACrE,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,0DAA0D,CAAC;EACjG,OAAOJ,qBAAqB,CAACE,CAAC,CAAC;AACjC;AACA,SAASD,0BAA0B,IAAII,OAAO", "ignoreList": []}]}