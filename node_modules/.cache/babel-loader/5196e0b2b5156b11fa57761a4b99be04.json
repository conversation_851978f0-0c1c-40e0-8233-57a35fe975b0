{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/colorUtils.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/colorUtils.js", "mtime": 1757335235099}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["console<PERSON>arn", "chunk", "padEnd", "toXYZ", "isCssColor", "color", "match", "colorToInt", "rgb", "c", "substring", "length", "_context", "_mapInstanceProperty", "split", "call", "char", "join", "concat", "_parseInt", "TypeError", "constructor", "name", "isNaN", "classToHex", "colors", "currentTheme", "_context2", "_color$toString$trim$", "_trimInstanceProperty", "toString", "replace", "_color$toString$trim$2", "_slicedToArray", "colorName", "colorModifier", "hexColor", "base", "intToHex", "_context3", "_repeatInstanceProperty", "colorToHex", "HSVAtoRGBA", "hsva", "_context4", "h", "s", "v", "a", "f", "n", "k", "Math", "max", "min", "round", "r", "g", "b", "RGBAtoHSVA", "rgba", "hsv", "HSVAtoHSLA", "l", "sprime", "HSLAtoHSVA", "hsl", "RGBAtoCSS", "_context5", "_context6", "_context7", "_concatInstanceProperty", "RGBtoCSS", "_objectSpread", "RGBAtoHex", "toHex", "substr", "toUpperCase", "HexToRGBA", "hex", "_context8", "_sliceInstanceProperty", "HexToHSVA", "HSVAtoHex", "parseHex", "_startsWithInstanceProperty", "_context9", "x", "parseGradient", "gradient", "_context0", "_Object$values", "RGBtoInt", "contrastRatio", "c1", "c2", "_toXYZ", "_toXYZ2", "y1", "_toXYZ3", "_toXYZ4", "y2"], "sources": ["../../src/util/colorUtils.ts"], "sourcesContent": ["// Utilities\nimport { consoleWarn } from './console'\nimport { chunk, padEnd } from './helpers'\nimport { toXYZ } from './color/transformSRGB'\n\n// Types\nimport { VuetifyThemeVariant } from 'types/services/theme'\n\nexport type ColorInt = number\nexport type XYZ = [number, number, number]\nexport type LAB = [number, number, number]\nexport type HSV = { h: number, s: number, v: number }\nexport type HSVA = HSV & { a: number }\nexport type RGB = { r: number, g: number, b: number }\nexport type RGBA = RGB & { a: number }\nexport type HSL = { h: number, s: number, l: number }\nexport type HSLA = HSL & { a: number }\nexport type Hex = string\nexport type Hexa = string\nexport type Color = string | number | {}\n\nexport function isCssColor (color?: string | false): boolean {\n  return !!color && !!color.match(/^(#|var\\(--|(rgb|hsl)a?\\()/)\n}\n\nexport function colorToInt (color: Color): ColorInt {\n  let rgb\n\n  if (typeof color === 'number') {\n    rgb = color\n  } else if (typeof color === 'string') {\n    let c = color[0] === '#' ? color.substring(1) : color\n    if (c.length === 3) {\n      c = c.split('').map(char => char + char).join('')\n    }\n    if (c.length !== 6) {\n      consoleWarn(`'${color}' is not a valid rgb color`)\n    }\n    rgb = parseInt(c, 16)\n  } else {\n    throw new TypeError(`Colors can only be numbers or strings, recieved ${color == null ? color : color.constructor.name} instead`)\n  }\n\n  if (rgb < 0) {\n    consoleWarn(`Colors cannot be negative: '${color}'`)\n    rgb = 0\n  } else if (rgb > 0xffffff || isNaN(rgb)) {\n    consoleWarn(`'${color}' is not a valid rgb color`)\n    rgb = 0xffffff\n  }\n\n  return rgb\n}\n\nexport function classToHex (\n  color: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<VuetifyThemeVariant>,\n): string {\n  const [colorName, colorModifier] = color\n    .toString().trim().replace('-', '').split(' ', 2) as (string | undefined)[]\n\n  let hexColor = ''\n  if (colorName && colorName in colors) {\n    if (colorModifier && colorModifier in colors[colorName]) {\n      hexColor = colors[colorName][colorModifier]\n    } else if ('base' in colors[colorName]) {\n      hexColor = colors[colorName].base\n    }\n  } else if (colorName && colorName in currentTheme) {\n    hexColor = currentTheme[colorName] as string\n  }\n\n  return hexColor\n}\n\nexport function intToHex (color: ColorInt): string {\n  let hexColor: string = color.toString(16)\n\n  if (hexColor.length < 6) hexColor = '0'.repeat(6 - hexColor.length) + hexColor\n\n  return '#' + hexColor\n}\n\nexport function colorToHex (color: Color): string {\n  return intToHex(colorToInt(color))\n}\n\n/**\n * Converts HSVA to RGBA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV\n *\n * @param color HSVA color as an array [0-360, 0-1, 0-1, 0-1]\n */\nexport function HSVAtoRGBA (hsva: HSVA): RGBA {\n  const { h, s, v, a } = hsva\n  const f = (n: number) => {\n    const k = (n + (h / 60)) % 6\n    return v - v * s * Math.max(Math.min(k, 4 - k, 1), 0)\n  }\n\n  const rgb = [f(5), f(3), f(1)].map(v => Math.round(v * 255))\n\n  return { r: rgb[0], g: rgb[1], b: rgb[2], a }\n}\n\n/**\n * Converts RGBA to HSVA. Based on formula from https://en.wikipedia.org/wiki/HSL_and_HSV\n *\n * @param color RGBA color as an array [0-255, 0-255, 0-255, 0-1]\n */\nexport function RGBAtoHSVA (rgba: RGBA): HSVA {\n  if (!rgba) return { h: 0, s: 1, v: 1, a: 1 }\n\n  const r = rgba.r / 255\n  const g = rgba.g / 255\n  const b = rgba.b / 255\n  const max = Math.max(r, g, b)\n  const min = Math.min(r, g, b)\n\n  let h = 0\n\n  if (max !== min) {\n    if (max === r) {\n      h = 60 * (0 + ((g - b) / (max - min)))\n    } else if (max === g) {\n      h = 60 * (2 + ((b - r) / (max - min)))\n    } else if (max === b) {\n      h = 60 * (4 + ((r - g) / (max - min)))\n    }\n  }\n\n  if (h < 0) h = h + 360\n\n  const s = max === 0 ? 0 : (max - min) / max\n  const hsv = [h, s, max]\n\n  return { h: hsv[0], s: hsv[1], v: hsv[2], a: rgba.a }\n}\n\nexport function HSVAtoHSLA (hsva: HSVA): HSLA {\n  const { h, s, v, a } = hsva\n\n  const l = v - (v * s / 2)\n\n  const sprime = l === 1 || l === 0 ? 0 : (v - l) / Math.min(l, 1 - l)\n\n  return { h, s: sprime, l, a }\n}\n\nexport function HSLAtoHSVA (hsl: HSLA): HSVA {\n  const { h, s, l, a } = hsl\n\n  const v = l + s * Math.min(l, 1 - l)\n\n  const sprime = v === 0 ? 0 : 2 - (2 * l / v)\n\n  return { h, s: sprime, v, a }\n}\n\nexport function RGBAtoCSS (rgba: RGBA): string {\n  return `rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, ${rgba.a})`\n}\n\nexport function RGBtoCSS (rgba: RGBA): string {\n  return RGBAtoCSS({ ...rgba, a: 1 })\n}\n\nexport function RGBAtoHex (rgba: RGBA): Hex {\n  const toHex = (v: number) => {\n    const h = Math.round(v).toString(16)\n    return ('00'.substr(0, 2 - h.length) + h).toUpperCase()\n  }\n\n  return `#${[\n    toHex(rgba.r),\n    toHex(rgba.g),\n    toHex(rgba.b),\n    toHex(Math.round(rgba.a * 255)),\n  ].join('')}`\n}\n\nexport function HexToRGBA (hex: Hex): RGBA {\n  const rgba = chunk(hex.slice(1), 2).map((c: string) => parseInt(c, 16))\n\n  return {\n    r: rgba[0],\n    g: rgba[1],\n    b: rgba[2],\n    a: Math.round((rgba[3] / 255) * 100) / 100,\n  }\n}\n\nexport function HexToHSVA (hex: Hex): HSVA {\n  const rgb = HexToRGBA(hex)\n  return RGBAtoHSVA(rgb)\n}\n\nexport function HSVAtoHex (hsva: HSVA): Hex {\n  return RGBAtoHex(HSVAtoRGBA(hsva))\n}\n\nexport function parseHex (hex: string): Hex {\n  if (hex.startsWith('#')) {\n    hex = hex.slice(1)\n  }\n\n  hex = hex.replace(/([^0-9a-f])/gi, 'F')\n\n  if (hex.length === 3 || hex.length === 4) {\n    hex = hex.split('').map(x => x + x).join('')\n  }\n\n  if (hex.length === 6) {\n    hex = padEnd(hex, 8, 'F')\n  } else {\n    hex = padEnd(padEnd(hex, 6), 8, 'F')\n  }\n\n  return `#${hex}`.toUpperCase().substr(0, 9)\n}\n\nexport function parseGradient (\n  gradient: string,\n  colors: Record<string, Record<string, string>>,\n  currentTheme: Partial<VuetifyThemeVariant>,\n) {\n  return gradient.replace(/([a-z]+(\\s[a-z]+-[1-5])?)(?=$|,)/gi, x => {\n    return classToHex(x, colors, currentTheme) || x\n  }).replace(/(rgba\\()#[0-9a-f]+(?=,)/gi, x => {\n    return 'rgba(' + Object.values(HexToRGBA(parseHex(x.replace(/rgba\\(/, '')))).slice(0, 3).join(',')\n  })\n}\n\nexport function RGBtoInt (rgba: RGBA): ColorInt {\n  return (rgba.r << 16) + (rgba.g << 8) + rgba.b\n}\n\n/**\n * Returns the contrast ratio (1-21) between two colors.\n *\n * @param c1 First color\n * @param c2 Second color\n */\nexport function contrastRatio (c1: RGBA, c2: RGBA): number {\n  const [, y1] = toXYZ(RGBtoInt(c1))\n  const [, y2] = toXYZ(RGBtoInt(c2))\n\n  return (Math.max(y1, y2) + 0.05) / (Math.min(y1, y2) + 0.05)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA,SAASA,WAAT,QAA4B,WAA5B;AACA,SAASC,KAAT,EAAgBC,MAAhB,QAA8B,WAA9B;AACA,SAASC,KAAT,QAAsB,uBAAtB;AAkBA,OAAM,SAAUC,UAAVA,CAAsBC,KAAtB,EAA4C;EAChD,OAAO,CAAC,CAACA,KAAF,IAAW,CAAC,CAACA,KAAK,CAACC,KAAN,CAAY,4BAAZ,CAApB;AACD;AAED,OAAM,SAAUC,UAAVA,CAAsBF,KAAtB,EAAkC;EACtC,IAAIG,GAAJ;EAEA,IAAI,OAAOH,KAAP,KAAiB,QAArB,EAA+B;IAC7BG,GAAG,GAAGH,KAAN;EACD,CAFD,MAEO,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IACpC,IAAII,CAAC,GAAGJ,KAAK,CAAC,CAAD,CAAL,KAAa,GAAb,GAAmBA,KAAK,CAACK,SAAN,CAAgB,CAAhB,CAAnB,GAAwCL,KAAhD;IACA,IAAII,CAAC,CAACE,MAAF,KAAa,CAAjB,EAAoB;MAAA,IAAAC,QAAA;MAClBH,CAAC,GAAGI,oBAAA,CAAAD,QAAA,GAAAH,CAAC,CAACK,KAAF,CAAQ,EAAR,GAAAC,IAAA,CAAAH,QAAA,EAAgB,UAAAI,KAAI;QAAA,OAAIA,KAAI,GAAGA,KAA/B;MAAA,GAAqCC,IAArC,CAA0C,EAA1C,CAAJ;IACD;IACD,IAAIR,CAAC,CAACE,MAAF,KAAa,CAAjB,EAAoB;MAClBX,WAAW,KAAAkB,MAAA,CAAKb,KAAK,+BAAV,CAAX;IACD;IACDG,GAAG,GAAGW,SAAA,CAASV,CAAD,EAAI,EAAJ,CAAd;EACD,CATM,MASA;IACL,MAAM,IAAIW,SAAJ,oDAAAF,MAAA,CAAiEb,KAAK,IAAI,IAAT,GAAgBA,KAAhB,GAAwBA,KAAK,CAACgB,WAAN,CAAkBC,IAAI,aAA/G,CAAN;EACD;EAED,IAAId,GAAG,GAAG,CAAV,EAAa;IACXR,WAAW,gCAAAkB,MAAA,CAAgCb,KAAK,MAArC,CAAX;IACAG,GAAG,GAAG,CAAN;EACD,CAHD,MAGO,IAAIA,GAAG,GAAG,QAAN,IAAkBe,KAAK,CAACf,GAAD,CAA3B,EAAkC;IACvCR,WAAW,KAAAkB,MAAA,CAAKb,KAAK,+BAAV,CAAX;IACAG,GAAG,GAAG,QAAN;EACD;EAED,OAAOA,GAAP;AACD;AAED,OAAM,SAAUgB,UAAVA,CACJnB,KADI,EAEJoB,MAFI,EAGJC,YAHI,EAGsC;EAAA,IAAAC,SAAA;EAE1C,IAAAC,qBAAA,GAAmCC,qBAAA,CAAAF,SAAA,GAAAtB,KAAK,CACrCyB,QADgC,IAAAf,IAAA,CAAAY,SAAA,EACdI,OADc,CACN,GADM,EACD,EADC,EACGjB,KADH,CACS,GADT,EACc,CADd,CAAnC;IAAAkB,sBAAA,GAAAC,cAAA,CAAAL,qBAAA;IAAOM,SAAD,GAAAF,sBAAA;IAAYG,aAAZ,GAAAH,sBAAA;EAGN,IAAII,QAAQ,GAAG,EAAf;EACA,IAAIF,SAAS,IAAIA,SAAS,IAAIT,MAA9B,EAAsC;IACpC,IAAIU,aAAa,IAAIA,aAAa,IAAIV,MAAM,CAACS,SAAD,CAA5C,EAAyD;MACvDE,QAAQ,GAAGX,MAAM,CAACS,SAAD,CAAN,CAAkBC,aAAlB,CAAX;IACD,CAFD,MAEO,IAAI,UAAUV,MAAM,CAACS,SAAD,CAApB,EAAiC;MACtCE,QAAQ,GAAGX,MAAM,CAACS,SAAD,CAAN,CAAkBG,IAA7B;IACD;EACF,CAND,MAMO,IAAIH,SAAS,IAAIA,SAAS,IAAIR,YAA9B,EAA4C;IACjDU,QAAQ,GAAGV,YAAY,CAACQ,SAAD,CAAvB;EACD;EAED,OAAOE,QAAP;AACD;AAED,OAAM,SAAUE,QAAVA,CAAoBjC,KAApB,EAAmC;EAAA,IAAAkC,SAAA;EACvC,IAAIH,QAAQ,GAAW/B,KAAK,CAACyB,QAAN,CAAe,EAAf,CAAvB;EAEA,IAAIM,QAAQ,CAACzB,MAAT,GAAkB,CAAtB,EAAyByB,QAAQ,GAAGI,uBAAA,CAAAD,SAAA,QAAAxB,IAAA,CAAAwB,SAAA,EAAW,IAAIH,QAAQ,CAACzB,MAAxB,IAAkCyB,QAA7C;EAEzB,OAAO,MAAMA,QAAb;AACD;AAED,OAAM,SAAUK,UAAVA,CAAsBpC,KAAtB,EAAkC;EACtC,OAAOiC,QAAQ,CAAC/B,UAAU,CAACF,KAAD,CAAX,CAAf;AACD;AAED;;;;AAIG;;AACH,OAAM,SAAUqC,UAAVA,CAAsBC,IAAtB,EAAgC;EAAA,IAAAC,SAAA;EACpC,IAAQC,CAAF,GAAiBF,IAAvB,CAAQE,CAAF;IAAKC,CAAL,GAAiBH,IAAvB,CAAWG,CAAL;IAAQC,CAAR,GAAiBJ,IAAvB,CAAcI,CAAR;IAAWC,CAAA,GAAML,IAAvB,CAAiBK,CAAA;EACjB,IAAMC,CAAC,GAAI,SAALA,CAACA,CAAIC,CAAD,EAAc;IACtB,IAAMC,CAAC,GAAG,CAACD,CAAC,GAAIL,CAAC,GAAG,EAAV,IAAiB,CAA3B;IACA,OAAOE,CAAC,GAAGA,CAAC,GAAGD,CAAJ,GAAQM,IAAI,CAACC,GAAL,CAASD,IAAI,CAACE,GAAL,CAASH,CAAT,EAAY,IAAIA,CAAhB,EAAmB,CAAnB,CAAT,EAAgC,CAAhC,CAAnB;EACD,CAHD;EAKA,IAAM3C,GAAG,GAAGK,oBAAA,CAAA+B,SAAA,IAACK,CAAC,CAAC,CAAD,CAAF,EAAOA,CAAC,CAAC,CAAD,CAAR,EAAaA,CAAC,CAAC,CAAD,CAAd,GAAAlC,IAAA,CAAA6B,SAAA,EAAuB,UAAAG,CAAC;IAAA,OAAIK,IAAI,CAACG,KAAL,CAAWR,CAAC,GAAG,GAAf,CAA5B;EAAA,EAAZ;EAEA,OAAO;IAAES,CAAC,EAAEhD,GAAG,CAAC,CAAD,CAAR;IAAaiD,CAAC,EAAEjD,GAAG,CAAC,CAAD,CAAnB;IAAwBkD,CAAC,EAAElD,GAAG,CAAC,CAAD,CAA9B;IAAmCwC,CAAA,EAAAA;EAAnC,CAAP;AACD;AAED;;;;AAIG;;AACH,OAAM,SAAUW,UAAVA,CAAsBC,IAAtB,EAAgC;EACpC,IAAI,CAACA,IAAL,EAAW,OAAO;IAAEf,CAAC,EAAE,CAAL;IAAQC,CAAC,EAAE,CAAX;IAAcC,CAAC,EAAE,CAAjB;IAAoBC,CAAC,EAAE;EAAvB,CAAP;EAEX,IAAMQ,CAAC,GAAGI,IAAI,CAACJ,CAAL,GAAS,GAAnB;EACA,IAAMC,CAAC,GAAGG,IAAI,CAACH,CAAL,GAAS,GAAnB;EACA,IAAMC,CAAC,GAAGE,IAAI,CAACF,CAAL,GAAS,GAAnB;EACA,IAAML,GAAG,GAAGD,IAAI,CAACC,GAAL,CAASG,CAAT,EAAYC,CAAZ,EAAeC,CAAf,CAAZ;EACA,IAAMJ,GAAG,GAAGF,IAAI,CAACE,GAAL,CAASE,CAAT,EAAYC,CAAZ,EAAeC,CAAf,CAAZ;EAEA,IAAIb,CAAC,GAAG,CAAR;EAEA,IAAIQ,GAAG,KAAKC,GAAZ,EAAiB;IACf,IAAID,GAAG,KAAKG,CAAZ,EAAe;MACbX,CAAC,GAAG,MAAM,IAAK,CAACY,CAAC,GAAGC,CAAL,KAAWL,GAAG,GAAGC,GAAjB,CAAX,CAAJ;IACD,CAFD,MAEO,IAAID,GAAG,KAAKI,CAAZ,EAAe;MACpBZ,CAAC,GAAG,MAAM,IAAK,CAACa,CAAC,GAAGF,CAAL,KAAWH,GAAG,GAAGC,GAAjB,CAAX,CAAJ;IACD,CAFM,MAEA,IAAID,GAAG,KAAKK,CAAZ,EAAe;MACpBb,CAAC,GAAG,MAAM,IAAK,CAACW,CAAC,GAAGC,CAAL,KAAWJ,GAAG,GAAGC,GAAjB,CAAX,CAAJ;IACD;EACF;EAED,IAAIT,CAAC,GAAG,CAAR,EAAWA,CAAC,GAAGA,CAAC,GAAG,GAAR;EAEX,IAAMC,CAAC,GAAGO,GAAG,KAAK,CAAR,GAAY,CAAZ,GAAgB,CAACA,GAAG,GAAGC,GAAP,IAAcD,GAAxC;EACA,IAAMQ,GAAG,GAAG,CAAChB,CAAD,EAAIC,CAAJ,EAAOO,GAAP,CAAZ;EAEA,OAAO;IAAER,CAAC,EAAEgB,GAAG,CAAC,CAAD,CAAR;IAAaf,CAAC,EAAEe,GAAG,CAAC,CAAD,CAAnB;IAAwBd,CAAC,EAAEc,GAAG,CAAC,CAAD,CAA9B;IAAmCb,CAAC,EAAEY,IAAI,CAACZ;EAA3C,CAAP;AACD;AAED,OAAM,SAAUc,UAAVA,CAAsBnB,IAAtB,EAAgC;EACpC,IAAQE,CAAF,GAAiBF,IAAvB,CAAQE,CAAF;IAAKC,CAAL,GAAiBH,IAAvB,CAAWG,CAAL;IAAQC,CAAR,GAAiBJ,IAAvB,CAAcI,CAAR;IAAWC,CAAA,GAAML,IAAvB,CAAiBK,CAAA;EAEjB,IAAMe,CAAC,GAAGhB,CAAC,GAAIA,CAAC,GAAGD,CAAJ,GAAQ,CAAvB;EAEA,IAAMkB,MAAM,GAAGD,CAAC,KAAK,CAAN,IAAWA,CAAC,KAAK,CAAjB,GAAqB,CAArB,GAAyB,CAAChB,CAAC,GAAGgB,CAAL,IAAUX,IAAI,CAACE,GAAL,CAASS,CAAT,EAAY,IAAIA,CAAhB,CAAlD;EAEA,OAAO;IAAElB,CAAF,EAAEA,CAAF;IAAKC,CAAC,EAAEkB,MAAR;IAAgBD,CAAhB,EAAgBA,CAAhB;IAAmBf,CAAA,EAAAA;EAAnB,CAAP;AACD;AAED,OAAM,SAAUiB,UAAVA,CAAsBC,GAAtB,EAA+B;EACnC,IAAQrB,CAAF,GAAiBqB,GAAvB,CAAQrB,CAAF;IAAKC,CAAL,GAAiBoB,GAAvB,CAAWpB,CAAL;IAAQiB,CAAR,GAAiBG,GAAvB,CAAcH,CAAR;IAAWf,CAAA,GAAMkB,GAAvB,CAAiBlB,CAAA;EAEjB,IAAMD,CAAC,GAAGgB,CAAC,GAAGjB,CAAC,GAAGM,IAAI,CAACE,GAAL,CAASS,CAAT,EAAY,IAAIA,CAAhB,CAAlB;EAEA,IAAMC,MAAM,GAAGjB,CAAC,KAAK,CAAN,GAAU,CAAV,GAAc,IAAK,IAAIgB,CAAJ,GAAQhB,CAA1C;EAEA,OAAO;IAAEF,CAAF,EAAEA,CAAF;IAAKC,CAAC,EAAEkB,MAAR;IAAgBjB,CAAhB,EAAgBA,CAAhB;IAAmBC,CAAA,EAAAA;EAAnB,CAAP;AACD;AAED,OAAM,SAAUmB,SAAVA,CAAqBP,IAArB,EAA+B;EAAA,IAAAQ,SAAA,EAAAC,SAAA,EAAAC,SAAA;EACnC,OAAAC,uBAAA,CAAAH,SAAA,GAAAG,uBAAA,CAAAF,SAAA,GAAAE,uBAAA,CAAAD,SAAA,WAAApD,MAAA,CAAe0C,IAAI,CAACJ,CAAC,SAAAzC,IAAA,CAAAuD,SAAA,EAAKV,IAAI,CAACH,CAAC,SAAA1C,IAAA,CAAAsD,SAAA,EAAKT,IAAI,CAACF,CAAC,SAAA3C,IAAA,CAAAqD,SAAA,EAAKR,IAAI,CAACZ,CAAC;AACvD;AAED,OAAM,SAAUwB,QAAVA,CAAoBZ,IAApB,EAA8B;EAClC,OAAOO,SAAS,CAAAM,aAAA,CAAAA,aAAA,KAAMb,IAAL;IAAWZ,CAAC,EAAE;EAAA,EAAf,CAAhB;AACD;AAED,OAAM,SAAU0B,SAAVA,CAAqBd,IAArB,EAA+B;EACnC,IAAMe,KAAK,GAAI,SAATA,KAAKA,CAAI5B,CAAD,EAAc;IAC1B,IAAMF,CAAC,GAAGO,IAAI,CAACG,KAAL,CAAWR,CAAX,EAAcjB,QAAd,CAAuB,EAAvB,CAAV;IACA,OAAO,CAAC,KAAK8C,MAAL,CAAY,CAAZ,EAAe,IAAI/B,CAAC,CAAClC,MAArB,IAA+BkC,CAAhC,EAAmCgC,WAAnC,EAAP;EACD,CAHD;EAKA,WAAA3D,MAAA,CAAW,CACTyD,KAAK,CAACf,IAAI,CAACJ,CAAN,CADI,EAETmB,KAAK,CAACf,IAAI,CAACH,CAAN,CAFI,EAGTkB,KAAK,CAACf,IAAI,CAACF,CAAN,CAHI,EAITiB,KAAK,CAACvB,IAAI,CAACG,KAAL,CAAWK,IAAI,CAACZ,CAAL,GAAS,GAApB,CAAD,CAJI,EAKT/B,IALS,CAKJ,EALI,CAKD;AACX;AAED,OAAM,SAAU6D,SAAVA,CAAqBC,GAArB,EAA6B;EAAA,IAAAC,SAAA;EACjC,IAAMpB,IAAI,GAAG/C,oBAAA,CAAAmE,SAAA,GAAA/E,KAAK,CAACgF,sBAAA,CAAAF,GAAG,EAAAhE,IAAA,CAAHgE,GAAG,EAAO,CAAV,CAAD,EAAe,CAAf,CAAL,EAAAhE,IAAA,CAAAiE,SAAA,EAA4B,UAAAvE,CAAD;IAAA,OAAeU,SAAA,CAASV,CAAD,EAAI,EAAJ,CAAlD;EAAA,EAAb;EAEA,OAAO;IACL+C,CAAC,EAAEI,IAAI,CAAC,CAAD,CADF;IAELH,CAAC,EAAEG,IAAI,CAAC,CAAD,CAFF;IAGLF,CAAC,EAAEE,IAAI,CAAC,CAAD,CAHF;IAILZ,CAAC,EAAEI,IAAI,CAACG,KAAL,CAAYK,IAAI,CAAC,CAAD,CAAJ,GAAU,GAAX,GAAkB,GAA7B,IAAoC;EAJlC,CAAP;AAMD;AAED,OAAM,SAAUsB,SAAVA,CAAqBH,GAArB,EAA6B;EACjC,IAAMvE,GAAG,GAAGsE,SAAS,CAACC,GAAD,CAArB;EACA,OAAOpB,UAAU,CAACnD,GAAD,CAAjB;AACD;AAED,OAAM,SAAU2E,SAAVA,CAAqBxC,IAArB,EAA+B;EACnC,OAAO+B,SAAS,CAAChC,UAAU,CAACC,IAAD,CAAX,CAAhB;AACD;AAED,OAAM,SAAUyC,QAAVA,CAAoBL,GAApB,EAA+B;EACnC,IAAIM,2BAAA,CAAAN,GAAG,EAAAhE,IAAA,CAAHgE,GAAG,EAAY,GAAf,CAAJ,EAAyB;IACvBA,GAAG,GAAGE,sBAAA,CAAAF,GAAG,EAAAhE,IAAA,CAAHgE,GAAG,EAAO,CAAV,CAAN;EACD;EAEDA,GAAG,GAAGA,GAAG,CAAChD,OAAJ,CAAY,eAAZ,EAA6B,GAA7B,CAAN;EAEA,IAAIgD,GAAG,CAACpE,MAAJ,KAAe,CAAf,IAAoBoE,GAAG,CAACpE,MAAJ,KAAe,CAAvC,EAA0C;IAAA,IAAA2E,SAAA;IACxCP,GAAG,GAAGlE,oBAAA,CAAAyE,SAAA,GAAAP,GAAG,CAACjE,KAAJ,CAAU,EAAV,GAAAC,IAAA,CAAAuE,SAAA,EAAkB,UAAAC,CAAC;MAAA,OAAIA,CAAC,GAAGA,CAA3B;IAAA,GAA8BtE,IAA9B,CAAmC,EAAnC,CAAN;EACD;EAED,IAAI8D,GAAG,CAACpE,MAAJ,KAAe,CAAnB,EAAsB;IACpBoE,GAAG,GAAG7E,MAAM,CAAC6E,GAAD,EAAM,CAAN,EAAS,GAAT,CAAZ;EACD,CAFD,MAEO;IACLA,GAAG,GAAG7E,MAAM,CAACA,MAAM,CAAC6E,GAAD,EAAM,CAAN,CAAP,EAAiB,CAAjB,EAAoB,GAApB,CAAZ;EACD;EAED,OAAO,IAAA7D,MAAA,CAAI6D,GAAG,EAAGF,WAAV,GAAwBD,MAAxB,CAA+B,CAA/B,EAAkC,CAAlC,CAAP;AACD;AAED,OAAM,SAAUY,aAAVA,CACJC,QADI,EAEJhE,MAFI,EAGJC,YAHI,EAGsC;EAE1C,OAAO+D,QAAQ,CAAC1D,OAAT,CAAiB,oCAAjB,EAAuD,UAAAwD,CAAC,EAAG;IAChE,OAAO/D,UAAU,CAAC+D,CAAD,EAAI9D,MAAJ,EAAYC,YAAZ,CAAV,IAAuC6D,CAA9C;EACD,CAFM,EAEJxD,OAFI,CAEI,2BAFJ,EAEiC,UAAAwD,CAAC,EAAG;IAAA,IAAAG,SAAA;IAC1C,OAAO,UAAUT,sBAAA,CAAAS,SAAA,GAAAC,cAAA,CAAcb,SAAS,CAACM,QAAQ,CAACG,CAAC,CAACxD,OAAF,CAAU,QAAV,EAAoB,EAApB,CAAD,CAAT,CAAvB,GAAAhB,IAAA,CAAA2E,SAAA,EAAkE,CAAlE,EAAqE,CAArE,EAAwEzE,IAAxE,CAA6E,GAA7E,CAAjB;EACD,CAJM,CAAP;AAKD;AAED,OAAM,SAAU2E,QAAVA,CAAoBhC,IAApB,EAA8B;EAClC,OAAO,CAACA,IAAI,CAACJ,CAAL,IAAU,EAAX,KAAkBI,IAAI,CAACH,CAAL,IAAU,CAA5B,IAAiCG,IAAI,CAACF,CAA7C;AACD;AAED;;;;;AAKG;;AACH,OAAM,SAAUmC,aAAVA,CAAyBC,EAAzB,EAAmCC,EAAnC,EAA2C;EAC/C,IAAAC,MAAA,GAAe7F,KAAK,CAACyF,QAAQ,CAACE,EAAD,CAAT,CAApB;IAAAG,OAAA,GAAAhE,cAAA,CAAA+D,MAAA;IAASE,EAAH,GAAAD,OAAA;EACN,IAAAE,OAAA,GAAehG,KAAK,CAACyF,QAAQ,CAACG,EAAD,CAAT,CAApB;IAAAK,OAAA,GAAAnE,cAAA,CAAAkE,OAAA;IAASE,EAAH,GAAAD,OAAA;EAEN,OAAO,CAAChD,IAAI,CAACC,GAAL,CAAS6C,EAAT,EAAaG,EAAb,IAAmB,IAApB,KAA6BjD,IAAI,CAACE,GAAL,CAAS4C,EAAT,EAAaG,EAAb,IAAmB,IAAhD,CAAP;AACD", "ignoreList": []}]}