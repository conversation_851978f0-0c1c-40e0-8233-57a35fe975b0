{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabsItems.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabsItems.js", "mtime": 1757335239366}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKLy8gRXh0ZW5zaW9ucwppbXBvcnQgVldpbmRvdyBmcm9tICcuLi9WV2luZG93L1ZXaW5kb3cnOyAvLyBUeXBlcyAmIENvbXBvbmVudHMKCmltcG9ydCB7IEJhc2VJdGVtR3JvdXAgfSBmcm9tICcuLy4uL1ZJdGVtR3JvdXAvVkl0ZW1Hcm91cCc7Ci8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBWV2luZG93LmV4dGVuZCh7CiAgbmFtZTogJ3YtdGFicy1pdGVtcycsCiAgcHJvcHM6IHsKICAgIG1hbmRhdG9yeTogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICAiZGVmYXVsdCI6IGZhbHNlCiAgICB9CiAgfSwKICBjb21wdXRlZDogewogICAgY2xhc3NlczogZnVuY3Rpb24gY2xhc3NlcygpIHsKICAgICAgcmV0dXJuIF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgVldpbmRvdy5vcHRpb25zLmNvbXB1dGVkLmNsYXNzZXMuY2FsbCh0aGlzKSksIHt9LCB7CiAgICAgICAgJ3YtdGFicy1pdGVtcyc6IHRydWUKICAgICAgfSk7CiAgICB9LAogICAgaXNEYXJrOiBmdW5jdGlvbiBpc0RhcmsoKSB7CiAgICAgIHJldHVybiB0aGlzLnJvb3RJc0Rhcms7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRWYWx1ZTogZnVuY3Rpb24gZ2V0VmFsdWUoaXRlbSwgaSkgewogICAgICByZXR1cm4gaXRlbS5pZCB8fCBCYXNlSXRlbUdyb3VwLm9wdGlvbnMubWV0aG9kcy5nZXRWYWx1ZS5jYWxsKHRoaXMsIGl0ZW0sIGkpOwogICAgfQogIH0KfSk7"}, {"version": 3, "names": ["VWindow", "BaseItemGroup", "extend", "name", "props", "mandatory", "type", "Boolean", "computed", "classes", "_objectSpread", "options", "call", "isDark", "rootIsDark", "methods", "getValue", "item", "i", "id"], "sources": ["../../../src/components/VTabs/VTabsItems.ts"], "sourcesContent": ["// Extensions\nimport VWindow from '../VWindow/VWindow'\n\n// Types & Components\nimport { BaseItemGroup, GroupableInstance } from './../VItemGroup/VItemGroup'\n\n/* @vue/component */\nexport default VWindow.extend({\n  name: 'v-tabs-items',\n\n  props: {\n    mandatory: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...VWindow.options.computed.classes.call(this),\n        'v-tabs-items': true,\n      }\n    },\n    isDark (): boolean {\n      return this.rootIsDark\n    },\n  },\n\n  methods: {\n    getValue (item: GroupableInstance, i: number) {\n      return item.id || BaseItemGroup.options.methods.getValue.call(this, item, i)\n    },\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAOA,OAAP,MAAoB,oBAApB,C,CAEA;;AACA,SAASC,aAAT,QAAiD,4BAAjD;AAEA;;AACA,eAAeD,OAAO,CAACE,MAAR,CAAe;EAC5BC,IAAI,EAAE,cADsB;EAG5BC,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,OADG;MAET,WAAS;IAFA;EADN,CAHqB;EAU5BC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKV,OAAO,CAACW,OAAR,CAAgBH,QAAhB,CAAyBC,OAAzB,CAAiCG,IAAjC,CAAsC,IAAtC,CADE;QAEL,gBAAgB;MAAA;IAEnB,CANO;IAORC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO,KAAKC,UAAZ;IACD;EATO,CAVkB;EAsB5BC,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAEC,IAAF,EAA2BC,CAA3B,EAAoC;MAC1C,OAAOD,IAAI,CAACE,EAAL,IAAWlB,aAAa,CAACU,OAAd,CAAsBI,OAAtB,CAA8BC,QAA9B,CAAuCJ,IAAvC,CAA4C,IAA5C,EAAkDK,IAAlD,EAAwDC,CAAxD,CAAlB;IACD;EAHM;AAtBmB,CAAf,CAAf", "ignoreList": []}]}