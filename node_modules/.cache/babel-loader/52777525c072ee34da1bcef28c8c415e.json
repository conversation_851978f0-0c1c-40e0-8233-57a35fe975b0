{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/fa-svg.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/fa-svg.js", "mtime": 1757335235765}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGljb25zIGZyb20gJy4vZmEnOwpleHBvcnQgZnVuY3Rpb24gY29udmVydFRvQ29tcG9uZW50RGVjbGFyYXRpb25zKGNvbXBvbmVudCwgaWNvblNldCkgewogIHZhciByZXN1bHQgPSB7fTsKICBmb3IgKHZhciBrZXkgaW4gaWNvblNldCkgewogICAgcmVzdWx0W2tleV0gPSB7CiAgICAgIGNvbXBvbmVudDogY29tcG9uZW50LAogICAgICBwcm9wczogewogICAgICAgIGljb246IGljb25TZXRba2V5XS5zcGxpdCgnIGZhLScpCiAgICAgIH0KICAgIH07CiAgfQogIHJldHVybiByZXN1bHQ7Cn0KZXhwb3J0IGRlZmF1bHQgY29udmVydFRvQ29tcG9uZW50RGVjbGFyYXRpb25zKCdmb250LWF3ZXNvbWUtaWNvbicsIGljb25zKTs="}, {"version": 3, "names": ["icons", "convertToComponentDeclarations", "component", "iconSet", "result", "key", "props", "icon", "split"], "sources": ["../../../../src/services/icons/presets/fa-svg.ts"], "sourcesContent": ["import { VuetifyIcons } from 'vuetify/types/services/icons'\nimport { Component } from 'vue'\nimport icons from './fa'\n\nexport function convertToComponentDeclarations (\n  component: Component | string,\n  iconSet: VuetifyIcons,\n) {\n  const result: Partial<VuetifyIcons> = {}\n\n  for (const key in iconSet) {\n    result[key] = {\n      component,\n      props: {\n        icon: (iconSet[key] as string).split(' fa-'),\n      },\n    }\n  }\n\n  return result as VuetifyIcons\n}\n\nexport default convertToComponentDeclarations('font-awesome-icon', icons)\n"], "mappings": "AAEA,OAAOA,KAAP,MAAkB,MAAlB;AAEA,OAAM,SAAUC,8BAAVA,CACJC,SADI,EAEJC,OAFI,EAEiB;EAErB,IAAMC,MAAM,GAA0B,EAAtC;EAEA,KAAK,IAAMC,GAAX,IAAkBF,OAAlB,EAA2B;IACzBC,MAAM,CAACC,GAAD,CAAN,GAAc;MACZH,SADY,EACZA,SADY;MAEZI,KAAK,EAAE;QACLC,IAAI,EAAGJ,OAAO,CAACE,GAAD,CAAP,CAAwBG,KAAxB,CAA8B,MAA9B;MADF;IAFK,CAAd;EAMD;EAED,OAAOJ,MAAP;AACD;AAED,eAAeH,8BAA8B,CAAC,mBAAD,EAAsBD,KAAtB,CAA7C", "ignoreList": []}]}