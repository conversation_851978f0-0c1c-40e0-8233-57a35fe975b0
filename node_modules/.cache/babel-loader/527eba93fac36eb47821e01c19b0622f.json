{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/proxyable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/proxyable/index.js", "mtime": 1757335237203}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwpleHBvcnQgZnVuY3Rpb24gZmFjdG9yeSgpIHsKICB2YXIgcHJvcCA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogJ3ZhbHVlJzsKICB2YXIgZXZlbnQgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6ICdjaGFuZ2UnOwogIHJldHVybiBWdWUuZXh0ZW5kKHsKICAgIG5hbWU6ICdwcm94eWFibGUnLAogICAgbW9kZWw6IHsKICAgICAgcHJvcDogcHJvcCwKICAgICAgZXZlbnQ6IGV2ZW50CiAgICB9LAogICAgcHJvcHM6IF9kZWZpbmVQcm9wZXJ0eSh7fSwgcHJvcCwgewogICAgICByZXF1aXJlZDogZmFsc2UKICAgIH0pLAogICAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBpbnRlcm5hbExhenlWYWx1ZTogdGhpc1twcm9wXQogICAgICB9OwogICAgfSwKICAgIGNvbXB1dGVkOiB7CiAgICAgIGludGVybmFsVmFsdWU6IHsKICAgICAgICBnZXQ6IGZ1bmN0aW9uIGdldCgpIHsKICAgICAgICAgIHJldHVybiB0aGlzLmludGVybmFsTGF6eVZhbHVlOwogICAgICAgIH0sCiAgICAgICAgc2V0OiBmdW5jdGlvbiBzZXQodmFsKSB7CiAgICAgICAgICBpZiAodmFsID09PSB0aGlzLmludGVybmFsTGF6eVZhbHVlKSByZXR1cm47CiAgICAgICAgICB0aGlzLmludGVybmFsTGF6eVZhbHVlID0gdmFsOwogICAgICAgICAgdGhpcy4kZW1pdChldmVudCwgdmFsKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICB3YXRjaDogX2RlZmluZVByb3BlcnR5KHt9LCBwcm9wLCBmdW5jdGlvbiAodmFsKSB7CiAgICAgIHRoaXMuaW50ZXJuYWxMYXp5VmFsdWUgPSB2YWw7CiAgICB9KQogIH0pOwp9Ci8qIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tcmVkZWNsYXJlICovCgp2YXIgUHJveHlhYmxlID0gZmFjdG9yeSgpOwpleHBvcnQgZGVmYXVsdCBQcm94eWFibGU7"}, {"version": 3, "names": ["<PERSON><PERSON>", "factory", "prop", "arguments", "length", "undefined", "event", "extend", "name", "model", "props", "_defineProperty", "required", "data", "internalLazyValue", "computed", "internalValue", "get", "set", "val", "$emit", "watch", "Proxyable"], "sources": ["../../../src/mixins/proxyable/index.ts"], "sourcesContent": ["import Vue, { VueConstructor } from 'vue'\n\nexport type Proxyable<T extends string = 'value'> = VueConstructor<Vue & {\n  internalLazyValue: unknown\n  internalValue: unknown\n} & Record<T, any>>\n\nexport function factory<T extends string = 'value'> (prop?: T, event?: string): Proxyable<T>\nexport function factory (\n  prop = 'value',\n  event = 'change'\n) {\n  return Vue.extend({\n    name: 'proxyable',\n\n    model: {\n      prop,\n      event,\n    },\n\n    props: {\n      [prop]: {\n        required: false,\n      },\n    },\n\n    data () {\n      return {\n        internalLazyValue: this[prop] as unknown,\n      }\n    },\n\n    computed: {\n      internalValue: {\n        get (): unknown {\n          return this.internalLazyValue\n        },\n        set (val: any) {\n          if (val === this.internalLazyValue) return\n\n          this.internalLazyValue = val\n\n          this.$emit(event, val)\n        },\n      },\n    },\n\n    watch: {\n      [prop] (val) {\n        this.internalLazyValue = val\n      },\n    },\n  })\n}\n\n/* eslint-disable-next-line @typescript-eslint/no-redeclare */\nconst Proxyable = factory()\n\nexport default Proxyable\n"], "mappings": ";AAAA,OAAOA,GAAP,MAAoC,KAApC;AAQA,OAAM,SAAUC,OAAVA,CAAA,EAEY;EAAA,IADhBC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OADH;EAAA,IAEJG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAFJ;EAIJ,OAAOH,GAAG,CAACO,MAAJ,CAAW;IAChBC,IAAI,EAAE,WADU;IAGhBC,KAAK,EAAE;MACLP,IADK,EACLA,IADK;MAELI,KAAA,EAAAA;IAFK,CAHS;IAQhBI,KAAK,EAAAC,eAAA,KACFT,IAAD,EAAQ;MACNU,QAAQ,EAAE;IADJ,EATM;IAchBC,IAAI,WAAJA,IAAIA,CAAA;MACF,OAAO;QACLC,iBAAiB,EAAE,KAAKZ,IAAL;MADd,CAAP;IAGD,CAlBe;IAoBhBa,QAAQ,EAAE;MACRC,aAAa,EAAE;QACbC,GAAG,WAAHA,GAAGA,CAAA;UACD,OAAO,KAAKH,iBAAZ;QACD,CAHY;QAIbI,GAAG,WAAHA,GAAGA,CAAEC,GAAF,EAAU;UACX,IAAIA,GAAG,KAAK,KAAKL,iBAAjB,EAAoC;UAEpC,KAAKA,iBAAL,GAAyBK,GAAzB;UAEA,KAAKC,KAAL,CAAWd,KAAX,EAAkBa,GAAlB;QACD;MAVY;IADP,CApBM;IAmChBE,KAAK,EAAAV,eAAA,KACFT,IAAD,YAAQiB,GAAR,EAAW;MACT,KAAKL,iBAAL,GAAyBK,GAAzB;IACD;EAtCa,CAAX,CAAP;AAyCD;AAED;;AACA,IAAMG,SAAS,GAAGrB,OAAO,EAAzB;AAEA,eAAeqB,SAAf", "ignoreList": []}]}