{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ru.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ru.js", "mtime": 1757335237503}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/ru.ts"], "sourcesContent": ["export default {\n  badge: 'знак',\n  close: 'Закрыть',\n  dataIterator: {\n    noResultsText: 'Не найдено подходящих записей',\n    loadingText: 'Запись загружается...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Строк на странице:',\n    ariaLabel: {\n      sortDescending: 'Упорядочено по убыванию.',\n      sortAscending: 'Упорядочено по возрастанию.',\n      sortNone: 'Не упорядочено.',\n      activateNone: 'Активируйте, чтобы убрать сортировку.',\n      activateDescending: 'Активируйте для упорядочивания убыванию.',\n      activateAscending: 'Активируйте для упорядочивания по возрастанию.',\n    },\n    sortBy: 'Сортировать по',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Записей на странице:',\n    itemsPerPageAll: 'Все',\n    nextPage: 'Следующая страница',\n    prevPage: 'Предыдущая страница',\n    firstPage: 'Первая страница',\n    lastPage: 'Последняя страница',\n    pageText: '{0}-{1} из {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} выбран',\n    nextMonthAriaLabel: 'Следующий месяц',\n    nextYearAriaLabel: 'Следующий год',\n    prevMonthAriaLabel: 'Прошлый месяц',\n    prevYearAriaLabel: 'Предыдущий год',\n  },\n  noDataText: 'Отсутствуют данные',\n  carousel: {\n    prev: 'Предыдущий слайд',\n    next: 'Следующий слайд',\n    ariaLabel: {\n      delimiter: 'Слайд {0} из {1}',\n    },\n  },\n  calendar: {\n    moreEvents: 'Еще {0}',\n  },\n  fileInput: {\n    counter: 'Файлов: {0}',\n    counterSize: 'Файлов: {0} (всего {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Навигация по страницам',\n      next: 'Следующая страница',\n      previous: 'Предыдущая страница',\n      page: 'Перейти на страницу {0}',\n      currentPage: 'Текущая страница, Страница {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,MADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,+BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,oBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,0BADP;MAETC,aAAa,EAAE,6BAFN;MAGTC,QAAQ,EAAE,iBAHD;MAITC,YAAY,EAAE,uCAJL;MAKTC,kBAAkB,EAAE,0CALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,sBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,oBAHA;IAIVC,QAAQ,EAAE,qBAJA;IAKVC,SAAS,EAAE,iBALD;IAMVC,QAAQ,EAAE,oBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,YADL;IAEVC,kBAAkB,EAAE,iBAFV;IAGVC,iBAAiB,EAAE,eAHT;IAIVC,kBAAkB,EAAE,eAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,oBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,kBADE;IAERC,IAAI,EAAE,iBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,wBADA;MAETX,IAAI,EAAE,oBAFG;MAGTY,QAAQ,EAAE,qBAHD;MAITC,IAAI,EAAE,yBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}