{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/cs.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/cs.js", "mtime": 1757335235380}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/cs.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Nenalezeny žádné záznamy',\n    loadingText: 'Načítám položky...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Řádků na stránku:',\n    ariaLabel: {\n      sortDescending: 'Řazeno sestupně.',\n      sortAscending: 'Řazeno vzestupně.',\n      sortNone: 'Neseřazeno.',\n      activateNone: 'Aktivováním vypnete řazení.',\n      activateDescending: 'Aktivováním se bude řadit sestupně.',\n      activateAscending: 'Aktivováním se bude řadit vzestupně.',\n    },\n    sortBy: 'Řadit dle',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Položek na stránku:',\n    itemsPerPageAll: 'Vše',\n    nextPage: '<PERSON><PERSON><PERSON> strana',\n    prevPage: 'Předchozí strana',\n    firstPage: 'Prvn<PERSON> strana',\n    lastPage: 'Poslední strana',\n    pageText: '{0}-{1} z {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} vybráno',\n    nextMonthAriaLabel: 'Příští měsíc',\n    nextYearAriaLabel: 'Příští rok',\n    prevMonthAriaLabel: 'Předchozí měsíc',\n    prevYearAriaLabel: 'Předchozí rok',\n  },\n  noDataText: 'Nejsou dostupná žádná data',\n  carousel: {\n    prev: 'Předchozí obrázek',\n    next: 'Další obrázek',\n    ariaLabel: {\n      delimiter: 'Slide {0} z {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} dalších',\n  },\n  fileInput: {\n    counter: '{0} souborů',\n    counterSize: '{0} souborů ({1} celkem)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigace po stránkách',\n      next: 'Další strana',\n      previous: 'Předchozí strana',\n      page: 'Přejít na stránku {0}',\n      currentPage: 'Aktuální stránka, stránka {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Hodnocení {0} z {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,QAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,0BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,mBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,kBADP;MAETC,aAAa,EAAE,mBAFN;MAGTC,QAAQ,EAAE,aAHD;MAITC,YAAY,EAAE,6BAJL;MAKTC,kBAAkB,EAAE,qCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,cAHA;IAIVC,QAAQ,EAAE,kBAJA;IAKVC,SAAS,EAAE,cALD;IAMVC,QAAQ,EAAE,iBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,aADL;IAEVC,kBAAkB,EAAE,cAFV;IAGVC,iBAAiB,EAAE,YAHT;IAIVC,kBAAkB,EAAE,iBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,4BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,mBADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,cAFG;MAGTY,QAAQ,EAAE,kBAHD;MAITC,IAAI,EAAE,uBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}