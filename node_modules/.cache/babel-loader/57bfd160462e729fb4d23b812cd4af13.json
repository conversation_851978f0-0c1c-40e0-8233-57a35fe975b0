{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VNavigationDrawer/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VNavigationDrawer/index.js", "mtime": 1757335236900}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZOYXZpZ2F0aW9uRHJhd2VyIGZyb20gJy4vVk5hdmlnYXRpb25EcmF3ZXInOwpleHBvcnQgeyBWTmF2aWdhdGlvbkRyYXdlciB9OwpleHBvcnQgZGVmYXVsdCBWTmF2aWdhdGlvbkRyYXdlcjs="}, {"version": 3, "names": ["VNavigationDrawer"], "sources": ["../../../src/components/VNavigationDrawer/index.ts"], "sourcesContent": ["import VNavigationDrawer from './VNavigationDrawer'\n\nexport { VNavigationDrawer }\nexport default VNavigationDrawer\n"], "mappings": "AAAA,OAAOA,iBAAP,MAA8B,qBAA9B;AAEA,SAASA,iBAAT;AACA,eAAeA,iBAAf", "ignoreList": []}]}