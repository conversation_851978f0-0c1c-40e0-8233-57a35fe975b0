{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/index.js", "mtime": 1757335236884}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgY3JlYXRlU2ltcGxlRnVuY3Rpb25hbCB9IGZyb20gJy4uLy4uL3V0aWwvaGVscGVycyc7CmltcG9ydCBWTGlzdCBmcm9tICcuL1ZMaXN0JzsKaW1wb3J0IFZMaXN0R3JvdXAgZnJvbSAnLi9WTGlzdEdyb3VwJzsKaW1wb3J0IFZMaXN0SXRlbSBmcm9tICcuL1ZMaXN0SXRlbSc7CmltcG9ydCBWTGlzdEl0ZW1Hcm91cCBmcm9tICcuL1ZMaXN0SXRlbUdyb3VwJzsKaW1wb3J0IFZMaXN0SXRlbUFjdGlvbiBmcm9tICcuL1ZMaXN0SXRlbUFjdGlvbic7CmltcG9ydCBWTGlzdEl0ZW1BdmF0YXIgZnJvbSAnLi9WTGlzdEl0ZW1BdmF0YXInOwppbXBvcnQgVkxpc3RJdGVtSWNvbiBmcm9tICcuL1ZMaXN0SXRlbUljb24nOwpleHBvcnQgdmFyIFZMaXN0SXRlbUFjdGlvblRleHQgPSBjcmVhdGVTaW1wbGVGdW5jdGlvbmFsKCd2LWxpc3QtaXRlbV9fYWN0aW9uLXRleHQnLCAnc3BhbicpOwpleHBvcnQgdmFyIFZMaXN0SXRlbUNvbnRlbnQgPSBjcmVhdGVTaW1wbGVGdW5jdGlvbmFsKCd2LWxpc3QtaXRlbV9fY29udGVudCcsICdkaXYnKTsKZXhwb3J0IHZhciBWTGlzdEl0ZW1UaXRsZSA9IGNyZWF0ZVNpbXBsZUZ1bmN0aW9uYWwoJ3YtbGlzdC1pdGVtX190aXRsZScsICdkaXYnKTsKZXhwb3J0IHZhciBWTGlzdEl0ZW1TdWJ0aXRsZSA9IGNyZWF0ZVNpbXBsZUZ1bmN0aW9uYWwoJ3YtbGlzdC1pdGVtX19zdWJ0aXRsZScsICdkaXYnKTsKZXhwb3J0IHsgVkxpc3QsIFZMaXN0R3JvdXAsIFZMaXN0SXRlbSwgVkxpc3RJdGVtQWN0aW9uLCBWTGlzdEl0ZW1BdmF0YXIsIFZMaXN0SXRlbUljb24sIFZMaXN0SXRlbUdyb3VwIH07CmV4cG9ydCBkZWZhdWx0IHsKICAkX3Z1ZXRpZnlfc3ViY29tcG9uZW50czogewogICAgVkxpc3Q6IFZMaXN0LAogICAgVkxpc3RHcm91cDogVkxpc3RHcm91cCwKICAgIFZMaXN0SXRlbTogVkxpc3RJdGVtLAogICAgVkxpc3RJdGVtQWN0aW9uOiBWTGlzdEl0ZW1BY3Rpb24sCiAgICBWTGlzdEl0ZW1BY3Rpb25UZXh0OiBWTGlzdEl0ZW1BY3Rpb25UZXh0LAogICAgVkxpc3RJdGVtQXZhdGFyOiBWTGlzdEl0ZW1BdmF0YXIsCiAgICBWTGlzdEl0ZW1Db250ZW50OiBWTGlzdEl0ZW1Db250ZW50LAogICAgVkxpc3RJdGVtR3JvdXA6IFZMaXN0SXRlbUdyb3VwLAogICAgVkxpc3RJdGVtSWNvbjogVkxpc3RJdGVtSWNvbiwKICAgIFZMaXN0SXRlbVN1YnRpdGxlOiBWTGlzdEl0ZW1TdWJ0aXRsZSwKICAgIFZMaXN0SXRlbVRpdGxlOiBWTGlzdEl0ZW1UaXRsZQogIH0KfTs="}, {"version": 3, "names": ["createSimpleFunctional", "VList", "VListGroup", "VListItem", "VListItemGroup", "VListItemAction", "VListItemAvatar", "VListItemIcon", "VListItemActionText", "VListItemContent", "VListItemTitle", "VListItemSubtitle", "$_vuetify_subcomponents"], "sources": ["../../../src/components/VList/index.ts"], "sourcesContent": ["import { createSimpleFunctional } from '../../util/helpers'\n\nimport VList from './VList'\nimport VListGroup from './VListGroup'\nimport VListItem from './VListItem'\nimport VListItemGroup from './VListItemGroup'\nimport VListItemAction from './VListItemAction'\nimport VListItemAvatar from './VListItemAvatar'\nimport VListItemIcon from './VListItemIcon'\n\nexport const VListItemActionText = createSimpleFunctional('v-list-item__action-text', 'span')\nexport const VListItemContent = createSimpleFunctional('v-list-item__content', 'div')\nexport const VListItemTitle = createSimpleFunctional('v-list-item__title', 'div')\nexport const VListItemSubtitle = createSimpleFunctional('v-list-item__subtitle', 'div')\n\nexport {\n  VList,\n  VListGroup,\n  VListItem,\n  VListItemAction,\n  VListItemAvatar,\n  VListItemIcon,\n  VListItemGroup,\n}\n\nexport default {\n  $_vuetify_subcomponents: {\n    VList,\n    VListGroup,\n    VListItem,\n    VListItemAction,\n    VListItemActionText,\n    VListItemAvatar,\n    VListItemContent,\n    VListItemGroup,\n    VListItemIcon,\n    VListItemSubtitle,\n    VListItemTitle,\n  },\n}\n"], "mappings": "AAAA,SAASA,sBAAT,QAAuC,oBAAvC;AAEA,OAAOC,KAAP,MAAkB,SAAlB;AACA,OAAOC,UAAP,MAAuB,cAAvB;AACA,OAAOC,SAAP,MAAsB,aAAtB;AACA,OAAOC,cAAP,MAA2B,kBAA3B;AACA,OAAOC,eAAP,MAA4B,mBAA5B;AACA,OAAOC,eAAP,MAA4B,mBAA5B;AACA,OAAOC,aAAP,MAA0B,iBAA1B;AAEA,OAAO,IAAMC,mBAAmB,GAAGR,sBAAsB,CAAC,0BAAD,EAA6B,MAA7B,CAAlD;AACP,OAAO,IAAMS,gBAAgB,GAAGT,sBAAsB,CAAC,sBAAD,EAAyB,KAAzB,CAA/C;AACP,OAAO,IAAMU,cAAc,GAAGV,sBAAsB,CAAC,oBAAD,EAAuB,KAAvB,CAA7C;AACP,OAAO,IAAMW,iBAAiB,GAAGX,sBAAsB,CAAC,uBAAD,EAA0B,KAA1B,CAAhD;AAEP,SACEC,KADF,EAEEC,UAFF,EAGEC,SAHF,EAIEE,eAJF,EAKEC,eALF,EAMEC,aANF,EAOEH,cAPF;AAUA,eAAe;EACbQ,uBAAuB,EAAE;IACvBX,KADuB,EACvBA,KADuB;IAEvBC,UAFuB,EAEvBA,UAFuB;IAGvBC,SAHuB,EAGvBA,SAHuB;IAIvBE,eAJuB,EAIvBA,eAJuB;IAKvBG,mBALuB,EAKvBA,mBALuB;IAMvBF,eANuB,EAMvBA,eANuB;IAOvBG,gBAPuB,EAOvBA,gBAPuB;IAQvBL,cARuB,EAQvBA,cARuB;IASvBG,aATuB,EASvBA,aATuB;IAUvBI,iBAVuB,EAUvBA,iBAVuB;IAWvBD,cAAA,EAAAA;EAXuB;AADZ,CAAf", "ignoreList": []}]}