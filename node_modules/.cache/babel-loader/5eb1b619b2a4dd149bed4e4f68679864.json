{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTextField/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTextField/index.js", "mtime": 1757335236982}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZUZXh0RmllbGQgZnJvbSAnLi9WVGV4dEZpZWxkJzsKZXhwb3J0IHsgVlRleHRGaWVsZCB9OwpleHBvcnQgZGVmYXVsdCBWVGV4dEZpZWxkOw=="}, {"version": 3, "names": ["VTextField"], "sources": ["../../../src/components/VTextField/index.ts"], "sourcesContent": ["import VTextField from './VTextField'\n\nexport { VTextField }\nexport default VTextField\n"], "mappings": "AAAA,OAAOA,UAAP,MAAuB,cAAvB;AAEA,SAASA,UAAT;AACA,eAAeA,UAAf", "ignoreList": []}]}