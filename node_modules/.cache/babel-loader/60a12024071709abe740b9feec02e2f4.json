{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/de.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/de.js", "mtime": 1757335235438}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/de.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Keine Elemente gefunden',\n    loadingText: 'Lade Elemente...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Zeilen pro Seite:',\n    ariaLabel: {\n      sortDescending: 'Absteigend sortiert.',\n      sortAscending: 'Aufsteigend sortiert.',\n      sortNone: 'Nicht sortiert.',\n      activateNone: 'Aktivieren um Sortierung zu entfernen.',\n      activateDescending: 'Aktivieren um absteigend zu sortieren.',\n      activateAscending: 'Aktivieren um aufsteigend zu sortieren.',\n    },\n    sortBy: 'Sortiere nach',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Elemente pro Seite:',\n    itemsPerPageAll: 'Alle',\n    nextPage: 'Nächste Seite',\n    prevPage: 'Vorherige Seite',\n    firstPage: 'Erste Seite',\n    lastPage: 'Letzte Seite',\n    pageText: '{0}-{1} von {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} ausgewählt',\n    nextMonthAriaLabel: 'Nächsten Monat',\n    nextYearAriaLabel: 'Nächstes Jahr',\n    prevMonthAriaLabel: 'Vorheriger Monat',\n    prevYearAriaLabel: 'Vorheriges Jahr',\n  },\n  noDataText: 'Keine Daten vorhanden',\n  carousel: {\n    prev: 'Vorheriges Bild',\n    next: 'Nächstes Bild',\n    ariaLabel: {\n      delimiter: 'Element {0} von {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} mehr',\n  },\n  fileInput: {\n    counter: '{0} Dateien',\n    counterSize: '{0} Dateien ({1} gesamt)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Seitennavigation',\n      next: 'Nächste Seite',\n      previous: 'Vorherige Seite',\n      page: 'Gehe zu Seite {0}',\n      currentPage: 'Aktuelle Seite, Seite {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,WADM;EAEbC,KAAK,EAAE,WAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,yBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,mBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,sBADP;MAETC,aAAa,EAAE,uBAFN;MAGTC,QAAQ,EAAE,iBAHD;MAITC,YAAY,EAAE,wCAJL;MAKTC,kBAAkB,EAAE,wCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,eAHA;IAIVC,QAAQ,EAAE,iBAJA;IAKVC,SAAS,EAAE,aALD;IAMVC,QAAQ,EAAE,cANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,gBADL;IAEVC,kBAAkB,EAAE,gBAFV;IAGVC,iBAAiB,EAAE,eAHT;IAIVC,kBAAkB,EAAE,kBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,uBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,kBADA;MAETX,IAAI,EAAE,eAFG;MAGTY,QAAQ,EAAE,iBAHD;MAITC,IAAI,EAAE,mBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}