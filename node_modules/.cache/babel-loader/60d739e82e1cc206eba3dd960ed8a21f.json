{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCheckbox/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCheckbox/index.js", "mtime": 1757335236815}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZDaGVja2JveCBmcm9tICcuL1ZDaGVja2JveCc7CmltcG9ydCBWU2ltcGxlQ2hlY2tib3ggZnJvbSAnLi9WU2ltcGxlQ2hlY2tib3gnOwpleHBvcnQgeyBWQ2hlY2tib3gsIFZTaW1wbGVDaGVja2JveCB9OwpleHBvcnQgZGVmYXVsdCB7CiAgJF92dWV0aWZ5X3N1YmNvbXBvbmVudHM6IHsKICAgIFZDaGVja2JveDogVkNoZWNrYm94LAogICAgVlNpbXBsZUNoZWNrYm94OiBWU2ltcGxlQ2hlY2tib3gKICB9Cn07"}, {"version": 3, "names": ["VCheckbox", "VSimpleCheckbox", "$_vuetify_subcomponents"], "sources": ["../../../src/components/VCheckbox/index.ts"], "sourcesContent": ["import VCheckbox from './VCheckbox'\nimport VSimpleCheckbox from './VSimpleCheckbox'\n\nexport { VCheckbox, VSimpleCheckbox }\nexport default {\n  $_vuetify_subcomponents: {\n    VCheckbox,\n    VSimpleCheckbox,\n  },\n}\n"], "mappings": "AAAA,OAAOA,SAAP,MAAsB,aAAtB;AACA,OAAOC,eAAP,MAA4B,mBAA5B;AAEA,SAASD,SAAT,EAAoBC,eAApB;AACA,eAAe;EACbC,uBAAuB,EAAE;IACvBF,SADuB,EACvBA,SADuB;IAEvBC,eAAA,EAAAA;EAFuB;AADZ,CAAf", "ignoreList": []}]}