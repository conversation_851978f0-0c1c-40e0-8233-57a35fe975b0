{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMenu/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMenu/index.js", "mtime": 1757335236894}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZNZW51IGZyb20gJy4vVk1lbnUnOwpleHBvcnQgeyBWTWVudSB9OwpleHBvcnQgZGVmYXVsdCBWTWVudTs="}, {"version": 3, "names": ["VMenu"], "sources": ["../../../src/components/VMenu/index.ts"], "sourcesContent": ["import VMenu from './VMenu'\n\nexport { VMenu }\nexport default VMenu\n"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,SAAlB;AAEA,SAASA,KAAT;AACA,eAAeA,KAAf", "ignoreList": []}]}