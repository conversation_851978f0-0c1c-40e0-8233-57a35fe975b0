{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/MediaDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/MediaDialog.vue", "mtime": 1703055368000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "rules", "urlRule", "v", "test", "showDialog", "mediaTab", "media", "video", "url", "title", "audio", "h5File", "methods", "show", "addMedia", "_context", "_context2", "_context3", "_startsWithInstanceProperty", "call", "$toasted", "error", "window", "teduBoard", "addVideoFile", "addElement", "TEduBoard", "TEduBoardElementType", "TEDU_BOARD_ELEMENT_AUDIO", "addH5File"], "sources": ["src/components/ToolbarDialog/MediaDialog.vue"], "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n    <v-card>\n      <v-card-title class=\"headline lighten-2\"> 多媒体资源 </v-card-title>\n      <v-card-text>\n        <v-tabs v-model=\"mediaTab\">\n          <v-tab value=\"video\">MP4视频</v-tab>\n          <v-tab value=\"audio\">MP3音频</v-tab>\n          <v-tab value=\"h5File\">H5网页</v-tab>\n        </v-tabs>\n        <v-row>\n          <v-col cols=\"12\" md=\"12\">\n            <v-tabs-items v-model=\"mediaTab\">\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"media['video'].url\"\n                  label=\"mp4视频URL\"\n                  prepend-icon=\"mdi-video\"\n                ></v-text-field>\n                <v-text-field\n                  v-model=\"media['video'].title\"\n                  label=\"mp4标题\"\n                  prepend-icon=\"mdi-format-title\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"media['audio'].url\"\n                  label=\"MP3音频URL\"\n                  prepend-icon=\"mdi-music\"\n                ></v-text-field>\n                <v-text-field\n                  v-model=\"media['audio'].title\"\n                  label=\"mp3标题\"\n                  prepend-icon=\"mdi-format-title\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"media['h5File'].url\"\n                  label=\"H5网页URL\"\n                  prepend-icon=\"mdi-language-html5\"\n                ></v-text-field>\n                <v-text-field\n                  v-model=\"media['audio'].title\"\n                  label=\"H5网页标题\"\n                  prepend-icon=\"mdi-format-title\"\n                ></v-text-field>\n              </v-tab-item>\n            </v-tabs-items>\n          </v-col>\n        </v-row>\n      </v-card-text>\n\n      <v-divider></v-divider>\n\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn color=\"primary\" text @click=\"addMedia\">添加</v-btn>\n\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      rules: {\n        urlRule: [\n          (v) => !!v || \"url is required\",\n          (v) => /^https:\\/\\//.test(v) || \"url必须是https协议\",\n        ],\n      },\n      showDialog: false,\n      mediaTab: \"video\",\n      media: {\n        video: {\n          url: \"\",\n          title: \"\",\n        },\n        audio: {\n          url: \"\",\n          title: \"\",\n        },\n        h5File: {\n          url: \"\",\n          title: \"\",\n        },\n      },\n    };\n  },\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n    addMedia() {\n      switch (this.mediaTab) {\n        case 0:\n          if (!this.media.video.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.addVideoFile(\n            this.media.video.url,\n            this.media.video.title\n          );\n          this.media['video'].url = \"\";\n          this.media['video'].title = \"\";\n          break;\n        case 1:\n          if (!this.media.audio.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_AUDIO,\n            this.media.audio.url,\n            {\n              title: this.media.audio.title,\n            }\n          );\n          this.media['audio'].url = \"\";\n          this.media['audio'].title = \"\";\n          break;\n        case 2:\n          if (!this.media.h5File.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.addH5File(\n            this.media.h5File.url,\n            this.media.h5File.title\n          );\n          this.media['h5File'].url = \"\";\n          this.media['h5File'].title = \"\";\n          break;\n      }\n\n      this.showDialog = false;\n    },\n  },\n};\n</script>"], "mappings": ";;AAmEA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;QACAC,OAAA,GACA,UAAAC,CAAA;UAAA,SAAAA,CAAA;QAAA,GACA,UAAAA,CAAA;UAAA,qBAAAC,IAAA,CAAAD,CAAA;QAAA;MAEA;MACAE,UAAA;MACAC,QAAA;MACAC,KAAA;QACAC,KAAA;UACAC,GAAA;UACAC,KAAA;QACA;QACAC,KAAA;UACAF,GAAA;UACAC,KAAA;QACA;QACAE,MAAA;UACAH,GAAA;UACAC,KAAA;QACA;MACA;IACA;EACA;EACAG,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAT,UAAA;IACA;IACAU,QAAA,WAAAA,SAAA;MAAA,IAAAC,QAAA,EAAAC,SAAA,EAAAC,SAAA;MACA,aAAAZ,QAAA;QACA;UACA,KAAAa,2BAAA,CAAAH,QAAA,QAAAT,KAAA,CAAAC,KAAA,CAAAC,GAAA,EAAAW,IAAA,CAAAJ,QAAA;YACA,KAAAK,QAAA,CAAAC,KAAA;YACA;UACA;UACAC,MAAA,CAAAC,SAAA,CAAAC,YAAA,CACA,KAAAlB,KAAA,CAAAC,KAAA,CAAAC,GAAA,EACA,KAAAF,KAAA,CAAAC,KAAA,CAAAE,KACA;UACA,KAAAH,KAAA,UAAAE,GAAA;UACA,KAAAF,KAAA,UAAAG,KAAA;UACA;QACA;UACA,KAAAS,2BAAA,CAAAF,SAAA,QAAAV,KAAA,CAAAI,KAAA,CAAAF,GAAA,EAAAW,IAAA,CAAAH,SAAA;YACA,KAAAI,QAAA,CAAAC,KAAA;YACA;UACA;UACAC,MAAA,CAAAC,SAAA,CAAAE,UAAA,CACAC,SAAA,CAAAC,oBAAA,CAAAC,wBAAA,EACA,KAAAtB,KAAA,CAAAI,KAAA,CAAAF,GAAA,EACA;YACAC,KAAA,OAAAH,KAAA,CAAAI,KAAA,CAAAD;UACA,CACA;UACA,KAAAH,KAAA,UAAAE,GAAA;UACA,KAAAF,KAAA,UAAAG,KAAA;UACA;QACA;UACA,KAAAS,2BAAA,CAAAD,SAAA,QAAAX,KAAA,CAAAK,MAAA,CAAAH,GAAA,EAAAW,IAAA,CAAAF,SAAA;YACA,KAAAG,QAAA,CAAAC,KAAA;YACA;UACA;UACAC,MAAA,CAAAC,SAAA,CAAAM,SAAA,CACA,KAAAvB,KAAA,CAAAK,MAAA,CAAAH,GAAA,EACA,KAAAF,KAAA,CAAAK,MAAA,CAAAF,KACA;UACA,KAAAH,KAAA,WAAAE,GAAA;UACA,KAAAF,KAAA,WAAAG,KAAA;UACA;MACA;MAEA,KAAAL,UAAA;IACA;EACA;AACA", "ignoreList": []}]}