{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/delayable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/delayable/index.js", "mtime": 1757335237075}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9wYXJzZUludCBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL3BhcnNlLWludCI7CmltcG9ydCBfc2V0VGltZW91dCBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL3NldC10aW1lb3V0IjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwovKioKICogRGVsYXlhYmxlCiAqCiAqIEBtaXhpbgogKgogKiBDaGFuZ2VzIHRoZSBvcGVuIG9yIGNsb3NlIGRlbGF5IHRpbWUgZm9yIGVsZW1lbnRzCiAqLwoKZXhwb3J0IGRlZmF1bHQgVnVlLmV4dGVuZCgpLmV4dGVuZCh7CiAgbmFtZTogJ2RlbGF5YWJsZScsCiAgcHJvcHM6IHsKICAgIG9wZW5EZWxheTogewogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6IDAKICAgIH0sCiAgICBjbG9zZURlbGF5OiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICAgICJkZWZhdWx0IjogMAogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG9wZW5UaW1lb3V0OiB1bmRlZmluZWQsCiAgICAgIGNsb3NlVGltZW91dDogdW5kZWZpbmVkCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgLyoqCiAgICAgKiBDbGVhciBhbnkgcGVuZGluZyBkZWxheSB0aW1lcnMgZnJvbSBleGVjdXRpbmcKICAgICAqLwogICAgY2xlYXJEZWxheTogZnVuY3Rpb24gY2xlYXJEZWxheSgpIHsKICAgICAgY2xlYXJUaW1lb3V0KHRoaXMub3BlblRpbWVvdXQpOwogICAgICBjbGVhclRpbWVvdXQodGhpcy5jbG9zZVRpbWVvdXQpOwogICAgfSwKICAgIC8qKgogICAgICogUnVucyBjYWxsYmFjayBhZnRlciBhIHNwZWNpZmllZCBkZWxheQogICAgICovCiAgICBydW5EZWxheTogZnVuY3Rpb24gcnVuRGVsYXkodHlwZSwgY2IpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5jbGVhckRlbGF5KCk7CiAgICAgIHZhciBkZWxheSA9IF9wYXJzZUludCh0aGlzWyIiLmNvbmNhdCh0eXBlLCAiRGVsYXkiKV0sIDEwKTsKICAgICAgdGhpc1siIi5jb25jYXQodHlwZSwgIlRpbWVvdXQiKV0gPSBfc2V0VGltZW91dChjYiB8fCBmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMuaXNBY3RpdmUgPSB7CiAgICAgICAgICBvcGVuOiB0cnVlLAogICAgICAgICAgY2xvc2U6IGZhbHNlCiAgICAgICAgfVt0eXBlXTsKICAgICAgfSwgZGVsYXkpOwogICAgfQogIH0KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "extend", "name", "props", "openDelay", "type", "Number", "String", "close<PERSON><PERSON><PERSON>", "data", "openTimeout", "undefined", "closeTimeout", "methods", "clear<PERSON>elay", "clearTimeout", "runDelay", "cb", "_this", "delay", "_parseInt", "concat", "_setTimeout", "isActive", "open", "close"], "sources": ["../../../src/mixins/delayable/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\n/**\n * Delayable\n *\n * @mixin\n *\n * Changes the open or close delay time for elements\n */\nexport default Vue.extend<Vue & { isActive?: boolean }>().extend({\n  name: 'delayable',\n\n  props: {\n    openDelay: {\n      type: [Number, String],\n      default: 0,\n    },\n    closeDelay: {\n      type: [Number, String],\n      default: 0,\n    },\n  },\n\n  data: () => ({\n    openTimeout: undefined as number | undefined,\n    closeTimeout: undefined as number | undefined,\n  }),\n\n  methods: {\n    /**\n     * Clear any pending delay timers from executing\n     */\n    clearDelay (): void {\n      clearTimeout(this.openTimeout)\n      clearTimeout(this.closeTimeout)\n    },\n    /**\n     * Runs callback after a specified delay\n     */\n    runDelay (type: 'open' | 'close', cb?: () => void): void {\n      this.clearDelay()\n\n      const delay = parseInt((this as any)[`${type}Delay`], 10)\n\n      ;(this as any)[`${type}Timeout`] = setTimeout(cb || (() => {\n        this.isActive = { open: true, close: false }[type]\n      }), delay)\n    },\n  },\n})\n"], "mappings": ";;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA;;;;;;AAMG;;AACH,eAAeA,GAAG,CAACC,MAAJ,GAA2CA,MAA3C,CAAkD;EAC/DC,IAAI,EAAE,WADyD;EAG/DC,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADG;MAET,WAAS;IAFA,CADN;IAKLC,UAAU,EAAE;MACVH,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADI;MAEV,WAAS;IAFC;EALP,CAHwD;EAc/DE,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,WAAW,EAAEC,SADF;MAEXC,YAAY,EAAED;IAFH,CAAP;EAAA,CAdyD;EAmB/DE,OAAO,EAAE;IACP;;AAEG;IACHC,UAAU,WAAVA,UAAUA,CAAA;MACRC,YAAY,CAAC,KAAKL,WAAN,CAAZ;MACAK,YAAY,CAAC,KAAKH,YAAN,CAAZ;IACD,CAPM;IAQP;;AAEG;IACHI,QAAQ,WAARA,QAAQA,CAAEX,IAAF,EAA0BY,EAA1B,EAAyC;MAAA,IAAAC,KAAA;MAC/C,KAAKJ,UAAL;MAEA,IAAMK,KAAK,GAAGC,SAAA,CAAU,QAAAC,MAAA,CAAgBhB,IAAI,WAAtB,EAAgC,EAAhC,CAAtB;MAEE,QAAAgB,MAAA,CAAgBhB,IAAI,aAApB,GAAiCiB,WAAA,CAAWL,EAAE,IAAK,YAAK;QACxDC,KAAA,CAAKK,QAAL,GAAgB;UAAEC,IAAI,EAAE,IAAR;UAAcC,KAAK,EAAE;QAArB,EAA6BpB,IAA7B,CAAhB;MACD,CAF4C,EAEzCc,KAFyC,CAA3C;IAGH;EAnBM;AAnBsD,CAAlD,CAAf", "ignoreList": []}]}