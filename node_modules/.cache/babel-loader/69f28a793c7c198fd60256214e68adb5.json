{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/service/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/service/index.js", "mtime": 1757335237306}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9jbGFzc0NhbGxDaGVjayBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2NsYXNzQ2FsbENoZWNrLmpzIjsKaW1wb3J0IF9jcmVhdGVDbGFzcyBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2NyZWF0ZUNsYXNzLmpzIjsKZXhwb3J0IHZhciBTZXJ2aWNlID0gLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHsKICBmdW5jdGlvbiBTZXJ2aWNlKCkgewogICAgX2NsYXNzQ2FsbENoZWNrKHRoaXMsIFNlcnZpY2UpOwogICAgdGhpcy5mcmFtZXdvcmsgPSB7fTsKICB9CiAgcmV0dXJuIF9jcmVhdGVDbGFzcyhTZXJ2aWNlLCBbewogICAga2V5OiAiaW5pdCIsCiAgICB2YWx1ZTogZnVuY3Rpb24gaW5pdChyb290LCBzc3JDb250ZXh0KSB7fQogIH1dKTsKfSgpOw=="}, {"version": 3, "names": ["Service", "_classCallCheck", "framework", "_createClass", "key", "value", "init", "root", "ssrContext"], "sources": ["../../../src/services/service/index.ts"], "sourcesContent": ["// Contracts\nimport { VuetifyServiceContract } from 'vuetify/types/services/index'\n\n// Types\nimport Vue from 'vue'\n\nexport class Service implements VuetifyServiceContract {\n  framework = {}\n\n  init (root: Vue, ssrContext?: object) {}\n}\n"], "mappings": ";;AAMA,WAAaA,OAAP;EAAN,SAAAA,QAAA;IAAAC,eAAA,OAAAD,OAAA;IACE,KAAAE,SAAA,GAAY,EAAZ;EAGD;EAAA,OAAAC,YAAA,CAAAH,OAAA;IAAAI,GAAA;IAAAC,KAAA,EADC,SAAAC,IAAIA,CAAEC,IAAF,EAAaC,UAAb,EAAgC,CAAI;EAAA;AAAA", "ignoreList": []}]}