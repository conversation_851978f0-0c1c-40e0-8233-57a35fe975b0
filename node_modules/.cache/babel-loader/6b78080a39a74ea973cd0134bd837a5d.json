{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/objectWithoutPropertiesLoose.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/objectWithoutPropertiesLoose.js", "mtime": 1757335237663}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9pbmRleE9mSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJjb3JlLWpzLXB1cmUvZmVhdHVyZXMvaW5zdGFuY2UvaW5kZXgtb2YuanMiOwpmdW5jdGlvbiBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZShyLCBlKSB7CiAgaWYgKG51bGwgPT0gcikgcmV0dXJuIHt9OwogIHZhciB0ID0ge307CiAgZm9yICh2YXIgbiBpbiByKSBpZiAoe30uaGFzT3duUHJvcGVydHkuY2FsbChyLCBuKSkgewogICAgaWYgKC0xICE9PSBfaW5kZXhPZkluc3RhbmNlUHJvcGVydHkoZSkuY2FsbChlLCBuKSkgY29udGludWU7CiAgICB0W25dID0gcltuXTsKICB9CiAgcmV0dXJuIHQ7Cn0KZXhwb3J0IHsgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UgYXMgZGVmYXVsdCB9Ow=="}, {"version": 3, "names": ["_indexOfInstanceProperty", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/objectWithoutPropertiesLoose.js"], "sourcesContent": ["import _indexOfInstanceProperty from \"core-js-pure/features/instance/index-of.js\";\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== _indexOfInstanceProperty(e).call(e, n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,4CAA4C;AACjF,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC3C,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EACxB,IAAIE,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IACjD,IAAI,CAAC,CAAC,KAAKL,wBAAwB,CAACG,CAAC,CAAC,CAACI,IAAI,CAACJ,CAAC,EAAEE,CAAC,CAAC,EAAE;IACnDD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACb;EACA,OAAOD,CAAC;AACV;AACA,SAASH,6BAA6B,IAAIO,OAAO", "ignoreList": []}]}