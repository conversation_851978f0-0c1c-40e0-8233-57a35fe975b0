{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCard/VCard.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCard/VCard.js", "mtime": 1757335237684}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF9mbGF0SW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL2ZsYXQiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiOwovLyBTdHlsZXMKaW1wb3J0ICIuLi8uLi8uLi9zcmMvY29tcG9uZW50cy9WQ2FyZC9WQ2FyZC5zYXNzIjsgLy8gRXh0ZW5zaW9ucwoKaW1wb3J0IFZTaGVldCBmcm9tICcuLi9WU2hlZXQnOyAvLyBNaXhpbnMKCmltcG9ydCBMb2FkYWJsZSBmcm9tICcuLi8uLi9taXhpbnMvbG9hZGFibGUnOwppbXBvcnQgUm91dGFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL3JvdXRhYmxlJzsgLy8gSGVscGVycwoKaW1wb3J0IG1peGlucyBmcm9tICcuLi8uLi91dGlsL21peGlucyc7Ci8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBtaXhpbnMoTG9hZGFibGUsIFJvdXRhYmxlLCBWU2hlZXQpLmV4dGVuZCh7CiAgbmFtZTogJ3YtY2FyZCcsCiAgcHJvcHM6IHsKICAgIGZsYXQ6IEJvb2xlYW4sCiAgICBob3ZlcjogQm9vbGVhbiwKICAgIGltZzogU3RyaW5nLAogICAgbGluazogQm9vbGVhbiwKICAgIGxvYWRlckhlaWdodDogewogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6IDQKICAgIH0sCiAgICByYWlzZWQ6IEJvb2xlYW4KICB9LAogIGNvbXB1dGVkOiB7CiAgICBjbGFzc2VzOiBmdW5jdGlvbiBjbGFzc2VzKCkgewogICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHsKICAgICAgICAndi1jYXJkJzogdHJ1ZQogICAgICB9LCBSb3V0YWJsZS5vcHRpb25zLmNvbXB1dGVkLmNsYXNzZXMuY2FsbCh0aGlzKSksIHt9LCB7CiAgICAgICAgJ3YtY2FyZC0tZmxhdCc6IF9mbGF0SW5zdGFuY2VQcm9wZXJ0eSh0aGlzKSwKICAgICAgICAndi1jYXJkLS1ob3Zlcic6IHRoaXMuaG92ZXIsCiAgICAgICAgJ3YtY2FyZC0tbGluayc6IHRoaXMuaXNDbGlja2FibGUsCiAgICAgICAgJ3YtY2FyZC0tbG9hZGluZyc6IHRoaXMubG9hZGluZywKICAgICAgICAndi1jYXJkLS1kaXNhYmxlZCc6IHRoaXMuZGlzYWJsZWQsCiAgICAgICAgJ3YtY2FyZC0tcmFpc2VkJzogdGhpcy5yYWlzZWQKICAgICAgfSwgVlNoZWV0Lm9wdGlvbnMuY29tcHV0ZWQuY2xhc3Nlcy5jYWxsKHRoaXMpKTsKICAgIH0sCiAgICBzdHlsZXM6IGZ1bmN0aW9uIHN0eWxlcygpIHsKICAgICAgdmFyIHN0eWxlID0gX29iamVjdFNwcmVhZCh7fSwgVlNoZWV0Lm9wdGlvbnMuY29tcHV0ZWQuc3R5bGVzLmNhbGwodGhpcykpOwogICAgICBpZiAodGhpcy5pbWcpIHsKICAgICAgICBzdHlsZS5iYWNrZ3JvdW5kID0gInVybChcIiIuY29uY2F0KHRoaXMuaW1nLCAiXCIpIGNlbnRlciBjZW50ZXIgLyBjb3ZlciBuby1yZXBlYXQiKTsKICAgICAgfQogICAgICByZXR1cm4gc3R5bGU7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZW5Qcm9ncmVzczogZnVuY3Rpb24gZ2VuUHJvZ3Jlc3MoKSB7CiAgICAgIHZhciByZW5kZXIgPSBMb2FkYWJsZS5vcHRpb25zLm1ldGhvZHMuZ2VuUHJvZ3Jlc3MuY2FsbCh0aGlzKTsKICAgICAgaWYgKCFyZW5kZXIpIHJldHVybiBudWxsOwogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1jYXJkX19wcm9ncmVzcycsCiAgICAgICAga2V5OiAncHJvZ3Jlc3MnCiAgICAgIH0sIFtyZW5kZXJdKTsKICAgIH0KICB9LAogIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgpIHsKICAgIHZhciBfdGhpcyRnZW5lcmF0ZVJvdXRlTGkgPSB0aGlzLmdlbmVyYXRlUm91dGVMaW5rKCksCiAgICAgIHRhZyA9IF90aGlzJGdlbmVyYXRlUm91dGVMaS50YWcsCiAgICAgIGRhdGEgPSBfdGhpcyRnZW5lcmF0ZVJvdXRlTGkuZGF0YTsKICAgIGRhdGEuc3R5bGUgPSB0aGlzLnN0eWxlczsKICAgIGlmICh0aGlzLmlzQ2xpY2thYmxlKSB7CiAgICAgIGRhdGEuYXR0cnMgPSBkYXRhLmF0dHJzIHx8IHt9OwogICAgICBkYXRhLmF0dHJzLnRhYmluZGV4ID0gMDsKICAgIH0KICAgIHJldHVybiBoKHRhZywgdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb2xvciwgZGF0YSksIFt0aGlzLmdlblByb2dyZXNzKCksIHRoaXMuJHNsb3RzWyJkZWZhdWx0Il1dKTsKICB9Cn0pOw=="}, {"version": 3, "names": ["VSheet", "Loadable", "Routable", "mixins", "extend", "name", "props", "flat", "Boolean", "hover", "img", "String", "link", "loaderHeight", "type", "Number", "raised", "computed", "classes", "_objectSpread", "options", "call", "_flatInstanceProperty", "isClickable", "loading", "disabled", "styles", "style", "background", "concat", "methods", "genProgress", "render", "$createElement", "staticClass", "key", "h", "_this$generateRouteLi", "generateRouteLink", "tag", "data", "attrs", "tabindex", "setBackgroundColor", "color", "$slots"], "sources": ["../../../src/components/VCard/VCard.ts"], "sourcesContent": ["// Styles\nimport './VCard.sass'\n\n// Extensions\nimport VSheet from '../VSheet'\n\n// Mixins\nimport Loadable from '../../mixins/loadable'\nimport Routable from '../../mixins/routable'\n\n// Helpers\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\n\n/* @vue/component */\nexport default mixins(\n  Loadable,\n  Routable,\n  VSheet\n).extend({\n  name: 'v-card',\n\n  props: {\n    flat: Boolean,\n    hover: Boolean,\n    img: String,\n    link: Boolean,\n    loaderHeight: {\n      type: [Number, String],\n      default: 4,\n    },\n    raised: Boolean,\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        'v-card': true,\n        ...Routable.options.computed.classes.call(this),\n        'v-card--flat': this.flat,\n        'v-card--hover': this.hover,\n        'v-card--link': this.isClickable,\n        'v-card--loading': this.loading,\n        'v-card--disabled': this.disabled,\n        'v-card--raised': this.raised,\n        ...VSheet.options.computed.classes.call(this),\n      }\n    },\n    styles (): object {\n      const style: Dictionary<string> = {\n        ...VSheet.options.computed.styles.call(this),\n      }\n\n      if (this.img) {\n        style.background = `url(\"${this.img}\") center center / cover no-repeat`\n      }\n\n      return style\n    },\n  },\n\n  methods: {\n    genProgress () {\n      const render = Loadable.options.methods.genProgress.call(this)\n\n      if (!render) return null\n\n      return this.$createElement('div', {\n        staticClass: 'v-card__progress',\n        key: 'progress',\n      }, [render])\n    },\n  },\n\n  render (h): VNode {\n    const { tag, data } = this.generateRouteLink()\n\n    data.style = this.styles\n\n    if (this.isClickable) {\n      data.attrs = data.attrs || {}\n      data.attrs.tabindex = 0\n    }\n\n    return h(tag, this.setBackgroundColor(this.color, data), [\n      this.genProgress(),\n      this.$slots.default,\n    ])\n  },\n})\n"], "mappings": ";;;AAAA;AACA,OAAO,0CAAP,C,CAEA;;AACA,OAAOA,MAAP,MAAmB,WAAnB,C,CAEA;;AACA,OAAOC,QAAP,MAAqB,uBAArB;AACA,OAAOC,QAAP,MAAqB,uBAArB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAKA;;AACA,eAAeA,MAAM,CACnBF,QADmB,EAEnBC,QAFmB,EAGnBF,MAHmB,CAAN,CAIbI,MAJa,CAIN;EACPC,IAAI,EAAE,QADC;EAGPC,KAAK,EAAE;IACLC,IAAI,EAAEC,OADD;IAELC,KAAK,EAAED,OAFF;IAGLE,GAAG,EAAEC,MAHA;IAILC,IAAI,EAAEJ,OAJD;IAKLK,YAAY,EAAE;MACZC,IAAI,EAAE,CAACC,MAAD,EAASJ,MAAT,CADM;MAEZ,WAAS;IAFG,CALT;IASLK,MAAM,EAAER;EATH,CAHA;EAePS,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA;QACE,UAAU;MADL,GAEFjB,QAAQ,CAACkB,OAAT,CAAiBH,QAAjB,CAA0BC,OAA1B,CAAkCG,IAAlC,CAAuC,IAAvC,CAFE;QAGL,gBAAAC,qBAAA,CAAgB,KAHX;QAIL,iBAAiB,KAAKb,KAJjB;QAKL,gBAAgB,KAAKc,WALhB;QAML,mBAAmB,KAAKC,OANnB;QAOL,oBAAoB,KAAKC,QAPpB;QAQL,kBAAkB,KAAKT;MARlB,GASFhB,MAAM,CAACoB,OAAP,CAAeH,QAAf,CAAwBC,OAAxB,CAAgCG,IAAhC,CAAqC,IAArC;IAEN,CAbO;IAcRK,MAAM,WAANA,MAAMA,CAAA;MACJ,IAAMC,KAAK,GAAAR,aAAA,KACNnB,MAAM,CAACoB,OAAP,CAAeH,QAAf,CAAwBS,MAAxB,CAA+BL,IAA/B,CAAoC,IAApC,EADL;MAIA,IAAI,KAAKX,GAAT,EAAc;QACZiB,KAAK,CAACC,UAAN,YAAAC,MAAA,CAA2B,KAAKnB,GAAG,wCAAnC;MACD;MAED,OAAOiB,KAAP;IACD;EAxBO,CAfH;EA0CPG,OAAO,EAAE;IACPC,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAMC,MAAM,GAAG/B,QAAQ,CAACmB,OAAT,CAAiBU,OAAjB,CAAyBC,WAAzB,CAAqCV,IAArC,CAA0C,IAA1C,CAAf;MAEA,IAAI,CAACW,MAAL,EAAa,OAAO,IAAP;MAEb,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,kBADmB;QAEhCC,GAAG,EAAE;MAF2B,CAA3B,EAGJ,CAACH,MAAD,CAHI,CAAP;IAID;EAVM,CA1CF;EAuDPA,MAAM,WAANA,MAAMA,CAAEI,CAAF,EAAG;IACP,IAAAC,qBAAA,GAAsB,KAAKC,iBAAL,EAAtB;MAAQC,GAAF,GAAAF,qBAAA,CAAEE,GAAF;MAAOC,IAAA,GAAAH,qBAAA,CAAAG,IAAA;IAEbA,IAAI,CAACb,KAAL,GAAa,KAAKD,MAAlB;IAEA,IAAI,KAAKH,WAAT,EAAsB;MACpBiB,IAAI,CAACC,KAAL,GAAaD,IAAI,CAACC,KAAL,IAAc,EAA3B;MACAD,IAAI,CAACC,KAAL,CAAWC,QAAX,GAAsB,CAAtB;IACD;IAED,OAAON,CAAC,CAACG,GAAD,EAAM,KAAKI,kBAAL,CAAwB,KAAKC,KAA7B,EAAoCJ,IAApC,CAAN,EAAiD,CACvD,KAAKT,WAAL,EADuD,EAEvD,KAAKc,MAAL,WAFuD,CAAjD,CAAR;EAID;AArEM,CAJM,CAAf", "ignoreList": []}]}