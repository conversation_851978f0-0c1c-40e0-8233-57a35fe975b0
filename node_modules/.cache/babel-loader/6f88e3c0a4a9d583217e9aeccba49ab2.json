{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/transitions/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/transitions/index.js", "mtime": 1757335236776}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["createSimpleTransition", "createJavascriptTransition", "ExpandTransitionGenerator", "VCarouselTransition", "VCarouselReverseTransition", "VTabTransition", "VTabReverseTransition", "VMenuTransition", "VFabTransition", "VDialogTransition", "VDialogBottomTransition", "VDialogTopTransition", "VFadeTransition", "VScaleTransition", "VScrollXTransition", "VScrollXReverseTransition", "VScrollYTransition", "VScrollYReverseTransition", "VSlideXTransition", "VSlideXReverseTransition", "VSlideYTransition", "VSlideYReverseTransition", "VExpandTransition", "VExpandXTransition", "$_vuetify_subcomponents"], "sources": ["../../../src/components/transitions/index.ts"], "sourcesContent": ["import {\n  createSimpleTransition,\n  createJavascriptTransition,\n} from './createTransition'\n\nimport ExpandTransitionGenerator from './expand-transition'\n\n// Component specific transitions\nexport const VCarouselTransition = createSimpleTransition('carousel-transition')\nexport const VCarouselReverseTransition = createSimpleTransition('carousel-reverse-transition')\nexport const VTabTransition = createSimpleTransition('tab-transition')\nexport const VTabReverseTransition = createSimpleTransition('tab-reverse-transition')\nexport const VMenuTransition = createSimpleTransition('menu-transition')\nexport const VFabTransition = createSimpleTransition('fab-transition', 'center center', 'out-in')\n\n// Generic transitions\nexport const VDialogTransition = createSimpleTransition('dialog-transition')\nexport const VDialogBottomTransition = createSimpleTransition('dialog-bottom-transition')\nexport const VDialogTopTransition = createSimpleTransition('dialog-top-transition')\nexport const VFadeTransition = createSimpleTransition('fade-transition')\nexport const VScaleTransition = createSimpleTransition('scale-transition')\nexport const VScrollXTransition = createSimpleTransition('scroll-x-transition')\nexport const VScrollXReverseTransition = createSimpleTransition('scroll-x-reverse-transition')\nexport const VScrollYTransition = createSimpleTransition('scroll-y-transition')\nexport const VScrollYReverseTransition = createSimpleTransition('scroll-y-reverse-transition')\nexport const VSlideXTransition = createSimpleTransition('slide-x-transition')\nexport const VSlideXReverseTransition = createSimpleTransition('slide-x-reverse-transition')\nexport const VSlideYTransition = createSimpleTransition('slide-y-transition')\nexport const VSlideYReverseTransition = createSimpleTransition('slide-y-reverse-transition')\n\n// Javascript transitions\nexport const VExpandTransition = createJavascriptTransition('expand-transition', ExpandTransitionGenerator())\nexport const VExpandXTransition = createJavascriptTransition('expand-x-transition', ExpandTransitionGenerator('', true))\n\nexport default {\n  $_vuetify_subcomponents: {\n    VCarouselTransition,\n    VCarouselReverseTransition,\n    VDialogTransition,\n    VDialogBottomTransition,\n    VDialogTopTransition,\n    VFabTransition,\n    VFadeTransition,\n    VMenuTransition,\n    VScaleTransition,\n    VScrollXTransition,\n    VScrollXReverseTransition,\n    VScrollYTransition,\n    VScrollYReverseTransition,\n    VSlideXTransition,\n    VSlideXReverseTransition,\n    VSlideYTransition,\n    VSlideYReverseTransition,\n    VTabReverseTransition,\n    VTabTransition,\n    VExpandTransition,\n    VExpandXTransition,\n  },\n}\n"], "mappings": "AAAA,SACEA,sBADF,EAEEC,0BAFF,QAGO,oBAHP;AAKA,OAAOC,yBAAP,MAAsC,qBAAtC,C,CAEA;;AACA,OAAO,IAAMC,mBAAmB,GAAGH,sBAAsB,CAAC,qBAAD,CAAlD;AACP,OAAO,IAAMI,0BAA0B,GAAGJ,sBAAsB,CAAC,6BAAD,CAAzD;AACP,OAAO,IAAMK,cAAc,GAAGL,sBAAsB,CAAC,gBAAD,CAA7C;AACP,OAAO,IAAMM,qBAAqB,GAAGN,sBAAsB,CAAC,wBAAD,CAApD;AACP,OAAO,IAAMO,eAAe,GAAGP,sBAAsB,CAAC,iBAAD,CAA9C;AACP,OAAO,IAAMQ,cAAc,GAAGR,sBAAsB,CAAC,gBAAD,EAAmB,eAAnB,EAAoC,QAApC,CAA7C,C,CAEP;;AACA,OAAO,IAAMS,iBAAiB,GAAGT,sBAAsB,CAAC,mBAAD,CAAhD;AACP,OAAO,IAAMU,uBAAuB,GAAGV,sBAAsB,CAAC,0BAAD,CAAtD;AACP,OAAO,IAAMW,oBAAoB,GAAGX,sBAAsB,CAAC,uBAAD,CAAnD;AACP,OAAO,IAAMY,eAAe,GAAGZ,sBAAsB,CAAC,iBAAD,CAA9C;AACP,OAAO,IAAMa,gBAAgB,GAAGb,sBAAsB,CAAC,kBAAD,CAA/C;AACP,OAAO,IAAMc,kBAAkB,GAAGd,sBAAsB,CAAC,qBAAD,CAAjD;AACP,OAAO,IAAMe,yBAAyB,GAAGf,sBAAsB,CAAC,6BAAD,CAAxD;AACP,OAAO,IAAMgB,kBAAkB,GAAGhB,sBAAsB,CAAC,qBAAD,CAAjD;AACP,OAAO,IAAMiB,yBAAyB,GAAGjB,sBAAsB,CAAC,6BAAD,CAAxD;AACP,OAAO,IAAMkB,iBAAiB,GAAGlB,sBAAsB,CAAC,oBAAD,CAAhD;AACP,OAAO,IAAMmB,wBAAwB,GAAGnB,sBAAsB,CAAC,4BAAD,CAAvD;AACP,OAAO,IAAMoB,iBAAiB,GAAGpB,sBAAsB,CAAC,oBAAD,CAAhD;AACP,OAAO,IAAMqB,wBAAwB,GAAGrB,sBAAsB,CAAC,4BAAD,CAAvD,C,CAEP;;AACA,OAAO,IAAMsB,iBAAiB,GAAGrB,0BAA0B,CAAC,mBAAD,EAAsBC,yBAAyB,EAA/C,CAApD;AACP,OAAO,IAAMqB,kBAAkB,GAAGtB,0BAA0B,CAAC,qBAAD,EAAwBC,yBAAyB,CAAC,EAAD,EAAK,IAAL,CAAjD,CAArD;AAEP,eAAe;EACbsB,uBAAuB,EAAE;IACvBrB,mBADuB,EACvBA,mBADuB;IAEvBC,0BAFuB,EAEvBA,0BAFuB;IAGvBK,iBAHuB,EAGvBA,iBAHuB;IAIvBC,uBAJuB,EAIvBA,uBAJuB;IAKvBC,oBALuB,EAKvBA,oBALuB;IAMvBH,cANuB,EAMvBA,cANuB;IAOvBI,eAPuB,EAOvBA,eAPuB;IAQvBL,eARuB,EAQvBA,eARuB;IASvBM,gBATuB,EASvBA,gBATuB;IAUvBC,kBAVuB,EAUvBA,kBAVuB;IAWvBC,yBAXuB,EAWvBA,yBAXuB;IAYvBC,kBAZuB,EAYvBA,kBAZuB;IAavBC,yBAbuB,EAavBA,yBAbuB;IAcvBC,iBAduB,EAcvBA,iBAduB;IAevBC,wBAfuB,EAevBA,wBAfuB;IAgBvBC,iBAhBuB,EAgBvBA,iBAhBuB;IAiBvBC,wBAjBuB,EAiBvBA,wBAjBuB;IAkBvBf,qBAlBuB,EAkBvBA,qBAlBuB;IAmBvBD,cAnBuB,EAmBvBA,cAnBuB;IAoBvBiB,iBApBuB,EAoBvBA,iBApBuB;IAqBvBC,kBAAA,EAAAA;EArBuB;AADZ,CAAf", "ignoreList": []}]}