{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/intersectable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/intersectable/index.js", "mtime": 1757335237124}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gRGlyZWN0aXZlcwppbXBvcnQgSW50ZXJzZWN0IGZyb20gJy4uLy4uL2RpcmVjdGl2ZXMvaW50ZXJzZWN0JzsgLy8gVXRpbGl0aWVzCgppbXBvcnQgeyBjb25zb2xlV2FybiB9IGZyb20gJy4uLy4uL3V0aWwvY29uc29sZSc7IC8vIFR5cGVzCgppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGludGVyc2VjdGFibGUob3B0aW9ucykgewogIHJldHVybiBWdWUuZXh0ZW5kKHsKICAgIG5hbWU6ICdpbnRlcnNlY3RhYmxlJywKICAgIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgaXNJbnRlcnNlY3Rpbmc6IGZhbHNlCiAgICAgIH07CiAgICB9LAogICAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgICAgSW50ZXJzZWN0Lmluc2VydGVkKHRoaXMuJGVsLCB7CiAgICAgICAgbmFtZTogJ2ludGVyc2VjdCcsCiAgICAgICAgdmFsdWU6IHRoaXMub25PYnNlcnZlCiAgICAgIH0sIHRoaXMuJHZub2RlKTsKICAgIH0sCiAgICBkZXN0cm95ZWQ6IGZ1bmN0aW9uIGRlc3Ryb3llZCgpIHsKICAgICAgSW50ZXJzZWN0LnVuYmluZCh0aGlzLiRlbCwgewogICAgICAgIG5hbWU6ICdpbnRlcnNlY3QnLAogICAgICAgIHZhbHVlOiB0aGlzLm9uT2JzZXJ2ZQogICAgICB9LCB0aGlzLiR2bm9kZSk7CiAgICB9LAogICAgbWV0aG9kczogewogICAgICBvbk9ic2VydmU6IGZ1bmN0aW9uIG9uT2JzZXJ2ZShlbnRyaWVzLCBvYnNlcnZlciwgaXNJbnRlcnNlY3RpbmcpIHsKICAgICAgICB0aGlzLmlzSW50ZXJzZWN0aW5nID0gaXNJbnRlcnNlY3Rpbmc7CiAgICAgICAgaWYgKCFpc0ludGVyc2VjdGluZykgcmV0dXJuOwogICAgICAgIGZvciAodmFyIGkgPSAwLCBsZW5ndGggPSBvcHRpb25zLm9uVmlzaWJsZS5sZW5ndGg7IGkgPCBsZW5ndGg7IGkrKykgewogICAgICAgICAgdmFyIGNhbGxiYWNrID0gdGhpc1tvcHRpb25zLm9uVmlzaWJsZVtpXV07CiAgICAgICAgICBpZiAodHlwZW9mIGNhbGxiYWNrID09PSAnZnVuY3Rpb24nKSB7CiAgICAgICAgICAgIGNhbGxiYWNrKCk7CiAgICAgICAgICAgIGNvbnRpbnVlOwogICAgICAgICAgfQogICAgICAgICAgY29uc29sZVdhcm4ob3B0aW9ucy5vblZpc2libGVbaV0gKyAnIG1ldGhvZCBpcyBub3QgYXZhaWxhYmxlIG9uIHRoZSBpbnN0YW5jZSBidXQgcmVmZXJlbmNlZCBpbiBpbnRlcnNlY3RhYmxlIG1peGluIG9wdGlvbnMnKTsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9KTsKfQ=="}, {"version": 3, "names": ["Intersect", "console<PERSON>arn", "<PERSON><PERSON>", "intersectable", "options", "extend", "name", "data", "isIntersecting", "mounted", "inserted", "$el", "value", "onObserve", "$vnode", "destroyed", "unbind", "methods", "entries", "observer", "i", "length", "onVisible", "callback"], "sources": ["../../../src/mixins/intersectable/index.ts"], "sourcesContent": ["// Directives\nimport Intersect from '../../directives/intersect'\n\n// Utilities\nimport { consoleWarn } from '../../util/console'\n\n// Types\nimport Vue from 'vue'\n\nexport default function intersectable (options: { onVisible: string[] }) {\n  return Vue.extend({\n    name: 'intersectable',\n\n    data: () => ({\n      isIntersecting: false,\n    }),\n\n    mounted () {\n      Intersect.inserted(this.$el as HTMLElement, {\n        name: 'intersect',\n        value: this.onObserve,\n      }, this.$vnode)\n    },\n\n    destroyed () {\n      Intersect.unbind(this.$el as HTMLElement, {\n        name: 'intersect',\n        value: this.onObserve,\n      }, this.$vnode)\n    },\n\n    methods: {\n      onObserve (entries: IntersectionObserverEntry[], observer: IntersectionObserver, isIntersecting: boolean) {\n        this.isIntersecting = isIntersecting\n\n        if (!isIntersecting) return\n\n        for (let i = 0, length = options.onVisible.length; i < length; i++) {\n          const callback = (this as any)[options.onVisible[i]]\n\n          if (typeof callback === 'function') {\n            callback()\n            continue\n          }\n\n          consoleWarn(options.onVisible[i] + ' method is not available on the instance but referenced in intersectable mixin options')\n        }\n      },\n    },\n  })\n}\n"], "mappings": "AAAA;AACA,OAAOA,SAAP,MAAsB,4BAAtB,C,CAEA;;AACA,SAASC,WAAT,QAA4B,oBAA5B,C,CAEA;;AACA,OAAOC,GAAP,MAAgB,KAAhB;AAEA,eAAc,SAAUC,aAAVA,CAAyBC,OAAzB,EAAyD;EACrE,OAAOF,GAAG,CAACG,MAAJ,CAAW;IAChBC,IAAI,EAAE,eADU;IAGhBC,IAAI,EAAE,SAANA,IAAIA,CAAA;MAAA,OAAS;QACXC,cAAc,EAAE;MADL,CAAP;IAAA,CAHU;IAOhBC,OAAO,WAAPA,OAAOA,CAAA;MACLT,SAAS,CAACU,QAAV,CAAmB,KAAKC,GAAxB,EAA4C;QAC1CL,IAAI,EAAE,WADoC;QAE1CM,KAAK,EAAE,KAAKC;MAF8B,CAA5C,EAGG,KAAKC,MAHR;IAID,CAZe;IAchBC,SAAS,WAATA,SAASA,CAAA;MACPf,SAAS,CAACgB,MAAV,CAAiB,KAAKL,GAAtB,EAA0C;QACxCL,IAAI,EAAE,WADkC;QAExCM,KAAK,EAAE,KAAKC;MAF4B,CAA1C,EAGG,KAAKC,MAHR;IAID,CAnBe;IAqBhBG,OAAO,EAAE;MACPJ,SAAS,WAATA,SAASA,CAAEK,OAAF,EAAwCC,QAAxC,EAAwEX,cAAxE,EAA+F;QACtG,KAAKA,cAAL,GAAsBA,cAAtB;QAEA,IAAI,CAACA,cAAL,EAAqB;QAErB,KAAK,IAAIY,CAAC,GAAG,CAAR,EAAWC,MAAM,GAAGjB,OAAO,CAACkB,SAAR,CAAkBD,MAA3C,EAAmDD,CAAC,GAAGC,MAAvD,EAA+DD,CAAC,EAAhE,EAAoE;UAClE,IAAMG,QAAQ,GAAI,KAAanB,OAAO,CAACkB,SAAR,CAAkBF,CAAlB,CAAb,CAAlB;UAEA,IAAI,OAAOG,QAAP,KAAoB,UAAxB,EAAoC;YAClCA,QAAQ;YACR;UACD;UAEDtB,WAAW,CAACG,OAAO,CAACkB,SAAR,CAAkBF,CAAlB,IAAuB,wFAAxB,CAAX;QACD;MACF;IAhBM;EArBO,CAAX,CAAP;AAwCD", "ignoreList": []}]}