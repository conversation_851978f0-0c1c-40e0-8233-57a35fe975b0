{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCarousel/VCarouselItem.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCarousel/VCarouselItem.js", "mtime": 1757335237692}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7Ci8vIEV4dGVuc2lvbnMKaW1wb3J0IFZXaW5kb3dJdGVtIGZyb20gJy4uL1ZXaW5kb3cvVldpbmRvd0l0ZW0nOyAvLyBDb21wb25lbnRzCgppbXBvcnQgeyBWSW1nIH0gZnJvbSAnLi4vVkltZyc7IC8vIFV0aWxpdGllcwoKaW1wb3J0IG1peGlucyBmcm9tICcuLi8uLi91dGlsL21peGlucyc7CmltcG9ydCB7IGdldFNsb3QgfSBmcm9tICcuLi8uLi91dGlsL2hlbHBlcnMnOwppbXBvcnQgUm91dGFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL3JvdXRhYmxlJzsgLy8gVHlwZXMKCnZhciBiYXNlTWl4aW5zID0gbWl4aW5zKFZXaW5kb3dJdGVtLCBSb3V0YWJsZSk7Ci8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBiYXNlTWl4aW5zLmV4dGVuZCgpLmV4dGVuZCh7CiAgbmFtZTogJ3YtY2Fyb3VzZWwtaXRlbScsCiAgaW5qZWN0OiB7CiAgICBwYXJlbnRUaGVtZTogewogICAgICAiZGVmYXVsdCI6IHsKICAgICAgICBpc0Rhcms6IGZhbHNlCiAgICAgIH0KICAgIH0KICB9LAogIC8vIHBhc3MgZG93biB0aGUgcGFyZW50J3MgdGhlbWUKICBwcm92aWRlOiBmdW5jdGlvbiBwcm92aWRlKCkgewogICAgcmV0dXJuIHsKICAgICAgdGhlbWU6IHRoaXMucGFyZW50VGhlbWUKICAgIH07CiAgfSwKICBpbmhlcml0QXR0cnM6IGZhbHNlLAogIG1ldGhvZHM6IHsKICAgIGdlbkRlZmF1bHRTbG90OiBmdW5jdGlvbiBnZW5EZWZhdWx0U2xvdCgpIHsKICAgICAgcmV0dXJuIFt0aGlzLiRjcmVhdGVFbGVtZW50KFZJbWcsIHsKICAgICAgICBzdGF0aWNDbGFzczogJ3YtY2Fyb3VzZWxfX2l0ZW0nLAogICAgICAgIHByb3BzOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHRoaXMuJGF0dHJzKSwge30sIHsKICAgICAgICAgIGhlaWdodDogdGhpcy53aW5kb3dHcm91cC5pbnRlcm5hbEhlaWdodAogICAgICAgIH0pLAogICAgICAgIG9uOiB0aGlzLiRsaXN0ZW5lcnMsCiAgICAgICAgc2NvcGVkU2xvdHM6IHsKICAgICAgICAgIHBsYWNlaG9sZGVyOiB0aGlzLiRzY29wZWRTbG90cy5wbGFjZWhvbGRlcgogICAgICAgIH0KICAgICAgfSwgZ2V0U2xvdCh0aGlzKSldOwogICAgfSwKICAgIGdlbldpbmRvd0l0ZW06IGZ1bmN0aW9uIGdlbldpbmRvd0l0ZW0oKSB7CiAgICAgIHZhciBfdGhpcyRnZW5lcmF0ZVJvdXRlTGkgPSB0aGlzLmdlbmVyYXRlUm91dGVMaW5rKCksCiAgICAgICAgdGFnID0gX3RoaXMkZ2VuZXJhdGVSb3V0ZUxpLnRhZywKICAgICAgICBkYXRhID0gX3RoaXMkZ2VuZXJhdGVSb3V0ZUxpLmRhdGE7CiAgICAgIGRhdGEuc3RhdGljQ2xhc3MgPSAndi13aW5kb3ctaXRlbSc7CiAgICAgIGRhdGEuZGlyZWN0aXZlcy5wdXNoKHsKICAgICAgICBuYW1lOiAnc2hvdycsCiAgICAgICAgdmFsdWU6IHRoaXMuaXNBY3RpdmUKICAgICAgfSk7CiAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KHRhZywgZGF0YSwgdGhpcy5nZW5EZWZhdWx0U2xvdCgpKTsKICAgIH0KICB9Cn0pOw=="}, {"version": 3, "names": ["VWindowItem", "VImg", "mixins", "getSlot", "Routable", "baseMixins", "extend", "name", "inject", "parentTheme", "isDark", "provide", "theme", "inheritAttrs", "methods", "genDefaultSlot", "$createElement", "staticClass", "props", "_objectSpread", "$attrs", "height", "windowGroup", "internalHeight", "on", "$listeners", "scopedSlots", "placeholder", "$scopedSlots", "genWindowItem", "_this$generateRouteLi", "generateRouteLink", "tag", "data", "directives", "push", "value", "isActive"], "sources": ["../../../src/components/VCarousel/VCarouselItem.ts"], "sourcesContent": ["// Extensions\nimport VWindowItem from '../VWindow/VWindowItem'\n\n// Components\nimport { VImg } from '../VImg'\n\n// Utilities\nimport mixins, { ExtractVue } from '../../util/mixins'\nimport { getSlot } from '../../util/helpers'\nimport Routable from '../../mixins/routable'\n\n// Types\nconst baseMixins = mixins(\n  VWindowItem,\n  Routable\n)\n\ninterface options extends ExtractVue<typeof baseMixins> {\n  parentTheme: {\n    isDark: boolean\n  }\n}\n\n/* @vue/component */\nexport default baseMixins.extend<options>().extend({\n  name: 'v-carousel-item',\n\n  inject: {\n    parentTheme: {\n      default: {\n        isDark: false,\n      },\n    },\n  },\n\n  // pass down the parent's theme\n  provide (): object {\n    return {\n      theme: this.parentTheme,\n    }\n  },\n\n  inheritAttrs: false,\n\n  methods: {\n    genDefaultSlot () {\n      return [\n        this.$createElement(VImg, {\n          staticClass: 'v-carousel__item',\n          props: {\n            ...this.$attrs,\n            height: this.windowGroup.internalHeight,\n          },\n          on: this.$listeners,\n          scopedSlots: {\n            placeholder: this.$scopedSlots.placeholder,\n          },\n        }, getSlot(this)),\n      ]\n    },\n    genWindowItem () {\n      const { tag, data } = this.generateRouteLink()\n\n      data.staticClass = 'v-window-item'\n      data.directives!.push({\n        name: 'show',\n        value: this.isActive,\n      })\n\n      return this.$createElement(tag, data, this.genDefaultSlot())\n    },\n  },\n})\n"], "mappings": ";;AAAA;AACA,OAAOA,WAAP,MAAwB,wBAAxB,C,CAEA;;AACA,SAASC,IAAT,QAAqB,SAArB,C,CAEA;;AACA,OAAOC,MAAP,MAAmC,mBAAnC;AACA,SAASC,OAAT,QAAwB,oBAAxB;AACA,OAAOC,QAAP,MAAqB,uBAArB,C,CAEA;;AACA,IAAMC,UAAU,GAAGH,MAAM,CACvBF,WADuB,EAEvBI,QAFuB,CAAzB;AAWA;;AACA,eAAeC,UAAU,CAACC,MAAX,GAA6BA,MAA7B,CAAoC;EACjDC,IAAI,EAAE,iBAD2C;EAGjDC,MAAM,EAAE;IACNC,WAAW,EAAE;MACX,WAAS;QACPC,MAAM,EAAE;MADD;IADE;EADP,CAHyC;EAWjD;EACAC,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,KAAK,EAAE,KAAKH;IADP,CAAP;EAGD,CAhBgD;EAkBjDI,YAAY,EAAE,KAlBmC;EAoBjDC,OAAO,EAAE;IACPC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,CACL,KAAKC,cAAL,CAAoBf,IAApB,EAA0B;QACxBgB,WAAW,EAAE,kBADW;QAExBC,KAAK,EAAAC,aAAA,CAAAA,aAAA,KACA,KAAKC,MADH;UAELC,MAAM,EAAE,KAAKC,WAAL,CAAiBC;QAAA,EAJH;QAMxBC,EAAE,EAAE,KAAKC,UANe;QAOxBC,WAAW,EAAE;UACXC,WAAW,EAAE,KAAKC,YAAL,CAAkBD;QADpB;MAPW,CAA1B,EAUGxB,OAAO,CAAC,IAAD,CAVV,CADK,CAAP;IAaD,CAfM;IAgBP0B,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAAC,qBAAA,GAAsB,KAAKC,iBAAL,EAAtB;QAAQC,GAAF,GAAAF,qBAAA,CAAEE,GAAF;QAAOC,IAAA,GAAAH,qBAAA,CAAAG,IAAA;MAEbA,IAAI,CAAChB,WAAL,GAAmB,eAAnB;MACAgB,IAAI,CAACC,UAAL,CAAiBC,IAAjB,CAAsB;QACpB5B,IAAI,EAAE,MADc;QAEpB6B,KAAK,EAAE,KAAKC;MAFQ,CAAtB;MAKA,OAAO,KAAKrB,cAAL,CAAoBgB,GAApB,EAAyBC,IAAzB,EAA+B,KAAKlB,cAAL,EAA/B,CAAP;IACD;EA1BM;AApBwC,CAApC,CAAf", "ignoreList": []}]}