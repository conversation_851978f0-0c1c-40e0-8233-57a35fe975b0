{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/transitions/createTransition.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/transitions/createTransition.js", "mtime": 1757335235375}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["mergeData", "mergeTransitions", "_Array", "_context", "dest", "arguments", "length", "undefined", "_len", "transitions", "Array", "_key", "_concatInstanceProperty", "apply", "call", "createSimpleTransition", "name", "origin", "mode", "functional", "props", "group", "type", "Boolean", "hideOnLeave", "leaveAbsolute", "String", "render", "h", "context", "tag", "concat", "data", "on", "beforeEnter", "el", "style", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "leave", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "_transitionInitialStyles", "position", "top", "left", "width", "height", "afterLeave", "_el$_transitionInitia", "setProperty", "children", "createJavascriptTransition", "functions"], "sources": ["../../../src/components/transitions/createTransition.ts"], "sourcesContent": ["import { FunctionalComponentOptions, VNode, VNodeData } from 'vue'\nimport mergeData from '../../util/mergeData'\n\nfunction mergeTransitions (\n  dest: Function | Function[] = [],\n  ...transitions: (Function | Function[])[]\n) {\n  /* eslint-disable-next-line no-array-constructor */\n  return Array<Function>().concat(dest, ...transitions)\n}\n\nexport function createSimpleTransition (\n  name: string,\n  origin = 'top center 0',\n  mode?: string\n): FunctionalComponentOptions {\n  return {\n    name,\n\n    functional: true,\n\n    props: {\n      group: {\n        type: Boolean,\n        default: false,\n      },\n      hideOnLeave: {\n        type: Boolean,\n        default: false,\n      },\n      leaveAbsolute: {\n        type: Boolean,\n        default: false,\n      },\n      mode: {\n        type: String,\n        default: mode,\n      },\n      origin: {\n        type: String,\n        default: origin,\n      },\n    },\n\n    render (h, context): VNode {\n      const tag = `transition${context.props.group ? '-group' : ''}`\n      const data: VNodeData = {\n        props: {\n          name,\n          mode: context.props.mode,\n        },\n        on: {\n          beforeEnter (el: HTMLElement) {\n            el.style.transformOrigin = context.props.origin\n            el.style.webkitTransformOrigin = context.props.origin\n          },\n        },\n      }\n\n      if (context.props.leaveAbsolute) {\n        data.on!.leave = mergeTransitions(data.on!.leave, (el: HTMLElement) => {\n          const { offsetTop, offsetLeft, offsetWidth, offsetHeight } = el\n          el._transitionInitialStyles = {\n            position: el.style.position,\n            top: el.style.top,\n            left: el.style.left,\n            width: el.style.width,\n            height: el.style.height,\n          }\n          el.style.position = 'absolute'\n          el.style.top = offsetTop + 'px'\n          el.style.left = offsetLeft + 'px'\n          el.style.width = offsetWidth + 'px'\n          el.style.height = offsetHeight + 'px'\n        })\n        data.on!.afterLeave = mergeTransitions(data.on!.afterLeave, (el?: HTMLElement) => {\n          if (el && el._transitionInitialStyles) {\n            const { position, top, left, width, height } = el._transitionInitialStyles\n            delete el._transitionInitialStyles\n            el.style.position = position || ''\n            el.style.top = top || ''\n            el.style.left = left || ''\n            el.style.width = width || ''\n            el.style.height = height || ''\n          }\n        })\n      }\n      if (context.props.hideOnLeave) {\n        data.on!.leave = mergeTransitions(data.on!.leave, (el: HTMLElement) => {\n          el.style.setProperty('display', 'none', 'important')\n        })\n      }\n\n      return h(tag, mergeData(context.data, data), context.children)\n    },\n  }\n}\n\nexport function createJavascriptTransition (\n  name: string,\n  functions: Record<string, any>,\n  mode = 'in-out'\n): FunctionalComponentOptions {\n  return {\n    name,\n\n    functional: true,\n\n    props: {\n      mode: {\n        type: String,\n        default: mode,\n      },\n    },\n\n    render (h, context): VNode {\n      return h(\n        'transition',\n        mergeData(context.data, {\n          props: { name },\n          on: functions,\n        }),\n        context.children\n      )\n    },\n  }\n}\n"], "mappings": ";AACA,OAAOA,SAAP,MAAsB,sBAAtB;AAEA,SAASC,gBAATA,CAAA,EAE2C;EAAA,IAAAC,MAAA,EAAAC,QAAA;EAAA,IADzCC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAA8B,EADhC;EAAA,SAAAG,IAAA,GAAAH,SAAA,CAAAC,MAAA,EAEKG,WAFL,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAEKF,WAFL,CAAAE,IAAA,QAAAN,SAAA,CAAAM,IAAA;EAAA;EAIE;EACA,OAAOC,uBAAA,CAAAV,MAAA,GAAAQ,KAAK,IAAAG,KAAA,CAAAX,MAAA,EAAAU,uBAAA,CAAAT,QAAA,IAAoBC,IAAzB,GAAAU,IAAA,CAAAX,QAAA,EAAkCM,WAAlC,EAAP;AACD;AAED,OAAM,SAAUM,sBAAVA,CACJC,IADI,EAGS;EAAA,IADbC,MAAM,GAAAZ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,cAFL;EAAA,IAGJa,IAHI,GAAAb,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAKJ,OAAO;IACLS,IADK,EACLA,IADK;IAGLG,UAAU,EAAE,IAHP;IAKLC,KAAK,EAAE;MACLC,KAAK,EAAE;QACLC,IAAI,EAAEC,OADD;QAEL,WAAS;MAFJ,CADF;MAKLC,WAAW,EAAE;QACXF,IAAI,EAAEC,OADK;QAEX,WAAS;MAFE,CALR;MASLE,aAAa,EAAE;QACbH,IAAI,EAAEC,OADO;QAEb,WAAS;MAFI,CATV;MAaLL,IAAI,EAAE;QACJI,IAAI,EAAEI,MADF;QAEJ,WAASR;MAFL,CAbD;MAiBLD,MAAM,EAAE;QACNK,IAAI,EAAEI,MADA;QAEN,WAAST;MAFH;IAjBH,CALF;IA4BLU,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAKC,OAAL,EAAY;MAChB,IAAMC,GAAG,gBAAAC,MAAA,CAAgBF,OAAO,CAACT,KAAR,CAAcC,KAAd,GAAsB,QAAtB,GAAiC,EAAE,CAA5D;MACA,IAAMW,IAAI,GAAc;QACtBZ,KAAK,EAAE;UACLJ,IADK,EACLA,IADK;UAELE,IAAI,EAAEW,OAAO,CAACT,KAAR,CAAcF;QAFf,CADe;QAKtBe,EAAE,EAAE;UACFC,WAAW,WAAXA,WAAWA,CAAEC,EAAF,EAAiB;YAC1BA,EAAE,CAACC,KAAH,CAASC,eAAT,GAA2BR,OAAO,CAACT,KAAR,CAAcH,MAAzC;YACAkB,EAAE,CAACC,KAAH,CAASE,qBAAT,GAAiCT,OAAO,CAACT,KAAR,CAAcH,MAA/C;UACD;QAJC;MALkB,CAAxB;MAaA,IAAIY,OAAO,CAACT,KAAR,CAAcK,aAAlB,EAAiC;QAC/BO,IAAI,CAACC,EAAL,CAASM,KAAT,GAAiBtC,gBAAgB,CAAC+B,IAAI,CAACC,EAAL,CAASM,KAAV,EAAkB,UAAAJ,EAAD,EAAoB;UACpE,IAAQK,SAAF,GAAuDL,EAA7D,CAAQK,SAAF;YAAaC,UAAb,GAAuDN,EAA7D,CAAmBM,UAAb;YAAyBC,WAAzB,GAAuDP,EAA7D,CAA+BO,WAAzB;YAAsCC,YAAA,GAAiBR,EAA7D,CAA4CQ,YAAA;UAC5CR,EAAE,CAACS,wBAAH,GAA8B;YAC5BC,QAAQ,EAAEV,EAAE,CAACC,KAAH,CAASS,QADS;YAE5BC,GAAG,EAAEX,EAAE,CAACC,KAAH,CAASU,GAFc;YAG5BC,IAAI,EAAEZ,EAAE,CAACC,KAAH,CAASW,IAHa;YAI5BC,KAAK,EAAEb,EAAE,CAACC,KAAH,CAASY,KAJY;YAK5BC,MAAM,EAAEd,EAAE,CAACC,KAAH,CAASa;UALW,CAA9B;UAOAd,EAAE,CAACC,KAAH,CAASS,QAAT,GAAoB,UAApB;UACAV,EAAE,CAACC,KAAH,CAASU,GAAT,GAAeN,SAAS,GAAG,IAA3B;UACAL,EAAE,CAACC,KAAH,CAASW,IAAT,GAAgBN,UAAU,GAAG,IAA7B;UACAN,EAAE,CAACC,KAAH,CAASY,KAAT,GAAiBN,WAAW,GAAG,IAA/B;UACAP,EAAE,CAACC,KAAH,CAASa,MAAT,GAAkBN,YAAY,GAAG,IAAjC;QACD,CAdgC,CAAjC;QAeAX,IAAI,CAACC,EAAL,CAASiB,UAAT,GAAsBjD,gBAAgB,CAAC+B,IAAI,CAACC,EAAL,CAASiB,UAAV,EAAuB,UAAAf,EAAD,EAAqB;UAC/E,IAAIA,EAAE,IAAIA,EAAE,CAACS,wBAAb,EAAuC;YACrC,IAAAO,qBAAA,GAA+ChB,EAAE,CAACS,wBAAlD;cAAQC,QAAF,GAAAM,qBAAA,CAAEN,QAAF;cAAYC,GAAZ,GAAAK,qBAAA,CAAYL,GAAZ;cAAiBC,IAAjB,GAAAI,qBAAA,CAAiBJ,IAAjB;cAAuBC,KAAvB,GAAAG,qBAAA,CAAuBH,KAAvB;cAA8BC,MAAA,GAAAE,qBAAA,CAAAF,MAAA;YACpC,OAAOd,EAAE,CAACS,wBAAV;YACAT,EAAE,CAACC,KAAH,CAASS,QAAT,GAAoBA,QAAQ,IAAI,EAAhC;YACAV,EAAE,CAACC,KAAH,CAASU,GAAT,GAAeA,GAAG,IAAI,EAAtB;YACAX,EAAE,CAACC,KAAH,CAASW,IAAT,GAAgBA,IAAI,IAAI,EAAxB;YACAZ,EAAE,CAACC,KAAH,CAASY,KAAT,GAAiBA,KAAK,IAAI,EAA1B;YACAb,EAAE,CAACC,KAAH,CAASa,MAAT,GAAkBA,MAAM,IAAI,EAA5B;UACD;QACF,CAVqC,CAAtC;MAWD;MACD,IAAIpB,OAAO,CAACT,KAAR,CAAcI,WAAlB,EAA+B;QAC7BQ,IAAI,CAACC,EAAL,CAASM,KAAT,GAAiBtC,gBAAgB,CAAC+B,IAAI,CAACC,EAAL,CAASM,KAAV,EAAkB,UAAAJ,EAAD,EAAoB;UACpEA,EAAE,CAACC,KAAH,CAASgB,WAAT,CAAqB,SAArB,EAAgC,MAAhC,EAAwC,WAAxC;QACD,CAFgC,CAAjC;MAGD;MAED,OAAOxB,CAAC,CAACE,GAAD,EAAM9B,SAAS,CAAC6B,OAAO,CAACG,IAAT,EAAeA,IAAf,CAAf,EAAqCH,OAAO,CAACwB,QAA7C,CAAR;IACD;EA9EI,CAAP;AAgFD;AAED,OAAM,SAAUC,0BAAVA,CACJtC,IADI,EAEJuC,SAFI,EAGW;EAAA,IAAfrC,IAAI,GAAAb,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,QAHH;EAKJ,OAAO;IACLW,IADK,EACLA,IADK;IAGLG,UAAU,EAAE,IAHP;IAKLC,KAAK,EAAE;MACLF,IAAI,EAAE;QACJI,IAAI,EAAEI,MADF;QAEJ,WAASR;MAFL;IADD,CALF;IAYLS,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAKC,OAAL,EAAY;MAChB,OAAOD,CAAC,CACN,YADM,EAEN5B,SAAS,CAAC6B,OAAO,CAACG,IAAT,EAAe;QACtBZ,KAAK,EAAE;UAAEJ,IAAA,EAAAA;QAAF,CADe;QAEtBiB,EAAE,EAAEsB;MAFkB,CAAf,CAFH,EAMN1B,OAAO,CAACwB,QANF,CAAR;IAQD;EArBI,CAAP;AAuBD", "ignoreList": []}]}