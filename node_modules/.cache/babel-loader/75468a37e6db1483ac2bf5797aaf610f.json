{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/ripple/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/ripple/index.js", "mtime": 1757335237027}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["console<PERSON>arn", "keyCodes", "DELAY_RIPPLE", "transform", "el", "value", "style", "webkitTransform", "isTouchEvent", "e", "constructor", "name", "isKeyboardEvent", "calculate", "arguments", "length", "undefined", "localX", "localY", "offset", "getBoundingClientRect", "target", "touches", "clientX", "left", "clientY", "top", "radius", "scale", "_ripple", "circle", "clientWidth", "center", "Math", "sqrt", "pow", "clientHeight", "centerX", "concat", "centerY", "x", "y", "ripples", "show", "_context", "_context2", "_context3", "_context4", "enabled", "container", "document", "createElement", "animation", "append<PERSON><PERSON><PERSON>", "className", "_calculate", "size", "width", "height", "computed", "window", "getComputedStyle", "position", "dataset", "previousPosition", "classList", "add", "_concatInstanceProperty", "call", "activated", "String", "performance", "now", "_setTimeout", "_context5", "remove", "hide", "getElementsByClassName", "isHiding", "diff", "Number", "delay", "max", "_a", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "isRippleEnabled", "rippleShow", "element", "currentTarget", "touched", "rippleStop", "is<PERSON><PERSON>ch", "centered", "showTimerCommit", "showTimer", "rippleHide", "clearTimeout", "type", "rippleCancelShow", "keyboardRipple", "keyboardRippleShow", "keyCode", "enter", "space", "keyboardRippleHide", "focusRippleHide", "updateRipple", "binding", "wasEnabled", "addEventListener", "passive", "removeListeners", "removeEventListener", "directive", "node", "process", "env", "NODE_ENV", "context", "$nextTick", "display", "_context6", "fnOptions", "componentInstance", "apply", "unbind", "update", "oldValue", "<PERSON><PERSON><PERSON>", "bind"], "sources": ["../../../src/directives/ripple/index.ts"], "sourcesContent": ["// Styles\nimport './VRipple.sass'\n\n// Utilities\nimport { consoleWarn } from '../../util/console'\nimport { keyCodes } from '../../util/helpers'\n\n// Types\nimport { VNode, VNodeDirective } from 'vue'\n\ntype VuetifyRippleEvent = (MouseEvent | TouchEvent | KeyboardEvent) & { rippleStop?: boolean }\n\nconst DELAY_RIPPLE = 80\n\nfunction transform (el: HTMLElement, value: string) {\n  el.style.transform = value\n  el.style.webkitTransform = value\n}\n\nexport interface RippleOptions {\n  class?: string\n  center?: boolean\n  circle?: boolean\n}\n\nfunction isTouchEvent (e: VuetifyRippleEvent): e is TouchEvent {\n  return e.constructor.name === 'TouchEvent'\n}\n\nfunction isKeyboardEvent (e: VuetifyRippleEvent): e is KeyboardEvent {\n  return e.constructor.name === 'KeyboardEvent'\n}\n\nconst calculate = (\n  e: VuetifyRippleEvent,\n  el: HTMLElement,\n  value: RippleOptions = {}\n) => {\n  let localX = 0\n  let localY = 0\n\n  if (!isKeyboardEvent(e)) {\n    const offset = el.getBoundingClientRect()\n    const target = isTouchEvent(e) ? e.touches[e.touches.length - 1] : e\n\n    localX = target.clientX - offset.left\n    localY = target.clientY - offset.top\n  }\n\n  let radius = 0\n  let scale = 0.3\n  if (el._ripple && el._ripple.circle) {\n    scale = 0.15\n    radius = el.clientWidth / 2\n    radius = value.center ? radius : radius + Math.sqrt((localX - radius) ** 2 + (localY - radius) ** 2) / 4\n  } else {\n    radius = Math.sqrt(el.clientWidth ** 2 + el.clientHeight ** 2) / 2\n  }\n\n  const centerX = `${(el.clientWidth - (radius * 2)) / 2}px`\n  const centerY = `${(el.clientHeight - (radius * 2)) / 2}px`\n\n  const x = value.center ? centerX : `${localX - radius}px`\n  const y = value.center ? centerY : `${localY - radius}px`\n\n  return { radius, scale, x, y, centerX, centerY }\n}\n\nconst ripples = {\n  /* eslint-disable max-statements */\n  show (\n    e: VuetifyRippleEvent,\n    el: HTMLElement,\n    value: RippleOptions = {}\n  ) {\n    if (!el._ripple || !el._ripple.enabled) {\n      return\n    }\n\n    const container = document.createElement('span')\n    const animation = document.createElement('span')\n\n    container.appendChild(animation)\n    container.className = 'v-ripple__container'\n\n    if (value.class) {\n      container.className += ` ${value.class}`\n    }\n\n    const { radius, scale, x, y, centerX, centerY } = calculate(e, el, value)\n\n    const size = `${radius * 2}px`\n    animation.className = 'v-ripple__animation'\n    animation.style.width = size\n    animation.style.height = size\n\n    el.appendChild(container)\n\n    const computed = window.getComputedStyle(el)\n    if (computed && computed.position === 'static') {\n      el.style.position = 'relative'\n      el.dataset.previousPosition = 'static'\n    }\n\n    animation.classList.add('v-ripple__animation--enter')\n    animation.classList.add('v-ripple__animation--visible')\n    transform(animation, `translate(${x}, ${y}) scale3d(${scale},${scale},${scale})`)\n    animation.dataset.activated = String(performance.now())\n\n    setTimeout(() => {\n      animation.classList.remove('v-ripple__animation--enter')\n      animation.classList.add('v-ripple__animation--in')\n      transform(animation, `translate(${centerX}, ${centerY}) scale3d(1,1,1)`)\n    }, 0)\n  },\n\n  hide (el: HTMLElement | null) {\n    if (!el || !el._ripple || !el._ripple.enabled) return\n\n    const ripples = el.getElementsByClassName('v-ripple__animation')\n\n    if (ripples.length === 0) return\n    const animation = ripples[ripples.length - 1]\n\n    if (animation.dataset.isHiding) return\n    else animation.dataset.isHiding = 'true'\n\n    const diff = performance.now() - Number(animation.dataset.activated)\n    const delay = Math.max(250 - diff, 0)\n\n    setTimeout(() => {\n      animation.classList.remove('v-ripple__animation--in')\n      animation.classList.add('v-ripple__animation--out')\n\n      setTimeout(() => {\n        const ripples = el.getElementsByClassName('v-ripple__animation')\n        if (ripples.length === 1 && el.dataset.previousPosition) {\n          el.style.position = el.dataset.previousPosition\n          delete el.dataset.previousPosition\n        }\n\n        if (animation.parentNode?.parentNode === el) el.removeChild(animation.parentNode)\n      }, 300)\n    }, delay)\n  },\n}\n\nfunction isRippleEnabled (value: any): value is true {\n  return typeof value === 'undefined' || !!value\n}\n\nfunction rippleShow (e: VuetifyRippleEvent) {\n  const value: RippleOptions = {}\n  const element = e.currentTarget as HTMLElement\n\n  if (!element || !element._ripple || element._ripple.touched || e.rippleStop) return\n\n  // Don't allow the event to trigger ripples on any other elements\n  e.rippleStop = true\n\n  if (isTouchEvent(e)) {\n    element._ripple.touched = true\n    element._ripple.isTouch = true\n  } else {\n    // It's possible for touch events to fire\n    // as mouse events on Android/iOS, this\n    // will skip the event call if it has\n    // already been registered as touch\n    if (element._ripple.isTouch) return\n  }\n  value.center = element._ripple.centered || isKeyboardEvent(e)\n  if (element._ripple.class) {\n    value.class = element._ripple.class\n  }\n\n  if (isTouchEvent(e)) {\n    // already queued that shows or hides the ripple\n    if (element._ripple.showTimerCommit) return\n\n    element._ripple.showTimerCommit = () => {\n      ripples.show(e, element, value)\n    }\n    element._ripple.showTimer = window.setTimeout(() => {\n      if (element && element._ripple && element._ripple.showTimerCommit) {\n        element._ripple.showTimerCommit()\n        element._ripple.showTimerCommit = null\n      }\n    }, DELAY_RIPPLE)\n  } else {\n    ripples.show(e, element, value)\n  }\n}\n\nfunction rippleHide (e: Event) {\n  const element = e.currentTarget as HTMLElement | null\n  if (!element || !element._ripple) return\n\n  window.clearTimeout(element._ripple.showTimer)\n\n  // The touch interaction occurs before the show timer is triggered.\n  // We still want to show ripple effect.\n  if (e.type === 'touchend' && element._ripple.showTimerCommit) {\n    element._ripple.showTimerCommit()\n    element._ripple.showTimerCommit = null\n\n    // re-queue ripple hiding\n    element._ripple.showTimer = setTimeout(() => {\n      rippleHide(e)\n    })\n    return\n  }\n\n  window.setTimeout(() => {\n    if (element._ripple) {\n      element._ripple.touched = false\n    }\n  })\n  ripples.hide(element)\n}\n\nfunction rippleCancelShow (e: MouseEvent | TouchEvent) {\n  const element = e.currentTarget as HTMLElement | undefined\n\n  if (!element || !element._ripple) return\n\n  if (element._ripple.showTimerCommit) {\n    element._ripple.showTimerCommit = null\n  }\n\n  window.clearTimeout(element._ripple.showTimer)\n}\n\nlet keyboardRipple = false\n\nfunction keyboardRippleShow (e: KeyboardEvent) {\n  if (!keyboardRipple && (e.keyCode === keyCodes.enter || e.keyCode === keyCodes.space)) {\n    keyboardRipple = true\n    rippleShow(e)\n  }\n}\n\nfunction keyboardRippleHide (e: KeyboardEvent) {\n  keyboardRipple = false\n  rippleHide(e)\n}\n\nfunction focusRippleHide (e: FocusEvent) {\n  if (keyboardRipple === true) {\n    keyboardRipple = false\n    rippleHide(e)\n  }\n}\n\nfunction updateRipple (el: HTMLElement, binding: VNodeDirective, wasEnabled: boolean) {\n  const enabled = isRippleEnabled(binding.value)\n  if (!enabled) {\n    ripples.hide(el)\n  }\n  el._ripple = el._ripple || {}\n  el._ripple.enabled = enabled\n  const value = binding.value || {}\n  if (value.center) {\n    el._ripple.centered = true\n  }\n  if (value.class) {\n    el._ripple.class = binding.value.class\n  }\n  if (value.circle) {\n    el._ripple.circle = value.circle\n  }\n  if (enabled && !wasEnabled) {\n    el.addEventListener('touchstart', rippleShow, { passive: true })\n    el.addEventListener('touchend', rippleHide, { passive: true })\n    el.addEventListener('touchmove', rippleCancelShow, { passive: true })\n    el.addEventListener('touchcancel', rippleHide)\n\n    el.addEventListener('mousedown', rippleShow)\n    el.addEventListener('mouseup', rippleHide)\n    el.addEventListener('mouseleave', rippleHide)\n\n    el.addEventListener('keydown', keyboardRippleShow)\n    el.addEventListener('keyup', keyboardRippleHide)\n\n    el.addEventListener('blur', focusRippleHide)\n\n    // Anchor tags can be dragged, causes other hides to fail - #1537\n    el.addEventListener('dragstart', rippleHide, { passive: true })\n  } else if (!enabled && wasEnabled) {\n    removeListeners(el)\n  }\n}\n\nfunction removeListeners (el: HTMLElement) {\n  el.removeEventListener('mousedown', rippleShow)\n  el.removeEventListener('touchstart', rippleShow)\n  el.removeEventListener('touchend', rippleHide)\n  el.removeEventListener('touchmove', rippleCancelShow)\n  el.removeEventListener('touchcancel', rippleHide)\n  el.removeEventListener('mouseup', rippleHide)\n  el.removeEventListener('mouseleave', rippleHide)\n  el.removeEventListener('keydown', keyboardRippleShow)\n  el.removeEventListener('keyup', keyboardRippleHide)\n  el.removeEventListener('dragstart', rippleHide)\n  el.removeEventListener('blur', focusRippleHide)\n}\n\nfunction directive (el: HTMLElement, binding: VNodeDirective, node: VNode) {\n  updateRipple(el, binding, false)\n\n  if (process.env.NODE_ENV === 'development') {\n    // warn if an inline element is used, waiting for el to be in the DOM first\n    node.context && node.context.$nextTick(() => {\n      const computed = window.getComputedStyle(el)\n      if (computed && computed.display === 'inline') {\n        const context = (node as any).fnOptions ? [(node as any).fnOptions, node.context] : [node.componentInstance]\n        consoleWarn('v-ripple can only be used on block-level elements', ...context)\n      }\n    })\n  }\n}\n\nfunction unbind (el: HTMLElement) {\n  delete el._ripple\n  removeListeners(el)\n}\n\nfunction update (el: HTMLElement, binding: VNodeDirective) {\n  if (binding.value === binding.oldValue) {\n    return\n  }\n\n  const wasEnabled = isRippleEnabled(binding.oldValue)\n  updateRipple(el, binding, wasEnabled)\n}\n\nexport const Ripple = {\n  bind: directive,\n  unbind,\n  update,\n}\n\nexport default Ripple\n"], "mappings": ";;;;AAAA;AACA,OAAO,6CAAP,C,CAEA;;AACA,SAASA,WAAT,QAA4B,oBAA5B;AACA,SAASC,QAAT,QAAyB,oBAAzB;AAOA,IAAMC,YAAY,GAAG,EAArB;AAEA,SAASC,SAATA,CAAoBC,EAApB,EAAqCC,KAArC,EAAkD;EAChDD,EAAE,CAACE,KAAH,CAASH,SAAT,GAAqBE,KAArB;EACAD,EAAE,CAACE,KAAH,CAASC,eAAT,GAA2BF,KAA3B;AACD;AAQD,SAASG,YAATA,CAAuBC,CAAvB,EAA4C;EAC1C,OAAOA,CAAC,CAACC,WAAF,CAAcC,IAAd,KAAuB,YAA9B;AACD;AAED,SAASC,eAATA,CAA0BH,CAA1B,EAA+C;EAC7C,OAAOA,CAAC,CAACC,WAAF,CAAcC,IAAd,KAAuB,eAA9B;AACD;AAED,IAAME,SAAS,GAAG,SAAZA,SAASA,CACbJ,CADgB,EAEhBL,EAFgB,EAId;EAAA,IADFC,KAAA,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB,EAHP;EAKhB,IAAIG,MAAM,GAAG,CAAb;EACA,IAAIC,MAAM,GAAG,CAAb;EAEA,IAAI,CAACN,eAAe,CAACH,CAAD,CAApB,EAAyB;IACvB,IAAMU,MAAM,GAAGf,EAAE,CAACgB,qBAAH,EAAf;IACA,IAAMC,MAAM,GAAGb,YAAY,CAACC,CAAD,CAAZ,GAAkBA,CAAC,CAACa,OAAF,CAAUb,CAAC,CAACa,OAAF,CAAUP,MAAV,GAAmB,CAA7B,CAAlB,GAAoDN,CAAnE;IAEAQ,MAAM,GAAGI,MAAM,CAACE,OAAP,GAAiBJ,MAAM,CAACK,IAAjC;IACAN,MAAM,GAAGG,MAAM,CAACI,OAAP,GAAiBN,MAAM,CAACO,GAAjC;EACD;EAED,IAAIC,MAAM,GAAG,CAAb;EACA,IAAIC,KAAK,GAAG,GAAZ;EACA,IAAIxB,EAAE,CAACyB,OAAH,IAAczB,EAAE,CAACyB,OAAH,CAAWC,MAA7B,EAAqC;IACnCF,KAAK,GAAG,IAAR;IACAD,MAAM,GAAGvB,EAAE,CAAC2B,WAAH,GAAiB,CAA1B;IACAJ,MAAM,GAAGtB,KAAK,CAAC2B,MAAN,GAAeL,MAAf,GAAwBA,MAAM,GAAGM,IAAI,CAACC,IAAL,CAAUD,IAAA,CAAAE,GAAA,CAAClB,MAAM,GAAGU,MAAV,EAAqB,CAArB,IAAAM,IAAA,CAAAE,GAAA,CAA0BjB,MAAM,GAAGS,MAAV,EAAqB,CAAxD,KAA6D,CAAvG;EACD,CAJD,MAIO;IACLA,MAAM,GAAGM,IAAI,CAACC,IAAL,CAAUD,IAAA,CAAAE,GAAA,CAAA/B,EAAE,CAAC2B,WAAH,EAAkB,CAAlB,IAAAE,IAAA,CAAAE,GAAA,CAAsB/B,EAAE,CAACgC,YAAH,EAAmB,CAAnD,KAAwD,CAAjE;EACD;EAED,IAAMC,OAAO,MAAAC,MAAA,CAAM,CAAClC,EAAE,CAAC2B,WAAH,GAAkBJ,MAAM,GAAG,CAA5B,IAAkC,CAAC,OAAtD;EACA,IAAMY,OAAO,MAAAD,MAAA,CAAM,CAAClC,EAAE,CAACgC,YAAH,GAAmBT,MAAM,GAAG,CAA7B,IAAmC,CAAC,OAAvD;EAEA,IAAMa,CAAC,GAAGnC,KAAK,CAAC2B,MAAN,GAAeK,OAAf,MAAAC,MAAA,CAA4BrB,MAAM,GAAGU,MAAM,OAArD;EACA,IAAMc,CAAC,GAAGpC,KAAK,CAAC2B,MAAN,GAAeO,OAAf,MAAAD,MAAA,CAA4BpB,MAAM,GAAGS,MAAM,OAArD;EAEA,OAAO;IAAEA,MAAF,EAAEA,MAAF;IAAUC,KAAV,EAAUA,KAAV;IAAiBY,CAAjB,EAAiBA,CAAjB;IAAoBC,CAApB,EAAoBA,CAApB;IAAuBJ,OAAvB,EAAuBA,OAAvB;IAAgCE,OAAA,EAAAA;EAAhC,CAAP;AACD,CAjCD;AAmCA,IAAMG,OAAO,GAAG;EACd,mCACAC,IAAI,WAAJA,IAAIA,CACFlC,CADE,EAEFL,EAFE,EAGuB;IAAA,IAAAwC,QAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA;IAAA,IAAzB1C,KAAA,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAuB,EAHrB;IAKF,IAAI,CAACV,EAAE,CAACyB,OAAJ,IAAe,CAACzB,EAAE,CAACyB,OAAH,CAAWmB,OAA/B,EAAwC;MACtC;IACD;IAED,IAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAlB;IACA,IAAMC,SAAS,GAAGF,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAlB;IAEAF,SAAS,CAACI,WAAV,CAAsBD,SAAtB;IACAH,SAAS,CAACK,SAAV,GAAsB,qBAAtB;IAEA,IAAIjD,KAAK,SAAT,EAAiB;MACf4C,SAAS,CAACK,SAAV,QAAAhB,MAAA,CAA2BjC,KAAK,SAAM,CAAtC;IACD;IAED,IAAAkD,UAAA,GAAkD1C,SAAS,CAACJ,CAAD,EAAIL,EAAJ,EAAQC,KAAR,CAA3D;MAAQsB,MAAF,GAAA4B,UAAA,CAAE5B,MAAF;MAAUC,KAAV,GAAA2B,UAAA,CAAU3B,KAAV;MAAiBY,CAAjB,GAAAe,UAAA,CAAiBf,CAAjB;MAAoBC,CAApB,GAAAc,UAAA,CAAoBd,CAApB;MAAuBJ,OAAvB,GAAAkB,UAAA,CAAuBlB,OAAvB;MAAgCE,OAAA,GAAAgB,UAAA,CAAAhB,OAAA;IAEtC,IAAMiB,IAAI,MAAAlB,MAAA,CAAMX,MAAM,GAAG,CAAC,OAA1B;IACAyB,SAAS,CAACE,SAAV,GAAsB,qBAAtB;IACAF,SAAS,CAAC9C,KAAV,CAAgBmD,KAAhB,GAAwBD,IAAxB;IACAJ,SAAS,CAAC9C,KAAV,CAAgBoD,MAAhB,GAAyBF,IAAzB;IAEApD,EAAE,CAACiD,WAAH,CAAeJ,SAAf;IAEA,IAAMU,QAAQ,GAAGC,MAAM,CAACC,gBAAP,CAAwBzD,EAAxB,CAAjB;IACA,IAAIuD,QAAQ,IAAIA,QAAQ,CAACG,QAAT,KAAsB,QAAtC,EAAgD;MAC9C1D,EAAE,CAACE,KAAH,CAASwD,QAAT,GAAoB,UAApB;MACA1D,EAAE,CAAC2D,OAAH,CAAWC,gBAAX,GAA8B,QAA9B;IACD;IAEDZ,SAAS,CAACa,SAAV,CAAoBC,GAApB,CAAwB,4BAAxB;IACAd,SAAS,CAACa,SAAV,CAAoBC,GAApB,CAAwB,8BAAxB;IACA/D,SAAS,CAACiD,SAAD,EAAAe,uBAAA,CAAAvB,QAAA,GAAAuB,uBAAA,CAAAtB,SAAA,GAAAsB,uBAAA,CAAArB,SAAA,GAAAqB,uBAAA,CAAApB,SAAA,gBAAAT,MAAA,CAAyBE,CAAC,SAAA4B,IAAA,CAAArB,SAAA,EAAKN,CAAC,iBAAA2B,IAAA,CAAAtB,SAAA,EAAalB,KAAK,QAAAwC,IAAA,CAAAvB,SAAA,EAAIjB,KAAK,QAAAwC,IAAA,CAAAxB,QAAA,EAAIhB,KAAK,MAApE,CAAT;IACAwB,SAAS,CAACW,OAAV,CAAkBM,SAAlB,GAA8BC,MAAM,CAACC,WAAW,CAACC,GAAZ,EAAD,CAApC;IAEAC,WAAA,CAAW,YAAK;MAAA,IAAAC,SAAA;MACdtB,SAAS,CAACa,SAAV,CAAoBU,MAApB,CAA2B,4BAA3B;MACAvB,SAAS,CAACa,SAAV,CAAoBC,GAApB,CAAwB,yBAAxB;MACA/D,SAAS,CAACiD,SAAD,EAAAe,uBAAA,CAAAO,SAAA,gBAAApC,MAAA,CAAyBD,OAAO,SAAA+B,IAAA,CAAAM,SAAA,EAAKnC,OAAO,qBAA5C,CAAT;IACD,CAJS,EAIP,CAJO,CAAV;EAKD,CA9Ca;EAgDdqC,IAAI,WAAJA,IAAIA,CAAExE,EAAF,EAAwB;IAC1B,IAAI,CAACA,EAAD,IAAO,CAACA,EAAE,CAACyB,OAAX,IAAsB,CAACzB,EAAE,CAACyB,OAAH,CAAWmB,OAAtC,EAA+C;IAE/C,IAAMN,OAAO,GAAGtC,EAAE,CAACyE,sBAAH,CAA0B,qBAA1B,CAAhB;IAEA,IAAInC,OAAO,CAAC3B,MAAR,KAAmB,CAAvB,EAA0B;IAC1B,IAAMqC,SAAS,GAAGV,OAAO,CAACA,OAAO,CAAC3B,MAAR,GAAiB,CAAlB,CAAzB;IAEA,IAAIqC,SAAS,CAACW,OAAV,CAAkBe,QAAtB,EAAgC,OAAhC,KACK1B,SAAS,CAACW,OAAV,CAAkBe,QAAlB,GAA6B,MAA7B;IAEL,IAAMC,IAAI,GAAGR,WAAW,CAACC,GAAZ,KAAoBQ,MAAM,CAAC5B,SAAS,CAACW,OAAV,CAAkBM,SAAnB,CAAvC;IACA,IAAMY,KAAK,GAAGhD,IAAI,CAACiD,GAAL,CAAS,MAAMH,IAAf,EAAqB,CAArB,CAAd;IAEAN,WAAA,CAAW,YAAK;MACdrB,SAAS,CAACa,SAAV,CAAoBU,MAApB,CAA2B,yBAA3B;MACAvB,SAAS,CAACa,SAAV,CAAoBC,GAApB,CAAwB,0BAAxB;MAEAO,WAAA,CAAW,YAAK;;QACd,IAAM/B,OAAO,GAAGtC,EAAE,CAACyE,sBAAH,CAA0B,qBAA1B,CAAhB;QACA,IAAInC,OAAO,CAAC3B,MAAR,KAAmB,CAAnB,IAAwBX,EAAE,CAAC2D,OAAH,CAAWC,gBAAvC,EAAyD;UACvD5D,EAAE,CAACE,KAAH,CAASwD,QAAT,GAAoB1D,EAAE,CAAC2D,OAAH,CAAWC,gBAA/B;UACA,OAAO5D,EAAE,CAAC2D,OAAH,CAAWC,gBAAlB;QACD;QAED,IAAI,EAAAmB,EAAA,GAAA/B,SAAS,CAACgC,UAAV,MAAoB,IAApB,IAAoBD,EAAA,WAApB,GAAoB,MAApB,GAAoBA,EAAA,CAAEC,UAAtB,MAAqChF,EAAzC,EAA6CA,EAAE,CAACiF,WAAH,CAAejC,SAAS,CAACgC,UAAzB;MAC9C,CARS,EAQP,GARO,CAAV;IASD,CAbS,EAaPH,KAbO,CAAV;EAcD;AA5Ea,CAAhB;AA+EA,SAASK,eAATA,CAA0BjF,KAA1B,EAAoC;EAClC,OAAO,OAAOA,KAAP,KAAiB,WAAjB,IAAgC,CAAC,CAACA,KAAzC;AACD;AAED,SAASkF,UAATA,CAAqB9E,CAArB,EAA0C;EACxC,IAAMJ,KAAK,GAAkB,EAA7B;EACA,IAAMmF,OAAO,GAAG/E,CAAC,CAACgF,aAAlB;EAEA,IAAI,CAACD,OAAD,IAAY,CAACA,OAAO,CAAC3D,OAArB,IAAgC2D,OAAO,CAAC3D,OAAR,CAAgB6D,OAAhD,IAA2DjF,CAAC,CAACkF,UAAjE,EAA6E,OAJrC,CAMxC;;EACAlF,CAAC,CAACkF,UAAF,GAAe,IAAf;EAEA,IAAInF,YAAY,CAACC,CAAD,CAAhB,EAAqB;IACnB+E,OAAO,CAAC3D,OAAR,CAAgB6D,OAAhB,GAA0B,IAA1B;IACAF,OAAO,CAAC3D,OAAR,CAAgB+D,OAAhB,GAA0B,IAA1B;EACD,CAHD,MAGO;IACL;IACA;IACA;IACA;IACA,IAAIJ,OAAO,CAAC3D,OAAR,CAAgB+D,OAApB,EAA6B;EAC9B;EACDvF,KAAK,CAAC2B,MAAN,GAAewD,OAAO,CAAC3D,OAAR,CAAgBgE,QAAhB,IAA4BjF,eAAe,CAACH,CAAD,CAA1D;EACA,IAAI+E,OAAO,CAAC3D,OAAR,SAAJ,EAA2B;IACzBxB,KAAK,SAAL,GAAcmF,OAAO,CAAC3D,OAAR,SAAd;EACD;EAED,IAAIrB,YAAY,CAACC,CAAD,CAAhB,EAAqB;IACnB;IACA,IAAI+E,OAAO,CAAC3D,OAAR,CAAgBiE,eAApB,EAAqC;IAErCN,OAAO,CAAC3D,OAAR,CAAgBiE,eAAhB,GAAkC,YAAK;MACrCpD,OAAO,CAACC,IAAR,CAAalC,CAAb,EAAgB+E,OAAhB,EAAyBnF,KAAzB;IACD,CAFD;IAGAmF,OAAO,CAAC3D,OAAR,CAAgBkE,SAAhB,GAA4BtB,WAAA,CAAkB,YAAK;MACjD,IAAIe,OAAO,IAAIA,OAAO,CAAC3D,OAAnB,IAA8B2D,OAAO,CAAC3D,OAAR,CAAgBiE,eAAlD,EAAmE;QACjEN,OAAO,CAAC3D,OAAR,CAAgBiE,eAAhB;QACAN,OAAO,CAAC3D,OAAR,CAAgBiE,eAAhB,GAAkC,IAAlC;MACD;IACF,CAL2B,EAKzB5F,YALyB,CAA5B;EAMD,CAbD,MAaO;IACLwC,OAAO,CAACC,IAAR,CAAalC,CAAb,EAAgB+E,OAAhB,EAAyBnF,KAAzB;EACD;AACF;AAED,SAAS2F,UAATA,CAAqBvF,CAArB,EAA6B;EAC3B,IAAM+E,OAAO,GAAG/E,CAAC,CAACgF,aAAlB;EACA,IAAI,CAACD,OAAD,IAAY,CAACA,OAAO,CAAC3D,OAAzB,EAAkC;EAElC+B,MAAM,CAACqC,YAAP,CAAoBT,OAAO,CAAC3D,OAAR,CAAgBkE,SAApC,EAJ2B,CAM3B;EACA;;EACA,IAAItF,CAAC,CAACyF,IAAF,KAAW,UAAX,IAAyBV,OAAO,CAAC3D,OAAR,CAAgBiE,eAA7C,EAA8D;IAC5DN,OAAO,CAAC3D,OAAR,CAAgBiE,eAAhB;IACAN,OAAO,CAAC3D,OAAR,CAAgBiE,eAAhB,GAAkC,IAAlC,CAF4D,CAI5D;;IACAN,OAAO,CAAC3D,OAAR,CAAgBkE,SAAhB,GAA4BtB,WAAA,CAAW,YAAK;MAC1CuB,UAAU,CAACvF,CAAD,CAAV;IACD,CAFqC,CAAtC;IAGA;EACD;EAEDgE,WAAA,CAAkB,YAAK;IACrB,IAAIe,OAAO,CAAC3D,OAAZ,EAAqB;MACnB2D,OAAO,CAAC3D,OAAR,CAAgB6D,OAAhB,GAA0B,KAA1B;IACD;EACF,CAJD;EAKAhD,OAAO,CAACkC,IAAR,CAAaY,OAAb;AACD;AAED,SAASW,gBAATA,CAA2B1F,CAA3B,EAAqD;EACnD,IAAM+E,OAAO,GAAG/E,CAAC,CAACgF,aAAlB;EAEA,IAAI,CAACD,OAAD,IAAY,CAACA,OAAO,CAAC3D,OAAzB,EAAkC;EAElC,IAAI2D,OAAO,CAAC3D,OAAR,CAAgBiE,eAApB,EAAqC;IACnCN,OAAO,CAAC3D,OAAR,CAAgBiE,eAAhB,GAAkC,IAAlC;EACD;EAEDlC,MAAM,CAACqC,YAAP,CAAoBT,OAAO,CAAC3D,OAAR,CAAgBkE,SAApC;AACD;AAED,IAAIK,cAAc,GAAG,KAArB;AAEA,SAASC,kBAATA,CAA6B5F,CAA7B,EAA6C;EAC3C,IAAI,CAAC2F,cAAD,KAAoB3F,CAAC,CAAC6F,OAAF,KAAcrG,QAAQ,CAACsG,KAAvB,IAAgC9F,CAAC,CAAC6F,OAAF,KAAcrG,QAAQ,CAACuG,KAA3E,CAAJ,EAAuF;IACrFJ,cAAc,GAAG,IAAjB;IACAb,UAAU,CAAC9E,CAAD,CAAV;EACD;AACF;AAED,SAASgG,kBAATA,CAA6BhG,CAA7B,EAA6C;EAC3C2F,cAAc,GAAG,KAAjB;EACAJ,UAAU,CAACvF,CAAD,CAAV;AACD;AAED,SAASiG,eAATA,CAA0BjG,CAA1B,EAAuC;EACrC,IAAI2F,cAAc,KAAK,IAAvB,EAA6B;IAC3BA,cAAc,GAAG,KAAjB;IACAJ,UAAU,CAACvF,CAAD,CAAV;EACD;AACF;AAED,SAASkG,YAATA,CAAuBvG,EAAvB,EAAwCwG,OAAxC,EAAiEC,UAAjE,EAAoF;EAClF,IAAM7D,OAAO,GAAGsC,eAAe,CAACsB,OAAO,CAACvG,KAAT,CAA/B;EACA,IAAI,CAAC2C,OAAL,EAAc;IACZN,OAAO,CAACkC,IAAR,CAAaxE,EAAb;EACD;EACDA,EAAE,CAACyB,OAAH,GAAazB,EAAE,CAACyB,OAAH,IAAc,EAA3B;EACAzB,EAAE,CAACyB,OAAH,CAAWmB,OAAX,GAAqBA,OAArB;EACA,IAAM3C,KAAK,GAAGuG,OAAO,CAACvG,KAAR,IAAiB,EAA/B;EACA,IAAIA,KAAK,CAAC2B,MAAV,EAAkB;IAChB5B,EAAE,CAACyB,OAAH,CAAWgE,QAAX,GAAsB,IAAtB;EACD;EACD,IAAIxF,KAAK,SAAT,EAAiB;IACfD,EAAE,CAACyB,OAAH,YAAmB+E,OAAO,CAACvG,KAAR,SAAnB;EACD;EACD,IAAIA,KAAK,CAACyB,MAAV,EAAkB;IAChB1B,EAAE,CAACyB,OAAH,CAAWC,MAAX,GAAoBzB,KAAK,CAACyB,MAA1B;EACD;EACD,IAAIkB,OAAO,IAAI,CAAC6D,UAAhB,EAA4B;IAC1BzG,EAAE,CAAC0G,gBAAH,CAAoB,YAApB,EAAkCvB,UAAlC,EAA8C;MAAEwB,OAAO,EAAE;IAAX,CAA9C;IACA3G,EAAE,CAAC0G,gBAAH,CAAoB,UAApB,EAAgCd,UAAhC,EAA4C;MAAEe,OAAO,EAAE;IAAX,CAA5C;IACA3G,EAAE,CAAC0G,gBAAH,CAAoB,WAApB,EAAiCX,gBAAjC,EAAmD;MAAEY,OAAO,EAAE;IAAX,CAAnD;IACA3G,EAAE,CAAC0G,gBAAH,CAAoB,aAApB,EAAmCd,UAAnC;IAEA5F,EAAE,CAAC0G,gBAAH,CAAoB,WAApB,EAAiCvB,UAAjC;IACAnF,EAAE,CAAC0G,gBAAH,CAAoB,SAApB,EAA+Bd,UAA/B;IACA5F,EAAE,CAAC0G,gBAAH,CAAoB,YAApB,EAAkCd,UAAlC;IAEA5F,EAAE,CAAC0G,gBAAH,CAAoB,SAApB,EAA+BT,kBAA/B;IACAjG,EAAE,CAAC0G,gBAAH,CAAoB,OAApB,EAA6BL,kBAA7B;IAEArG,EAAE,CAAC0G,gBAAH,CAAoB,MAApB,EAA4BJ,eAA5B,EAb0B,CAe1B;;IACAtG,EAAE,CAAC0G,gBAAH,CAAoB,WAApB,EAAiCd,UAAjC,EAA6C;MAAEe,OAAO,EAAE;IAAX,CAA7C;EACD,CAjBD,MAiBO,IAAI,CAAC/D,OAAD,IAAY6D,UAAhB,EAA4B;IACjCG,eAAe,CAAC5G,EAAD,CAAf;EACD;AACF;AAED,SAAS4G,eAATA,CAA0B5G,EAA1B,EAAyC;EACvCA,EAAE,CAAC6G,mBAAH,CAAuB,WAAvB,EAAoC1B,UAApC;EACAnF,EAAE,CAAC6G,mBAAH,CAAuB,YAAvB,EAAqC1B,UAArC;EACAnF,EAAE,CAAC6G,mBAAH,CAAuB,UAAvB,EAAmCjB,UAAnC;EACA5F,EAAE,CAAC6G,mBAAH,CAAuB,WAAvB,EAAoCd,gBAApC;EACA/F,EAAE,CAAC6G,mBAAH,CAAuB,aAAvB,EAAsCjB,UAAtC;EACA5F,EAAE,CAAC6G,mBAAH,CAAuB,SAAvB,EAAkCjB,UAAlC;EACA5F,EAAE,CAAC6G,mBAAH,CAAuB,YAAvB,EAAqCjB,UAArC;EACA5F,EAAE,CAAC6G,mBAAH,CAAuB,SAAvB,EAAkCZ,kBAAlC;EACAjG,EAAE,CAAC6G,mBAAH,CAAuB,OAAvB,EAAgCR,kBAAhC;EACArG,EAAE,CAAC6G,mBAAH,CAAuB,WAAvB,EAAoCjB,UAApC;EACA5F,EAAE,CAAC6G,mBAAH,CAAuB,MAAvB,EAA+BP,eAA/B;AACD;AAED,SAASQ,SAATA,CAAoB9G,EAApB,EAAqCwG,OAArC,EAA8DO,IAA9D,EAAyE;EACvER,YAAY,CAACvG,EAAD,EAAKwG,OAAL,EAAc,KAAd,CAAZ;EAEA,IAAIQ,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,aAA7B,EAA4C;IAC1C;IACAH,IAAI,CAACI,OAAL,IAAgBJ,IAAI,CAACI,OAAL,CAAaC,SAAb,CAAuB,YAAK;MAC1C,IAAM7D,QAAQ,GAAGC,MAAM,CAACC,gBAAP,CAAwBzD,EAAxB,CAAjB;MACA,IAAIuD,QAAQ,IAAIA,QAAQ,CAAC8D,OAAT,KAAqB,QAArC,EAA+C;QAAA,IAAAC,SAAA;QAC7C,IAAMH,OAAO,GAAIJ,IAAY,CAACQ,SAAb,GAAyB,CAAER,IAAY,CAACQ,SAAf,EAA0BR,IAAI,CAACI,OAA/B,CAAzB,GAAmE,CAACJ,IAAI,CAACS,iBAAN,CAApF;QACA5H,WAAW,CAAA6H,KAAA,SAAA1D,uBAAA,CAAAuD,SAAA,IAAC,mDAAD,GAAAtD,IAAA,CAAAsD,SAAA,EAAyDH,OAAzD,EAAX;MACD;IACF,CANe,CAAhB;EAOD;AACF;AAED,SAASO,MAATA,CAAiB1H,EAAjB,EAAgC;EAC9B,OAAOA,EAAE,CAACyB,OAAV;EACAmF,eAAe,CAAC5G,EAAD,CAAf;AACD;AAED,SAAS2H,MAATA,CAAiB3H,EAAjB,EAAkCwG,OAAlC,EAAyD;EACvD,IAAIA,OAAO,CAACvG,KAAR,KAAkBuG,OAAO,CAACoB,QAA9B,EAAwC;IACtC;EACD;EAED,IAAMnB,UAAU,GAAGvB,eAAe,CAACsB,OAAO,CAACoB,QAAT,CAAlC;EACArB,YAAY,CAACvG,EAAD,EAAKwG,OAAL,EAAcC,UAAd,CAAZ;AACD;AAED,OAAO,IAAMoB,MAAM,GAAG;EACpBC,IAAI,EAAEhB,SADc;EAEpBY,MAFoB,EAEpBA,MAFoB;EAGpBC,MAAA,EAAAA;AAHoB,CAAf;AAMP,eAAeE,MAAf", "ignoreList": []}]}