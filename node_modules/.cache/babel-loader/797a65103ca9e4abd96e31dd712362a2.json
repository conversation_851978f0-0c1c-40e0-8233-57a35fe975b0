{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/WatermarkDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/WatermarkDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "showDialog", "watermarkElementTab", "elementParams", "watermark", "title", "content", "left", "top", "width", "height", "opacity", "textWatermarkFix", "deg", "textSize", "color", "interval", "textWatermark", "rules", "urlRule", "v", "test", "computed", "hasTextWatermark", "window", "teduBoard", "getElementById", "TEduBoard", "TEduWatermarkType", "Text", "hasTextFixWatermark", "TextFix", "watch", "tabIndex", "element", "style", "fillColor", "methods", "loanMinRule", "value", "min", "concat", "loanMaxRule", "max", "show", "addElement", "type", "$refs", "watermarkForm", "validate", "TEduBoardElementType", "TEDU_BOARD_ELEMENT_WATERMARK", "fix", "textWatermarkFixForm", "params", "textWatermarkForm", "deleteWatermark", "watermarkType", "removeElement", "updateElement", "updateElementById", "TEduElementOperatorType", "CONTENT", "OPACITY", "CHANGE_TEXT_SIZE", "BOARDER_COLOR"], "sources": ["src/components/ToolbarDialog/WatermarkDialog.vue"], "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n    <v-card>\n      <v-card-title class=\"headline lighten-2\"> 水印元素 </v-card-title>\n      <v-card-text>\n        <v-tabs v-model=\"watermarkElementTab\">\n          <v-tab>图片水印</v-tab>\n          <v-tab>静态文字水印</v-tab>\n          <v-tab>动态文字水印</v-tab>\n          <v-tab>水印元素管理</v-tab>\n        </v-tabs>\n        <v-row>\n          <v-col cols=\"12\" md=\"12\">\n            <v-tabs-items v-model=\"watermarkElementTab\">\n              <!-- 图片水印 -->\n              <v-tab-item>\n                <v-form ref=\"watermarkForm\">\n                  <v-row>\n                    <v-col cols=\"4\" md=\"6\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].content\"\n                        :rules=\"rules.urlRule\"\n                        label=\"图片水印url\"\n                        hint=\"请输入https协议的图片url\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].left\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].left, 0),\n                          loanMaxRule(elementParams['watermark'].left, 1000),\n                        ]\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].top\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].top, 0),\n                          loanMaxRule(elementParams['watermark'].top, 500),\n                        ]\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                  <v-row>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].width\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].width, 10),\n                          loanMaxRule(elementParams['watermark'].width, 200),\n                        ]\"\n                        label=\"宽度\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].height\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].height, 10),\n                          loanMaxRule(elementParams['watermark'].height, 200),\n                        ]\"\n                        label=\"高度\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"4\">\n                      <v-subheader class=\"pl-0\">\n                        透明度(范围[0.1, 1])\n                      </v-subheader>\n                      <v-slider\n                        v-model.number=\"elementParams['watermark'].opacity\"\n                        step=\"0.1\"\n                        max=\"1\"\n                        min=\"0.1\"\n                        :thumb-size=\"32\"\n                        thumb-label=\"always\"\n                      ></v-slider>\n                    </v-col>\n                  </v-row>\n                </v-form>\n              </v-tab-item>\n              <!-- 静态文字水印 -->\n              <v-tab-item>\n                <v-form ref=\"textWatermarkFixForm\">\n                  <v-row>\n                    <v-col cols=\"4\" md=\"6\">\n                      <v-text-field\n                        counter=\"20\"\n                        v-model.number=\"\n                          elementParams['textWatermarkFix'].content\n                        \"\n                        label=\"文字水印\"\n                        hint=\"文字水印内容\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermarkFix'].deg\"\n                        :rules=\"[\n                          loanMinRule(elementParams['textWatermarkFix'].deg, 0),\n                          loanMaxRule(\n                            elementParams['textWatermarkFix'].deg,\n                            360\n                          ),\n                        ]\"\n                        label=\"旋转角度\"\n                        suffix=\"度\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"4\">\n                      <v-subheader class=\"pl-0\">\n                        透明度(范围[0.1, 1])\n                      </v-subheader>\n                      <v-slider\n                        v-model.number=\"\n                          elementParams['textWatermarkFix'].opacity\n                        \"\n                        step=\"0.1\"\n                        max=\"1\"\n                        min=\"0.1\"\n                        :thumb-size=\"32\"\n                        thumb-label=\"always\"\n                      ></v-slider>\n                    </v-col>\n                  </v-row>\n                  <v-row>\n                    <v-col cols=\"2\">\n                      <v-text-field\n                        v-model.number=\"\n                          elementParams['textWatermarkFix'].textSize\n                        \"\n                        label=\"字号\"\n                        suffix=\"px\"\n                        :rules=\"[\n                          loanMinRule(\n                            elementParams['textWatermarkFix'].textSize,\n                            12\n                          ),\n                          loanMaxRule(\n                            elementParams['textWatermarkFix'].textSize,\n                            48\n                          ),\n                        ]\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\">\n                      <v-color-picker\n                        label=\"颜色\"\n                        v-model=\"elementParams['textWatermarkFix'].color\"\n                        canvas-height=\"60\"\n                        width=\"300\"\n                        hide-inputs\n                        mode=\"hexa\"\n                        flat\n                      ></v-color-picker>\n                    </v-col>\n                  </v-row>\n                </v-form>\n              </v-tab-item>\n              <!-- 动态文字水印 -->\n              <v-tab-item>\n                <v-form ref=\"textWatermarkForm\">\n                  <v-row>\n                    <v-col cols=\"4\" md=\"6\">\n                      <v-text-field\n                        counter=\"20\"\n                        v-model.number=\"elementParams['textWatermark'].content\"\n                        label=\"文字水印\"\n                        hint=\"文字水印内容\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermark'].deg\"\n                        :rules=\"[\n                          loanMinRule(elementParams['textWatermark'].deg, 0),\n                          loanMaxRule(elementParams['textWatermark'].deg, 360),\n                        ]\"\n                        label=\"旋转角度\"\n                        suffix=\"度\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"4\">\n                      <v-subheader class=\"pl-0\">\n                        透明度(范围[0.1, 1])\n                      </v-subheader>\n                      <v-slider\n                        v-model.number=\"elementParams['textWatermark'].opacity\"\n                        step=\"0.1\"\n                        max=\"1\"\n                        min=\"0.1\"\n                        :thumb-size=\"32\"\n                        thumb-label=\"always\"\n                      ></v-slider>\n                    </v-col>\n                  </v-row>\n                  <v-row>\n                    <v-col cols=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermark'].interval\"\n                        label=\"位置变化间隔\"\n                        suffix=\"ms\"\n                        :rules=\"[\n                          loanMinRule(\n                            elementParams['textWatermark'].interval,\n                            1000\n                          ),\n                          loanMaxRule(\n                            elementParams['textWatermark'].interval,\n                            10000\n                          ),\n                        ]\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermark'].textSize\"\n                        label=\"字号\"\n                        suffix=\"px\"\n                        :rules=\"[\n                          loanMinRule(\n                            elementParams['textWatermark'].textSize,\n                            12\n                          ),\n                          loanMaxRule(\n                            elementParams['textWatermark'].textSize,\n                            48\n                          ),\n                        ]\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\">\n                      <v-color-picker\n                        label=\"颜色\"\n                        v-model=\"elementParams['textWatermark'].color\"\n                        canvas-height=\"60\"\n                        width=\"300\"\n                        hide-inputs\n                        mode=\"hexa\"\n                        flat\n                      ></v-color-picker>\n                    </v-col>\n                  </v-row>\n                </v-form>\n              </v-tab-item>\n              <v-tab-item>\n                <v-btn\n                  class=\"ma-2\"\n                  outlined\n                  color=\"indigo\"\n                  @click=\"deleteWatermark(TEduBoard.TEduWatermarkType.Image)\"\n                >\n                  删除图片水印\n                </v-btn>\n                <v-btn\n                  class=\"ma-2\"\n                  outlined\n                  color=\"indigo\"\n                  @click=\"deleteWatermark(TEduBoard.TEduWatermarkType.TextFix)\"\n                >\n                  删除静态文本水印\n                </v-btn>\n                <v-btn\n                  class=\"ma-2\"\n                  outlined\n                  color=\"indigo\"\n                  @click=\"deleteWatermark(TEduBoard.TEduWatermarkType.Text)\"\n                >\n                  删除动态文本水印\n                </v-btn>\n              </v-tab-item>\n            </v-tabs-items>\n          </v-col>\n        </v-row>\n      </v-card-text>\n\n      <v-divider></v-divider>\n\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn color=\"primary\" text @click=\"addElement\">添加</v-btn>\n        <v-btn\n          v-if=\"\n            (watermarkElementTab === 1 && hasTextFixWatermark) ||\n            (watermarkElementTab === 2 && hasTextWatermark)\n          \"\n          color=\"primary\"\n          text\n          @click=\"updateElement\"\n          >修改(暂不支持角度和时间间隔)</v-btn\n        >\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      watermarkElementTab: null,\n      elementParams: {\n        watermark: {\n          title: \"图片水印\",\n          content: \"https://test-1259648581.file.myqcloud.com/image/logo.png\",\n          left: \"10\",\n          top: \"10\",\n          width: \"100\",\n          height: \"74\",\n          opacity: 0.8,\n        },\n\n        textWatermarkFix: {\n          title: \"静态文字水印\",\n          content: \"\",\n          deg: 0,\n          textSize: 16,\n          opacity: 0.8,\n          color: \"#FF0000\",\n          interval: 5000,\n        },\n\n        textWatermark: {\n          title: \"动态文字水印\",\n          content: \"\",\n          deg: 0,\n          textSize: 16,\n          opacity: 0.8,\n          color: \"#FF0000\",\n          interval: 5000,\n        },\n      },\n      rules: {\n        urlRule: [\n          (v) => !!v || \"url is required\",\n          (v) => /^https:\\/\\//.test(v) || \"url必须是https协议\",\n        ],\n      },\n    };\n  },\n\n  computed: {\n    hasTextWatermark() {\n      return !!window.teduBoard.getElementById(\n        TEduBoard.TEduWatermarkType.Text\n      );\n    },\n\n    hasTextFixWatermark() {\n      return !!window.teduBoard.getElementById(\n        TEduBoard.TEduWatermarkType.TextFix\n      );\n    },\n  },\n  watch: {\n    watermarkElementTab(tabIndex) {\n      if (tabIndex === 0) {\n        return;\n      }\n      if (tabIndex === 1) {\n        const element = window.teduBoard.getElementById(\n          TEduBoard.TEduWatermarkType.TextFix\n        );\n        if (element) {\n          this.elementParams.textWatermarkFix = {\n            title: \"静态文字水印\",\n            content: element.content,\n            deg: element.deg,\n            textSize: element.textSize,\n            opacity: element.opacity,\n            color: element.style.fillColor,\n            interval: element.interval,\n          };\n        }\n      } else if (tabIndex === 2) {\n        const element = window.teduBoard.getElementById(\n          TEduBoard.TEduWatermarkType.Text\n        );\n        if (element) {\n          this.elementParams.textWatermark = {\n            title: \"动态文字水印\",\n            content: element.content,\n            deg: element.deg,\n            textSize: element.textSize,\n            opacity: element.opacity,\n            color: element.style.fillColor,\n            interval: element.interval,\n          };\n        }\n      }\n    },\n  },\n\n  methods: {\n    loanMinRule(value, min) {\n      return (value || \"\") >= min || `不能小于最小值 ${min}`;\n    },\n    loanMaxRule(value, max) {\n      return (value || \"\") <= max || `不能大于最大值 ${max}`;\n    },\n    show() {\n      this.showDialog = true;\n    },\n    addElement() {\n      let type = \"\";\n      if (this.watermarkElementTab === 0) {\n        type = \"watermark\";\n      } else if (this.watermarkElementTab === 1) {\n        type = \"textWatermarkFix\";\n      } else if (this.watermarkElementTab === 2) {\n        type = \"textWatermark\";\n      }\n\n      switch (type) {\n        case \"watermark\": {\n          if (!this.$refs.watermarkForm.validate()) {\n            return;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_WATERMARK,\n            {\n              content: `${this.elementParams.watermark.content}`,\n              left: `${this.elementParams.watermark.left}px`,\n              top: `${this.elementParams.watermark.top}px`,\n              width: `${this.elementParams.watermark.width}px`,\n              height: `${this.elementParams.watermark.height}px`,\n              opacity: `${this.elementParams.watermark.opacity}`,\n              fix: false,\n            }\n          );\n          this.elementParams.watermark = {\n            title: \"学科公式\",\n            content: \"https://test-1259648581.file.myqcloud.com/image/logo.png\",\n            left: \"10\",\n            top: \"10\",\n            width: \"100\",\n            height: \"74\",\n            opacity: 0.8,\n          };\n          break;\n        }\n        case \"textWatermarkFix\": {\n          if (!this.$refs.textWatermarkFixForm.validate()) {\n            return;\n          }\n          const params = {\n            content: `${this.elementParams.textWatermarkFix.content}`,\n            deg: this.elementParams.textWatermarkFix.deg,\n            textSize: `${this.elementParams.textWatermarkFix.textSize}px`,\n            color: `${this.elementParams.textWatermarkFix.color}`,\n            fix: true,\n            opacity: this.elementParams.textWatermarkFix.opacity,\n          };\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_WATERMARK,\n            params\n          );\n          this.elementParams.textWatermarkFix = {\n            title: \"文字水印\",\n            content: \"\",\n            deg: 0,\n            textSize: 16,\n            opacity: 0.8,\n            color: \"#FF0000\",\n            interval: 5000,\n          };\n          break;\n        }\n        case \"textWatermark\": {\n          if (!this.$refs.textWatermarkForm.validate()) {\n            return;\n          }\n          const params = {\n            content: `${this.elementParams.textWatermark.content}`,\n            deg: this.elementParams.textWatermark.deg,\n            textSize: `${this.elementParams.textWatermark.textSize}px`,\n            color: `${this.elementParams.textWatermark.color}`,\n            fix: false,\n            opacity: this.elementParams.textWatermark.opacity,\n            interval: this.elementParams.textWatermark.interval,\n          };\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_WATERMARK,\n            params\n          );\n          this.elementParams.textWatermark = {\n            title: \"文字水印\",\n            content: \"\",\n            deg: 0,\n            textSize: 16,\n            opacity: 0.8,\n            color: \"#FF0000\",\n            interval: 5000,\n          };\n          break;\n        }\n      }\n      this.showDialog = false;\n    },\n\n    deleteWatermark(watermarkType) {\n      window.teduBoard.removeElement(watermarkType);\n    },\n\n    updateElement() {\n      if (this.watermarkElementTab === 1) {\n        // 修改内容\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.CONTENT,\n            value: this.elementParams[\"textWatermarkFix\"].content,\n          }\n        );\n        // 修改透明度\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.OPACITY,\n            value: this.elementParams[\"textWatermarkFix\"].opacity,\n          }\n        );\n        // 修改字号\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.CHANGE_TEXT_SIZE,\n            value: this.elementParams[\"textWatermarkFix\"].textSize + \"px\",\n          }\n        );\n        // 修改颜色\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.BOARDER_COLOR,\n            value: this.elementParams[\"textWatermarkFix\"].color,\n          }\n        );\n      } else if (this.watermarkElementTab === 2) {\n        // 修改内容\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.CONTENT,\n          value: this.elementParams[\"textWatermark\"].content,\n        });\n        // 修改透明度\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.OPACITY,\n          value: this.elementParams[\"textWatermark\"].opacity,\n        });\n        // 修改字号\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.CHANGE_TEXT_SIZE,\n          value: this.elementParams[\"textWatermark\"].textSize + \"px\",\n        });\n        // 修改颜色\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.BOARDER_COLOR,\n          value: this.elementParams[\"textWatermark\"].color,\n        });\n      }\n      this.showDialog = false;\n    },\n  },\n};\n</script>"], "mappings": ";AAsTA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,mBAAA;MACAC,aAAA;QACAC,SAAA;UACAC,KAAA;UACAC,OAAA;UACAC,IAAA;UACAC,GAAA;UACAC,KAAA;UACAC,MAAA;UACAC,OAAA;QACA;QAEAC,gBAAA;UACAP,KAAA;UACAC,OAAA;UACAO,GAAA;UACAC,QAAA;UACAH,OAAA;UACAI,KAAA;UACAC,QAAA;QACA;QAEAC,aAAA;UACAZ,KAAA;UACAC,OAAA;UACAO,GAAA;UACAC,QAAA;UACAH,OAAA;UACAI,KAAA;UACAC,QAAA;QACA;MACA;MACAE,KAAA;QACAC,OAAA,GACA,UAAAC,CAAA;UAAA,SAAAA,CAAA;QAAA,GACA,UAAAA,CAAA;UAAA,qBAAAC,IAAA,CAAAD,CAAA;QAAA;MAEA;IACA;EACA;EAEAE,QAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,SAAAC,MAAA,CAAAC,SAAA,CAAAC,cAAA,CACAC,SAAA,CAAAC,iBAAA,CAAAC,IACA;IACA;IAEAC,mBAAA,WAAAA,oBAAA;MACA,SAAAN,MAAA,CAAAC,SAAA,CAAAC,cAAA,CACAC,SAAA,CAAAC,iBAAA,CAAAG,OACA;IACA;EACA;EACAC,KAAA;IACA9B,mBAAA,WAAAA,oBAAA+B,QAAA;MACA,IAAAA,QAAA;QACA;MACA;MACA,IAAAA,QAAA;QACA,IAAAC,OAAA,GAAAV,MAAA,CAAAC,SAAA,CAAAC,cAAA,CACAC,SAAA,CAAAC,iBAAA,CAAAG,OACA;QACA,IAAAG,OAAA;UACA,KAAA/B,aAAA,CAAAS,gBAAA;YACAP,KAAA;YACAC,OAAA,EAAA4B,OAAA,CAAA5B,OAAA;YACAO,GAAA,EAAAqB,OAAA,CAAArB,GAAA;YACAC,QAAA,EAAAoB,OAAA,CAAApB,QAAA;YACAH,OAAA,EAAAuB,OAAA,CAAAvB,OAAA;YACAI,KAAA,EAAAmB,OAAA,CAAAC,KAAA,CAAAC,SAAA;YACApB,QAAA,EAAAkB,OAAA,CAAAlB;UACA;QACA;MACA,WAAAiB,QAAA;QACA,IAAAC,QAAA,GAAAV,MAAA,CAAAC,SAAA,CAAAC,cAAA,CACAC,SAAA,CAAAC,iBAAA,CAAAC,IACA;QACA,IAAAK,QAAA;UACA,KAAA/B,aAAA,CAAAc,aAAA;YACAZ,KAAA;YACAC,OAAA,EAAA4B,QAAA,CAAA5B,OAAA;YACAO,GAAA,EAAAqB,QAAA,CAAArB,GAAA;YACAC,QAAA,EAAAoB,QAAA,CAAApB,QAAA;YACAH,OAAA,EAAAuB,QAAA,CAAAvB,OAAA;YACAI,KAAA,EAAAmB,QAAA,CAAAC,KAAA,CAAAC,SAAA;YACApB,QAAA,EAAAkB,QAAA,CAAAlB;UACA;QACA;MACA;IACA;EACA;EAEAqB,OAAA;IACAC,WAAA,WAAAA,YAAAC,KAAA,EAAAC,GAAA;MACA,QAAAD,KAAA,WAAAC,GAAA,kDAAAC,MAAA,CAAAD,GAAA;IACA;IACAE,WAAA,WAAAA,YAAAH,KAAA,EAAAI,GAAA;MACA,QAAAJ,KAAA,WAAAI,GAAA,kDAAAF,MAAA,CAAAE,GAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAA3C,UAAA;IACA;IACA4C,UAAA,WAAAA,WAAA;MACA,IAAAC,IAAA;MACA,SAAA5C,mBAAA;QACA4C,IAAA;MACA,gBAAA5C,mBAAA;QACA4C,IAAA;MACA,gBAAA5C,mBAAA;QACA4C,IAAA;MACA;MAEA,QAAAA,IAAA;QACA;UAAA;YACA,UAAAC,KAAA,CAAAC,aAAA,CAAAC,QAAA;cACA;YACA;YACAzB,MAAA,CAAAC,SAAA,CAAAoB,UAAA,CACAlB,SAAA,CAAAuB,oBAAA,CAAAC,4BAAA,EACA;cACA7C,OAAA,KAAAmC,MAAA,MAAAtC,aAAA,CAAAC,SAAA,CAAAE,OAAA;cACAC,IAAA,KAAAkC,MAAA,MAAAtC,aAAA,CAAAC,SAAA,CAAAG,IAAA;cACAC,GAAA,KAAAiC,MAAA,MAAAtC,aAAA,CAAAC,SAAA,CAAAI,GAAA;cACAC,KAAA,KAAAgC,MAAA,MAAAtC,aAAA,CAAAC,SAAA,CAAAK,KAAA;cACAC,MAAA,KAAA+B,MAAA,MAAAtC,aAAA,CAAAC,SAAA,CAAAM,MAAA;cACAC,OAAA,KAAA8B,MAAA,MAAAtC,aAAA,CAAAC,SAAA,CAAAO,OAAA;cACAyC,GAAA;YACA,CACA;YACA,KAAAjD,aAAA,CAAAC,SAAA;cACAC,KAAA;cACAC,OAAA;cACAC,IAAA;cACAC,GAAA;cACAC,KAAA;cACAC,MAAA;cACAC,OAAA;YACA;YACA;UACA;QACA;UAAA;YACA,UAAAoC,KAAA,CAAAM,oBAAA,CAAAJ,QAAA;cACA;YACA;YACA,IAAAK,MAAA;cACAhD,OAAA,KAAAmC,MAAA,MAAAtC,aAAA,CAAAS,gBAAA,CAAAN,OAAA;cACAO,GAAA,OAAAV,aAAA,CAAAS,gBAAA,CAAAC,GAAA;cACAC,QAAA,KAAA2B,MAAA,MAAAtC,aAAA,CAAAS,gBAAA,CAAAE,QAAA;cACAC,KAAA,KAAA0B,MAAA,MAAAtC,aAAA,CAAAS,gBAAA,CAAAG,KAAA;cACAqC,GAAA;cACAzC,OAAA,OAAAR,aAAA,CAAAS,gBAAA,CAAAD;YACA;YACAa,MAAA,CAAAC,SAAA,CAAAoB,UAAA,CACAlB,SAAA,CAAAuB,oBAAA,CAAAC,4BAAA,EACAG,MACA;YACA,KAAAnD,aAAA,CAAAS,gBAAA;cACAP,KAAA;cACAC,OAAA;cACAO,GAAA;cACAC,QAAA;cACAH,OAAA;cACAI,KAAA;cACAC,QAAA;YACA;YACA;UACA;QACA;UAAA;YACA,UAAA+B,KAAA,CAAAQ,iBAAA,CAAAN,QAAA;cACA;YACA;YACA,IAAAK,OAAA;cACAhD,OAAA,KAAAmC,MAAA,MAAAtC,aAAA,CAAAc,aAAA,CAAAX,OAAA;cACAO,GAAA,OAAAV,aAAA,CAAAc,aAAA,CAAAJ,GAAA;cACAC,QAAA,KAAA2B,MAAA,MAAAtC,aAAA,CAAAc,aAAA,CAAAH,QAAA;cACAC,KAAA,KAAA0B,MAAA,MAAAtC,aAAA,CAAAc,aAAA,CAAAF,KAAA;cACAqC,GAAA;cACAzC,OAAA,OAAAR,aAAA,CAAAc,aAAA,CAAAN,OAAA;cACAK,QAAA,OAAAb,aAAA,CAAAc,aAAA,CAAAD;YACA;YACAQ,MAAA,CAAAC,SAAA,CAAAoB,UAAA,CACAlB,SAAA,CAAAuB,oBAAA,CAAAC,4BAAA,EACAG,OACA;YACA,KAAAnD,aAAA,CAAAc,aAAA;cACAZ,KAAA;cACAC,OAAA;cACAO,GAAA;cACAC,QAAA;cACAH,OAAA;cACAI,KAAA;cACAC,QAAA;YACA;YACA;UACA;MACA;MACA,KAAAf,UAAA;IACA;IAEAuD,eAAA,WAAAA,gBAAAC,aAAA;MACAjC,MAAA,CAAAC,SAAA,CAAAiC,aAAA,CAAAD,aAAA;IACA;IAEAE,aAAA,WAAAA,cAAA;MACA,SAAAzD,mBAAA;QACA;QACAsB,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CACAjC,SAAA,CAAAC,iBAAA,CAAAG,OAAA,EACA;UACAe,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAC,OAAA;UACAvB,KAAA,OAAApC,aAAA,qBAAAG;QACA,CACA;QACA;QACAkB,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CACAjC,SAAA,CAAAC,iBAAA,CAAAG,OAAA,EACA;UACAe,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAE,OAAA;UACAxB,KAAA,OAAApC,aAAA,qBAAAQ;QACA,CACA;QACA;QACAa,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CACAjC,SAAA,CAAAC,iBAAA,CAAAG,OAAA,EACA;UACAe,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAG,gBAAA;UACAzB,KAAA,OAAApC,aAAA,qBAAAW,QAAA;QACA,CACA;QACA;QACAU,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CACAjC,SAAA,CAAAC,iBAAA,CAAAG,OAAA,EACA;UACAe,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAI,aAAA;UACA1B,KAAA,OAAApC,aAAA,qBAAAY;QACA,CACA;MACA,gBAAAb,mBAAA;QACA;QACAsB,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CAAAjC,SAAA,CAAAC,iBAAA,CAAAC,IAAA;UACAiB,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAC,OAAA;UACAvB,KAAA,OAAApC,aAAA,kBAAAG;QACA;QACA;QACAkB,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CAAAjC,SAAA,CAAAC,iBAAA,CAAAC,IAAA;UACAiB,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAE,OAAA;UACAxB,KAAA,OAAApC,aAAA,kBAAAQ;QACA;QACA;QACAa,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CAAAjC,SAAA,CAAAC,iBAAA,CAAAC,IAAA;UACAiB,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAG,gBAAA;UACAzB,KAAA,OAAApC,aAAA,kBAAAW,QAAA;QACA;QACA;QACAU,MAAA,CAAAC,SAAA,CAAAmC,iBAAA,CAAAjC,SAAA,CAAAC,iBAAA,CAAAC,IAAA;UACAiB,IAAA,EAAAnB,SAAA,CAAAkC,uBAAA,CAAAI,aAAA;UACA1B,KAAA,OAAApC,aAAA,kBAAAY;QACA;MACA;MACA,KAAAd,UAAA;IACA;EACA;AACA", "ignoreList": []}]}