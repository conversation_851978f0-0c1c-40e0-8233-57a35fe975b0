{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/measurable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/measurable/index.js", "mtime": 1757335237139}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKLy8gSGVscGVycwppbXBvcnQgeyBjb252ZXJ0VG9Vbml0IH0gZnJvbSAnLi4vLi4vdXRpbC9oZWxwZXJzJzsgLy8gVHlwZXMKCmltcG9ydCBWdWUgZnJvbSAndnVlJzsKZXhwb3J0IGRlZmF1bHQgVnVlLmV4dGVuZCh7CiAgbmFtZTogJ21lYXN1cmFibGUnLAogIHByb3BzOiB7CiAgICBoZWlnaHQ6IFtOdW1iZXIsIFN0cmluZ10sCiAgICBtYXhIZWlnaHQ6IFtOdW1iZXIsIFN0cmluZ10sCiAgICBtYXhXaWR0aDogW051bWJlciwgU3RyaW5nXSwKICAgIG1pbkhlaWdodDogW051bWJlciwgU3RyaW5nXSwKICAgIG1pbldpZHRoOiBbTnVtYmVyLCBTdHJpbmddLAogICAgd2lkdGg6IFtOdW1iZXIsIFN0cmluZ10KICB9LAogIGNvbXB1dGVkOiB7CiAgICBtZWFzdXJhYmxlU3R5bGVzOiBmdW5jdGlvbiBtZWFzdXJhYmxlU3R5bGVzKCkgewogICAgICB2YXIgc3R5bGVzID0ge307CiAgICAgIHZhciBoZWlnaHQgPSBjb252ZXJ0VG9Vbml0KHRoaXMuaGVpZ2h0KTsKICAgICAgdmFyIG1pbkhlaWdodCA9IGNvbnZlcnRUb1VuaXQodGhpcy5taW5IZWlnaHQpOwogICAgICB2YXIgbWluV2lkdGggPSBjb252ZXJ0VG9Vbml0KHRoaXMubWluV2lkdGgpOwogICAgICB2YXIgbWF4SGVpZ2h0ID0gY29udmVydFRvVW5pdCh0aGlzLm1heEhlaWdodCk7CiAgICAgIHZhciBtYXhXaWR0aCA9IGNvbnZlcnRUb1VuaXQodGhpcy5tYXhXaWR0aCk7CiAgICAgIHZhciB3aWR0aCA9IGNvbnZlcnRUb1VuaXQodGhpcy53aWR0aCk7CiAgICAgIGlmIChoZWlnaHQpIHN0eWxlcy5oZWlnaHQgPSBoZWlnaHQ7CiAgICAgIGlmIChtaW5IZWlnaHQpIHN0eWxlcy5taW5IZWlnaHQgPSBtaW5IZWlnaHQ7CiAgICAgIGlmIChtaW5XaWR0aCkgc3R5bGVzLm1pbldpZHRoID0gbWluV2lkdGg7CiAgICAgIGlmIChtYXhIZWlnaHQpIHN0eWxlcy5tYXhIZWlnaHQgPSBtYXhIZWlnaHQ7CiAgICAgIGlmIChtYXhXaWR0aCkgc3R5bGVzLm1heFdpZHRoID0gbWF4V2lkdGg7CiAgICAgIGlmICh3aWR0aCkgc3R5bGVzLndpZHRoID0gd2lkdGg7CiAgICAgIHJldHVybiBzdHlsZXM7CiAgICB9CiAgfQp9KTs="}, {"version": 3, "names": ["convertToUnit", "<PERSON><PERSON>", "extend", "name", "props", "height", "Number", "String", "maxHeight", "max<PERSON><PERSON><PERSON>", "minHeight", "min<PERSON><PERSON><PERSON>", "width", "computed", "measurableStyles", "styles"], "sources": ["../../../src/mixins/measurable/index.ts"], "sourcesContent": ["// Helpers\nimport { convertToUnit } from '../../util/helpers'\n\n// Types\nimport Vue, { PropType } from 'vue'\n\nexport type NumberOrNumberString = PropType<string | number | undefined>\n\nexport default Vue.extend({\n  name: 'measurable',\n\n  props: {\n    height: [Number, String] as NumberOrNumberString,\n    maxHeight: [Number, String] as NumberOrNumberString,\n    maxWidth: [Number, String] as NumberOrNumberString,\n    minHeight: [Number, String] as NumberOrNumberString,\n    minWidth: [Number, String] as NumberOrNumberString,\n    width: [Number, String] as NumberOrNumberString,\n  },\n\n  computed: {\n    measurableStyles (): object {\n      const styles: Record<string, string> = {}\n\n      const height = convertToUnit(this.height)\n      const minHeight = convertToUnit(this.minHeight)\n      const minWidth = convertToUnit(this.minWidth)\n      const maxHeight = convertToUnit(this.maxHeight)\n      const maxWidth = convertToUnit(this.maxWidth)\n      const width = convertToUnit(this.width)\n\n      if (height) styles.height = height\n      if (minHeight) styles.minHeight = minHeight\n      if (minWidth) styles.minWidth = minWidth\n      if (maxHeight) styles.maxHeight = maxHeight\n      if (maxWidth) styles.maxWidth = maxWidth\n      if (width) styles.width = width\n\n      return styles\n    },\n  },\n})\n"], "mappings": ";AAAA;AACA,SAASA,aAAT,QAA8B,oBAA9B,C,CAEA;;AACA,OAAOC,GAAP,MAA8B,KAA9B;AAIA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,YADkB;EAGxBC,KAAK,EAAE;IACLC,MAAM,EAAE,CAACC,MAAD,EAASC,MAAT,CADH;IAELC,SAAS,EAAE,CAACF,MAAD,EAASC,MAAT,CAFN;IAGLE,QAAQ,EAAE,CAACH,MAAD,EAASC,MAAT,CAHL;IAILG,SAAS,EAAE,CAACJ,MAAD,EAASC,MAAT,CAJN;IAKLI,QAAQ,EAAE,CAACL,MAAD,EAASC,MAAT,CALL;IAMLK,KAAK,EAAE,CAACN,MAAD,EAASC,MAAT;EANF,CAHiB;EAYxBM,QAAQ,EAAE;IACRC,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,IAAMC,MAAM,GAA2B,EAAvC;MAEA,IAAMV,MAAM,GAAGL,aAAa,CAAC,KAAKK,MAAN,CAA5B;MACA,IAAMK,SAAS,GAAGV,aAAa,CAAC,KAAKU,SAAN,CAA/B;MACA,IAAMC,QAAQ,GAAGX,aAAa,CAAC,KAAKW,QAAN,CAA9B;MACA,IAAMH,SAAS,GAAGR,aAAa,CAAC,KAAKQ,SAAN,CAA/B;MACA,IAAMC,QAAQ,GAAGT,aAAa,CAAC,KAAKS,QAAN,CAA9B;MACA,IAAMG,KAAK,GAAGZ,aAAa,CAAC,KAAKY,KAAN,CAA3B;MAEA,IAAIP,MAAJ,EAAYU,MAAM,CAACV,MAAP,GAAgBA,MAAhB;MACZ,IAAIK,SAAJ,EAAeK,MAAM,CAACL,SAAP,GAAmBA,SAAnB;MACf,IAAIC,QAAJ,EAAcI,MAAM,CAACJ,QAAP,GAAkBA,QAAlB;MACd,IAAIH,SAAJ,EAAeO,MAAM,CAACP,SAAP,GAAmBA,SAAnB;MACf,IAAIC,QAAJ,EAAcM,MAAM,CAACN,QAAP,GAAkBA,QAAlB;MACd,IAAIG,KAAJ,EAAWG,MAAM,CAACH,KAAP,GAAeA,KAAf;MAEX,OAAOG,MAAP;IACD;EAnBO;AAZc,CAAX,CAAf", "ignoreList": []}]}