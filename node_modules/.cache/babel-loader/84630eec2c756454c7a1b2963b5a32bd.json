{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/colorable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/colorable/index.js", "mtime": 1757335237063}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "consoleError", "isCssColor", "extend", "name", "props", "color", "String", "methods", "setBackgroundColor", "data", "arguments", "length", "undefined", "style", "_objectSpread", "concat", "_defineProperty", "setTextColor", "_context", "_color$toString$trim$", "_trimInstanceProperty", "toString", "call", "split", "_color$toString$trim$2", "_slicedToArray", "colorName", "colorModifier"], "sources": ["../../../src/mixins/colorable/index.ts"], "sourcesContent": ["import Vue from 'vue'\nimport { VNodeData } from 'vue/types/vnode'\nimport { consoleError } from '../../util/console'\nimport { isCssColor } from '../../util/colorUtils'\n\nexport default Vue.extend({\n  name: 'colorable',\n\n  props: {\n    color: String,\n  },\n\n  methods: {\n    setBackgroundColor (color?: string | false, data: VNodeData = {}): VNodeData {\n      if (typeof data.style === 'string') {\n        // istanbul ignore next\n        consoleError('style must be an object', this)\n        // istanbul ignore next\n        return data\n      }\n      if (typeof data.class === 'string') {\n        // istanbul ignore next\n        consoleError('class must be an object', this)\n        // istanbul ignore next\n        return data\n      }\n      if (isCssColor(color)) {\n        data.style = {\n          ...data.style as object,\n          'background-color': `${color}`,\n          'border-color': `${color}`,\n        }\n      } else if (color) {\n        data.class = {\n          ...data.class,\n          [color]: true,\n        }\n      }\n\n      return data\n    },\n\n    setTextColor (color?: string | false, data: VNodeData = {}): VNodeData {\n      if (typeof data.style === 'string') {\n        // istanbul ignore next\n        consoleError('style must be an object', this)\n        // istanbul ignore next\n        return data\n      }\n      if (typeof data.class === 'string') {\n        // istanbul ignore next\n        consoleError('class must be an object', this)\n        // istanbul ignore next\n        return data\n      }\n      if (isCssColor(color)) {\n        data.style = {\n          ...data.style as object,\n          color: `${color}`,\n          'caret-color': `${color}`,\n        }\n      } else if (color) {\n        const [colorName, colorModifier] = color.toString().trim().split(' ', 2) as (string | undefined)[]\n        data.class = {\n          ...data.class,\n          [colorName + '--text']: true,\n        }\n        if (colorModifier) {\n          data.class['text--' + colorModifier] = true\n        }\n      }\n      return data\n    },\n  },\n})\n"], "mappings": ";;;;;;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA,SAASC,YAAT,QAA6B,oBAA7B;AACA,SAASC,UAAT,QAA2B,uBAA3B;AAEA,eAAeF,GAAG,CAACG,MAAJ,CAAW;EACxBC,IAAI,EAAE,WADkB;EAGxBC,KAAK,EAAE;IACLC,KAAK,EAAEC;EADF,CAHiB;EAOxBC,OAAO,EAAE;IACPC,kBAAkB,WAAlBA,kBAAkBA,CAAEH,KAAF,EAA8C;MAAA,IAApBI,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkB,EAA5C;MAChB,IAAI,OAAOD,IAAI,CAACI,KAAZ,KAAsB,QAA1B,EAAoC;QAClC;QACAb,YAAY,CAAC,yBAAD,EAA4B,IAA5B,CAAZ,CAFkC,CAGlC;;QACA,OAAOS,IAAP;MACD;MACD,IAAI,OAAOA,IAAI,SAAX,KAAsB,QAA1B,EAAoC;QAClC;QACAT,YAAY,CAAC,yBAAD,EAA4B,IAA5B,CAAZ,CAFkC,CAGlC;;QACA,OAAOS,IAAP;MACD;MACD,IAAIR,UAAU,CAACI,KAAD,CAAd,EAAuB;QACrBI,IAAI,CAACI,KAAL,GAAAC,aAAA,CAAAA,aAAA,KACKL,IAAI,CAACI,KADG;UAEX,uBAAAE,MAAA,CAAuBV,KAAK,CAFjB;UAGX,mBAAAU,MAAA,CAAmBV,KAAK;QAAA,EAH1B;MAKD,CAND,MAMO,IAAIA,KAAJ,EAAW;QAChBI,IAAI,SAAJ,GAAAK,aAAA,CAAAA,aAAA,KACKL,IAAI,SADI,OAAAO,eAAA,KAEVX,KAAD,EAAS,MAFX;MAID;MAED,OAAOI,IAAP;IACD,CA5BM;IA8BPQ,YAAY,WAAZA,YAAYA,CAAEZ,KAAF,EAA8C;MAAA,IAApBI,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkB,EAA5C;MACV,IAAI,OAAOD,IAAI,CAACI,KAAZ,KAAsB,QAA1B,EAAoC;QAClC;QACAb,YAAY,CAAC,yBAAD,EAA4B,IAA5B,CAAZ,CAFkC,CAGlC;;QACA,OAAOS,IAAP;MACD;MACD,IAAI,OAAOA,IAAI,SAAX,KAAsB,QAA1B,EAAoC;QAClC;QACAT,YAAY,CAAC,yBAAD,EAA4B,IAA5B,CAAZ,CAFkC,CAGlC;;QACA,OAAOS,IAAP;MACD;MACD,IAAIR,UAAU,CAACI,KAAD,CAAd,EAAuB;QACrBI,IAAI,CAACI,KAAL,GAAAC,aAAA,CAAAA,aAAA,KACKL,IAAI,CAACI,KADG;UAEXR,KAAK,KAAAU,MAAA,CAAKV,KAAK,CAFJ;UAGX,kBAAAU,MAAA,CAAkBV,KAAK;QAAA,EAHzB;MAKD,CAND,MAMO,IAAIA,KAAJ,EAAW;QAAA,IAAAa,QAAA;QAChB,IAAAC,qBAAA,GAAmCC,qBAAA,CAAAF,QAAA,GAAAb,KAAK,CAACgB,QAAN,IAAAC,IAAA,CAAAJ,QAAA,EAAwBK,KAAxB,CAA8B,GAA9B,EAAmC,CAAnC,CAAnC;UAAAC,sBAAA,GAAAC,cAAA,CAAAN,qBAAA;UAAOO,SAAD,GAAAF,sBAAA;UAAYG,aAAZ,GAAAH,sBAAA;QACNf,IAAI,SAAJ,GAAAK,aAAA,CAAAA,aAAA,KACKL,IAAI,SADI,OAAAO,eAAA,KAEVU,SAAS,GAAG,QAAb,EAAwB,MAF1B;QAIA,IAAIC,aAAJ,EAAmB;UACjBlB,IAAI,SAAJ,CAAW,WAAWkB,aAAtB,IAAuC,IAAvC;QACD;MACF;MACD,OAAOlB,IAAP;IACD;EA5DM;AAPe,CAAX,CAAf", "ignoreList": []}]}