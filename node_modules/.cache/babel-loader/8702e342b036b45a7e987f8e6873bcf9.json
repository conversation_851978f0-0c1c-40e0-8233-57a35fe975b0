{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTab.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTab.js", "mtime": 1757335239353}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["factory", "GroupableFactory", "Routable", "Themeable", "keyCodes", "mixins", "baseMixins", "extend", "name", "props", "ripple", "type", "Boolean", "Object", "data", "proxyClass", "computed", "classes", "_objectSpread", "options", "call", "disabled", "groupClasses", "value", "to", "href", "$router", "resolve", "$route", "append", "replace", "methods", "click", "e", "_context", "preventDefault", "_indexOfInstanceProperty", "detail", "$el", "blur", "$emit", "toggle", "isActive", "tabsBar", "mandatory", "render", "h", "_this", "_this$generateRouteLi", "generateRouteLink", "tag", "attrs", "String", "role", "tabindex", "on", "keydown", "keyCode", "enter", "$slots"], "sources": ["../../../src/components/VTabs/VTab.ts"], "sourcesContent": ["// Mixins\nimport { factory as GroupableFactory } from '../../mixins/groupable'\nimport Routable from '../../mixins/routable'\nimport Themeable from '../../mixins/themeable'\n\n// Utilities\nimport { keyCodes } from './../../util/helpers'\nimport mixins from '../../util/mixins'\nimport { ExtractVue } from './../../util/mixins'\n\n// Types\nimport { VNode } from 'vue/types'\n\n// Components\nimport VTabsBar from '../VTabs/VTabsBar'\n\nconst baseMixins = mixins(\n  Routable,\n  // Must be after routable\n  // to overwrite activeClass\n  GroupableFactory('tabsBar'),\n  Themeable\n)\n\ntype VTabBarInstance = InstanceType<typeof VTabsBar>\n\ninterface options extends ExtractVue<typeof baseMixins> {\n  $el: HTMLElement\n  tabsBar: VTabBarInstance\n}\n\nexport default baseMixins.extend<options>().extend(\n  /* @vue/component */\n).extend({\n  name: 'v-tab',\n\n  props: {\n    ripple: {\n      type: [Boolean, Object],\n      default: true,\n    },\n  },\n\n  data: () => ({\n    proxyClass: 'v-tab--active',\n  }),\n\n  computed: {\n    classes (): object {\n      return {\n        'v-tab': true,\n        ...Routable.options.computed.classes.call(this),\n        'v-tab--disabled': this.disabled,\n        ...this.groupClasses,\n      }\n    },\n    value (): any {\n      let to = this.to || this.href\n\n      if (to == null) return to\n\n      if (this.$router &&\n        this.to === Object(this.to)\n      ) {\n        const resolve = this.$router.resolve(\n          this.to,\n          this.$route,\n          this.append\n        )\n\n        to = resolve.href\n      }\n\n      return to.replace('#', '')\n    },\n  },\n\n  methods: {\n    click (e: KeyboardEvent | MouseEvent): void {\n      // Prevent keyboard actions\n      // from children elements\n      // within disabled tabs\n      if (this.disabled) {\n        e.preventDefault()\n        return\n      }\n\n      // If user provides an\n      // actual link, do not\n      // prevent default\n      if (this.href &&\n        this.href.indexOf('#') > -1\n      ) e.preventDefault()\n\n      if (e.detail) this.$el.blur()\n\n      this.$emit('click', e)\n\n      this.to || this.toggle()\n    },\n    toggle () {\n      // VItemGroup treats a change event as a click\n      if (!this.isActive || (!this.tabsBar.mandatory && !this.to)) {\n        this.$emit('change')\n      }\n    },\n  },\n\n  render (h): VNode {\n    const { tag, data } = this.generateRouteLink()\n\n    data.attrs = {\n      ...data.attrs,\n      'aria-selected': String(this.isActive),\n      role: 'tab',\n      tabindex: this.disabled ? -1 : 0,\n    }\n    data.on = {\n      ...data.on,\n      keydown: (e: KeyboardEvent) => {\n        if (e.keyCode === keyCodes.enter) this.click(e)\n\n        this.$emit('keydown', e)\n      },\n    }\n\n    return h(tag, data, this.$slots.default)\n  },\n})\n"], "mappings": ";;;;AAAA;AACA,SAASA,OAAO,IAAIC,gBAApB,QAA4C,wBAA5C;AACA,OAAOC,QAAP,MAAqB,uBAArB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,SAASC,QAAT,QAAyB,sBAAzB;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AASA,IAAMC,UAAU,GAAGD,MAAM,CACvBH,QADuB;AAEvB;AACA;AACAD,gBAAgB,CAAC,SAAD,CAJO,EAKvBE,SALuB,CAAzB;AAeA,eAAeG,UAAU,CAACC,MAAX,GAA6BA,MAA7B,GAEbA,MAFa,CAEN;EACPC,IAAI,EAAE,OADC;EAGPC,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAE,CAACC,OAAD,EAAUC,MAAV,CADA;MAEN,WAAS;IAFH;EADH,CAHA;EAUPC,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,UAAU,EAAE;IADD,CAAP;EAAA,CAVC;EAcPC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA;QACE,SAAS;MADJ,GAEFhB,QAAQ,CAACiB,OAAT,CAAiBH,QAAjB,CAA0BC,OAA1B,CAAkCG,IAAlC,CAAuC,IAAvC,CAFE;QAGL,mBAAmB,KAAKC;MAHnB,GAIF,KAAKC,YAAA;IAEX,CARO;IASRC,KAAK,WAALA,KAAKA,CAAA;MACH,IAAIC,EAAE,GAAG,KAAKA,EAAL,IAAW,KAAKC,IAAzB;MAEA,IAAID,EAAE,IAAI,IAAV,EAAgB,OAAOA,EAAP;MAEhB,IAAI,KAAKE,OAAL,IACF,KAAKF,EAAL,KAAYX,MAAM,CAAC,KAAKW,EAAN,CADpB,EAEE;QACA,IAAMG,OAAO,GAAG,KAAKD,OAAL,CAAaC,OAAb,CACd,KAAKH,EADS,EAEd,KAAKI,MAFS,EAGd,KAAKC,MAHS,CAAhB;QAMAL,EAAE,GAAGG,OAAO,CAACF,IAAb;MACD;MAED,OAAOD,EAAE,CAACM,OAAH,CAAW,GAAX,EAAgB,EAAhB,CAAP;IACD;EA3BO,CAdH;EA4CPC,OAAO,EAAE;IACPC,KAAK,WAALA,KAAKA,CAAEC,CAAF,EAA+B;MAAA,IAAAC,QAAA;MAClC;MACA;MACA;MACA,IAAI,KAAKb,QAAT,EAAmB;QACjBY,CAAC,CAACE,cAAF;QACA;MACD,CAPiC,CASlC;MACA;MACA;;MACA,IAAI,KAAKV,IAAL,IACFW,wBAAA,CAAAF,QAAA,QAAKT,IAAL,EAAAL,IAAA,CAAAc,QAAA,EAAkB,GAAlB,IAAyB,CAAC,CAD5B,EAEED,CAAC,CAACE,cAAF;MAEF,IAAIF,CAAC,CAACI,MAAN,EAAc,KAAKC,GAAL,CAASC,IAAT;MAEd,KAAKC,KAAL,CAAW,OAAX,EAAoBP,CAApB;MAEA,KAAKT,EAAL,IAAW,KAAKiB,MAAL,EAAX;IACD,CAtBM;IAuBPA,MAAM,WAANA,MAAMA,CAAA;MACJ;MACA,IAAI,CAAC,KAAKC,QAAN,IAAmB,CAAC,KAAKC,OAAL,CAAaC,SAAd,IAA2B,CAAC,KAAKpB,EAAxD,EAA6D;QAC3D,KAAKgB,KAAL,CAAW,QAAX;MACD;IACF;EA5BM,CA5CF;EA2EPK,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IAAA,IAAAC,KAAA;IACP,IAAAC,qBAAA,GAAsB,KAAKC,iBAAL,EAAtB;MAAQC,GAAF,GAAAF,qBAAA,CAAEE,GAAF;MAAOpC,IAAA,GAAAkC,qBAAA,CAAAlC,IAAA;IAEbA,IAAI,CAACqC,KAAL,GAAAjC,aAAA,CAAAA,aAAA,KACKJ,IAAI,CAACqC,KADG;MAEX,iBAAiBC,MAAM,CAAC,KAAKV,QAAN,CAFZ;MAGXW,IAAI,EAAE,KAHK;MAIXC,QAAQ,EAAE,KAAKjC,QAAL,GAAgB,CAAC,CAAjB,GAAqB;IAAA,EAJjC;IAMAP,IAAI,CAACyC,EAAL,GAAArC,aAAA,CAAAA,aAAA,KACKJ,IAAI,CAACyC,EADA;MAERC,OAAO,EAAG,SAAVA,OAAOA,CAAGvB,CAAD,EAAqB;QAC5B,IAAIA,CAAC,CAACwB,OAAF,KAAcrD,QAAQ,CAACsD,KAA3B,EAAkCX,KAAA,CAAKf,KAAL,CAAWC,CAAX;QAElCc,KAAA,CAAKP,KAAL,CAAW,SAAX,EAAsBP,CAAtB;MACD;IAAA,EANH;IASA,OAAOa,CAAC,CAACI,GAAD,EAAMpC,IAAN,EAAY,KAAK6C,MAAL,WAAZ,CAAR;EACD;AA9FM,CAFM,CAAf", "ignoreList": []}]}