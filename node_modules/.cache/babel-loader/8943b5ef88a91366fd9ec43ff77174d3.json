{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSelect/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSelect/index.js", "mtime": 1757335236939}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZTZWxlY3QgZnJvbSAnLi9WU2VsZWN0JzsKZXhwb3J0IHsgVlNlbGVjdCB9OwpleHBvcnQgZGVmYXVsdCBWU2VsZWN0Ow=="}, {"version": 3, "names": ["VSelect"], "sources": ["../../../src/components/VSelect/index.ts"], "sourcesContent": ["import VSelect from './VSelect'\n\nexport { VSelect }\nexport default VSelect\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,WAApB;AAEA,SAASA,OAAT;AACA,eAAeA,OAAf", "ignoreList": []}]}