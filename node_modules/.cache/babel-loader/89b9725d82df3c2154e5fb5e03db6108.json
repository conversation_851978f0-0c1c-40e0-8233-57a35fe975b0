{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/VSpacer.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/VSpacer.js", "mtime": 1757335239101}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICIuLi8uLi8uLi9zcmMvY29tcG9uZW50cy9WR3JpZC9fZ3JpZC5zYXNzIjsKaW1wb3J0IHsgY3JlYXRlU2ltcGxlRnVuY3Rpb25hbCB9IGZyb20gJy4uLy4uL3V0aWwvaGVscGVycyc7CmV4cG9ydCBkZWZhdWx0IGNyZWF0ZVNpbXBsZUZ1bmN0aW9uYWwoJ3NwYWNlcicsICdkaXYnLCAndi1zcGFjZXInKTs="}, {"version": 3, "names": ["createSimpleFunctional"], "sources": ["../../../src/components/VGrid/VSpacer.ts"], "sourcesContent": ["import './_grid.sass'\nimport { createSimpleFunctional } from '../../util/helpers'\n\nexport default createSimpleFunctional('spacer', 'div', 'v-spacer')\n"], "mappings": "AAAA,OAAO,0CAAP;AACA,SAASA,sBAAT,QAAuC,oBAAvC;AAEA,eAAeA,sBAAsB,CAAC,QAAD,EAAW,KAAX,EAAkB,UAAlB,CAArC", "ignoreList": []}]}