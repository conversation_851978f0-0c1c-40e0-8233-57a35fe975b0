{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCheckbox/VSimpleCheckbox.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCheckbox/VSimpleCheckbox.js", "mtime": 1757335238987}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "VIcon", "Colorable", "Themeable", "mergeData", "wrapInArray", "extend", "name", "functional", "directives", "props", "_objectSpread", "options", "disabled", "Boolean", "ripple", "type", "value", "indeterminate", "indeterminateIcon", "String", "onIcon", "offIcon", "render", "h", "_ref", "data", "listeners", "children", "icon", "push", "methods", "setTextColor", "color", "dark", "light", "staticClass", "def", "center", "on", "click", "e", "stopPropagation", "input", "_context", "_forEachInstanceProperty", "call", "f"], "sources": ["../../../src/components/VCheckbox/VSimpleCheckbox.ts"], "sourcesContent": ["import './VSimpleCheckbox.sass'\n\nimport Ripple from '../../directives/ripple'\n\nimport Vue, { VNode, VNodeDirective } from 'vue'\nimport { VIcon } from '../VIcon'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport Themeable from '../../mixins/themeable'\n\n// Utilities\nimport mergeData from '../../util/mergeData'\nimport { wrapInArray } from '../../util/helpers'\n\nexport default Vue.extend({\n  name: 'v-simple-checkbox',\n\n  functional: true,\n\n  directives: {\n    Ripple,\n  },\n\n  props: {\n    ...Colorable.options.props,\n    ...Themeable.options.props,\n    disabled: Boolean,\n    ripple: {\n      type: Boolean,\n      default: true,\n    },\n    value: Boolean,\n    indeterminate: Boolean,\n    indeterminateIcon: {\n      type: String,\n      default: '$checkboxIndeterminate',\n    },\n    onIcon: {\n      type: String,\n      default: '$checkboxOn',\n    },\n    offIcon: {\n      type: String,\n      default: '$checkboxOff',\n    },\n  },\n\n  render (h, { props, data, listeners }): VNode {\n    const children = []\n    let icon = props.offIcon\n    if (props.indeterminate) icon = props.indeterminateIcon\n    else if (props.value) icon = props.onIcon\n\n    children.push(h(VIcon, Colorable.options.methods.setTextColor(props.value && props.color, {\n      props: {\n        disabled: props.disabled,\n        dark: props.dark,\n        light: props.light,\n      },\n    }), icon))\n\n    if (props.ripple && !props.disabled) {\n      const ripple = h('div', Colorable.options.methods.setTextColor(props.color, {\n        staticClass: 'v-input--selection-controls__ripple',\n        directives: [{\n          def: Ripple,\n          name: 'ripple',\n          value: { center: true },\n        }] as VNodeDirective[],\n      }))\n\n      children.push(ripple)\n    }\n\n    return h('div',\n      mergeData(data, {\n        class: {\n          'v-simple-checkbox': true,\n          'v-simple-checkbox--disabled': props.disabled,\n        },\n        on: {\n          click: (e: MouseEvent) => {\n            e.stopPropagation()\n\n            if (data.on && data.on.input && !props.disabled) {\n              wrapInArray(data.on.input).forEach(f => f(!props.value))\n            }\n          },\n        },\n      }), [\n        h('div', { staticClass: 'v-input--selection-controls__input' }, children),\n      ])\n  },\n})\n"], "mappings": ";;;AAAA,OAAO,wDAAP;AAEA,OAAOA,MAAP,MAAmB,yBAAnB;AAEA,OAAOC,GAAP,MAA2C,KAA3C;AACA,SAASC,KAAT,QAAsB,UAAtB,C,CAEA;;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,SAAP,MAAsB,sBAAtB;AACA,SAASC,WAAT,QAA4B,oBAA5B;AAEA,eAAeL,GAAG,CAACM,MAAJ,CAAW;EACxBC,IAAI,EAAE,mBADkB;EAGxBC,UAAU,EAAE,IAHY;EAKxBC,UAAU,EAAE;IACVV,MAAA,EAAAA;EADU,CALY;EASxBW,KAAK,EAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACAT,SAAS,CAACU,OAAV,CAAkBF,KADhB,GAEFP,SAAS,CAACS,OAAV,CAAkBF,KAFhB;IAGLG,QAAQ,EAAEC,OAHL;IAILC,MAAM,EAAE;MACNC,IAAI,EAAEF,OADA;MAEN,WAAS;IAFH,CAJH;IAQLG,KAAK,EAAEH,OARF;IASLI,aAAa,EAAEJ,OATV;IAULK,iBAAiB,EAAE;MACjBH,IAAI,EAAEI,MADW;MAEjB,WAAS;IAFQ,CAVd;IAcLC,MAAM,EAAE;MACNL,IAAI,EAAEI,MADA;MAEN,WAAS;IAFH,CAdH;IAkBLE,OAAO,EAAE;MACPN,IAAI,EAAEI,MADC;MAEP,WAAS;IAFF;EAAA,EA3Ba;EAiCxBG,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAAC,IAAA,EAA+B;IAAA,IAAxBf,KAAF,GAAAe,IAAA,CAAEf,KAAF;MAASgB,IAAT,GAAAD,IAAA,CAASC,IAAT;MAAeC,SAAA,GAAAF,IAAA,CAAAE,SAAA;IACxB,IAAMC,QAAQ,GAAG,EAAjB;IACA,IAAIC,IAAI,GAAGnB,KAAK,CAACY,OAAjB;IACA,IAAIZ,KAAK,CAACQ,aAAV,EAAyBW,IAAI,GAAGnB,KAAK,CAACS,iBAAb,CAAzB,KACK,IAAIT,KAAK,CAACO,KAAV,EAAiBY,IAAI,GAAGnB,KAAK,CAACW,MAAb;IAEtBO,QAAQ,CAACE,IAAT,CAAcN,CAAC,CAACvB,KAAD,EAAQC,SAAS,CAACU,OAAV,CAAkBmB,OAAlB,CAA0BC,YAA1B,CAAuCtB,KAAK,CAACO,KAAN,IAAeP,KAAK,CAACuB,KAA5D,EAAmE;MACxFvB,KAAK,EAAE;QACLG,QAAQ,EAAEH,KAAK,CAACG,QADX;QAELqB,IAAI,EAAExB,KAAK,CAACwB,IAFP;QAGLC,KAAK,EAAEzB,KAAK,CAACyB;MAHR;IADiF,CAAnE,CAAR,EAMXN,IANW,CAAf;IAQA,IAAInB,KAAK,CAACK,MAAN,IAAgB,CAACL,KAAK,CAACG,QAA3B,EAAqC;MACnC,IAAME,MAAM,GAAGS,CAAC,CAAC,KAAD,EAAQtB,SAAS,CAACU,OAAV,CAAkBmB,OAAlB,CAA0BC,YAA1B,CAAuCtB,KAAK,CAACuB,KAA7C,EAAoD;QAC1EG,WAAW,EAAE,qCAD6D;QAE1E3B,UAAU,EAAE,CAAC;UACX4B,GAAG,EAAEtC,MADM;UAEXQ,IAAI,EAAE,QAFK;UAGXU,KAAK,EAAE;YAAEqB,MAAM,EAAE;UAAV;QAHI,CAAD;MAF8D,CAApD,CAAR,CAAhB;MASAV,QAAQ,CAACE,IAAT,CAAcf,MAAd;IACD;IAED,OAAOS,CAAC,CAAC,KAAD,EACNpB,SAAS,CAACsB,IAAD,EAAO;MACd,SAAO;QACL,qBAAqB,IADhB;QAEL,+BAA+BhB,KAAK,CAACG;MAFhC,CADO;MAKd0B,EAAE,EAAE;QACFC,KAAK,EAAG,SAARA,KAAKA,CAAGC,CAAD,EAAkB;UACvBA,CAAC,CAACC,eAAF;UAEA,IAAIhB,IAAI,CAACa,EAAL,IAAWb,IAAI,CAACa,EAAL,CAAQI,KAAnB,IAA4B,CAACjC,KAAK,CAACG,QAAvC,EAAiD;YAAA,IAAA+B,QAAA;YAC/CC,wBAAA,CAAAD,QAAA,GAAAvC,WAAW,CAACqB,IAAI,CAACa,EAAL,CAAQI,KAAT,CAAX,EAAAG,IAAA,CAAAF,QAAA,EAAmC,UAAAG,CAAC;cAAA,OAAIA,CAAC,CAAC,CAACrC,KAAK,CAACO,KAAR,CAAzC;YAAA;UACD;QACF;MAPC;IALU,CAAP,CADH,EAeF,CACFO,CAAC,CAAC,KAAD,EAAQ;MAAEY,WAAW,EAAE;IAAf,CAAR,EAA+DR,QAA/D,CADC,CAfE,CAAR;EAkBD;AA9EuB,CAAX,CAAf", "ignoreList": []}]}