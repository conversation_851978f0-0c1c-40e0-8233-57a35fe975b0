{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAvatar/VAvatar.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAvatar/VAvatar.js", "mtime": 1757335237629}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICIuLi8uLi8uLi9zcmMvY29tcG9uZW50cy9WQXZhdGFyL1ZBdmF0YXIuc2FzcyI7IC8vIE1peGlucwoKaW1wb3J0IENvbG9yYWJsZSBmcm9tICcuLi8uLi9taXhpbnMvY29sb3JhYmxlJzsKaW1wb3J0IE1lYXN1cmFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL21lYXN1cmFibGUnOwppbXBvcnQgUm91bmRhYmxlIGZyb20gJy4uLy4uL21peGlucy9yb3VuZGFibGUnOyAvLyBVdGlsaXRpZXMKCmltcG9ydCB7IGNvbnZlcnRUb1VuaXQgfSBmcm9tICcuLi8uLi91dGlsL2hlbHBlcnMnOwppbXBvcnQgbWl4aW5zIGZyb20gJy4uLy4uL3V0aWwvbWl4aW5zJzsKZXhwb3J0IGRlZmF1bHQgbWl4aW5zKENvbG9yYWJsZSwgTWVhc3VyYWJsZSwgUm91bmRhYmxlKS5leHRlbmQoewogIG5hbWU6ICd2LWF2YXRhcicsCiAgcHJvcHM6IHsKICAgIGxlZnQ6IEJvb2xlYW4sCiAgICByaWdodDogQm9vbGVhbiwKICAgIHNpemU6IHsKICAgICAgdHlwZTogW051bWJlciwgU3RyaW5nXSwKICAgICAgImRlZmF1bHQiOiA0OAogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNsYXNzZXM6IGZ1bmN0aW9uIGNsYXNzZXMoKSB7CiAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKHsKICAgICAgICAndi1hdmF0YXItLWxlZnQnOiB0aGlzLmxlZnQsCiAgICAgICAgJ3YtYXZhdGFyLS1yaWdodCc6IHRoaXMucmlnaHQKICAgICAgfSwgdGhpcy5yb3VuZGVkQ2xhc3Nlcyk7CiAgICB9LAogICAgc3R5bGVzOiBmdW5jdGlvbiBzdHlsZXMoKSB7CiAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKHsKICAgICAgICBoZWlnaHQ6IGNvbnZlcnRUb1VuaXQodGhpcy5zaXplKSwKICAgICAgICBtaW5XaWR0aDogY29udmVydFRvVW5pdCh0aGlzLnNpemUpLAogICAgICAgIHdpZHRoOiBjb252ZXJ0VG9Vbml0KHRoaXMuc2l6ZSkKICAgICAgfSwgdGhpcy5tZWFzdXJhYmxlU3R5bGVzKTsKICAgIH0KICB9LAogIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgpIHsKICAgIHZhciBkYXRhID0gewogICAgICBzdGF0aWNDbGFzczogJ3YtYXZhdGFyJywKICAgICAgImNsYXNzIjogdGhpcy5jbGFzc2VzLAogICAgICBzdHlsZTogdGhpcy5zdHlsZXMsCiAgICAgIG9uOiB0aGlzLiRsaXN0ZW5lcnMKICAgIH07CiAgICByZXR1cm4gaCgnZGl2JywgdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb2xvciwgZGF0YSksIHRoaXMuJHNsb3RzWyJkZWZhdWx0Il0pOwogIH0KfSk7"}, {"version": 3, "names": ["Colorable", "Measurable", "Roundable", "convertToUnit", "mixins", "extend", "name", "props", "left", "Boolean", "right", "size", "type", "Number", "String", "computed", "classes", "_objectSpread", "roundedClasses", "styles", "height", "min<PERSON><PERSON><PERSON>", "width", "measurableStyles", "render", "h", "data", "staticClass", "style", "on", "$listeners", "setBackgroundColor", "color", "$slots"], "sources": ["../../../src/components/VAvatar/VAvatar.ts"], "sourcesContent": ["import './VAvatar.sass'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport Measurable from '../../mixins/measurable'\nimport Roundable from '../../mixins/roundable'\n\n// Utilities\nimport { convertToUnit } from '../../util/helpers'\n\n// Types\nimport { VNode } from 'vue'\nimport mixins from '../../util/mixins'\n\nexport default mixins(\n  Colorable,\n  Measurable,\n  Roundable,\n  /* @vue/component */\n).extend({\n  name: 'v-avatar',\n\n  props: {\n    left: Boolean,\n    right: Boolean,\n    size: {\n      type: [Number, String],\n      default: 48,\n    },\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        'v-avatar--left': this.left,\n        'v-avatar--right': this.right,\n        ...this.roundedClasses,\n      }\n    },\n    styles (): object {\n      return {\n        height: convertToUnit(this.size),\n        minWidth: convertToUnit(this.size),\n        width: convertToUnit(this.size),\n        ...this.measurableStyles,\n      }\n    },\n  },\n\n  render (h): VNode {\n    const data = {\n      staticClass: 'v-avatar',\n      class: this.classes,\n      style: this.styles,\n      on: this.$listeners,\n    }\n\n    return h('div', this.setBackgroundColor(this.color, data), this.$slots.default)\n  },\n})\n"], "mappings": ";;AAAA,OAAO,8CAAP,C,CAEA;;AACA,OAAOA,SAAP,MAAsB,wBAAtB;AACA,OAAOC,UAAP,MAAuB,yBAAvB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,SAASC,aAAT,QAA8B,oBAA9B;AAIA,OAAOC,MAAP,MAAmB,mBAAnB;AAEA,eAAeA,MAAM,CACnBJ,SADmB,EAEnBC,UAFmB,EAGnBC,SAHmB,CAAN,CAKbG,MALa,CAKN;EACPC,IAAI,EAAE,UADC;EAGPC,KAAK,EAAE;IACLC,IAAI,EAAEC,OADD;IAELC,KAAK,EAAED,OAFF;IAGLE,IAAI,EAAE;MACJC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADF;MAEJ,WAAS;IAFL;EAHD,CAHA;EAYPC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA;QACE,kBAAkB,KAAKT,IADlB;QAEL,mBAAmB,KAAKE;MAFnB,GAGF,KAAKQ,cAAA;IAEX,CAPO;IAQRC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAAF,aAAA;QACEG,MAAM,EAAEjB,aAAa,CAAC,KAAKQ,IAAN,CADhB;QAELU,QAAQ,EAAElB,aAAa,CAAC,KAAKQ,IAAN,CAFlB;QAGLW,KAAK,EAAEnB,aAAa,CAAC,KAAKQ,IAAN;MAHf,GAIF,KAAKY,gBAAA;IAEX;EAfO,CAZH;EA8BPC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMC,IAAI,GAAG;MACXC,WAAW,EAAE,UADF;MAEX,SAAO,KAAKX,OAFD;MAGXY,KAAK,EAAE,KAAKT,MAHD;MAIXU,EAAE,EAAE,KAAKC;IAJE,CAAb;IAOA,OAAOL,CAAC,CAAC,KAAD,EAAQ,KAAKM,kBAAL,CAAwB,KAAKC,KAA7B,EAAoCN,IAApC,CAAR,EAAmD,KAAKO,MAAL,WAAnD,CAAR;EACD;AAvCM,CALM,CAAf", "ignoreList": []}]}