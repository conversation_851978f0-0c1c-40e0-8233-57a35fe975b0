{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VHover/VHover.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VHover/VHover.js", "mtime": 1757335238286}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Delayable", "Toggleable", "mixins", "console<PERSON>arn", "extend", "name", "props", "disabled", "type", "Boolean", "value", "undefined", "methods", "onMouseEnter", "runDelay", "onMouseLeave", "render", "$scopedSlots", "element", "hover", "isActive", "_Array$isArray", "length", "tag", "data", "_g", "mouseenter", "mouseleave"], "sources": ["../../../src/components/VHover/VHover.ts"], "sourcesContent": ["// Mixins\nimport Delayable from '../../mixins/delayable'\nimport Toggleable from '../../mixins/toggleable'\n\n// Utilities\nimport mixins from '../../util/mixins'\nimport { consoleWarn } from '../../util/console'\n\n// Types\nimport { VNode, ScopedSlotChildren } from 'vue/types/vnode'\n\nexport default mixins(\n  Delayable,\n  Toggleable\n  /* @vue/component */\n).extend({\n  name: 'v-hover',\n\n  props: {\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n    value: {\n      type: Boolean,\n      default: undefined,\n    },\n  },\n\n  methods: {\n    onMouseEnter () {\n      this.runDelay('open')\n    },\n    onMouseLeave () {\n      this.runDelay('close')\n    },\n  },\n\n  render (): VNode {\n    if (!this.$scopedSlots.default && this.value === undefined) {\n      consoleWarn('v-hover is missing a default scopedSlot or bound value', this)\n\n      return null as any\n    }\n\n    let element: VNode | ScopedSlotChildren\n\n    /* istanbul ignore else */\n    if (this.$scopedSlots.default) {\n      element = this.$scopedSlots.default({ hover: this.isActive })\n    }\n\n    if (Array.isArray(element) && element.length === 1) {\n      element = element[0]\n    }\n\n    if (!element || Array.isArray(element) || !element.tag) {\n      consoleWarn('v-hover should only contain a single element', this)\n\n      return element as any\n    }\n\n    if (!this.disabled) {\n      element.data = element.data || {}\n      this._g(element.data, {\n        mouseenter: this.onMouseEnter,\n        mouseleave: this.onMouseLeave,\n      })\n    }\n\n    return element\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAOA,SAAP,MAAsB,wBAAtB;AACA,OAAOC,UAAP,MAAuB,yBAAvB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,WAAT,QAA4B,oBAA5B;AAKA,eAAeD,MAAM,CACnBF,SADmB,EAEnBC;AACA,oBAHmB,CAAN,CAIbG,MAJa,CAIN;EACPC,IAAI,EAAE,SADC;EAGPC,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,OADE;MAER,WAAS;IAFD,CADL;IAKLC,KAAK,EAAE;MACLF,IAAI,EAAEC,OADD;MAEL,WAASE;IAFJ;EALF,CAHA;EAcPC,OAAO,EAAE;IACPC,YAAY,WAAZA,YAAYA,CAAA;MACV,KAAKC,QAAL,CAAc,MAAd;IACD,CAHM;IAIPC,YAAY,WAAZA,YAAYA,CAAA;MACV,KAAKD,QAAL,CAAc,OAAd;IACD;EANM,CAdF;EAuBPE,MAAM,WAANA,MAAMA,CAAA;IACJ,IAAI,CAAC,KAAKC,YAAL,WAAD,IAA8B,KAAKP,KAAL,KAAeC,SAAjD,EAA4D;MAC1DR,WAAW,CAAC,wDAAD,EAA2D,IAA3D,CAAX;MAEA,OAAO,IAAP;IACD;IAED,IAAIe,OAAJ;IAEA;;IACA,IAAI,KAAKD,YAAL,WAAJ,EAA+B;MAC7BC,OAAO,GAAG,KAAKD,YAAL,YAA0B;QAAEE,KAAK,EAAE,KAAKC;MAAd,CAA1B,CAAV;IACD;IAED,IAAIC,cAAA,CAAcH,OAAd,KAA0BA,OAAO,CAACI,MAAR,KAAmB,CAAjD,EAAoD;MAClDJ,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;IACD;IAED,IAAI,CAACA,OAAD,IAAYG,cAAA,CAAcH,OAAd,CAAZ,IAAsC,CAACA,OAAO,CAACK,GAAnD,EAAwD;MACtDpB,WAAW,CAAC,8CAAD,EAAiD,IAAjD,CAAX;MAEA,OAAOe,OAAP;IACD;IAED,IAAI,CAAC,KAAKX,QAAV,EAAoB;MAClBW,OAAO,CAACM,IAAR,GAAeN,OAAO,CAACM,IAAR,IAAgB,EAA/B;MACA,KAAKC,EAAL,CAAQP,OAAO,CAACM,IAAhB,EAAsB;QACpBE,UAAU,EAAE,KAAKb,YADG;QAEpBc,UAAU,EAAE,KAAKZ;MAFG,CAAtB;IAID;IAED,OAAOG,OAAP;EACD;AAxDM,CAJM,CAAf", "ignoreList": []}]}