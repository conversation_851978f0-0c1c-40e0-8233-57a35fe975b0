{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAvatar/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAvatar/index.js", "mtime": 1757335236790}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZBdmF0YXIgZnJvbSAnLi9WQXZhdGFyJzsKZXhwb3J0IHsgVkF2YXRhciB9OwpleHBvcnQgZGVmYXVsdCBWQXZhdGFyOw=="}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>"], "sources": ["../../../src/components/VAvatar/index.ts"], "sourcesContent": ["import VAvatar from './VAvatar'\n\nexport { VAvatar }\nexport default VAvatar\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,WAApB;AAEA,SAASA,OAAT;AACA,eAAeA,OAAf", "ignoreList": []}]}