{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/toggleable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/toggleable/index.js", "mtime": 1757335237264}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwpleHBvcnQgZnVuY3Rpb24gZmFjdG9yeSgpIHsKICB2YXIgcHJvcCA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogJ3ZhbHVlJzsKICB2YXIgZXZlbnQgPSBhcmd1bWVudHMubGVuZ3RoID4gMSAmJiBhcmd1bWVudHNbMV0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1sxXSA6ICdpbnB1dCc7CiAgcmV0dXJuIFZ1ZS5leHRlbmQoewogICAgbmFtZTogJ3RvZ2dsZWFibGUnLAogICAgbW9kZWw6IHsKICAgICAgcHJvcDogcHJvcCwKICAgICAgZXZlbnQ6IGV2ZW50CiAgICB9LAogICAgcHJvcHM6IF9kZWZpbmVQcm9wZXJ0eSh7fSwgcHJvcCwgewogICAgICByZXF1aXJlZDogZmFsc2UKICAgIH0pLAogICAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBpc0FjdGl2ZTogISF0aGlzW3Byb3BdCiAgICAgIH07CiAgICB9LAogICAgd2F0Y2g6IF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoe30sIHByb3AsIGZ1bmN0aW9uICh2YWwpIHsKICAgICAgdGhpcy5pc0FjdGl2ZSA9ICEhdmFsOwogICAgfSksICJpc0FjdGl2ZSIsIGZ1bmN0aW9uIGlzQWN0aXZlKHZhbCkgewogICAgICAhIXZhbCAhPT0gdGhpc1twcm9wXSAmJiB0aGlzLiRlbWl0KGV2ZW50LCB2YWwpOwogICAgfSkKICB9KTsKfQovKiBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXJlZGVjbGFyZSAqLwoKdmFyIFRvZ2dsZWFibGUgPSBmYWN0b3J5KCk7CmV4cG9ydCBkZWZhdWx0IFRvZ2dsZWFibGU7"}, {"version": 3, "names": ["<PERSON><PERSON>", "factory", "prop", "arguments", "length", "undefined", "event", "extend", "name", "model", "props", "_defineProperty", "required", "data", "isActive", "watch", "val", "$emit", "Toggleable"], "sources": ["../../../src/mixins/toggleable/index.ts"], "sourcesContent": ["import Vue, { VueConstructor } from 'vue'\n\nexport type Toggleable<T extends string = 'value'> = VueConstructor<Vue & { isActive: boolean } & Record<T, any>>\n\nexport function factory<T extends string = 'value'> (prop?: T, event?: string): Toggleable<T>\nexport function factory (prop = 'value', event = 'input') {\n  return Vue.extend({\n    name: 'toggleable',\n\n    model: { prop, event },\n\n    props: {\n      [prop]: { required: false },\n    },\n\n    data () {\n      return {\n        isActive: !!this[prop],\n      }\n    },\n\n    watch: {\n      [prop] (val) {\n        this.isActive = !!val\n      },\n      isActive (val) {\n        !!val !== this[prop] && this.$emit(event, val)\n      },\n    },\n  })\n}\n\n/* eslint-disable-next-line @typescript-eslint/no-redeclare */\nconst Toggleable = factory()\n\nexport default Toggleable\n"], "mappings": ";AAAA,OAAOA,GAAP,MAAoC,KAApC;AAKA,OAAM,SAAUC,OAAVA,CAAA,EAAkD;EAAA,IAA/BC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAA1B;EAAA,IAAmCG,KAAK,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,OAA3C;EACJ,OAAOH,GAAG,CAACO,MAAJ,CAAW;IAChBC,IAAI,EAAE,YADU;IAGhBC,KAAK,EAAE;MAAEP,IAAF,EAAEA,IAAF;MAAQI,KAAA,EAAAA;IAAR,CAHS;IAKhBI,KAAK,EAAAC,eAAA,KACFT,IAAD,EAAQ;MAAEU,QAAQ,EAAE;IAAZ,EANM;IAShBC,IAAI,WAAJA,IAAIA,CAAA;MACF,OAAO;QACLC,QAAQ,EAAE,CAAC,CAAC,KAAKZ,IAAL;MADP,CAAP;IAGD,CAbe;IAehBa,KAAK,EAAAJ,eAAA,CAAAA,eAAA,KACFT,IAAD,YAAQc,GAAR,EAAW;MACT,KAAKF,QAAL,GAAgB,CAAC,CAACE,GAAlB;IACD,CAHI,wBAILF,QAAQA,CAAEE,GAAF,EAAK;MACX,CAAC,CAACA,GAAF,KAAU,KAAKd,IAAL,CAAV,IAAwB,KAAKe,KAAL,CAAWX,KAAX,EAAkBU,GAAlB,CAAxB;IACD;EArBa,CAAX,CAAP;AAwBD;AAED;;AACA,IAAME,UAAU,GAAGjB,OAAO,EAA1B;AAEA,eAAeiB,UAAf", "ignoreList": []}]}