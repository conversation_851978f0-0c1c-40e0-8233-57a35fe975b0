{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/colors.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/colors.js", "mtime": 1757335235077}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["red", "_Object$freeze", "base", "lighten5", "lighten4", "lighten3", "lighten2", "lighten1", "darken1", "darken2", "darken3", "darken4", "accent1", "accent2", "accent3", "accent4", "pink", "purple", "deepPurple", "indigo", "blue", "lightBlue", "cyan", "teal", "green", "lightGreen", "lime", "yellow", "amber", "orange", "deepOrange", "brown", "blue<PERSON>rey", "grey", "shades", "black", "white", "transparent"], "sources": ["../../src/util/colors.ts"], "sourcesContent": ["const red = Object.freeze({\n  base: '#f44336',\n  lighten5: '#ffebee',\n  lighten4: '#ffcdd2',\n  lighten3: '#ef9a9a',\n  lighten2: '#e57373',\n  lighten1: '#ef5350',\n  darken1: '#e53935',\n  darken2: '#d32f2f',\n  darken3: '#c62828',\n  darken4: '#b71c1c',\n  accent1: '#ff8a80',\n  accent2: '#ff5252',\n  accent3: '#ff1744',\n  accent4: '#d50000',\n})\n\nconst pink = Object.freeze({\n  base: '#e91e63',\n  lighten5: '#fce4ec',\n  lighten4: '#f8bbd0',\n  lighten3: '#f48fb1',\n  lighten2: '#f06292',\n  lighten1: '#ec407a',\n  darken1: '#d81b60',\n  darken2: '#c2185b',\n  darken3: '#ad1457',\n  darken4: '#880e4f',\n  accent1: '#ff80ab',\n  accent2: '#ff4081',\n  accent3: '#f50057',\n  accent4: '#c51162',\n})\n\nconst purple = Object.freeze({\n  base: '#9c27b0',\n  lighten5: '#f3e5f5',\n  lighten4: '#e1bee7',\n  lighten3: '#ce93d8',\n  lighten2: '#ba68c8',\n  lighten1: '#ab47bc',\n  darken1: '#8e24aa',\n  darken2: '#7b1fa2',\n  darken3: '#6a1b9a',\n  darken4: '#4a148c',\n  accent1: '#ea80fc',\n  accent2: '#e040fb',\n  accent3: '#d500f9',\n  accent4: '#aa00ff',\n})\n\nconst deepPurple = Object.freeze({\n  base: '#673ab7',\n  lighten5: '#ede7f6',\n  lighten4: '#d1c4e9',\n  lighten3: '#b39ddb',\n  lighten2: '#9575cd',\n  lighten1: '#7e57c2',\n  darken1: '#5e35b1',\n  darken2: '#512da8',\n  darken3: '#4527a0',\n  darken4: '#311b92',\n  accent1: '#b388ff',\n  accent2: '#7c4dff',\n  accent3: '#651fff',\n  accent4: '#6200ea',\n})\n\nconst indigo = Object.freeze({\n  base: '#3f51b5',\n  lighten5: '#e8eaf6',\n  lighten4: '#c5cae9',\n  lighten3: '#9fa8da',\n  lighten2: '#7986cb',\n  lighten1: '#5c6bc0',\n  darken1: '#3949ab',\n  darken2: '#303f9f',\n  darken3: '#283593',\n  darken4: '#1a237e',\n  accent1: '#8c9eff',\n  accent2: '#536dfe',\n  accent3: '#3d5afe',\n  accent4: '#304ffe',\n})\n\nconst blue = Object.freeze({\n  base: '#2196f3',\n  lighten5: '#e3f2fd',\n  lighten4: '#bbdefb',\n  lighten3: '#90caf9',\n  lighten2: '#64b5f6',\n  lighten1: '#42a5f5',\n  darken1: '#1e88e5',\n  darken2: '#1976d2',\n  darken3: '#1565c0',\n  darken4: '#0d47a1',\n  accent1: '#82b1ff',\n  accent2: '#448aff',\n  accent3: '#2979ff',\n  accent4: '#2962ff',\n})\n\nconst lightBlue = Object.freeze({\n  base: '#03a9f4',\n  lighten5: '#e1f5fe',\n  lighten4: '#b3e5fc',\n  lighten3: '#81d4fa',\n  lighten2: '#4fc3f7',\n  lighten1: '#29b6f6',\n  darken1: '#039be5',\n  darken2: '#0288d1',\n  darken3: '#0277bd',\n  darken4: '#01579b',\n  accent1: '#80d8ff',\n  accent2: '#40c4ff',\n  accent3: '#00b0ff',\n  accent4: '#0091ea',\n})\n\nconst cyan = Object.freeze({\n  base: '#00bcd4',\n  lighten5: '#e0f7fa',\n  lighten4: '#b2ebf2',\n  lighten3: '#80deea',\n  lighten2: '#4dd0e1',\n  lighten1: '#26c6da',\n  darken1: '#00acc1',\n  darken2: '#0097a7',\n  darken3: '#00838f',\n  darken4: '#006064',\n  accent1: '#84ffff',\n  accent2: '#18ffff',\n  accent3: '#00e5ff',\n  accent4: '#00b8d4',\n})\n\nconst teal = Object.freeze({\n  base: '#009688',\n  lighten5: '#e0f2f1',\n  lighten4: '#b2dfdb',\n  lighten3: '#80cbc4',\n  lighten2: '#4db6ac',\n  lighten1: '#26a69a',\n  darken1: '#00897b',\n  darken2: '#00796b',\n  darken3: '#00695c',\n  darken4: '#004d40',\n  accent1: '#a7ffeb',\n  accent2: '#64ffda',\n  accent3: '#1de9b6',\n  accent4: '#00bfa5',\n})\n\nconst green = Object.freeze({\n  base: '#4caf50',\n  lighten5: '#e8f5e9',\n  lighten4: '#c8e6c9',\n  lighten3: '#a5d6a7',\n  lighten2: '#81c784',\n  lighten1: '#66bb6a',\n  darken1: '#43a047',\n  darken2: '#388e3c',\n  darken3: '#2e7d32',\n  darken4: '#1b5e20',\n  accent1: '#b9f6ca',\n  accent2: '#69f0ae',\n  accent3: '#00e676',\n  accent4: '#00c853',\n})\n\nconst lightGreen = Object.freeze({\n  base: '#8bc34a',\n  lighten5: '#f1f8e9',\n  lighten4: '#dcedc8',\n  lighten3: '#c5e1a5',\n  lighten2: '#aed581',\n  lighten1: '#9ccc65',\n  darken1: '#7cb342',\n  darken2: '#689f38',\n  darken3: '#558b2f',\n  darken4: '#33691e',\n  accent1: '#ccff90',\n  accent2: '#b2ff59',\n  accent3: '#76ff03',\n  accent4: '#64dd17',\n})\n\nconst lime = Object.freeze({\n  base: '#cddc39',\n  lighten5: '#f9fbe7',\n  lighten4: '#f0f4c3',\n  lighten3: '#e6ee9c',\n  lighten2: '#dce775',\n  lighten1: '#d4e157',\n  darken1: '#c0ca33',\n  darken2: '#afb42b',\n  darken3: '#9e9d24',\n  darken4: '#827717',\n  accent1: '#f4ff81',\n  accent2: '#eeff41',\n  accent3: '#c6ff00',\n  accent4: '#aeea00',\n})\n\nconst yellow = Object.freeze({\n  base: '#ffeb3b',\n  lighten5: '#fffde7',\n  lighten4: '#fff9c4',\n  lighten3: '#fff59d',\n  lighten2: '#fff176',\n  lighten1: '#ffee58',\n  darken1: '#fdd835',\n  darken2: '#fbc02d',\n  darken3: '#f9a825',\n  darken4: '#f57f17',\n  accent1: '#ffff8d',\n  accent2: '#ffff00',\n  accent3: '#ffea00',\n  accent4: '#ffd600',\n})\n\nconst amber = Object.freeze({\n  base: '#ffc107',\n  lighten5: '#fff8e1',\n  lighten4: '#ffecb3',\n  lighten3: '#ffe082',\n  lighten2: '#ffd54f',\n  lighten1: '#ffca28',\n  darken1: '#ffb300',\n  darken2: '#ffa000',\n  darken3: '#ff8f00',\n  darken4: '#ff6f00',\n  accent1: '#ffe57f',\n  accent2: '#ffd740',\n  accent3: '#ffc400',\n  accent4: '#ffab00',\n})\n\nconst orange = Object.freeze({\n  base: '#ff9800',\n  lighten5: '#fff3e0',\n  lighten4: '#ffe0b2',\n  lighten3: '#ffcc80',\n  lighten2: '#ffb74d',\n  lighten1: '#ffa726',\n  darken1: '#fb8c00',\n  darken2: '#f57c00',\n  darken3: '#ef6c00',\n  darken4: '#e65100',\n  accent1: '#ffd180',\n  accent2: '#ffab40',\n  accent3: '#ff9100',\n  accent4: '#ff6d00',\n})\n\nconst deepOrange = Object.freeze({\n  base: '#ff5722',\n  lighten5: '#fbe9e7',\n  lighten4: '#ffccbc',\n  lighten3: '#ffab91',\n  lighten2: '#ff8a65',\n  lighten1: '#ff7043',\n  darken1: '#f4511e',\n  darken2: '#e64a19',\n  darken3: '#d84315',\n  darken4: '#bf360c',\n  accent1: '#ff9e80',\n  accent2: '#ff6e40',\n  accent3: '#ff3d00',\n  accent4: '#dd2c00',\n})\n\nconst brown = Object.freeze({\n  base: '#795548',\n  lighten5: '#efebe9',\n  lighten4: '#d7ccc8',\n  lighten3: '#bcaaa4',\n  lighten2: '#a1887f',\n  lighten1: '#8d6e63',\n  darken1: '#6d4c41',\n  darken2: '#5d4037',\n  darken3: '#4e342e',\n  darken4: '#3e2723',\n})\n\nconst blueGrey = Object.freeze({\n  base: '#607d8b',\n  lighten5: '#eceff1',\n  lighten4: '#cfd8dc',\n  lighten3: '#b0bec5',\n  lighten2: '#90a4ae',\n  lighten1: '#78909c',\n  darken1: '#546e7a',\n  darken2: '#455a64',\n  darken3: '#37474f',\n  darken4: '#263238',\n})\n\nconst grey = Object.freeze({\n  base: '#9e9e9e',\n  lighten5: '#fafafa',\n  lighten4: '#f5f5f5',\n  lighten3: '#eeeeee',\n  lighten2: '#e0e0e0',\n  lighten1: '#bdbdbd',\n  darken1: '#757575',\n  darken2: '#616161',\n  darken3: '#424242',\n  darken4: '#212121',\n})\n\nconst shades = Object.freeze({\n  black: '#000000',\n  white: '#ffffff',\n  transparent: 'transparent',\n})\n\nexport default Object.freeze({\n  red,\n  pink,\n  purple,\n  deepPurple,\n  indigo,\n  blue,\n  lightBlue,\n  cyan,\n  teal,\n  green,\n  lightGreen,\n  lime,\n  yellow,\n  amber,\n  orange,\n  deepOrange,\n  brown,\n  blueGrey,\n  grey,\n  shades,\n})\n"], "mappings": ";AAAA,IAAMA,GAAG,GAAGC,cAAA,CAAc;EACxBC,IAAI,EAAE,SADkB;EAExBC,QAAQ,EAAE,SAFc;EAGxBC,QAAQ,EAAE,SAHc;EAIxBC,QAAQ,EAAE,SAJc;EAKxBC,QAAQ,EAAE,SALc;EAMxBC,QAAQ,EAAE,SANc;EAOxBC,OAAO,EAAE,SAPe;EAQxBC,OAAO,EAAE,SARe;EASxBC,OAAO,EAAE,SATe;EAUxBC,OAAO,EAAE,SAVe;EAWxBC,OAAO,EAAE,SAXe;EAYxBC,OAAO,EAAE,SAZe;EAaxBC,OAAO,EAAE,SAbe;EAcxBC,OAAO,EAAE;AAde,CAAd,CAAZ;AAiBA,IAAMC,IAAI,GAAGf,cAAA,CAAc;EACzBC,IAAI,EAAE,SADmB;EAEzBC,QAAQ,EAAE,SAFe;EAGzBC,QAAQ,EAAE,SAHe;EAIzBC,QAAQ,EAAE,SAJe;EAKzBC,QAAQ,EAAE,SALe;EAMzBC,QAAQ,EAAE,SANe;EAOzBC,OAAO,EAAE,SAPgB;EAQzBC,OAAO,EAAE,SARgB;EASzBC,OAAO,EAAE,SATgB;EAUzBC,OAAO,EAAE,SAVgB;EAWzBC,OAAO,EAAE,SAXgB;EAYzBC,OAAO,EAAE,SAZgB;EAazBC,OAAO,EAAE,SAbgB;EAczBC,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAME,MAAM,GAAGhB,cAAA,CAAc;EAC3BC,IAAI,EAAE,SADqB;EAE3BC,QAAQ,EAAE,SAFiB;EAG3BC,QAAQ,EAAE,SAHiB;EAI3BC,QAAQ,EAAE,SAJiB;EAK3BC,QAAQ,EAAE,SALiB;EAM3BC,QAAQ,EAAE,SANiB;EAO3BC,OAAO,EAAE,SAPkB;EAQ3BC,OAAO,EAAE,SARkB;EAS3BC,OAAO,EAAE,SATkB;EAU3BC,OAAO,EAAE,SAVkB;EAW3BC,OAAO,EAAE,SAXkB;EAY3BC,OAAO,EAAE,SAZkB;EAa3BC,OAAO,EAAE,SAbkB;EAc3BC,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAMG,UAAU,GAAGjB,cAAA,CAAc;EAC/BC,IAAI,EAAE,SADyB;EAE/BC,QAAQ,EAAE,SAFqB;EAG/BC,QAAQ,EAAE,SAHqB;EAI/BC,QAAQ,EAAE,SAJqB;EAK/BC,QAAQ,EAAE,SALqB;EAM/BC,QAAQ,EAAE,SANqB;EAO/BC,OAAO,EAAE,SAPsB;EAQ/BC,OAAO,EAAE,SARsB;EAS/BC,OAAO,EAAE,SATsB;EAU/BC,OAAO,EAAE,SAVsB;EAW/BC,OAAO,EAAE,SAXsB;EAY/BC,OAAO,EAAE,SAZsB;EAa/BC,OAAO,EAAE,SAbsB;EAc/BC,OAAO,EAAE;AAdsB,CAAd,CAAnB;AAiBA,IAAMI,MAAM,GAAGlB,cAAA,CAAc;EAC3BC,IAAI,EAAE,SADqB;EAE3BC,QAAQ,EAAE,SAFiB;EAG3BC,QAAQ,EAAE,SAHiB;EAI3BC,QAAQ,EAAE,SAJiB;EAK3BC,QAAQ,EAAE,SALiB;EAM3BC,QAAQ,EAAE,SANiB;EAO3BC,OAAO,EAAE,SAPkB;EAQ3BC,OAAO,EAAE,SARkB;EAS3BC,OAAO,EAAE,SATkB;EAU3BC,OAAO,EAAE,SAVkB;EAW3BC,OAAO,EAAE,SAXkB;EAY3BC,OAAO,EAAE,SAZkB;EAa3BC,OAAO,EAAE,SAbkB;EAc3BC,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAMK,IAAI,GAAGnB,cAAA,CAAc;EACzBC,IAAI,EAAE,SADmB;EAEzBC,QAAQ,EAAE,SAFe;EAGzBC,QAAQ,EAAE,SAHe;EAIzBC,QAAQ,EAAE,SAJe;EAKzBC,QAAQ,EAAE,SALe;EAMzBC,QAAQ,EAAE,SANe;EAOzBC,OAAO,EAAE,SAPgB;EAQzBC,OAAO,EAAE,SARgB;EASzBC,OAAO,EAAE,SATgB;EAUzBC,OAAO,EAAE,SAVgB;EAWzBC,OAAO,EAAE,SAXgB;EAYzBC,OAAO,EAAE,SAZgB;EAazBC,OAAO,EAAE,SAbgB;EAczBC,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAMM,SAAS,GAAGpB,cAAA,CAAc;EAC9BC,IAAI,EAAE,SADwB;EAE9BC,QAAQ,EAAE,SAFoB;EAG9BC,QAAQ,EAAE,SAHoB;EAI9BC,QAAQ,EAAE,SAJoB;EAK9BC,QAAQ,EAAE,SALoB;EAM9BC,QAAQ,EAAE,SANoB;EAO9BC,OAAO,EAAE,SAPqB;EAQ9BC,OAAO,EAAE,SARqB;EAS9BC,OAAO,EAAE,SATqB;EAU9BC,OAAO,EAAE,SAVqB;EAW9BC,OAAO,EAAE,SAXqB;EAY9BC,OAAO,EAAE,SAZqB;EAa9BC,OAAO,EAAE,SAbqB;EAc9BC,OAAO,EAAE;AAdqB,CAAd,CAAlB;AAiBA,IAAMO,IAAI,GAAGrB,cAAA,CAAc;EACzBC,IAAI,EAAE,SADmB;EAEzBC,QAAQ,EAAE,SAFe;EAGzBC,QAAQ,EAAE,SAHe;EAIzBC,QAAQ,EAAE,SAJe;EAKzBC,QAAQ,EAAE,SALe;EAMzBC,QAAQ,EAAE,SANe;EAOzBC,OAAO,EAAE,SAPgB;EAQzBC,OAAO,EAAE,SARgB;EASzBC,OAAO,EAAE,SATgB;EAUzBC,OAAO,EAAE,SAVgB;EAWzBC,OAAO,EAAE,SAXgB;EAYzBC,OAAO,EAAE,SAZgB;EAazBC,OAAO,EAAE,SAbgB;EAczBC,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAMQ,IAAI,GAAGtB,cAAA,CAAc;EACzBC,IAAI,EAAE,SADmB;EAEzBC,QAAQ,EAAE,SAFe;EAGzBC,QAAQ,EAAE,SAHe;EAIzBC,QAAQ,EAAE,SAJe;EAKzBC,QAAQ,EAAE,SALe;EAMzBC,QAAQ,EAAE,SANe;EAOzBC,OAAO,EAAE,SAPgB;EAQzBC,OAAO,EAAE,SARgB;EASzBC,OAAO,EAAE,SATgB;EAUzBC,OAAO,EAAE,SAVgB;EAWzBC,OAAO,EAAE,SAXgB;EAYzBC,OAAO,EAAE,SAZgB;EAazBC,OAAO,EAAE,SAbgB;EAczBC,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAMS,KAAK,GAAGvB,cAAA,CAAc;EAC1BC,IAAI,EAAE,SADoB;EAE1BC,QAAQ,EAAE,SAFgB;EAG1BC,QAAQ,EAAE,SAHgB;EAI1BC,QAAQ,EAAE,SAJgB;EAK1BC,QAAQ,EAAE,SALgB;EAM1BC,QAAQ,EAAE,SANgB;EAO1BC,OAAO,EAAE,SAPiB;EAQ1BC,OAAO,EAAE,SARiB;EAS1BC,OAAO,EAAE,SATiB;EAU1BC,OAAO,EAAE,SAViB;EAW1BC,OAAO,EAAE,SAXiB;EAY1BC,OAAO,EAAE,SAZiB;EAa1BC,OAAO,EAAE,SAbiB;EAc1BC,OAAO,EAAE;AAdiB,CAAd,CAAd;AAiBA,IAAMU,UAAU,GAAGxB,cAAA,CAAc;EAC/BC,IAAI,EAAE,SADyB;EAE/BC,QAAQ,EAAE,SAFqB;EAG/BC,QAAQ,EAAE,SAHqB;EAI/BC,QAAQ,EAAE,SAJqB;EAK/BC,QAAQ,EAAE,SALqB;EAM/BC,QAAQ,EAAE,SANqB;EAO/BC,OAAO,EAAE,SAPsB;EAQ/BC,OAAO,EAAE,SARsB;EAS/BC,OAAO,EAAE,SATsB;EAU/BC,OAAO,EAAE,SAVsB;EAW/BC,OAAO,EAAE,SAXsB;EAY/BC,OAAO,EAAE,SAZsB;EAa/BC,OAAO,EAAE,SAbsB;EAc/BC,OAAO,EAAE;AAdsB,CAAd,CAAnB;AAiBA,IAAMW,IAAI,GAAGzB,cAAA,CAAc;EACzBC,IAAI,EAAE,SADmB;EAEzBC,QAAQ,EAAE,SAFe;EAGzBC,QAAQ,EAAE,SAHe;EAIzBC,QAAQ,EAAE,SAJe;EAKzBC,QAAQ,EAAE,SALe;EAMzBC,QAAQ,EAAE,SANe;EAOzBC,OAAO,EAAE,SAPgB;EAQzBC,OAAO,EAAE,SARgB;EASzBC,OAAO,EAAE,SATgB;EAUzBC,OAAO,EAAE,SAVgB;EAWzBC,OAAO,EAAE,SAXgB;EAYzBC,OAAO,EAAE,SAZgB;EAazBC,OAAO,EAAE,SAbgB;EAczBC,OAAO,EAAE;AAdgB,CAAd,CAAb;AAiBA,IAAMY,MAAM,GAAG1B,cAAA,CAAc;EAC3BC,IAAI,EAAE,SADqB;EAE3BC,QAAQ,EAAE,SAFiB;EAG3BC,QAAQ,EAAE,SAHiB;EAI3BC,QAAQ,EAAE,SAJiB;EAK3BC,QAAQ,EAAE,SALiB;EAM3BC,QAAQ,EAAE,SANiB;EAO3BC,OAAO,EAAE,SAPkB;EAQ3BC,OAAO,EAAE,SARkB;EAS3BC,OAAO,EAAE,SATkB;EAU3BC,OAAO,EAAE,SAVkB;EAW3BC,OAAO,EAAE,SAXkB;EAY3BC,OAAO,EAAE,SAZkB;EAa3BC,OAAO,EAAE,SAbkB;EAc3BC,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAMa,KAAK,GAAG3B,cAAA,CAAc;EAC1BC,IAAI,EAAE,SADoB;EAE1BC,QAAQ,EAAE,SAFgB;EAG1BC,QAAQ,EAAE,SAHgB;EAI1BC,QAAQ,EAAE,SAJgB;EAK1BC,QAAQ,EAAE,SALgB;EAM1BC,QAAQ,EAAE,SANgB;EAO1BC,OAAO,EAAE,SAPiB;EAQ1BC,OAAO,EAAE,SARiB;EAS1BC,OAAO,EAAE,SATiB;EAU1BC,OAAO,EAAE,SAViB;EAW1BC,OAAO,EAAE,SAXiB;EAY1BC,OAAO,EAAE,SAZiB;EAa1BC,OAAO,EAAE,SAbiB;EAc1BC,OAAO,EAAE;AAdiB,CAAd,CAAd;AAiBA,IAAMc,MAAM,GAAG5B,cAAA,CAAc;EAC3BC,IAAI,EAAE,SADqB;EAE3BC,QAAQ,EAAE,SAFiB;EAG3BC,QAAQ,EAAE,SAHiB;EAI3BC,QAAQ,EAAE,SAJiB;EAK3BC,QAAQ,EAAE,SALiB;EAM3BC,QAAQ,EAAE,SANiB;EAO3BC,OAAO,EAAE,SAPkB;EAQ3BC,OAAO,EAAE,SARkB;EAS3BC,OAAO,EAAE,SATkB;EAU3BC,OAAO,EAAE,SAVkB;EAW3BC,OAAO,EAAE,SAXkB;EAY3BC,OAAO,EAAE,SAZkB;EAa3BC,OAAO,EAAE,SAbkB;EAc3BC,OAAO,EAAE;AAdkB,CAAd,CAAf;AAiBA,IAAMe,UAAU,GAAG7B,cAAA,CAAc;EAC/BC,IAAI,EAAE,SADyB;EAE/BC,QAAQ,EAAE,SAFqB;EAG/BC,QAAQ,EAAE,SAHqB;EAI/BC,QAAQ,EAAE,SAJqB;EAK/BC,QAAQ,EAAE,SALqB;EAM/BC,QAAQ,EAAE,SANqB;EAO/BC,OAAO,EAAE,SAPsB;EAQ/BC,OAAO,EAAE,SARsB;EAS/BC,OAAO,EAAE,SATsB;EAU/BC,OAAO,EAAE,SAVsB;EAW/BC,OAAO,EAAE,SAXsB;EAY/BC,OAAO,EAAE,SAZsB;EAa/BC,OAAO,EAAE,SAbsB;EAc/BC,OAAO,EAAE;AAdsB,CAAd,CAAnB;AAiBA,IAAMgB,KAAK,GAAG9B,cAAA,CAAc;EAC1BC,IAAI,EAAE,SADoB;EAE1BC,QAAQ,EAAE,SAFgB;EAG1BC,QAAQ,EAAE,SAHgB;EAI1BC,QAAQ,EAAE,SAJgB;EAK1BC,QAAQ,EAAE,SALgB;EAM1BC,QAAQ,EAAE,SANgB;EAO1BC,OAAO,EAAE,SAPiB;EAQ1BC,OAAO,EAAE,SARiB;EAS1BC,OAAO,EAAE,SATiB;EAU1BC,OAAO,EAAE;AAViB,CAAd,CAAd;AAaA,IAAMqB,QAAQ,GAAG/B,cAAA,CAAc;EAC7BC,IAAI,EAAE,SADuB;EAE7BC,QAAQ,EAAE,SAFmB;EAG7BC,QAAQ,EAAE,SAHmB;EAI7BC,QAAQ,EAAE,SAJmB;EAK7BC,QAAQ,EAAE,SALmB;EAM7BC,QAAQ,EAAE,SANmB;EAO7BC,OAAO,EAAE,SAPoB;EAQ7BC,OAAO,EAAE,SARoB;EAS7BC,OAAO,EAAE,SAToB;EAU7BC,OAAO,EAAE;AAVoB,CAAd,CAAjB;AAaA,IAAMsB,IAAI,GAAGhC,cAAA,CAAc;EACzBC,IAAI,EAAE,SADmB;EAEzBC,QAAQ,EAAE,SAFe;EAGzBC,QAAQ,EAAE,SAHe;EAIzBC,QAAQ,EAAE,SAJe;EAKzBC,QAAQ,EAAE,SALe;EAMzBC,QAAQ,EAAE,SANe;EAOzBC,OAAO,EAAE,SAPgB;EAQzBC,OAAO,EAAE,SARgB;EASzBC,OAAO,EAAE,SATgB;EAUzBC,OAAO,EAAE;AAVgB,CAAd,CAAb;AAaA,IAAMuB,MAAM,GAAGjC,cAAA,CAAc;EAC3BkC,KAAK,EAAE,SADoB;EAE3BC,KAAK,EAAE,SAFoB;EAG3BC,WAAW,EAAE;AAHc,CAAd,CAAf;AAMA,eAAepC,cAAA,CAAc;EAC3BD,GAD2B,EAC3BA,GAD2B;EAE3BgB,IAF2B,EAE3BA,IAF2B;EAG3BC,MAH2B,EAG3BA,MAH2B;EAI3BC,UAJ2B,EAI3BA,UAJ2B;EAK3BC,MAL2B,EAK3BA,MAL2B;EAM3BC,IAN2B,EAM3BA,IAN2B;EAO3BC,SAP2B,EAO3BA,SAP2B;EAQ3BC,IAR2B,EAQ3BA,IAR2B;EAS3BC,IAT2B,EAS3BA,IAT2B;EAU3BC,KAV2B,EAU3BA,KAV2B;EAW3BC,UAX2B,EAW3BA,UAX2B;EAY3BC,IAZ2B,EAY3BA,IAZ2B;EAa3BC,MAb2B,EAa3BA,MAb2B;EAc3BC,KAd2B,EAc3BA,KAd2B;EAe3BC,MAf2B,EAe3BA,MAf2B;EAgB3BC,UAhB2B,EAgB3BA,UAhB2B;EAiB3BC,KAjB2B,EAiB3BA,KAjB2B;EAkB3BC,QAlB2B,EAkB3BA,QAlB2B;EAmB3BC,IAnB2B,EAmB3BA,IAnB2B;EAoB3BC,MAAA,EAAAA;AApB2B,CAAd,CAAf", "ignoreList": []}]}