{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSlideGroup/VSlideGroup.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSlideGroup/VSlideGroup.js", "mtime": 1757335239008}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VIcon", "VFadeTransition", "BaseItemGroup", "Mobile", "Resize", "Touch", "mixins", "<PERSON><PERSON><PERSON>", "bias", "val", "c", "x", "Math", "abs", "_Math$sign", "calculateUpdatedOffset", "selectedElement", "widths", "rtl", "currentScrollOffset", "clientWidth", "offsetLeft", "content", "totalWidth", "wrapper", "itemOffset", "additionalOffset", "max", "min", "calculateCenteredOffset", "offsetCentered", "BaseSlideGroup", "extend", "name", "directives", "props", "activeClass", "type", "String", "centerActive", "Boolean", "nextIcon", "prevIcon", "showArrows", "validator", "v", "_context", "_includesInstanceProperty", "call", "data", "isOverflowing", "resizeTimeout", "startX", "isSwipingHorizontal", "isSwiping", "scrollOffset", "computed", "canTouch", "window", "__cachedNext", "genTransition", "__cachedPrev", "classes", "_objectSpread", "options", "hasAffixes", "isMobile", "hasNext", "_this$widths", "has<PERSON>rev", "watch", "internalValue", "$vuetify", "scroll", "$refs", "style", "transform", "concat", "mounted", "_this", "ResizeObserver", "obs", "onResize", "observe", "$el", "$on", "disconnect", "itemsLength", "_a", "children", "length", "setWidths", "methods", "onScroll", "scrollLeft", "onFocusin", "e", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "el", "value", "_iterator2", "items", "_step2", "vm", "err", "f", "genNext", "_this2", "slot", "$scopedSlots", "next", "$slots", "$createElement", "staticClass", "on", "click", "onAffixClick", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "focusin", "genData", "genIcon", "location", "_context2", "icon", "upperLocation", "_concatInstanceProperty", "toUpperCase", "_sliceInstanceProperty", "hasAffix", "disabled", "genPrev", "_this3", "prev", "genWrapper", "_this4", "start", "overflowCheck", "onTouchStart", "move", "onTouchMove", "end", "onTouchEnd", "calculateNewOffset", "direction", "sign", "newAbosluteOffset", "$emit", "scrollTo", "_isDestroyed", "touchstartX", "setProperty", "diffX", "touchmoveX", "diffY", "touchmoveY", "touchstartY", "document", "documentElement", "overflowY", "_this$$refs", "maxScrollOffset", "removeProperty", "fn", "stopPropagation", "scrollIntoView", "selectedItem", "lastItemPosition", "getBoundingClientRect", "wrapperPosition", "right", "left", "selectedIndex", "_this5", "requestAnimationFrame", "_this5$$refs", "render", "h", "provide", "slideGroup"], "sources": ["../../../src/components/VSlideGroup/VSlideGroup.ts"], "sourcesContent": ["// Styles\nimport './VSlideGroup.sass'\n\n// Components\nimport VIcon from '../VIcon'\nimport { VFadeTransition } from '../transitions'\n\n// Extensions\nimport { BaseItemGroup } from '../VItemGroup/VItemGroup'\n\n// Mixins\nimport Mobile from '../../mixins/mobile'\n\n// Directives\nimport Resize from '../../directives/resize'\nimport Touch from '../../directives/touch'\n\n// Utilities\nimport mixins, { ExtractVue } from '../../util/mixins'\n\n// Types\nimport Vue, { VNode } from 'vue'\nimport { composedPath } from '../../util/helpers'\n\ninterface TouchEvent {\n  touchstartX: number\n  touchstartY: number\n  touchmoveX: number\n  touchmoveY: number\n  stopPropagation: Function\n}\n\ninterface Widths {\n  content: number\n  wrapper: number\n}\n\ninterface options extends Vue {\n  $refs: {\n    content: HTMLElement\n    wrapper: HTMLElement\n  }\n}\n\nfunction bias (val: number) {\n  const c = 0.501\n  const x = Math.abs(val)\n  return Math.sign(val) * (x / ((1 / c - 2) * (1 - x) + 1))\n}\n\nexport function calculateUpdatedOffset (\n  selectedElement: HTMLElement,\n  widths: Widths,\n  rtl: boolean,\n  currentScrollOffset: number\n): number {\n  const clientWidth = selectedElement.clientWidth\n  const offsetLeft = rtl\n    ? (widths.content - selectedElement.offsetLeft - clientWidth)\n    : selectedElement.offsetLeft\n\n  if (rtl) {\n    currentScrollOffset = -currentScrollOffset\n  }\n\n  const totalWidth = widths.wrapper + currentScrollOffset\n  const itemOffset = clientWidth + offsetLeft\n  const additionalOffset = clientWidth * 0.4\n\n  if (offsetLeft <= currentScrollOffset) {\n    currentScrollOffset = Math.max(offsetLeft - additionalOffset, 0)\n  } else if (totalWidth <= itemOffset) {\n    currentScrollOffset = Math.min(currentScrollOffset - (totalWidth - itemOffset - additionalOffset), widths.content - widths.wrapper)\n  }\n\n  return rtl ? -currentScrollOffset : currentScrollOffset\n}\n\nexport function calculateCenteredOffset (\n  selectedElement: HTMLElement,\n  widths: Widths,\n  rtl: boolean\n): number {\n  const { offsetLeft, clientWidth } = selectedElement\n\n  if (rtl) {\n    const offsetCentered = widths.content - offsetLeft - clientWidth / 2 - widths.wrapper / 2\n    return -Math.min(widths.content - widths.wrapper, Math.max(0, offsetCentered))\n  } else {\n    const offsetCentered = offsetLeft + clientWidth / 2 - widths.wrapper / 2\n    return Math.min(widths.content - widths.wrapper, Math.max(0, offsetCentered))\n  }\n}\n\nexport const BaseSlideGroup = mixins<options &\n/* eslint-disable indent */\n  ExtractVue<[\n    typeof BaseItemGroup,\n    typeof Mobile,\n  ]>\n/* eslint-enable indent */\n>(\n  BaseItemGroup,\n  Mobile,\n  /* @vue/component */\n).extend({\n  name: 'base-slide-group',\n\n  directives: {\n    Resize,\n    Touch,\n  },\n\n  props: {\n    activeClass: {\n      type: String,\n      default: 'v-slide-item--active',\n    },\n    centerActive: Boolean,\n    nextIcon: {\n      type: String,\n      default: '$next',\n    },\n    prevIcon: {\n      type: String,\n      default: '$prev',\n    },\n    showArrows: {\n      type: [Boolean, String],\n      validator: (v: any) => (\n        typeof v === 'boolean' || [\n          'always',\n          'desktop',\n          'mobile',\n        ].includes(v)\n      ),\n    },\n  },\n\n  data: () => ({\n    isOverflowing: false,\n    resizeTimeout: 0,\n    startX: 0,\n    isSwipingHorizontal: false,\n    isSwiping: false,\n    scrollOffset: 0,\n    widths: {\n      content: 0,\n      wrapper: 0,\n    },\n  }),\n\n  computed: {\n    canTouch (): boolean {\n      return typeof window !== 'undefined'\n    },\n    __cachedNext (): VNode {\n      return this.genTransition('next')\n    },\n    __cachedPrev (): VNode {\n      return this.genTransition('prev')\n    },\n    classes (): object {\n      return {\n        ...BaseItemGroup.options.computed.classes.call(this),\n        'v-slide-group': true,\n        'v-slide-group--has-affixes': this.hasAffixes,\n        'v-slide-group--is-overflowing': this.isOverflowing,\n      }\n    },\n    hasAffixes (): Boolean {\n      switch (this.showArrows) {\n        // Always show arrows on desktop & mobile\n        case 'always': return true\n\n        // Always show arrows on desktop\n        case 'desktop': return !this.isMobile\n\n        // Show arrows on mobile when overflowing.\n        // This matches the default 2.2 behavior\n        case true: return this.isOverflowing || Math.abs(this.scrollOffset) > 0\n\n        // Always show on mobile\n        case 'mobile': return (\n          this.isMobile ||\n          (this.isOverflowing || Math.abs(this.scrollOffset) > 0)\n        )\n\n        // https://material.io/components/tabs#scrollable-tabs\n        // Always show arrows when\n        // overflowed on desktop\n        default: return (\n          !this.isMobile &&\n          (this.isOverflowing || Math.abs(this.scrollOffset) > 0)\n        )\n      }\n    },\n    hasNext (): boolean {\n      if (!this.hasAffixes) return false\n\n      const { content, wrapper } = this.widths\n\n      // Check one scroll ahead to know the width of right-most item\n      return content > Math.abs(this.scrollOffset) + wrapper\n    },\n    hasPrev (): boolean {\n      return this.hasAffixes && this.scrollOffset !== 0\n    },\n  },\n\n  watch: {\n    internalValue: 'setWidths',\n    // When overflow changes, the arrows alter\n    // the widths of the content and wrapper\n    // and need to be recalculated\n    isOverflowing: 'setWidths',\n    scrollOffset (val) {\n      if (this.$vuetify.rtl) val = -val\n\n      let scroll =\n        val <= 0\n          ? bias(-val)\n          : val > this.widths.content - this.widths.wrapper\n            ? -(this.widths.content - this.widths.wrapper) + bias(this.widths.content - this.widths.wrapper - val)\n            : -val\n\n      if (this.$vuetify.rtl) scroll = -scroll\n\n      this.$refs.content.style.transform = `translateX(${scroll}px)`\n    },\n  },\n\n  mounted () {\n    if (typeof ResizeObserver !== 'undefined') {\n      const obs = new ResizeObserver(() => {\n        this.onResize()\n      })\n      obs.observe(this.$el)\n      obs.observe(this.$refs.content)\n      this.$on('hook:destroyed', () => {\n        obs.disconnect()\n      })\n    } else {\n      let itemsLength = 0\n      this.$on('hook:beforeUpdate', () => {\n        itemsLength = (this.$refs.content?.children || []).length\n      })\n      this.$on('hook:updated', () => {\n        if (itemsLength === (this.$refs.content?.children || []).length) return\n        this.setWidths()\n      })\n    }\n  },\n\n  methods: {\n    onScroll () {\n      this.$refs.wrapper.scrollLeft = 0\n    },\n    onFocusin (e: FocusEvent) {\n      if (!this.isOverflowing) return\n\n      // Focused element is likely to be the root of an item, so a\n      // breadth-first search will probably find it in the first iteration\n      for (const el of composedPath(e)) {\n        for (const vm of this.items) {\n          if (vm.$el === el) {\n            this.scrollOffset = calculateUpdatedOffset(\n              vm.$el as HTMLElement,\n              this.widths,\n              this.$vuetify.rtl,\n              this.scrollOffset\n            )\n            return\n          }\n        }\n      }\n    },\n    // Always generate next for scrollable hint\n    genNext (): VNode | null {\n      const slot = this.$scopedSlots.next\n        ? this.$scopedSlots.next({})\n        : this.$slots.next || this.__cachedNext\n\n      return this.$createElement('div', {\n        staticClass: 'v-slide-group__next',\n        class: {\n          'v-slide-group__next--disabled': !this.hasNext,\n        },\n        on: {\n          click: () => this.onAffixClick('next'),\n        },\n        key: 'next',\n      }, [slot])\n    },\n    genContent (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-slide-group__content',\n        ref: 'content',\n        on: {\n          focusin: this.onFocusin,\n        },\n      }, this.$slots.default)\n    },\n    genData (): object {\n      return {\n        class: this.classes,\n        directives: [{\n          name: 'resize',\n          value: this.onResize,\n        }],\n      }\n    },\n    genIcon (location: 'prev' | 'next'): VNode | null {\n      let icon = location\n\n      if (this.$vuetify.rtl && location === 'prev') {\n        icon = 'next'\n      } else if (this.$vuetify.rtl && location === 'next') {\n        icon = 'prev'\n      }\n\n      const upperLocation = `${location[0].toUpperCase()}${location.slice(1)}`\n      const hasAffix = (this as any)[`has${upperLocation}`]\n\n      if (\n        !this.showArrows &&\n        !hasAffix\n      ) return null\n\n      return this.$createElement(VIcon, {\n        props: {\n          disabled: !hasAffix,\n        },\n      }, (this as any)[`${icon}Icon`])\n    },\n    // Always generate prev for scrollable hint\n    genPrev (): VNode | null {\n      const slot = this.$scopedSlots.prev\n        ? this.$scopedSlots.prev({})\n        : this.$slots.prev || this.__cachedPrev\n\n      return this.$createElement('div', {\n        staticClass: 'v-slide-group__prev',\n        class: {\n          'v-slide-group__prev--disabled': !this.hasPrev,\n        },\n        on: {\n          click: () => this.onAffixClick('prev'),\n        },\n        key: 'prev',\n      }, [slot])\n    },\n    genTransition (location: 'prev' | 'next') {\n      return this.$createElement(VFadeTransition, [this.genIcon(location)])\n    },\n    genWrapper (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-slide-group__wrapper',\n        directives: [{\n          name: 'touch',\n          value: {\n            start: (e: TouchEvent) => this.overflowCheck(e, this.onTouchStart),\n            move: (e: TouchEvent) => this.overflowCheck(e, this.onTouchMove),\n            end: (e: TouchEvent) => this.overflowCheck(e, this.onTouchEnd),\n          },\n        }],\n        ref: 'wrapper',\n        on: {\n          scroll: this.onScroll,\n        },\n      }, [this.genContent()])\n    },\n    calculateNewOffset (direction: 'prev' | 'next', widths: Widths, rtl: boolean, currentScrollOffset: number) {\n      const sign = rtl ? -1 : 1\n      const newAbosluteOffset = sign * currentScrollOffset +\n        (direction === 'prev' ? -1 : 1) * widths.wrapper\n\n      return sign * Math.max(Math.min(newAbosluteOffset, widths.content - widths.wrapper), 0)\n    },\n    onAffixClick (location: 'prev' | 'next') {\n      this.$emit(`click:${location}`)\n      this.scrollTo(location)\n    },\n    onResize () {\n      /* istanbul ignore next */\n      if (this._isDestroyed) return\n\n      this.setWidths()\n    },\n    onTouchStart (e: TouchEvent) {\n      const { content } = this.$refs\n\n      this.startX = this.scrollOffset + e.touchstartX as number\n\n      content.style.setProperty('transition', 'none')\n      content.style.setProperty('willChange', 'transform')\n    },\n    onTouchMove (e: TouchEvent) {\n      if (!this.canTouch) return\n\n      if (!this.isSwiping) {\n        // only calculate disableSwipeHorizontal during the first onTouchMove invoke\n        // in order to ensure disableSwipeHorizontal value is consistent between onTouchStart and onTouchEnd\n        const diffX = e.touchmoveX - e.touchstartX\n        const diffY = e.touchmoveY - e.touchstartY\n        this.isSwipingHorizontal = Math.abs(diffX) > Math.abs(diffY)\n        this.isSwiping = true\n      }\n\n      if (this.isSwipingHorizontal) {\n        // sliding horizontally\n        this.scrollOffset = this.startX - e.touchmoveX\n        // temporarily disable window vertical scrolling\n        document.documentElement.style.overflowY = 'hidden'\n      }\n    },\n    onTouchEnd () {\n      if (!this.canTouch) return\n\n      const { content, wrapper } = this.$refs\n      const maxScrollOffset = content.clientWidth - wrapper.clientWidth\n\n      content.style.setProperty('transition', null)\n      content.style.setProperty('willChange', null)\n\n      if (this.$vuetify.rtl) {\n        /* istanbul ignore else */\n        if (this.scrollOffset > 0 || !this.isOverflowing) {\n          this.scrollOffset = 0\n        } else if (this.scrollOffset <= -maxScrollOffset) {\n          this.scrollOffset = -maxScrollOffset\n        }\n      } else {\n        /* istanbul ignore else */\n        if (this.scrollOffset < 0 || !this.isOverflowing) {\n          this.scrollOffset = 0\n        } else if (this.scrollOffset >= maxScrollOffset) {\n          this.scrollOffset = maxScrollOffset\n        }\n      }\n\n      this.isSwiping = false\n      // rollback whole page scrolling to default\n      document.documentElement.style.removeProperty('overflow-y')\n    },\n    overflowCheck (e: TouchEvent, fn: (e: TouchEvent) => void) {\n      e.stopPropagation()\n      this.isOverflowing && fn(e)\n    },\n    scrollIntoView /* istanbul ignore next */ () {\n      if (!this.selectedItem && this.items.length) {\n        const lastItemPosition = this.items[this.items.length - 1].$el.getBoundingClientRect()\n        const wrapperPosition = this.$refs.wrapper.getBoundingClientRect()\n\n        if (\n          (this.$vuetify.rtl && wrapperPosition.right < lastItemPosition.right) ||\n          (!this.$vuetify.rtl && wrapperPosition.left > lastItemPosition.left)\n        ) {\n          this.scrollTo('prev')\n        }\n      }\n\n      if (!this.selectedItem) {\n        return\n      }\n\n      if (\n        this.selectedIndex === 0 ||\n        (!this.centerActive && !this.isOverflowing)\n      ) {\n        this.scrollOffset = 0\n      } else if (this.centerActive) {\n        this.scrollOffset = calculateCenteredOffset(\n          this.selectedItem.$el as HTMLElement,\n          this.widths,\n          this.$vuetify.rtl\n        )\n      } else if (this.isOverflowing) {\n        this.scrollOffset = calculateUpdatedOffset(\n          this.selectedItem.$el as HTMLElement,\n          this.widths,\n          this.$vuetify.rtl,\n          this.scrollOffset\n        )\n      }\n    },\n    scrollTo /* istanbul ignore next */ (location: 'prev' | 'next') {\n      this.scrollOffset = this.calculateNewOffset(location, {\n        // Force reflow\n        content: this.$refs.content ? this.$refs.content.clientWidth : 0,\n        wrapper: this.$refs.wrapper ? this.$refs.wrapper.clientWidth : 0,\n      }, this.$vuetify.rtl, this.scrollOffset)\n    },\n    setWidths () {\n      window.requestAnimationFrame(() => {\n        if (this._isDestroyed) return\n\n        const { content, wrapper } = this.$refs\n\n        this.widths = {\n          content: content ? content.clientWidth : 0,\n          wrapper: wrapper ? wrapper.clientWidth : 0,\n        }\n\n        // https://github.com/vuetifyjs/vuetify/issues/13212\n        // We add +1 to the wrappers width to prevent an issue where the `clientWidth`\n        // gets calculated wrongly by the browser if using a different zoom-level.\n        this.isOverflowing = this.widths.wrapper + 1 < this.widths.content\n\n        this.scrollIntoView()\n      })\n    },\n  },\n\n  render (h): VNode {\n    return h('div', this.genData(), [\n      this.genPrev(),\n      this.genWrapper(),\n      this.genNext(),\n    ])\n  },\n})\n\nexport default BaseSlideGroup.extend({\n  name: 'v-slide-group',\n\n  provide (): object {\n    return {\n      slideGroup: this,\n    }\n  },\n})\n"], "mappings": ";;;;;;AAAA;AACA,OAAO,sDAAP,C,CAEA;;AACA,OAAOA,KAAP,MAAkB,UAAlB;AACA,SAASC,eAAT,QAAgC,gBAAhC,C,CAEA;;AACA,SAASC,aAAT,QAA8B,0BAA9B,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,qBAAnB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,yBAAnB;AACA,OAAOC,KAAP,MAAkB,wBAAlB,C,CAEA;;AACA,OAAOC,MAAP,MAAmC,mBAAnC;AAIA,SAASC,YAAT,QAA6B,oBAA7B;AAsBA,SAASC,IAATA,CAAeC,GAAf,EAA0B;EACxB,IAAMC,CAAC,GAAG,KAAV;EACA,IAAMC,CAAC,GAAGC,IAAI,CAACC,GAAL,CAASJ,GAAT,CAAV;EACA,OAAOK,UAAA,CAAUL,GAAV,KAAkBE,CAAC,IAAI,CAAC,IAAID,CAAJ,GAAQ,CAAT,KAAe,IAAIC,CAAnB,IAAwB,CAA5B,CAAnB,CAAP;AACD;AAED,OAAM,SAAUI,sBAAVA,CACJC,eADI,EAEJC,MAFI,EAGJC,GAHI,EAIJC,mBAJI,EAIuB;EAE3B,IAAMC,WAAW,GAAGJ,eAAe,CAACI,WAApC;EACA,IAAMC,UAAU,GAAGH,GAAG,GACjBD,MAAM,CAACK,OAAP,GAAiBN,eAAe,CAACK,UAAjC,GAA8CD,WAD7B,GAElBJ,eAAe,CAACK,UAFpB;EAIA,IAAIH,GAAJ,EAAS;IACPC,mBAAmB,GAAG,CAACA,mBAAvB;EACD;EAED,IAAMI,UAAU,GAAGN,MAAM,CAACO,OAAP,GAAiBL,mBAApC;EACA,IAAMM,UAAU,GAAGL,WAAW,GAAGC,UAAjC;EACA,IAAMK,gBAAgB,GAAGN,WAAW,GAAG,GAAvC;EAEA,IAAIC,UAAU,IAAIF,mBAAlB,EAAuC;IACrCA,mBAAmB,GAAGP,IAAI,CAACe,GAAL,CAASN,UAAU,GAAGK,gBAAtB,EAAwC,CAAxC,CAAtB;EACD,CAFD,MAEO,IAAIH,UAAU,IAAIE,UAAlB,EAA8B;IACnCN,mBAAmB,GAAGP,IAAI,CAACgB,GAAL,CAAST,mBAAmB,IAAII,UAAU,GAAGE,UAAb,GAA0BC,gBAA9B,CAA5B,EAA6ET,MAAM,CAACK,OAAP,GAAiBL,MAAM,CAACO,OAArG,CAAtB;EACD;EAED,OAAON,GAAG,GAAG,CAACC,mBAAJ,GAA0BA,mBAApC;AACD;AAED,OAAM,SAAUU,uBAAVA,CACJb,eADI,EAEJC,MAFI,EAGJC,GAHI,EAGQ;EAEZ,IAAQG,UAAF,GAA8BL,eAApC,CAAQK,UAAF;IAAcD,WAAA,GAAgBJ,eAApC,CAAoBI,WAAA;EAEpB,IAAIF,GAAJ,EAAS;IACP,IAAMY,cAAc,GAAGb,MAAM,CAACK,OAAP,GAAiBD,UAAjB,GAA8BD,WAAW,GAAG,CAA5C,GAAgDH,MAAM,CAACO,OAAP,GAAiB,CAAxF;IACA,OAAO,CAACZ,IAAI,CAACgB,GAAL,CAASX,MAAM,CAACK,OAAP,GAAiBL,MAAM,CAACO,OAAjC,EAA0CZ,IAAI,CAACe,GAAL,CAAS,CAAT,EAAYG,cAAZ,CAA1C,CAAR;EACD,CAHD,MAGO;IACL,IAAMA,eAAc,GAAGT,UAAU,GAAGD,WAAW,GAAG,CAA3B,GAA+BH,MAAM,CAACO,OAAP,GAAiB,CAAvE;IACA,OAAOZ,IAAI,CAACgB,GAAL,CAASX,MAAM,CAACK,OAAP,GAAiBL,MAAM,CAACO,OAAjC,EAA0CZ,IAAI,CAACe,GAAL,CAAS,CAAT,EAAYG,eAAZ,CAA1C,CAAP;EACD;AACF;AAED,OAAO,IAAMC,cAAc,GAAGzB,MAAM,CAQlCJ,aARkC,EASlCC,MATkC,CAAN,CAW5B6B,MAX4B,CAWrB;EACPC,IAAI,EAAE,kBADC;EAGPC,UAAU,EAAE;IACV9B,MADU,EACVA,MADU;IAEVC,KAAA,EAAAA;EAFU,CAHL;EAQP8B,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC,MADK;MAEX,WAAS;IAFE,CADR;IAKLC,YAAY,EAAEC,OALT;IAMLC,QAAQ,EAAE;MACRJ,IAAI,EAAEC,MADE;MAER,WAAS;IAFD,CANL;IAULI,QAAQ,EAAE;MACRL,IAAI,EAAEC,MADE;MAER,WAAS;IAFD,CAVL;IAcLK,UAAU,EAAE;MACVN,IAAI,EAAE,CAACG,OAAD,EAAUF,MAAV,CADI;MAEVM,SAAS,EAAG,SAAZA,SAASA,CAAGC,CAAD;QAAA,IAAAC,QAAA;QAAA,OACT,OAAOD,CAAP,KAAa,SAAb,IAA0BE,yBAAA,CAAAD,QAAA,IACxB,QADwB,EAExB,SAFwB,EAGxB,QAHwB,GAAAE,IAAA,CAAAF,QAAA,EAIfD,CAJe;MAAA;IAHlB;EAdP,CARA;EAkCPI,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,aAAa,EAAE,KADJ;MAEXC,aAAa,EAAE,CAFJ;MAGXC,MAAM,EAAE,CAHG;MAIXC,mBAAmB,EAAE,KAJV;MAKXC,SAAS,EAAE,KALA;MAMXC,YAAY,EAAE,CANH;MAOXtC,MAAM,EAAE;QACNK,OAAO,EAAE,CADH;QAENE,OAAO,EAAE;MAFH;IAPG,CAAP;EAAA,CAlCC;EA+CPgC,QAAQ,EAAE;IACRC,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAO,OAAOC,MAAP,KAAkB,WAAzB;IACD,CAHO;IAIRC,YAAY,WAAZA,YAAYA,CAAA;MACV,OAAO,KAAKC,aAAL,CAAmB,MAAnB,CAAP;IACD,CANO;IAORC,YAAY,WAAZA,YAAYA,CAAA;MACV,OAAO,KAAKD,aAAL,CAAmB,MAAnB,CAAP;IACD,CATO;IAURE,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACK7D,aAAa,CAAC8D,OAAd,CAAsBR,QAAtB,CAA+BM,OAA/B,CAAuCd,IAAvC,CAA4C,IAA5C,CADE;QAEL,iBAAiB,IAFZ;QAGL,8BAA8B,KAAKiB,UAH9B;QAIL,iCAAiC,KAAKf;MAAA;IAEzC,CAjBO;IAkBRe,UAAU,WAAVA,UAAUA,CAAA;MACR,QAAQ,KAAKtB,UAAb;QACE;QACA,KAAK,QAAL;UAAe,OAAO,IAAP;QAEf;;QACA,KAAK,SAAL;UAAgB,OAAO,CAAC,KAAKuB,QAAb;QAEhB;QACA;;QACA,KAAK,IAAL;UAAW,OAAO,KAAKhB,aAAL,IAAsBtC,IAAI,CAACC,GAAL,CAAS,KAAK0C,YAAd,IAA8B,CAA3D;QAEX;;QACA,KAAK,QAAL;UAAe,OACb,KAAKW,QAAL,IACC,KAAKhB,aAAL,IAAsBtC,IAAI,CAACC,GAAL,CAAS,KAAK0C,YAAd,IAA8B,CAFxC;QAKf;QACA;QACA;;QACA;UAAS,OACP,CAAC,KAAKW,QAAN,KACC,KAAKhB,aAAL,IAAsBtC,IAAI,CAACC,GAAL,CAAS,KAAK0C,YAAd,IAA8B,CADrD,CADO;MApBX;IAyBD,CA5CO;IA6CRY,OAAO,WAAPA,OAAOA,CAAA;MACL,IAAI,CAAC,KAAKF,UAAV,EAAsB,OAAO,KAAP;MAEtB,IAAAG,YAAA,GAA6B,KAAKnD,MAAlC;QAAQK,OAAF,GAAA8C,YAAA,CAAE9C,OAAF;QAAWE,OAAA,GAAA4C,YAAA,CAAA5C,OAAA,CAHZ,CAKL;;MACA,OAAOF,OAAO,GAAGV,IAAI,CAACC,GAAL,CAAS,KAAK0C,YAAd,IAA8B/B,OAA/C;IACD,CApDO;IAqDR6C,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAKJ,UAAL,IAAmB,KAAKV,YAAL,KAAsB,CAAhD;IACD;EAvDO,CA/CH;EAyGPe,KAAK,EAAE;IACLC,aAAa,EAAE,WADV;IAEL;IACA;IACA;IACArB,aAAa,EAAE,WALV;IAMLK,YAAY,WAAZA,YAAYA,CAAE9C,GAAF,EAAK;MACf,IAAI,KAAK+D,QAAL,CAActD,GAAlB,EAAuBT,GAAG,GAAG,CAACA,GAAP;MAEvB,IAAIgE,MAAM,GACRhE,GAAG,IAAI,CAAP,GACID,IAAI,CAAC,CAACC,GAAF,CADR,GAEIA,GAAG,GAAG,KAAKQ,MAAL,CAAYK,OAAZ,GAAsB,KAAKL,MAAL,CAAYO,OAAxC,GACE,EAAE,KAAKP,MAAL,CAAYK,OAAZ,GAAsB,KAAKL,MAAL,CAAYO,OAApC,IAA+ChB,IAAI,CAAC,KAAKS,MAAL,CAAYK,OAAZ,GAAsB,KAAKL,MAAL,CAAYO,OAAlC,GAA4Cf,GAA7C,CADrD,GAEE,CAACA,GALT;MAOA,IAAI,KAAK+D,QAAL,CAActD,GAAlB,EAAuBuD,MAAM,GAAG,CAACA,MAAV;MAEvB,KAAKC,KAAL,CAAWpD,OAAX,CAAmBqD,KAAnB,CAAyBC,SAAzB,iBAAAC,MAAA,CAAmDJ,MAAM,QAAzD;IACD;EAnBI,CAzGA;EA+HPK,OAAO,WAAPA,OAAOA,CAAA;IAAA,IAAAC,KAAA;IACL,IAAI,OAAOC,cAAP,KAA0B,WAA9B,EAA2C;MACzC,IAAMC,GAAG,GAAG,IAAID,cAAJ,CAAmB,YAAK;QAClCD,KAAA,CAAKG,QAAL;MACD,CAFW,CAAZ;MAGAD,GAAG,CAACE,OAAJ,CAAY,KAAKC,GAAjB;MACAH,GAAG,CAACE,OAAJ,CAAY,KAAKT,KAAL,CAAWpD,OAAvB;MACA,KAAK+D,GAAL,CAAS,gBAAT,EAA2B,YAAK;QAC9BJ,GAAG,CAACK,UAAJ;MACD,CAFD;IAGD,CATD,MASO;MACL,IAAIC,WAAW,GAAG,CAAlB;MACA,KAAKF,GAAL,CAAS,mBAAT,EAA8B,YAAK;;QACjCE,WAAW,GAAG,CAAC,EAAAC,EAAA,GAAAT,KAAA,CAAKL,KAAL,CAAWpD,OAAX,MAAkB,IAAlB,IAAkBkE,EAAA,WAAlB,GAAkB,MAAlB,GAAkBA,EAAA,CAAEC,QAApB,KAAgC,EAAjC,EAAqCC,MAAnD;MACD,CAFD;MAGA,KAAKL,GAAL,CAAS,cAAT,EAAyB,YAAK;;QAC5B,IAAIE,WAAW,KAAK,CAAC,EAAAC,EAAA,GAAAT,KAAA,CAAKL,KAAL,CAAWpD,OAAX,MAAkB,IAAlB,IAAkBkE,EAAA,WAAlB,GAAkB,MAAlB,GAAkBA,EAAA,CAAEC,QAApB,KAAgC,EAAjC,EAAqCC,MAAzD,EAAiE;QACjEX,KAAA,CAAKY,SAAL;MACD,CAHD;IAID;EACF,CAnJM;EAqJPC,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAA;MACN,KAAKnB,KAAL,CAAWlD,OAAX,CAAmBsE,UAAnB,GAAgC,CAAhC;IACD,CAHM;IAIPC,SAAS,WAATA,SAASA,CAAEC,CAAF,EAAe;MACtB,IAAI,CAAC,KAAK9C,aAAV,EAAyB,OADH,CAGtB;MACA;MAAA,IAAA+C,SAAA,GAAAC,0BAAA,CACiB3F,YAAY,CAACyF,CAAD,CAA7B;QAAAG,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAAkC;UAAA,IAAvBC,EAAX,GAAAJ,KAAA,CAAAK,KAAA;UAAA,IAAAC,UAAA,GAAAP,0BAAA,CACmB,KAAKQ,KAAtB;YAAAC,MAAA;UAAA;YAAA,KAAAF,UAAA,CAAAL,CAAA,MAAAO,MAAA,GAAAF,UAAA,CAAAJ,CAAA,IAAAC,IAAA,GAA6B;cAAA,IAAlBM,EAAX,GAAAD,MAAA,CAAAH,KAAA;cACE,IAAII,EAAE,CAACxB,GAAH,KAAWmB,EAAf,EAAmB;gBACjB,KAAKhD,YAAL,GAAoBxC,sBAAsB,CACxC6F,EAAE,CAACxB,GADqC,EAExC,KAAKnE,MAFmC,EAGxC,KAAKuD,QAAL,CAActD,GAH0B,EAIxC,KAAKqC,YAJmC,CAA1C;gBAMA;cACD;YACF;UAAA,SAAAsD,GAAA;YAAAJ,UAAA,CAAAT,CAAA,CAAAa,GAAA;UAAA;YAAAJ,UAAA,CAAAK,CAAA;UAAA;QACF;MAAA,SAAAD,GAAA;QAAAZ,SAAA,CAAAD,CAAA,CAAAa,GAAA;MAAA;QAAAZ,SAAA,CAAAa,CAAA;MAAA;IACF,CAtBM;IAuBP;IACAC,OAAO,WAAPA,OAAOA,CAAA;MAAA,IAAAC,MAAA;MACL,IAAMC,IAAI,GAAG,KAAKC,YAAL,CAAkBC,IAAlB,GACT,KAAKD,YAAL,CAAkBC,IAAlB,CAAuB,EAAvB,CADS,GAET,KAAKC,MAAL,CAAYD,IAAZ,IAAoB,KAAKxD,YAF7B;MAIA,OAAO,KAAK0D,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,qBADmB;QAEhC,SAAO;UACL,iCAAiC,CAAC,KAAKnD;QADlC,CAFyB;QAKhCoD,EAAE,EAAE;UACFC,KAAK,EAAE,SAAPA,KAAKA,CAAA;YAAA,OAAQR,MAAA,CAAKS,YAAL,CAAkB,MAAlB;UAAA;QADX,CAL4B;QAQhCC,GAAG,EAAE;MAR2B,CAA3B,EASJ,CAACT,IAAD,CATI,CAAP;IAUD,CAvCM;IAwCPU,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKN,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,wBADmB;QAEhCM,GAAG,EAAE,SAF2B;QAGhCL,EAAE,EAAE;UACFM,OAAO,EAAE,KAAK9B;QADZ;MAH4B,CAA3B,EAMJ,KAAKqB,MAAL,WANI,CAAP;IAOD,CAhDM;IAiDPU,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO;QACL,SAAO,KAAKhE,OADP;QAEL5B,UAAU,EAAE,CAAC;UACXD,IAAI,EAAE,QADK;UAEXuE,KAAK,EAAE,KAAKtB;QAFD,CAAD;MAFP,CAAP;IAOD,CAzDM;IA0DP6C,OAAO,WAAPA,OAAOA,CAAEC,QAAF,EAA2B;MAAA,IAAAC,SAAA;MAChC,IAAIC,IAAI,GAAGF,QAAX;MAEA,IAAI,KAAKxD,QAAL,CAActD,GAAd,IAAqB8G,QAAQ,KAAK,MAAtC,EAA8C;QAC5CE,IAAI,GAAG,MAAP;MACD,CAFD,MAEO,IAAI,KAAK1D,QAAL,CAActD,GAAd,IAAqB8G,QAAQ,KAAK,MAAtC,EAA8C;QACnDE,IAAI,GAAG,MAAP;MACD;MAED,IAAMC,aAAa,GAAAC,uBAAA,CAAAH,SAAA,MAAApD,MAAA,CAAMmD,QAAQ,CAAC,CAAD,CAAR,CAAYK,WAAZ,EAAyB,GAAArF,IAAA,CAAAiF,SAAA,EAAGK,sBAAA,CAAAN,QAAQ,EAAAhF,IAAA,CAARgF,QAAQ,EAAO,CAAf,CAAiB,CAAtE;MACA,IAAMO,QAAQ,GAAI,WAAA1D,MAAA,CAAmBsD,aAAa,EAAlD;MAEA,IACE,CAAC,KAAKxF,UAAN,IACA,CAAC4F,QAFH,EAGE,OAAO,IAAP;MAEF,OAAO,KAAKlB,cAAL,CAAoBrH,KAApB,EAA2B;QAChCmC,KAAK,EAAE;UACLqG,QAAQ,EAAE,CAACD;QADN;MADyB,CAA3B,EAIH,QAAA1D,MAAA,CAAgBqD,IAAI,UAJjB,CAAP;IAKD,CAhFM;IAiFP;IACAO,OAAO,WAAPA,OAAOA,CAAA;MAAA,IAAAC,MAAA;MACL,IAAMzB,IAAI,GAAG,KAAKC,YAAL,CAAkByB,IAAlB,GACT,KAAKzB,YAAL,CAAkByB,IAAlB,CAAuB,EAAvB,CADS,GAET,KAAKvB,MAAL,CAAYuB,IAAZ,IAAoB,KAAK9E,YAF7B;MAIA,OAAO,KAAKwD,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,qBADmB;QAEhC,SAAO;UACL,iCAAiC,CAAC,KAAKjD;QADlC,CAFyB;QAKhCkD,EAAE,EAAE;UACFC,KAAK,EAAE,SAAPA,KAAKA,CAAA;YAAA,OAAQkB,MAAA,CAAKjB,YAAL,CAAkB,MAAlB;UAAA;QADX,CAL4B;QAQhCC,GAAG,EAAE;MAR2B,CAA3B,EASJ,CAACT,IAAD,CATI,CAAP;IAUD,CAjGM;IAkGPrD,aAAa,WAAbA,aAAaA,CAAEoE,QAAF,EAA2B;MACtC,OAAO,KAAKX,cAAL,CAAoBpH,eAApB,EAAqC,CAAC,KAAK8H,OAAL,CAAaC,QAAb,CAAD,CAArC,CAAP;IACD,CApGM;IAqGPY,UAAU,WAAVA,UAAUA,CAAA;MAAA,IAAAC,MAAA;MACR,OAAO,KAAKxB,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,wBADmB;QAEhCpF,UAAU,EAAE,CAAC;UACXD,IAAI,EAAE,OADK;UAEXuE,KAAK,EAAE;YACLsC,KAAK,EAAG,SAARA,KAAKA,CAAG9C,CAAD;cAAA,OAAmB6C,MAAA,CAAKE,aAAL,CAAmB/C,CAAnB,EAAsB6C,MAAA,CAAKG,YAA3B,CADrB;YAAA;YAELC,IAAI,EAAG,SAAPA,IAAIA,CAAGjD,CAAD;cAAA,OAAmB6C,MAAA,CAAKE,aAAL,CAAmB/C,CAAnB,EAAsB6C,MAAA,CAAKK,WAA3B,CAFpB;YAAA;YAGLC,GAAG,EAAG,SAANA,GAAGA,CAAGnD,CAAD;cAAA,OAAmB6C,MAAA,CAAKE,aAAL,CAAmB/C,CAAnB,EAAsB6C,MAAA,CAAKO,UAA3B;YAAA;UAHnB;QAFI,CAAD,CAFoB;QAUhCxB,GAAG,EAAE,SAV2B;QAWhCL,EAAE,EAAE;UACF9C,MAAM,EAAE,KAAKoB;QADX;MAX4B,CAA3B,EAcJ,CAAC,KAAK8B,UAAL,EAAD,CAdI,CAAP;IAeD,CArHM;IAsHP0B,kBAAkB,WAAlBA,kBAAkBA,CAAEC,SAAF,EAA8BrI,MAA9B,EAA8CC,GAA9C,EAA4DC,mBAA5D,EAAuF;MACvG,IAAMoI,IAAI,GAAGrI,GAAG,GAAG,CAAC,CAAJ,GAAQ,CAAxB;MACA,IAAMsI,iBAAiB,GAAGD,IAAI,GAAGpI,mBAAP,GACxB,CAACmI,SAAS,KAAK,MAAd,GAAuB,CAAC,CAAxB,GAA4B,CAA7B,IAAkCrI,MAAM,CAACO,OAD3C;MAGA,OAAO+H,IAAI,GAAG3I,IAAI,CAACe,GAAL,CAASf,IAAI,CAACgB,GAAL,CAAS4H,iBAAT,EAA4BvI,MAAM,CAACK,OAAP,GAAiBL,MAAM,CAACO,OAApD,CAAT,EAAuE,CAAvE,CAAd;IACD,CA5HM;IA6HPiG,YAAY,WAAZA,YAAYA,CAAEO,QAAF,EAA2B;MACrC,KAAKyB,KAAL,UAAA5E,MAAA,CAAoBmD,QAAQ,CAA5B;MACA,KAAK0B,QAAL,CAAc1B,QAAd;IACD,CAhIM;IAiIP9C,QAAQ,WAARA,QAAQA,CAAA;MACN;MACA,IAAI,KAAKyE,YAAT,EAAuB;MAEvB,KAAKhE,SAAL;IACD,CAtIM;IAuIPqD,YAAY,WAAZA,YAAYA,CAAEhD,CAAF,EAAe;MACzB,IAAQ1E,OAAA,GAAY,KAAKoD,KAAzB,CAAQpD,OAAA;MAER,KAAK8B,MAAL,GAAc,KAAKG,YAAL,GAAoByC,CAAC,CAAC4D,WAApC;MAEAtI,OAAO,CAACqD,KAAR,CAAckF,WAAd,CAA0B,YAA1B,EAAwC,MAAxC;MACAvI,OAAO,CAACqD,KAAR,CAAckF,WAAd,CAA0B,YAA1B,EAAwC,WAAxC;IACD,CA9IM;IA+IPX,WAAW,WAAXA,WAAWA,CAAElD,CAAF,EAAe;MACxB,IAAI,CAAC,KAAKvC,QAAV,EAAoB;MAEpB,IAAI,CAAC,KAAKH,SAAV,EAAqB;QACnB;QACA;QACA,IAAMwG,KAAK,GAAG9D,CAAC,CAAC+D,UAAF,GAAe/D,CAAC,CAAC4D,WAA/B;QACA,IAAMI,KAAK,GAAGhE,CAAC,CAACiE,UAAF,GAAejE,CAAC,CAACkE,WAA/B;QACA,KAAK7G,mBAAL,GAA2BzC,IAAI,CAACC,GAAL,CAASiJ,KAAT,IAAkBlJ,IAAI,CAACC,GAAL,CAASmJ,KAAT,CAA7C;QACA,KAAK1G,SAAL,GAAiB,IAAjB;MACD;MAED,IAAI,KAAKD,mBAAT,EAA8B;QAC5B;QACA,KAAKE,YAAL,GAAoB,KAAKH,MAAL,GAAc4C,CAAC,CAAC+D,UAApC,CAF4B,CAG5B;;QACAI,QAAQ,CAACC,eAAT,CAAyBzF,KAAzB,CAA+B0F,SAA/B,GAA2C,QAA3C;MACD;IACF,CAjKM;IAkKPjB,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAI,CAAC,KAAK3F,QAAV,EAAoB;MAEpB,IAAA6G,WAAA,GAA6B,KAAK5F,KAAlC;QAAQpD,OAAF,GAAAgJ,WAAA,CAAEhJ,OAAF;QAAWE,OAAA,GAAA8I,WAAA,CAAA9I,OAAA;MACjB,IAAM+I,eAAe,GAAGjJ,OAAO,CAACF,WAAR,GAAsBI,OAAO,CAACJ,WAAtD;MAEAE,OAAO,CAACqD,KAAR,CAAckF,WAAd,CAA0B,YAA1B,EAAwC,IAAxC;MACAvI,OAAO,CAACqD,KAAR,CAAckF,WAAd,CAA0B,YAA1B,EAAwC,IAAxC;MAEA,IAAI,KAAKrF,QAAL,CAActD,GAAlB,EAAuB;QACrB;QACA,IAAI,KAAKqC,YAAL,GAAoB,CAApB,IAAyB,CAAC,KAAKL,aAAnC,EAAkD;UAChD,KAAKK,YAAL,GAAoB,CAApB;QACD,CAFD,MAEO,IAAI,KAAKA,YAAL,IAAqB,CAACgH,eAA1B,EAA2C;UAChD,KAAKhH,YAAL,GAAoB,CAACgH,eAArB;QACD;MACF,CAPD,MAOO;QACL;QACA,IAAI,KAAKhH,YAAL,GAAoB,CAApB,IAAyB,CAAC,KAAKL,aAAnC,EAAkD;UAChD,KAAKK,YAAL,GAAoB,CAApB;QACD,CAFD,MAEO,IAAI,KAAKA,YAAL,IAAqBgH,eAAzB,EAA0C;UAC/C,KAAKhH,YAAL,GAAoBgH,eAApB;QACD;MACF;MAED,KAAKjH,SAAL,GAAiB,KAAjB,CAzBQ,CA0BR;;MACA6G,QAAQ,CAACC,eAAT,CAAyBzF,KAAzB,CAA+B6F,cAA/B,CAA8C,YAA9C;IACD,CA9LM;IA+LPzB,aAAa,WAAbA,aAAaA,CAAE/C,CAAF,EAAiByE,EAAjB,EAA4C;MACvDzE,CAAC,CAAC0E,eAAF;MACA,KAAKxH,aAAL,IAAsBuH,EAAE,CAACzE,CAAD,CAAxB;IACD,CAlMM;IAmMP2E;IAAe,qCAAfA,eAAA,EAAyC;MACvC,IAAI,CAAC,KAAKC,YAAN,IAAsB,KAAKlE,KAAL,CAAWhB,MAArC,EAA6C;QAC3C,IAAMmF,gBAAgB,GAAG,KAAKnE,KAAL,CAAW,KAAKA,KAAL,CAAWhB,MAAX,GAAoB,CAA/B,EAAkCN,GAAlC,CAAsC0F,qBAAtC,EAAzB;QACA,IAAMC,eAAe,GAAG,KAAKrG,KAAL,CAAWlD,OAAX,CAAmBsJ,qBAAnB,EAAxB;QAEA,IACG,KAAKtG,QAAL,CAActD,GAAd,IAAqB6J,eAAe,CAACC,KAAhB,GAAwBH,gBAAgB,CAACG,KAA/D,IACC,CAAC,KAAKxG,QAAL,CAActD,GAAf,IAAsB6J,eAAe,CAACE,IAAhB,GAAuBJ,gBAAgB,CAACI,IAFjE,EAGE;UACA,KAAKvB,QAAL,CAAc,MAAd;QACD;MACF;MAED,IAAI,CAAC,KAAKkB,YAAV,EAAwB;QACtB;MACD;MAED,IACE,KAAKM,aAAL,KAAuB,CAAvB,IACC,CAAC,KAAK3I,YAAN,IAAsB,CAAC,KAAKW,aAF/B,EAGE;QACA,KAAKK,YAAL,GAAoB,CAApB;MACD,CALD,MAKO,IAAI,KAAKhB,YAAT,EAAuB;QAC5B,KAAKgB,YAAL,GAAoB1B,uBAAuB,CACzC,KAAK+I,YAAL,CAAkBxF,GADuB,EAEzC,KAAKnE,MAFoC,EAGzC,KAAKuD,QAAL,CAActD,GAH2B,CAA3C;MAKD,CANM,MAMA,IAAI,KAAKgC,aAAT,EAAwB;QAC7B,KAAKK,YAAL,GAAoBxC,sBAAsB,CACxC,KAAK6J,YAAL,CAAkBxF,GADsB,EAExC,KAAKnE,MAFmC,EAGxC,KAAKuD,QAAL,CAActD,GAH0B,EAIxC,KAAKqC,YAJmC,CAA1C;MAMD;IACF,CAvOM;IAwOPmG;IAAS,qCAATA,SAAqC1B,QAA7B,EAAsD;MAC5D,KAAKzE,YAAL,GAAoB,KAAK8F,kBAAL,CAAwBrB,QAAxB,EAAkC;QACpD;QACA1G,OAAO,EAAE,KAAKoD,KAAL,CAAWpD,OAAX,GAAqB,KAAKoD,KAAL,CAAWpD,OAAX,CAAmBF,WAAxC,GAAsD,CAFX;QAGpDI,OAAO,EAAE,KAAKkD,KAAL,CAAWlD,OAAX,GAAqB,KAAKkD,KAAL,CAAWlD,OAAX,CAAmBJ,WAAxC,GAAsD;MAHX,CAAlC,EAIjB,KAAKoD,QAAL,CAActD,GAJG,EAIE,KAAKqC,YAJP,CAApB;IAKD,CA9OM;IA+OPoC,SAAS,WAATA,SAASA,CAAA;MAAA,IAAAwF,MAAA;MACPzH,MAAM,CAAC0H,qBAAP,CAA6B,YAAK;QAChC,IAAID,MAAA,CAAKxB,YAAT,EAAuB;QAEvB,IAAA0B,YAAA,GAA6BF,MAAA,CAAKzG,KAAlC;UAAQpD,OAAF,GAAA+J,YAAA,CAAE/J,OAAF;UAAWE,OAAA,GAAA6J,YAAA,CAAA7J,OAAA;QAEjB2J,MAAA,CAAKlK,MAAL,GAAc;UACZK,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAACF,WAAX,GAAyB,CAD7B;UAEZI,OAAO,EAAEA,OAAO,GAAGA,OAAO,CAACJ,WAAX,GAAyB;QAF7B,CAAd,CALgC,CAUhC;QACA;QACA;;QACA+J,MAAA,CAAKjI,aAAL,GAAqBiI,MAAA,CAAKlK,MAAL,CAAYO,OAAZ,GAAsB,CAAtB,GAA0B2J,MAAA,CAAKlK,MAAL,CAAYK,OAA3D;QAEA6J,MAAA,CAAKR,cAAL;MACD,CAhBD;IAiBD;EAjQM,CArJF;EAyZPW,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ,KAAKzD,OAAL,EAAR,EAAwB,CAC9B,KAAKW,OAAL,EAD8B,EAE9B,KAAKG,UAAL,EAF8B,EAG9B,KAAK7B,OAAL,EAH8B,CAAxB,CAAR;EAKD;AA/ZM,CAXqB,CAAvB;AA6aP,eAAehF,cAAc,CAACC,MAAf,CAAsB;EACnCC,IAAI,EAAE,eAD6B;EAGnCuJ,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,UAAU,EAAE;IADP,CAAP;EAGD;AAPkC,CAAtB,CAAf", "ignoreList": []}]}