{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabsSlider.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabsSlider.js", "mtime": 1757335239369}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8gTWl4aW5zCmltcG9ydCBDb2xvcmFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL2NvbG9yYWJsZSc7IC8vIFV0aWxpdGllcwoKaW1wb3J0IG1peGlucyBmcm9tICcuLi8uLi91dGlsL21peGlucyc7Ci8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBtaXhpbnMoQ29sb3JhYmxlKS5leHRlbmQoewogIG5hbWU6ICd2LXRhYnMtc2xpZGVyJywKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICByZXR1cm4gaCgnZGl2JywgdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb2xvciwgewogICAgICBzdGF0aWNDbGFzczogJ3YtdGFicy1zbGlkZXInCiAgICB9KSk7CiAgfQp9KTs="}, {"version": 3, "names": ["Colorable", "mixins", "extend", "name", "render", "h", "setBackgroundColor", "color", "staticClass"], "sources": ["../../../src/components/VTabs/VTabsSlider.ts"], "sourcesContent": ["// Mixins\nimport Colorable from '../../mixins/colorable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue/types'\n\n/* @vue/component */\nexport default mixins(Colorable).extend({\n  name: 'v-tabs-slider',\n\n  render (h): VNode {\n    return h('div', this.setBackgroundColor(this.color, {\n      staticClass: 'v-tabs-slider',\n    }))\n  },\n})\n"], "mappings": "AAAA;AACA,OAAOA,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAKA;;AACA,eAAeA,MAAM,CAACD,SAAD,CAAN,CAAkBE,MAAlB,CAAyB;EACtCC,IAAI,EAAE,eADgC;EAGtCC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ,KAAKC,kBAAL,CAAwB,KAAKC,KAA7B,EAAoC;MAClDC,WAAW,EAAE;IADqC,CAApC,CAAR,CAAR;EAGD;AAPqC,CAAzB,CAAf", "ignoreList": []}]}