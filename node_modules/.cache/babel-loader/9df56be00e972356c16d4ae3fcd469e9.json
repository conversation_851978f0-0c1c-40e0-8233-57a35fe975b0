{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/tr.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/tr.js", "mtime": 1757335237556}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/tr.ts"], "sourcesContent": ["export default {\n  badge: 'rozet',\n  close: '<PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Eşleşen veri bulunamadı',\n    loadingText: 'Yükleniyor... Lütfen bekleyin.',\n  },\n  dataTable: {\n    itemsPerPageText: '<PERSON><PERSON> başına satır:',\n    ariaLabel: {\n      sortDescending: 'Z den A ya sıralı.',\n      sortAscending: 'A dan Z ye sıralı.',\n      sortNone: 'Sıral<PERSON> değil. ',\n      activateNone: 'Sıralamayı kaldırmak için etkinleştir.',\n      activateDescending: 'Z den A ya sıralamak için etkinleştir.',\n      activateAscending: 'A dan Z ye sıralamak için etkinleştir.',\n    },\n    sortBy: 'Sırala',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Sayfa başına satır:',\n    itemsPerPageAll: 'Hepsi',\n    nextPage: '<PERSON><PERSON><PERSON> sayfa',\n    prevPage: '<PERSON><PERSON><PERSON> sayfa',\n    firstPage: '<PERSON>lk sayfa',\n    lastPage: '<PERSON> sayfa',\n    pageText: '{0} - {1} arası, Toplam: {2} kayıt',\n  },\n  datePicker: {\n    itemsSelected: '{0} öge seçildi',\n    nextMonthAriaLabel: 'Gelecek ay',\n    nextYearAriaLabel: 'Gelecek yıl',\n    prevMonthAriaLabel: 'Geçtiğimiz ay',\n    prevYearAriaLabel: 'Geçen yıl',\n  },\n  noDataText: 'Bu görünümde veri yok.',\n  carousel: {\n    prev: 'Önceki görsel',\n    next: 'Sonraki görsel',\n    ariaLabel: {\n      delimiter: 'Galeri sayfa {0} / {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} tane daha',\n  },\n  fileInput: {\n    counter: '{0} dosya',\n    counterSize: '{0} dosya (toplamda {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Sayfalandırma Navigasyonu',\n      next: 'Sonraki sayfa',\n      previous: 'Önceki sayfa',\n      page: 'Sayfaya git {0}',\n      currentPage: 'Geçerli Sayfa, Sayfa {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,OADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,yBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,qBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,oBAFN;MAGTC,QAAQ,EAAE,gBAHD;MAITC,YAAY,EAAE,wCAJL;MAKTC,kBAAkB,EAAE,wCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,OAFP;IAGVC,QAAQ,EAAE,eAHA;IAIVC,QAAQ,EAAE,cAJA;IAKVC,SAAS,EAAE,WALD;IAMVC,QAAQ,EAAE,WANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,iBADL;IAEVC,kBAAkB,EAAE,YAFV;IAGVC,iBAAiB,EAAE,aAHT;IAIVC,kBAAkB,EAAE,eAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,wBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,eADE;IAERC,IAAI,EAAE,gBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,2BADA;MAETX,IAAI,EAAE,eAFG;MAGTY,QAAQ,EAAE,cAHD;MAITC,IAAI,EAAE,iBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}