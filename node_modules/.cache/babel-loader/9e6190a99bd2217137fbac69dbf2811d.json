{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSwitch/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSwitch/index.js", "mtime": 1757335236970}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZTd2l0Y2ggZnJvbSAnLi9WU3dpdGNoJzsKZXhwb3J0IHsgVlN3aXRjaCB9OwpleHBvcnQgZGVmYXVsdCBWU3dpdGNoOw=="}, {"version": 3, "names": ["VSwitch"], "sources": ["../../../src/components/VSwitch/index.ts"], "sourcesContent": ["import VSwitch from './VSwitch'\n\nexport { VSwitch }\nexport default VSwitch\n"], "mappings": "AAAA,OAAOA,OAAP,MAAoB,WAApB;AAEA,SAASA,OAAT;AACA,eAAeA,OAAf", "ignoreList": []}]}