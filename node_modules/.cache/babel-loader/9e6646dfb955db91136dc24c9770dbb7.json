{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/it.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/it.js", "mtime": 1757335237314}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/it.ts"], "sourcesContent": ["export default {\n  badge: 'Distint<PERSON>',\n  close: '<PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: '<PERSON>essun risultato trovato',\n    loadingText: 'Caricamento in corso...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Righe per pagina:',\n    ariaLabel: {\n      sortDescending: 'Ordinati in ordine decrescente.',\n      sortAscending: 'Ordinati in ordine crescente.',\n      sortNone: 'Non ordinato.',\n      activateNone: `Attiva per rimuovere l'ordinamento.`,\n      activateDescending: 'Attiva per ordinare in ordine decrescente.',\n      activateAscending: 'Attiva per ordinare in ordine crescente.',\n    },\n    sortBy: 'Ordina per',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Elementi per pagina:',\n    itemsPerPageAll: 'Tutti',\n    nextPage: 'Pagina seguente',\n    prevPage: 'Pagina precedente',\n    firstPage: 'Prima pagina',\n    lastPage: 'Ultima pagina',\n    pageText: '{0}-{1} di {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} selezionati',\n    nextMonthAriaLabel: 'Il prossimo mese',\n    nextYearAriaLabel: `L'anno prossimo`,\n    prevMonthAriaLabel: 'Il mese scorso',\n    prevYearAriaLabel: `L'anno scorso`,\n  },\n  noDataText: 'Nessun elemento disponibile',\n  carousel: {\n    prev: 'Vista precedente',\n    next: 'Prossima vista',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} di {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} di più',\n  },\n  fileInput: {\n    counter: '{0} file',\n    counterSize: '{0} file ({1} in totale)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigazione impaginazione',\n      next: 'Pagina seguente',\n      previous: 'Pagina precedente',\n      page: 'Vai alla pagina {0}',\n      currentPage: 'Pagina corrente, pagina {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Valutazione {0} di {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,YADM;EAEbC,KAAK,EAAE,QAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,0BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,mBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,iCADP;MAETC,aAAa,EAAE,+BAFN;MAGTC,QAAQ,EAAE,eAHD;MAITC,YAAY,uCAJH;MAKTC,kBAAkB,EAAE,4CALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,sBADR;IAEVU,eAAe,EAAE,OAFP;IAGVC,QAAQ,EAAE,iBAHA;IAIVC,QAAQ,EAAE,mBAJA;IAKVC,SAAS,EAAE,cALD;IAMVC,QAAQ,EAAE,eANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,iBADL;IAEVC,kBAAkB,EAAE,kBAFV;IAGVC,iBAAiB,mBAHP;IAIVC,kBAAkB,EAAE,gBAJV;IAKVC,iBAAiB;EALP,CA5BC;EAmCbC,UAAU,EAAE,6BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,kBADE;IAERC,IAAI,EAAE,gBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,UADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,2BADA;MAETX,IAAI,EAAE,iBAFG;MAGTY,QAAQ,EAAE,mBAHD;MAITC,IAAI,EAAE,qBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}