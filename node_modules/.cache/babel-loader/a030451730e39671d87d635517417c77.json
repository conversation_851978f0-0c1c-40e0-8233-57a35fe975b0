{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VThemeProvider/VThemeProvider.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VThemeProvider/VThemeProvider.js", "mtime": 1757335239381}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9maW5kSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL2ZpbmQiOwovLyBNaXhpbnMKaW1wb3J0IFRoZW1lYWJsZSBmcm9tICcuLi8uLi9taXhpbnMvdGhlbWVhYmxlJzsKLyogQHZ1ZS9jb21wb25lbnQgKi8KCmV4cG9ydCBkZWZhdWx0IFRoZW1lYWJsZS5leHRlbmQoewogIG5hbWU6ICd2LXRoZW1lLXByb3ZpZGVyJywKICBwcm9wczogewogICAgcm9vdDogQm9vbGVhbgogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzRGFyazogZnVuY3Rpb24gaXNEYXJrKCkgewogICAgICByZXR1cm4gdGhpcy5yb290ID8gdGhpcy5yb290SXNEYXJrIDogVGhlbWVhYmxlLm9wdGlvbnMuY29tcHV0ZWQuaXNEYXJrLmNhbGwodGhpcyk7CiAgICB9CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcigpIHsKICAgIHZhciBfY29udGV4dDsKICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovCiAgICByZXR1cm4gdGhpcy4kc2xvdHNbImRlZmF1bHQiXSAmJiBfZmluZEluc3RhbmNlUHJvcGVydHkoX2NvbnRleHQgPSB0aGlzLiRzbG90c1siZGVmYXVsdCJdKS5jYWxsKF9jb250ZXh0LCBmdW5jdGlvbiAobm9kZSkgewogICAgICByZXR1cm4gIW5vZGUuaXNDb21tZW50ICYmIG5vZGUudGV4dCAhPT0gJyAnOwogICAgfSk7CiAgfQp9KTs="}, {"version": 3, "names": ["Themeable", "extend", "name", "props", "root", "Boolean", "computed", "isDark", "rootIsDark", "options", "call", "render", "_context", "$slots", "_findInstanceProperty", "node", "isComment", "text"], "sources": ["../../../src/components/VThemeProvider/VThemeProvider.ts"], "sourcesContent": ["// Mixins\nimport Themeable from '../../mixins/themeable'\n\n// Types\nimport { VNode } from 'vue'\n\n/* @vue/component */\nexport default Themeable.extend({\n  name: 'v-theme-provider',\n\n  props: { root: <PERSON><PERSON><PERSON> },\n\n  computed: {\n    isDark (): boolean {\n      return this.root\n        ? this.rootIsDark\n        : Themeable.options.computed.isDark.call(this)\n    },\n  },\n\n  render (): VNode {\n    /* istanbul ignore next */\n    return (\n      this.$slots.default! &&\n      this.$slots.default!.find(node => !node.isComment && node.text !== ' ')!\n    )\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAOA,SAAP,MAAsB,wBAAtB;AAKA;;AACA,eAAeA,SAAS,CAACC,MAAV,CAAiB;EAC9BC,IAAI,EAAE,kBADwB;EAG9BC,KAAK,EAAE;IAAEC,IAAI,EAAEC;EAAR,CAHuB;EAK9BC,QAAQ,EAAE;IACRC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO,KAAKH,IAAL,GACH,KAAKI,UADF,GAEHR,SAAS,CAACS,OAAV,CAAkBH,QAAlB,CAA2BC,MAA3B,CAAkCG,IAAlC,CAAuC,IAAvC,CAFJ;IAGD;EALO,CALoB;EAa9BC,MAAM,WAANA,MAAMA,CAAA;IAAA,IAAAC,QAAA;IACJ;IACA,OACE,KAAKC,MAAL,eACAC,qBAAA,CAAAF,QAAA,QAAKC,MAAL,aAAAH,IAAA,CAAAE,QAAA,EAA0B,UAAAG,IAAI;MAAA,OAAI,CAACA,IAAI,CAACC,SAAN,IAAmBD,IAAI,CAACE,IAAL,KAAc,GAAnE;IAAA,EAFF;EAID;AAnB6B,CAAjB,CAAf", "ignoreList": []}]}