{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/lt.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/lt.js", "mtime": 1757335237321}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/lt.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Nerasta atitinkanč<PERSON>ų įrašų',\n    loadingText: 'Kraunama...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Eilutės per puslapį:',\n    ariaLabel: {\n      sortDescending: 'Išrikiuota mažėjimo tvarka.',\n      sortAscending: 'Išrikiu<PERSON> didėjimo tvarka.',\n      sortNone: 'Nerikiuota.',\n      activateNone: '<PERSON><PERSON><PERSON><PERSON>kite, jei norite rikiavimą pašalinti.',\n      activateDescending: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jei norite rikiuoti ma<PERSON> tvarka.',\n      activateAscending: '<PERSON><PERSON><PERSON><PERSON>ki<PERSON>, jei norite rikiuoti didėjimo tvarka.',\n    },\n    sortBy: 'Sort by',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Įrašai per puslapį:',\n    itemsPerPageAll: 'Visi',\n    nextPage: '<PERSON><PERSON> puslapis',\n    prevPage: '<PERSON><PERSON><PERSON><PERSON> puslapis',\n    firstPage: '<PERSON><PERSON><PERSON> puslapis',\n    lastPage: 'Pask<PERSON><PERSON> puslapis',\n    pageText: '{0}-{1} iš {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} pasirinkta',\n    nextMonthAriaLabel: 'Kitą mėnesį',\n    nextYearAriaLabel: 'Kitais metais',\n    prevMonthAriaLabel: 'Praeita mėnesį',\n    prevYearAriaLabel: 'Praeiti metai',\n  },\n  noDataText: 'Nėra duomenų',\n  carousel: {\n    prev: 'Ankstesnioji skaidrė',\n    next: 'Kita skaidrė',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: 'Daugiau {0}',\n  },\n  fileInput: {\n    counter: '{0} failų',\n    counterSize: '{0} failų ({1} iš viso)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Puslapio naršymas',\n      next: 'Kitas puslapis',\n      previous: 'Ankstesnis puslapis',\n      page: 'Eiti į puslapį {0}',\n      currentPage: 'Dabartinis puslapis, puslapis {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,WADM;EAEbC,KAAK,EAAE,UAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,4BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,sBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,6BADP;MAETC,aAAa,EAAE,6BAFN;MAGTC,QAAQ,EAAE,aAHD;MAITC,YAAY,EAAE,gDAJL;MAKTC,kBAAkB,EAAE,qDALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,gBAHA;IAIVC,QAAQ,EAAE,qBAJA;IAKVC,SAAS,EAAE,iBALD;IAMVC,QAAQ,EAAE,qBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,gBADL;IAEVC,kBAAkB,EAAE,aAFV;IAGVC,iBAAiB,EAAE,eAHT;IAIVC,kBAAkB,EAAE,gBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,cAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,sBADE;IAERC,IAAI,EAAE,cAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,mBADA;MAETX,IAAI,EAAE,gBAFG;MAGTY,QAAQ,EAAE,qBAHD;MAITC,IAAI,EAAE,oBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}