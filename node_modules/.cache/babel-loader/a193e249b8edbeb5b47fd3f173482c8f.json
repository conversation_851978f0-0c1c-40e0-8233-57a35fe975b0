{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/core-js-stable/instance/every.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/core-js-stable/instance/every.js", "mtime": 1757335235147}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCJjb3JlLWpzLXB1cmUvc3RhYmxlL2luc3RhbmNlL2V2ZXJ5Iik7"}, {"version": 3, "names": ["module", "exports", "require"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/core-js-stable/instance/every.js"], "sourcesContent": ["module.exports = require(\"core-js-pure/stable/instance/every\");"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,oCAAoC,CAAC", "ignoreList": []}]}