{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTooltip/VTooltip.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTooltip/VTooltip.js", "mtime": 1757335239659}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Activatable", "Colorable", "Delayable", "Dependent", "Menuable", "convertToUnit", "keyCodes", "getSlotType", "consoleError", "mixins", "extend", "name", "props", "close<PERSON><PERSON><PERSON>", "type", "Number", "String", "disabled", "Boolean", "openDelay", "openOnHover", "openOnFocus", "tag", "transition", "data", "calculated<PERSON><PERSON><PERSON><PERSON><PERSON>", "closeDependents", "computed", "calculatedLeft", "_this$dimensions", "dimensions", "activator", "content", "unknown", "bottom", "left", "top", "right", "activatorLeft", "attach", "offsetLeft", "width", "nudgeLeft", "_parseInt", "nudgeRight", "concat", "calcXOverflow", "calculatedTop", "_this$dimensions2", "activatorTop", "offsetTop", "height", "nudgeTop", "nudgeBottom", "pageYOffset", "calcYOverflow", "classes", "computedTransition", "isActive", "offsetY", "offsetX", "styles", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "zIndex", "activeZIndex", "beforeMount", "_this", "$nextTick", "value", "callActivate", "mounted", "methods", "activate", "updateDimensions", "requestAnimationFrame", "startTransition", "deactivate", "runDelay", "genActivatorListeners", "_this2", "listeners", "options", "call", "focus", "e", "getActivator", "blur", "keydown", "keyCode", "esc", "genActivatorAttributes", "genTransition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$createElement", "setBackgroundColor", "color", "staticClass", "_defineProperty", "contentClass", "activatorFixed", "style", "attrs", "getScopeIdAttrs", "directives", "isContentActive", "ref", "getContentSlot", "render", "h", "_this3", "showLazyContent", "genActivator"], "sources": ["../../../src/components/VTooltip/VTooltip.ts"], "sourcesContent": ["import './VTooltip.sass'\n\n// Mixins\nimport Activatable from '../../mixins/activatable'\nimport Colorable from '../../mixins/colorable'\nimport Delayable from '../../mixins/delayable'\nimport Dependent from '../../mixins/dependent'\nimport Menuable from '../../mixins/menuable'\n\n// Helpers\nimport { convertToUnit, keyCodes, getSlotType } from '../../util/helpers'\nimport { consoleError } from '../../util/console'\n\n// Types\nimport { VNode } from 'vue'\nimport mixins from '../../util/mixins'\n\n/* @vue/component */\nexport default mixins(Colorable, Delayable, Dependent, Menuable).extend({\n  name: 'v-tooltip',\n\n  props: {\n    closeDelay: {\n      type: [Number, String],\n      default: 0,\n    },\n    disabled: Boolean,\n    openDelay: {\n      type: [Number, String],\n      default: 0,\n    },\n    openOnHover: {\n      type: Boolean,\n      default: true,\n    },\n    openOnFocus: {\n      type: Boolean,\n      default: true,\n    },\n    tag: {\n      type: String,\n      default: 'span',\n    },\n    transition: String,\n  },\n\n  data: () => ({\n    calculatedMinWidth: 0,\n    closeDependents: false,\n  }),\n\n  computed: {\n    calculatedLeft (): string {\n      const { activator, content } = this.dimensions\n      const unknown = !this.bottom && !this.left && !this.top && !this.right\n      const activatorLeft = this.attach !== false ? activator.offsetLeft : activator.left\n      let left = 0\n\n      if (this.top || this.bottom || unknown) {\n        left = (\n          activatorLeft +\n          (activator.width / 2) -\n          (content.width / 2)\n        )\n      } else if (this.left || this.right) {\n        left = (\n          activatorLeft +\n          (this.right ? activator.width : -content.width) +\n          (this.right ? 10 : -10)\n        )\n      }\n\n      if (this.nudgeLeft) left -= parseInt(this.nudgeLeft)\n      if (this.nudgeRight) left += parseInt(this.nudgeRight)\n\n      return `${this.calcXOverflow(left, this.dimensions.content.width)}px`\n    },\n    calculatedTop (): string {\n      const { activator, content } = this.dimensions\n      const activatorTop = this.attach !== false ? activator.offsetTop : activator.top\n      let top = 0\n\n      if (this.top || this.bottom) {\n        top = (\n          activatorTop +\n          (this.bottom ? activator.height : -content.height) +\n          (this.bottom ? 10 : -10)\n        )\n      } else if (this.left || this.right) {\n        top = (\n          activatorTop +\n          (activator.height / 2) -\n          (content.height / 2)\n        )\n      }\n\n      if (this.nudgeTop) top -= parseInt(this.nudgeTop)\n      if (this.nudgeBottom) top += parseInt(this.nudgeBottom)\n      if (this.attach === false) top += this.pageYOffset\n\n      return `${this.calcYOverflow(top)}px`\n    },\n    classes (): object {\n      return {\n        'v-tooltip--top': this.top,\n        'v-tooltip--right': this.right,\n        'v-tooltip--bottom': this.bottom,\n        'v-tooltip--left': this.left,\n        'v-tooltip--attached':\n          this.attach === '' ||\n          this.attach === true ||\n          this.attach === 'attach',\n      }\n    },\n    computedTransition (): string {\n      if (this.transition) return this.transition\n\n      return this.isActive ? 'scale-transition' : 'fade-transition'\n    },\n    offsetY (): boolean {\n      return this.top || this.bottom\n    },\n    offsetX (): boolean {\n      return this.left || this.right\n    },\n    styles (): object {\n      return {\n        left: this.calculatedLeft,\n        maxWidth: convertToUnit(this.maxWidth),\n        minWidth: convertToUnit(this.minWidth),\n        top: this.calculatedTop,\n        zIndex: this.zIndex || this.activeZIndex,\n      }\n    },\n  },\n\n  beforeMount () {\n    this.$nextTick(() => {\n      this.value && this.callActivate()\n    })\n  },\n\n  mounted () {\n    if (getSlotType(this, 'activator', true) === 'v-slot') {\n      consoleError(`v-tooltip's activator slot must be bound, try '<template #activator=\"data\"><v-btn v-on=\"data.on>'`, this)\n    }\n  },\n\n  methods: {\n    activate () {\n      // Update coordinates and dimensions of menu\n      // and its activator\n      this.updateDimensions()\n      // Start the transition\n      requestAnimationFrame(this.startTransition)\n    },\n    deactivate () {\n      this.runDelay('close')\n    },\n    genActivatorListeners () {\n      const listeners = Activatable.options.methods.genActivatorListeners.call(this)\n\n      if (this.openOnFocus) {\n        listeners.focus = (e: Event) => {\n          this.getActivator(e)\n          this.runDelay('open')\n        }\n        listeners.blur = (e: Event) => {\n          this.getActivator(e)\n          this.runDelay('close')\n        }\n      }\n\n      listeners.keydown = (e: KeyboardEvent) => {\n        if (e.keyCode === keyCodes.esc) {\n          this.getActivator(e)\n          this.runDelay('close')\n        }\n      }\n\n      return listeners\n    },\n    genActivatorAttributes () {\n      return {\n        'aria-haspopup': true,\n        'aria-expanded': String(this.isActive),\n      }\n    },\n    genTransition () {\n      const content = this.genContent()\n\n      if (!this.computedTransition) return content\n\n      return this.$createElement('transition', {\n        props: {\n          name: this.computedTransition,\n        },\n      }, [content])\n    },\n    genContent () {\n      return this.$createElement(\n        'div',\n        this.setBackgroundColor(this.color, {\n          staticClass: 'v-tooltip__content',\n          class: {\n            [this.contentClass]: true,\n            menuable__content__active: this.isActive,\n            'v-tooltip__content--fixed': this.activatorFixed,\n          },\n          style: this.styles,\n          attrs: this.getScopeIdAttrs(),\n          directives: [{\n            name: 'show',\n            value: this.isContentActive,\n          }],\n          ref: 'content',\n        }),\n        this.getContentSlot()\n      )\n    },\n  },\n\n  render (h): VNode {\n    return h(this.tag, {\n      staticClass: 'v-tooltip',\n      class: this.classes,\n    }, [\n      this.showLazyContent(() => [this.genTransition()]),\n      this.genActivator(),\n    ])\n  },\n})\n"], "mappings": ";;;AAAA,OAAO,gDAAP,C,CAEA;;AACA,OAAOA,WAAP,MAAwB,0BAAxB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,QAAP,MAAqB,uBAArB,C,CAEA;;AACA,SAASC,aAAT,EAAwBC,QAAxB,EAAkCC,WAAlC,QAAqD,oBAArD;AACA,SAASC,YAAT,QAA6B,oBAA7B;AAIA,OAAOC,MAAP,MAAmB,mBAAnB;AAEA;;AACA,eAAeA,MAAM,CAACR,SAAD,EAAYC,SAAZ,EAAuBC,SAAvB,EAAkCC,QAAlC,CAAN,CAAkDM,MAAlD,CAAyD;EACtEC,IAAI,EAAE,WADgE;EAGtEC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADI;MAEV,WAAS;IAFC,CADP;IAKLC,QAAQ,EAAEC,OALL;IAMLC,SAAS,EAAE;MACTL,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADG;MAET,WAAS;IAFA,CANN;IAULI,WAAW,EAAE;MACXN,IAAI,EAAEI,OADK;MAEX,WAAS;IAFE,CAVR;IAcLG,WAAW,EAAE;MACXP,IAAI,EAAEI,OADK;MAEX,WAAS;IAFE,CAdR;IAkBLI,GAAG,EAAE;MACHR,IAAI,EAAEE,MADH;MAEH,WAAS;IAFN,CAlBA;IAsBLO,UAAU,EAAEP;EAtBP,CAH+D;EA4BtEQ,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,kBAAkB,EAAE,CADT;MAEXC,eAAe,EAAE;IAFN,CAAP;EAAA,CA5BgE;EAiCtEC,QAAQ,EAAE;IACRC,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAAC,gBAAA,GAA+B,KAAKC,UAApC;QAAQC,SAAF,GAAAF,gBAAA,CAAEE,SAAF;QAAaC,OAAA,GAAAH,gBAAA,CAAAG,OAAA;MACnB,IAAMC,OAAO,GAAG,CAAC,KAAKC,MAAN,IAAgB,CAAC,KAAKC,IAAtB,IAA8B,CAAC,KAAKC,GAApC,IAA2C,CAAC,KAAKC,KAAjE;MACA,IAAMC,aAAa,GAAG,KAAKC,MAAL,KAAgB,KAAhB,GAAwBR,SAAS,CAACS,UAAlC,GAA+CT,SAAS,CAACI,IAA/E;MACA,IAAIA,IAAI,GAAG,CAAX;MAEA,IAAI,KAAKC,GAAL,IAAY,KAAKF,MAAjB,IAA2BD,OAA/B,EAAwC;QACtCE,IAAI,GACFG,aAAa,GACZP,SAAS,CAACU,KAAV,GAAkB,CADnB,GAECT,OAAO,CAACS,KAAR,GAAgB,CAHnB;MAKD,CAND,MAMO,IAAI,KAAKN,IAAL,IAAa,KAAKE,KAAtB,EAA6B;QAClCF,IAAI,GACFG,aAAa,IACZ,KAAKD,KAAL,GAAaN,SAAS,CAACU,KAAvB,GAA+B,CAACT,OAAO,CAACS,KAD5B,CAAb,IAEC,KAAKJ,KAAL,GAAa,EAAb,GAAkB,CAAC,EAFpB,CADF;MAKD;MAED,IAAI,KAAKK,SAAT,EAAoBP,IAAI,IAAIQ,SAAA,CAAS,KAAKD,SAAN,CAAhB;MACpB,IAAI,KAAKE,UAAT,EAAqBT,IAAI,IAAIQ,SAAA,CAAS,KAAKC,UAAN,CAAhB;MAErB,UAAAC,MAAA,CAAU,KAAKC,aAAL,CAAmBX,IAAnB,EAAyB,KAAKL,UAAL,CAAgBE,OAAhB,CAAwBS,KAAjD,CAAuD;IAClE,CAzBO;IA0BRM,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAAC,iBAAA,GAA+B,KAAKlB,UAApC;QAAQC,SAAF,GAAAiB,iBAAA,CAAEjB,SAAF;QAAaC,OAAA,GAAAgB,iBAAA,CAAAhB,OAAA;MACnB,IAAMiB,YAAY,GAAG,KAAKV,MAAL,KAAgB,KAAhB,GAAwBR,SAAS,CAACmB,SAAlC,GAA8CnB,SAAS,CAACK,GAA7E;MACA,IAAIA,GAAG,GAAG,CAAV;MAEA,IAAI,KAAKA,GAAL,IAAY,KAAKF,MAArB,EAA6B;QAC3BE,GAAG,GACDa,YAAY,IACX,KAAKf,MAAL,GAAcH,SAAS,CAACoB,MAAxB,GAAiC,CAACnB,OAAO,CAACmB,MAD/B,CAAZ,IAEC,KAAKjB,MAAL,GAAc,EAAd,GAAmB,CAAC,EAFrB,CADF;MAKD,CAND,MAMO,IAAI,KAAKC,IAAL,IAAa,KAAKE,KAAtB,EAA6B;QAClCD,GAAG,GACDa,YAAY,GACXlB,SAAS,CAACoB,MAAV,GAAmB,CADpB,GAECnB,OAAO,CAACmB,MAAR,GAAiB,CAHpB;MAKD;MAED,IAAI,KAAKC,QAAT,EAAmBhB,GAAG,IAAIO,SAAA,CAAS,KAAKS,QAAN,CAAf;MACnB,IAAI,KAAKC,WAAT,EAAsBjB,GAAG,IAAIO,SAAA,CAAS,KAAKU,WAAN,CAAf;MACtB,IAAI,KAAKd,MAAL,KAAgB,KAApB,EAA2BH,GAAG,IAAI,KAAKkB,WAAZ;MAE3B,UAAAT,MAAA,CAAU,KAAKU,aAAL,CAAmBnB,GAAnB,CAAuB;IAClC,CAlDO;IAmDRoB,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO;QACL,kBAAkB,KAAKpB,GADlB;QAEL,oBAAoB,KAAKC,KAFpB;QAGL,qBAAqB,KAAKH,MAHrB;QAIL,mBAAmB,KAAKC,IAJnB;QAKL,uBACE,KAAKI,MAAL,KAAgB,EAAhB,IACA,KAAKA,MAAL,KAAgB,IADhB,IAEA,KAAKA,MAAL,KAAgB;MARb,CAAP;IAUD,CA9DO;IA+DRkB,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAI,KAAKlC,UAAT,EAAqB,OAAO,KAAKA,UAAZ;MAErB,OAAO,KAAKmC,QAAL,GAAgB,kBAAhB,GAAqC,iBAA5C;IACD,CAnEO;IAoERC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAKvB,GAAL,IAAY,KAAKF,MAAxB;IACD,CAtEO;IAuER0B,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAKzB,IAAL,IAAa,KAAKE,KAAzB;IACD,CAzEO;IA0ERwB,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO;QACL1B,IAAI,EAAE,KAAKP,cADN;QAELkC,QAAQ,EAAEzD,aAAa,CAAC,KAAKyD,QAAN,CAFlB;QAGLC,QAAQ,EAAE1D,aAAa,CAAC,KAAK0D,QAAN,CAHlB;QAIL3B,GAAG,EAAE,KAAKW,aAJL;QAKLiB,MAAM,EAAE,KAAKA,MAAL,IAAe,KAAKC;MALvB,CAAP;IAOD;EAlFO,CAjC4D;EAsHtEC,WAAW,WAAXA,WAAWA,CAAA;IAAA,IAAAC,KAAA;IACT,KAAKC,SAAL,CAAe,YAAK;MAClBD,KAAA,CAAKE,KAAL,IAAcF,KAAA,CAAKG,YAAL,EAAd;IACD,CAFD;EAGD,CA1HqE;EA4HtEC,OAAO,WAAPA,OAAOA,CAAA;IACL,IAAIhE,WAAW,CAAC,IAAD,EAAO,WAAP,EAAoB,IAApB,CAAX,KAAyC,QAA7C,EAAuD;MACrDC,YAAY,yGAAsG,IAAtG,CAAZ;IACD;EACF,CAhIqE;EAkItEgE,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAA;MACN;MACA;MACA,KAAKC,gBAAL,GAHM,CAIN;;MACAC,qBAAqB,CAAC,KAAKC,eAAN,CAArB;IACD,CAPM;IAQPC,UAAU,WAAVA,UAAUA,CAAA;MACR,KAAKC,QAAL,CAAc,OAAd;IACD,CAVM;IAWPC,qBAAqB,WAArBA,qBAAqBA,CAAA;MAAA,IAAAC,MAAA;MACnB,IAAMC,SAAS,GAAGjF,WAAW,CAACkF,OAAZ,CAAoBV,OAApB,CAA4BO,qBAA5B,CAAkDI,IAAlD,CAAuD,IAAvD,CAAlB;MAEA,IAAI,KAAK9D,WAAT,EAAsB;QACpB4D,SAAS,CAACG,KAAV,GAAmB,UAAAC,CAAD,EAAa;UAC7BL,MAAA,CAAKM,YAAL,CAAkBD,CAAlB;UACAL,MAAA,CAAKF,QAAL,CAAc,MAAd;QACD,CAHD;QAIAG,SAAS,CAACM,IAAV,GAAkB,UAAAF,CAAD,EAAa;UAC5BL,MAAA,CAAKM,YAAL,CAAkBD,CAAlB;UACAL,MAAA,CAAKF,QAAL,CAAc,OAAd;QACD,CAHD;MAID;MAEDG,SAAS,CAACO,OAAV,GAAqB,UAAAH,CAAD,EAAqB;QACvC,IAAIA,CAAC,CAACI,OAAF,KAAcnF,QAAQ,CAACoF,GAA3B,EAAgC;UAC9BV,MAAA,CAAKM,YAAL,CAAkBD,CAAlB;UACAL,MAAA,CAAKF,QAAL,CAAc,OAAd;QACD;MACF,CALD;MAOA,OAAOG,SAAP;IACD,CAjCM;IAkCPU,sBAAsB,WAAtBA,sBAAsBA,CAAA;MACpB,OAAO;QACL,iBAAiB,IADZ;QAEL,iBAAiB3E,MAAM,CAAC,KAAK0C,QAAN;MAFlB,CAAP;IAID,CAvCM;IAwCPkC,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAM5D,OAAO,GAAG,KAAK6D,UAAL,EAAhB;MAEA,IAAI,CAAC,KAAKpC,kBAAV,EAA8B,OAAOzB,OAAP;MAE9B,OAAO,KAAK8D,cAAL,CAAoB,YAApB,EAAkC;QACvClF,KAAK,EAAE;UACLD,IAAI,EAAE,KAAK8C;QADN;MADgC,CAAlC,EAIJ,CAACzB,OAAD,CAJI,CAAP;IAKD,CAlDM;IAmDP6D,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKC,cAAL,CACL,KADK,EAEL,KAAKC,kBAAL,CAAwB,KAAKC,KAA7B,EAAoC;QAClCC,WAAW,EAAE,oBADqB;QAElC,SAAAC,eAAA,CAAAA,eAAA,CAAAA,eAAA,KACG,KAAKC,YAAN,EAAqB,IADhB,gCAEsB,KAAKzC,QAF3B,GAGL,6BAA6B,KAAK0C,cAAA,CALF;QAOlCC,KAAK,EAAE,KAAKxC,MAPsB;QAQlCyC,KAAK,EAAE,KAAKC,eAAL,EAR2B;QASlCC,UAAU,EAAE,CAAC;UACX7F,IAAI,EAAE,MADK;UAEX0D,KAAK,EAAE,KAAKoC;QAFD,CAAD,CATsB;QAalCC,GAAG,EAAE;MAb6B,CAApC,CAFK,EAiBL,KAAKC,cAAL,EAjBK,CAAP;IAmBD;EAvEM,CAlI6D;EA4MtEC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IAAA,IAAAC,MAAA;IACP,OAAOD,CAAC,CAAC,KAAKvF,GAAN,EAAW;MACjB2E,WAAW,EAAE,WADI;MAEjB,SAAO,KAAKzC;IAFK,CAAX,EAGL,CACD,KAAKuD,eAAL,CAAqB;MAAA,OAAM,CAACD,MAAA,CAAKlB,aAAL,EAAD,CAA3B;IAAA,EADC,EAED,KAAKoB,YAAL,EAFC,CAHK,CAAR;EAOD;AApNqE,CAAzD,CAAf", "ignoreList": []}]}