{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/index.js", "mtime": 1757335236820}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZDb2xvclBpY2tlciBmcm9tICcuL1ZDb2xvclBpY2tlcic7CmltcG9ydCBWQ29sb3JQaWNrZXJTd2F0Y2hlcyBmcm9tICcuL1ZDb2xvclBpY2tlclN3YXRjaGVzJzsKaW1wb3J0IFZDb2xvclBpY2tlckNhbnZhcyBmcm9tICcuL1ZDb2xvclBpY2tlckNhbnZhcyc7CmV4cG9ydCB7IFZDb2xvclBpY2tlciwgVkNvbG9yUGlja2VyU3dhdGNoZXMsIFZDb2xvclBpY2tlckNhbnZhcyB9OwpleHBvcnQgZGVmYXVsdCBWQ29sb3JQaWNrZXI7"}, {"version": 3, "names": ["VColorPicker", "VColorPickerSwatches", "VColorPickerCanvas"], "sources": ["../../../src/components/VColorPicker/index.ts"], "sourcesContent": ["import VColorPicker from './VColorPicker'\nimport VColorPickerSwatches from './VColorPickerSwatches'\nimport VColorPickerCanvas from './VColorPickerCanvas'\n\nexport { VColorPicker, VColorPickerSwatches, VColorPickerCanvas }\nexport default VColorPicker\n"], "mappings": "AAAA,OAAOA,YAAP,MAAyB,gBAAzB;AACA,OAAOC,oBAAP,MAAiC,wBAAjC;AACA,OAAOC,kBAAP,MAA+B,sBAA/B;AAEA,SAASF,YAAT,EAAuBC,oBAAvB,EAA6CC,kBAA7C;AACA,eAAeF,YAAf", "ignoreList": []}]}