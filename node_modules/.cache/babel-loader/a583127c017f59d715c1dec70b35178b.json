{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSubheader/VSubheader.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSubheader/VSubheader.js", "mtime": 1757335239122}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKLy8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL2NvbXBvbmVudHMvVlN1YmhlYWRlci9WU3ViaGVhZGVyLnNhc3MiOyAvLyBNaXhpbnMKCmltcG9ydCBUaGVtZWFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL3RoZW1lYWJsZSc7CmltcG9ydCBtaXhpbnMgZnJvbSAnLi4vLi4vdXRpbC9taXhpbnMnOwpleHBvcnQgZGVmYXVsdCBtaXhpbnMoVGhlbWVhYmxlCi8qIEB2dWUvY29tcG9uZW50ICovKS5leHRlbmQoewogIG5hbWU6ICd2LXN1YmhlYWRlcicsCiAgcHJvcHM6IHsKICAgIGluc2V0OiBCb29sZWFuCiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICByZXR1cm4gaCgnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogJ3Ytc3ViaGVhZGVyJywKICAgICAgImNsYXNzIjogX29iamVjdFNwcmVhZCh7CiAgICAgICAgJ3Ytc3ViaGVhZGVyLS1pbnNldCc6IHRoaXMuaW5zZXQKICAgICAgfSwgdGhpcy50aGVtZUNsYXNzZXMpLAogICAgICBhdHRyczogdGhpcy4kYXR0cnMsCiAgICAgIG9uOiB0aGlzLiRsaXN0ZW5lcnMKICAgIH0sIHRoaXMuJHNsb3RzWyJkZWZhdWx0Il0pOwogIH0KfSk7"}, {"version": 3, "names": ["Themeable", "mixins", "extend", "name", "props", "inset", "Boolean", "render", "h", "staticClass", "_objectSpread", "themeClasses", "attrs", "$attrs", "on", "$listeners", "$slots"], "sources": ["../../../src/components/VSubheader/VSubheader.ts"], "sourcesContent": ["// Styles\nimport './VSubheader.sass'\n\n// Mixins\nimport Themeable from '../../mixins/themeable'\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\n\nexport default mixins(\n  Themeable\n  /* @vue/component */\n).extend({\n  name: 'v-subheader',\n\n  props: {\n    inset: <PERSON>olean,\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-subheader',\n      class: {\n        'v-subheader--inset': this.inset,\n        ...this.themeClasses,\n      },\n      attrs: this.$attrs,\n      on: this.$listeners,\n    }, this.$slots.default)\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAO,oDAAP,C,CAEA;;AACA,OAAOA,SAAP,MAAsB,wBAAtB;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAKA,eAAeA,MAAM,CACnBD;AACA,oBAFmB,CAAN,CAGbE,MAHa,CAGN;EACPC,IAAI,EAAE,aADC;EAGPC,KAAK,EAAE;IACLC,KAAK,EAAEC;EADF,CAHA;EAOPC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ;MACdC,WAAW,EAAE,aADC;MAEd,SAAAC,aAAA;QACE,sBAAsB,KAAKL;MADtB,GAEF,KAAKM,YAAA,CAJI;MAMdC,KAAK,EAAE,KAAKC,MANE;MAOdC,EAAE,EAAE,KAAKC;IAPK,CAAR,EAQL,KAAKC,MAAL,WARK,CAAR;EASD;AAjBM,CAHM,CAAf", "ignoreList": []}]}