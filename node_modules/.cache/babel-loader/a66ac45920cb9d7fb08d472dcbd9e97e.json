{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSheet/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSheet/index.js", "mtime": 1757335236942}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZTaGVldCBmcm9tICcuL1ZTaGVldCc7CmV4cG9ydCB7IFZTaGVldCB9OwpleHBvcnQgZGVmYXVsdCBWU2hlZXQ7"}, {"version": 3, "names": ["VSheet"], "sources": ["../../../src/components/VSheet/index.ts"], "sourcesContent": ["import VSheet from './VSheet'\n\nexport { VSheet }\nexport default VSheet\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,UAAnB;AAEA,SAASA,MAAT;AACA,eAAeA,MAAf", "ignoreList": []}]}