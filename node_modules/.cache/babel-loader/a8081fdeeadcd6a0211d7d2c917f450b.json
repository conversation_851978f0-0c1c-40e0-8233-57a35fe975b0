{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/en.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/en.js", "mtime": 1757335235508}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/en.ts"], "sourcesContent": ["export default {\n  badge: 'Badge',\n  close: 'Close',\n  dataIterator: {\n    noResultsText: 'No matching records found',\n    loadingText: 'Loading items...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Rows per page:',\n    ariaLabel: {\n      sortDescending: 'Sorted descending.',\n      sortAscending: 'Sorted ascending.',\n      sortNone: 'Not sorted.',\n      activateNone: 'Activate to remove sorting.',\n      activateDescending: 'Activate to sort descending.',\n      activateAscending: 'Activate to sort ascending.',\n    },\n    sortBy: 'Sort by',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Items per page:',\n    itemsPerPageAll: 'All',\n    nextPage: 'Next page',\n    prevPage: 'Previous page',\n    firstPage: 'First page',\n    lastPage: 'Last page',\n    pageText: '{0}-{1} of {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} selected',\n    nextMonthAriaLabel: 'Next month',\n    nextYearAriaLabel: 'Next year',\n    prevMonthAriaLabel: 'Previous month',\n    prevYearAriaLabel: 'Previous year',\n  },\n  noDataText: 'No data available',\n  carousel: {\n    prev: 'Previous visual',\n    next: 'Next visual',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} more',\n  },\n  fileInput: {\n    counter: '{0} files',\n    counterSize: '{0} files ({1} in total)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Pagination Navigation',\n      next: 'Next page',\n      previous: 'Previous page',\n      page: 'Goto Page {0}',\n      currentPage: 'Current Page, Page {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,OADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,2BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,gBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,mBAFN;MAGTC,QAAQ,EAAE,aAHD;MAITC,YAAY,EAAE,6BAJL;MAKTC,kBAAkB,EAAE,8BALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,iBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,WAHA;IAIVC,QAAQ,EAAE,eAJA;IAKVC,SAAS,EAAE,YALD;IAMVC,QAAQ,EAAE,WANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,cADL;IAEVC,kBAAkB,EAAE,YAFV;IAGVC,iBAAiB,EAAE,WAHT;IAIVC,kBAAkB,EAAE,gBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,mBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,aAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,WAFG;MAGTY,QAAQ,EAAE,eAHD;MAITC,IAAI,EAAE,eAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}