{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/theme/utils.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/theme/utils.js", "mtime": 1757335237592}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["keys", "colorToInt", "intToHex", "colorToHex", "sRGB", "LAB", "parse", "theme", "isItem", "arguments", "length", "undefined", "variations", "anchor", "variant", "_objectWithoutProperties", "_excluded", "colors", "_Object$keys", "parsedTheme", "i", "name", "value", "base", "_startsWithInstanceProperty", "call", "_typeof", "genVariations", "primary", "genBaseColor", "_context", "_context2", "_context3", "_context4", "_context5", "_concatInstanceProperty", "concat", "genVariantColor", "_context6", "_context7", "_context8", "_context9", "_context0", "_context1", "_context10", "_context11", "_context12", "_variant$split", "split", "_variant$split2", "_slicedToArray", "type", "n", "genColorVariableName", "_context13", "genColorVariable", "genStyles", "_context14", "cssVar", "_excluded2", "variablesCss", "css", "aColor", "_context15", "variants", "_context16", "variantValue", "values", "lighten", "darken", "amount", "lab", "fromXYZ", "toXYZ"], "sources": ["../../../src/services/theme/utils.ts"], "sourcesContent": ["import { keys } from '../../util/helpers'\nimport { colorToInt, intToHex, colorToHex, ColorInt } from '../../util/colorUtils'\nimport * as sRGB from '../../util/color/transformSRGB'\nimport * as LAB from '../../util/color/transformCIELAB'\nimport {\n  VuetifyParsedTheme,\n  VuetifyThemeItem,\n} from 'vuetify/types/services/theme'\n\nexport function parse (\n  theme: Record<string, VuetifyThemeItem>,\n  isItem = false,\n  variations = true,\n): VuetifyParsedTheme {\n  const { anchor, ...variant } = theme\n  const colors = Object.keys(variant)\n  const parsedTheme: any = {}\n\n  for (let i = 0; i < colors.length; ++i) {\n    const name = colors[i]\n    const value = theme[name]\n\n    if (value == null) continue\n\n    if (!variations) {\n      parsedTheme[name] = { base: intToHex(colorToInt(value)) }\n    } else if (isItem) {\n      /* istanbul ignore else */\n      if (name === 'base' || name.startsWith('lighten') || name.startsWith('darken')) {\n        parsedTheme[name] = colorToHex(value)\n      }\n    } else if (typeof value === 'object') {\n      parsedTheme[name] = parse(value, true, variations)\n    } else {\n      parsedTheme[name] = genVariations(name, colorToInt(value))\n    }\n  }\n\n  if (!isItem) {\n    parsedTheme.anchor = anchor || parsedTheme.base || parsedTheme.primary.base\n  }\n\n  return parsedTheme\n}\n\n/**\n * Generate the CSS for a base color (.primary)\n */\nconst genBaseColor = (name: string, value: string): string => {\n  return `\n.v-application .${name} {\n  background-color: ${value} !important;\n  border-color: ${value} !important;\n}\n.v-application .${name}--text {\n  color: ${value} !important;\n  caret-color: ${value} !important;\n}`\n}\n\n/**\n * Generate the CSS for a variant color (.primary.darken-2)\n */\nconst genVariantColor = (name: string, variant: string, value: string): string => {\n  const [type, n] = variant.split(/(\\d)/, 2)\n  return `\n.v-application .${name}.${type}-${n} {\n  background-color: ${value} !important;\n  border-color: ${value} !important;\n}\n.v-application .${name}--text.text--${type}-${n} {\n  color: ${value} !important;\n  caret-color: ${value} !important;\n}`\n}\n\nconst genColorVariableName = (name: string, variant = 'base'): string => `--v-${name}-${variant}`\n\nconst genColorVariable = (name: string, variant = 'base'): string => `var(${genColorVariableName(name, variant)})`\n\nexport function genStyles (theme: VuetifyParsedTheme, cssVar = false): string {\n  const { anchor, ...variant } = theme\n  const colors = Object.keys(variant)\n\n  if (!colors.length) return ''\n\n  let variablesCss = ''\n  let css = ''\n\n  const aColor = cssVar ? genColorVariable('anchor') : anchor\n  css += `.v-application a { color: ${aColor}; }`\n  cssVar && (variablesCss += `  ${genColorVariableName('anchor')}: ${anchor};\\n`)\n\n  for (let i = 0; i < colors.length; ++i) {\n    const name = colors[i]\n    const value = theme[name]\n\n    css += genBaseColor(name, cssVar ? genColorVariable(name) : value.base)\n    cssVar && (variablesCss += `  ${genColorVariableName(name)}: ${value.base};\\n`)\n\n    const variants = keys(value)\n    for (let i = 0; i < variants.length; ++i) {\n      const variant = variants[i]\n      const variantValue = value[variant]\n      if (variant === 'base') continue\n\n      css += genVariantColor(name, variant, cssVar ? genColorVariable(name, variant) : variantValue)\n      cssVar && (variablesCss += `  ${genColorVariableName(name, variant)}: ${variantValue};\\n`)\n    }\n  }\n\n  if (cssVar) {\n    variablesCss = `:root {\\n${variablesCss}}\\n\\n`\n  }\n\n  return variablesCss + css\n}\n\nexport function genVariations (name: string, value: ColorInt): Record<string, string> {\n  const values: Record<string, string> = {\n    base: intToHex(value),\n  }\n\n  for (let i = 5; i > 0; --i) {\n    values[`lighten${i}`] = intToHex(lighten(value, i))\n  }\n\n  for (let i = 1; i <= 4; ++i) {\n    values[`darken${i}`] = intToHex(darken(value, i))\n  }\n\n  return values\n}\n\nexport function lighten (value: ColorInt, amount: number): ColorInt {\n  const lab = LAB.fromXYZ(sRGB.toXYZ(value))\n  lab[0] = lab[0] + amount * 10\n  return sRGB.fromXYZ(LAB.toXYZ(lab))\n}\n\nexport function darken (value: ColorInt, amount: number): ColorInt {\n  const lab = LAB.fromXYZ(sRGB.toXYZ(value))\n  lab[0] = lab[0] - amount * 10\n  return sRGB.fromXYZ(LAB.toXYZ(lab))\n}\n"], "mappings": ";;;;;;;;;;;AAAA,SAASA,IAAT,QAAqB,oBAArB;AACA,SAASC,UAAT,EAAqBC,QAArB,EAA+BC,UAA/B,QAA2D,uBAA3D;AACA,OAAO,KAAKC,IAAZ,MAAsB,gCAAtB;AACA,OAAO,KAAKC,GAAZ,MAAqB,kCAArB;AAMA,OAAM,SAAUC,KAAVA,CACJC,KADI,EAGa;EAAA,IADjBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAFL;EAAA,IAGJG,UAAU,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAHT;EAKJ,IAAQI,MAAF,GAAyBN,KAA/B,CAAQM,MAAF;IAAaC,OAAA,GAAAC,wBAAA,CAAYR,KAA/B,EAAAS,SAAA;EACA,IAAMC,MAAM,GAAGC,YAAA,CAAYJ,OAAZ,CAAf;EACA,IAAMK,WAAW,GAAQ,EAAzB;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,MAAM,CAACP,MAA3B,EAAmC,EAAEU,CAArC,EAAwC;IACtC,IAAMC,IAAI,GAAGJ,MAAM,CAACG,CAAD,CAAnB;IACA,IAAME,KAAK,GAAGf,KAAK,CAACc,IAAD,CAAnB;IAEA,IAAIC,KAAK,IAAI,IAAb,EAAmB;IAEnB,IAAI,CAACV,UAAL,EAAiB;MACfO,WAAW,CAACE,IAAD,CAAX,GAAoB;QAAEE,IAAI,EAAErB,QAAQ,CAACD,UAAU,CAACqB,KAAD,CAAX;MAAhB,CAApB;IACD,CAFD,MAEO,IAAId,MAAJ,EAAY;MACjB;MACA,IAAIa,IAAI,KAAK,MAAT,IAAmBG,2BAAA,CAAAH,IAAI,EAAAI,IAAA,CAAJJ,IAAI,EAAY,SAAhB,CAAnB,IAAiDG,2BAAA,CAAAH,IAAI,EAAAI,IAAA,CAAJJ,IAAI,EAAY,QAAhB,CAArD,EAAgF;QAC9EF,WAAW,CAACE,IAAD,CAAX,GAAoBlB,UAAU,CAACmB,KAAD,CAA9B;MACD;IACF,CALM,MAKA,IAAII,OAAA,CAAOJ,KAAP,MAAiB,QAArB,EAA+B;MACpCH,WAAW,CAACE,IAAD,CAAX,GAAoBf,KAAK,CAACgB,KAAD,EAAQ,IAAR,EAAcV,UAAd,CAAzB;IACD,CAFM,MAEA;MACLO,WAAW,CAACE,IAAD,CAAX,GAAoBM,aAAa,CAACN,IAAD,EAAOpB,UAAU,CAACqB,KAAD,CAAjB,CAAjC;IACD;EACF;EAED,IAAI,CAACd,MAAL,EAAa;IACXW,WAAW,CAACN,MAAZ,GAAqBA,MAAM,IAAIM,WAAW,CAACI,IAAtB,IAA8BJ,WAAW,CAACS,OAAZ,CAAoBL,IAAvE;EACD;EAED,OAAOJ,WAAP;AACD;AAED;;AAEG;;AACH,IAAMU,YAAY,GAAG,SAAfA,YAAYA,CAAIR,IAAD,EAAeC,KAAf,EAAwC;EAAA,IAAAQ,QAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA;EAC3D,OAAAC,uBAAA,CAAAL,QAAA,GAAAK,uBAAA,CAAAJ,SAAA,GAAAI,uBAAA,CAAAH,SAAA,GAAAG,uBAAA,CAAAF,SAAA,GAAAE,uBAAA,CAAAD,SAAA,wBAAAE,MAAA,CACgBf,IAAI,+BAAAI,IAAA,CAAAS,SAAA,EACAZ,KAAK,qCAAAG,IAAA,CAAAQ,SAAA,EACTX,KAAK,wCAAAG,IAAA,CAAAO,SAAA,EAELX,IAAI,0BAAAI,IAAA,CAAAM,SAAA,EACXT,KAAK,oCAAAG,IAAA,CAAAK,QAAA,EACCR,KAAK;AAErB,CAVD;AAYA;;AAEG;;AACH,IAAMe,eAAe,GAAG,SAAlBA,eAAeA,CAAIhB,IAAD,EAAeP,OAAf,EAAgCQ,KAAhC,EAAyD;EAAA,IAAAgB,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,UAAA;EAC/E,IAAAC,cAAA,GAAkBjC,OAAO,CAACkC,KAAR,CAAc,MAAd,EAAsB,CAAtB,CAAlB;IAAAC,eAAA,GAAAC,cAAA,CAAAH,cAAA;IAAOI,IAAD,GAAAF,eAAA;IAAOG,CAAP,GAAAH,eAAA;EACN,OAAAd,uBAAA,CAAAG,SAAA,GAAAH,uBAAA,CAAAI,SAAA,GAAAJ,uBAAA,CAAAK,SAAA,GAAAL,uBAAA,CAAAM,SAAA,GAAAN,uBAAA,CAAAO,SAAA,GAAAP,uBAAA,CAAAQ,SAAA,GAAAR,uBAAA,CAAAS,UAAA,GAAAT,uBAAA,CAAAU,UAAA,GAAAV,uBAAA,CAAAW,UAAA,wBAAAV,MAAA,CACgBf,IAAI,QAAAI,IAAA,CAAAqB,UAAA,EAAIK,IAAI,QAAA1B,IAAA,CAAAoB,UAAA,EAAIO,CAAC,+BAAA3B,IAAA,CAAAmB,UAAA,EACbtB,KAAK,qCAAAG,IAAA,CAAAkB,SAAA,EACTrB,KAAK,wCAAAG,IAAA,CAAAiB,SAAA,EAELrB,IAAI,oBAAAI,IAAA,CAAAgB,SAAA,EAAgBU,IAAI,QAAA1B,IAAA,CAAAe,SAAA,EAAIY,CAAC,oBAAA3B,IAAA,CAAAc,SAAA,EACpCjB,KAAK,oCAAAG,IAAA,CAAAa,SAAA,EACChB,KAAK;AAErB,CAXD;AAaA,IAAM+B,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIhC,IAAD;EAAA,IAAAiC,UAAA;EAAA,IAAexC,OAAO,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAzB;EAAA,OAAA0B,uBAAA,CAAAmB,UAAA,UAAAlB,MAAA,CAAmDf,IAAI,QAAAI,IAAA,CAAA6B,UAAA,EAAIxC,OAAO;AAAA,CAA/F;AAEA,IAAMyC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIlC,IAAD;EAAA,IAAeP,OAAO,GAAAL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,MAAzB;EAAA,cAAA2B,MAAA,CAAmDiB,oBAAoB,CAAChC,IAAD,EAAOP,OAAP,CAAe;AAAA,CAA/G;AAEA,OAAM,SAAU0C,SAAVA,CAAqBjD,KAArB,EAA8D;EAAA,IAAAkD,UAAA;EAAA,IAAdC,MAAM,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAzD;EACJ,IAAQI,MAAF,GAAyBN,KAA/B,CAAQM,MAAF;IAAaC,OAAA,GAAAC,wBAAA,CAAYR,KAA/B,EAAAoD,UAAA;EACA,IAAM1C,MAAM,GAAGC,YAAA,CAAYJ,OAAZ,CAAf;EAEA,IAAI,CAACG,MAAM,CAACP,MAAZ,EAAoB,OAAO,EAAP;EAEpB,IAAIkD,YAAY,GAAG,EAAnB;EACA,IAAIC,GAAG,GAAG,EAAV;EAEA,IAAMC,MAAM,GAAGJ,MAAM,GAAGH,gBAAgB,CAAC,QAAD,CAAnB,GAAgC1C,MAArD;EACAgD,GAAG,iCAAAzB,MAAA,CAAiC0B,MAAM,QAA1C;EACAJ,MAAM,KAAKE,YAAY,IAAAzB,uBAAA,CAAAsB,UAAA,QAAArB,MAAA,CAASiB,oBAAoB,CAAC,QAAD,CAAU,SAAA5B,IAAA,CAAAgC,UAAA,EAAK5C,MAAM,QAAnE,CAAN;EAEA,KAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,MAAM,CAACP,MAA3B,EAAmC,EAAEU,CAArC,EAAwC;IAAA,IAAA2C,UAAA;IACtC,IAAM1C,IAAI,GAAGJ,MAAM,CAACG,CAAD,CAAnB;IACA,IAAME,KAAK,GAAGf,KAAK,CAACc,IAAD,CAAnB;IAEAwC,GAAG,IAAIhC,YAAY,CAACR,IAAD,EAAOqC,MAAM,GAAGH,gBAAgB,CAAClC,IAAD,CAAnB,GAA4BC,KAAK,CAACC,IAA/C,CAAnB;IACAmC,MAAM,KAAKE,YAAY,IAAAzB,uBAAA,CAAA4B,UAAA,QAAA3B,MAAA,CAASiB,oBAAoB,CAAChC,IAAD,CAAM,SAAAI,IAAA,CAAAsC,UAAA,EAAKzC,KAAK,CAACC,IAAI,QAAnE,CAAN;IAEA,IAAMyC,QAAQ,GAAGhE,IAAI,CAACsB,KAAD,CAArB;IACA,KAAK,IAAIF,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG4C,QAAQ,CAACtD,MAA7B,EAAqC,EAAEU,EAAvC,EAA0C;MAAA,IAAA6C,UAAA;MACxC,IAAMnD,QAAO,GAAGkD,QAAQ,CAAC5C,EAAD,CAAxB;MACA,IAAM8C,YAAY,GAAG5C,KAAK,CAACR,QAAD,CAA1B;MACA,IAAIA,QAAO,KAAK,MAAhB,EAAwB;MAExB+C,GAAG,IAAIxB,eAAe,CAAChB,IAAD,EAAOP,QAAP,EAAgB4C,MAAM,GAAGH,gBAAgB,CAAClC,IAAD,EAAOP,QAAP,CAAnB,GAAqCoD,YAA3D,CAAtB;MACAR,MAAM,KAAKE,YAAY,IAAAzB,uBAAA,CAAA8B,UAAA,QAAA7B,MAAA,CAASiB,oBAAoB,CAAChC,IAAD,EAAOP,QAAP,CAAe,SAAAW,IAAA,CAAAwC,UAAA,EAAKC,YAAY,QAA9E,CAAN;IACD;EACF;EAED,IAAIR,MAAJ,EAAY;IACVE,YAAY,eAAAxB,MAAA,CAAewB,YAAY,UAAvC;EACD;EAED,OAAOA,YAAY,GAAGC,GAAtB;AACD;AAED,OAAM,SAAUlC,aAAVA,CAAyBN,IAAzB,EAAuCC,KAAvC,EAAsD;EAC1D,IAAM6C,MAAM,GAA2B;IACrC5C,IAAI,EAAErB,QAAQ,CAACoB,KAAD;EADuB,CAAvC;EAIA,KAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuB,EAAEA,CAAzB,EAA4B;IAC1B+C,MAAM,WAAA/B,MAAA,CAAWhB,CAAC,EAAlB,GAAwBlB,QAAQ,CAACkE,OAAO,CAAC9C,KAAD,EAAQF,CAAR,CAAR,CAAhC;EACD;EAED,KAAK,IAAIA,GAAC,GAAG,CAAb,EAAgBA,GAAC,IAAI,CAArB,EAAwB,EAAEA,GAA1B,EAA6B;IAC3B+C,MAAM,UAAA/B,MAAA,CAAUhB,GAAC,EAAjB,GAAuBlB,QAAQ,CAACmE,MAAM,CAAC/C,KAAD,EAAQF,GAAR,CAAP,CAA/B;EACD;EAED,OAAO+C,MAAP;AACD;AAED,OAAM,SAAUC,OAAVA,CAAmB9C,KAAnB,EAAoCgD,MAApC,EAAkD;EACtD,IAAMC,GAAG,GAAGlE,GAAG,CAACmE,OAAJ,CAAYpE,IAAI,CAACqE,KAAL,CAAWnD,KAAX,CAAZ,CAAZ;EACAiD,GAAG,CAAC,CAAD,CAAH,GAASA,GAAG,CAAC,CAAD,CAAH,GAASD,MAAM,GAAG,EAA3B;EACA,OAAOlE,IAAI,CAACoE,OAAL,CAAanE,GAAG,CAACoE,KAAJ,CAAUF,GAAV,CAAb,CAAP;AACD;AAED,OAAM,SAAUF,MAAVA,CAAkB/C,KAAlB,EAAmCgD,MAAnC,EAAiD;EACrD,IAAMC,GAAG,GAAGlE,GAAG,CAACmE,OAAJ,CAAYpE,IAAI,CAACqE,KAAL,CAAWnD,KAAX,CAAZ,CAAZ;EACAiD,GAAG,CAAC,CAAD,CAAH,GAASA,GAAG,CAAC,CAAD,CAAH,GAASD,MAAM,GAAG,EAA3B;EACA,OAAOlE,IAAI,CAACoE,OAAL,CAAanE,GAAG,CAACoE,KAAJ,CAAUF,GAAV,CAAb,CAAP;AACD", "ignoreList": []}]}