{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/positionable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/positionable/index.js", "mtime": 1757335237194}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgeyBmaWx0ZXJPYmplY3RPbktleXMgfSBmcm9tICcuLi8uLi91dGlsL2hlbHBlcnMnOwp2YXIgYXZhaWxhYmxlUHJvcHMgPSB7CiAgYWJzb2x1dGU6IEJvb2xlYW4sCiAgYm90dG9tOiBCb29sZWFuLAogIGZpeGVkOiBCb29sZWFuLAogIGxlZnQ6IEJvb2xlYW4sCiAgcmlnaHQ6IEJvb2xlYW4sCiAgdG9wOiBCb29sZWFuCn07CmV4cG9ydCBmdW5jdGlvbiBmYWN0b3J5KCkgewogIHZhciBzZWxlY3RlZCA9IGFyZ3VtZW50cy5sZW5ndGggPiAwICYmIGFyZ3VtZW50c1swXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzBdIDogW107CiAgcmV0dXJuIFZ1ZS5leHRlbmQoewogICAgbmFtZTogJ3Bvc2l0aW9uYWJsZScsCiAgICBwcm9wczogc2VsZWN0ZWQubGVuZ3RoID8gZmlsdGVyT2JqZWN0T25LZXlzKGF2YWlsYWJsZVByb3BzLCBzZWxlY3RlZCkgOiBhdmFpbGFibGVQcm9wcwogIH0pOwp9CmV4cG9ydCBkZWZhdWx0IGZhY3RvcnkoKTsgLy8gQWRkIGEgYCpgIGJlZm9yZSB0aGUgc2Vjb25kIGAvYAoKLyogVGVzdHMgLwpsZXQgc2luZ2xlID0gZmFjdG9yeShbJ3RvcCddKS5leHRlbmQoewogIGNyZWF0ZWQgKCkgewogICAgdGhpcy50b3AKICAgIHRoaXMuYm90dG9tCiAgICB0aGlzLmFic29sdXRlCiAgfQp9KQoKbGV0IHNvbWUgPSBmYWN0b3J5KFsndG9wJywgJ2JvdHRvbSddKS5leHRlbmQoewogIGNyZWF0ZWQgKCkgewogICAgdGhpcy50b3AKICAgIHRoaXMuYm90dG9tCiAgICB0aGlzLmFic29sdXRlCiAgfQp9KQoKbGV0IGFsbCA9IGZhY3RvcnkoKS5leHRlbmQoewogIGNyZWF0ZWQgKCkgewogICAgdGhpcy50b3AKICAgIHRoaXMuYm90dG9tCiAgICB0aGlzLmFic29sdXRlCiAgICB0aGlzLmZvb2JhcgogIH0KfSkKLyoqLw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "filterObjectOnKeys", "availableProps", "absolute", "Boolean", "bottom", "fixed", "left", "right", "top", "factory", "selected", "arguments", "length", "undefined", "extend", "name", "props"], "sources": ["../../../src/mixins/positionable/index.ts"], "sourcesContent": ["import Vue from 'vue'\nimport { filterObjectOnKeys } from '../../util/helpers'\nimport { OptionsVue, VueConstructor } from 'vue/types/vue'\n\nconst availableProps = {\n  absolute: Boolean,\n  bottom: Boolean,\n  fixed: Boolean,\n  left: <PERSON><PERSON>an,\n  right: <PERSON><PERSON><PERSON>,\n  top: Boolean,\n}\ntype props = Record<keyof typeof availableProps, boolean>\n\nexport type Positionable<S extends keyof props> = VueConstructor<Vue & { [P in S]: boolean }, { [P in S]: BooleanConstructor }>\n\nexport function factory <S extends keyof props> (selected?: S[]): Positionable<S>\nexport function factory (selected: undefined): OptionsVue<Vue, {}, {}, {}, props, typeof availableProps>\nexport function factory (selected: any[] = []): any {\n  return Vue.extend({\n    name: 'positionable',\n    props: selected.length ? filterObjectOnKeys(availableProps, selected) : availableProps,\n  })\n}\n\nexport default factory()\n\n// Add a `*` before the second `/`\n/* Tests /\nlet single = factory(['top']).extend({\n  created () {\n    this.top\n    this.bottom\n    this.absolute\n  }\n})\n\nlet some = factory(['top', 'bottom']).extend({\n  created () {\n    this.top\n    this.bottom\n    this.absolute\n  }\n})\n\nlet all = factory().extend({\n  created () {\n    this.top\n    this.bottom\n    this.absolute\n    this.foobar\n  }\n})\n/**/\n"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,SAASC,kBAAT,QAAmC,oBAAnC;AAGA,IAAMC,cAAc,GAAG;EACrBC,QAAQ,EAAEC,OADW;EAErBC,MAAM,EAAED,OAFa;EAGrBE,KAAK,EAAEF,OAHc;EAIrBG,IAAI,EAAEH,OAJe;EAKrBI,KAAK,EAAEJ,OALc;EAMrBK,GAAG,EAAEL;AANgB,CAAvB;AAcA,OAAM,SAAUM,OAAVA,CAAA,EAAuC;EAAA,IAApBC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkB,EAArC;EACJ,OAAOZ,GAAG,CAACe,MAAJ,CAAW;IAChBC,IAAI,EAAE,cADU;IAEhBC,KAAK,EAAEN,QAAQ,CAACE,MAAT,GAAkBZ,kBAAkB,CAACC,cAAD,EAAiBS,QAAjB,CAApC,GAAiET;EAFxD,CAAX,CAAP;AAID;AAED,eAAeQ,OAAO,EAAtB,C,CAEA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBI", "ignoreList": []}]}