{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMessages/VMessages.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMessages/VMessages.js", "mtime": 1757335238548}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9tYXBJbnN0YW5jZVByb3BlcnR5IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvaW5zdGFuY2UvbWFwIjsKLy8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL2NvbXBvbmVudHMvVk1lc3NhZ2VzL1ZNZXNzYWdlcy5zYXNzIjsgLy8gTWl4aW5zCgppbXBvcnQgQ29sb3JhYmxlIGZyb20gJy4uLy4uL21peGlucy9jb2xvcmFibGUnOwppbXBvcnQgVGhlbWVhYmxlIGZyb20gJy4uLy4uL21peGlucy90aGVtZWFibGUnOwppbXBvcnQgbWl4aW5zIGZyb20gJy4uLy4uL3V0aWwvbWl4aW5zJzsgLy8gVXRpbGl0aWVzCgppbXBvcnQgeyBnZXRTbG90IH0gZnJvbSAnLi4vLi4vdXRpbC9oZWxwZXJzJzsKLyogQHZ1ZS9jb21wb25lbnQgKi8KCmV4cG9ydCBkZWZhdWx0IG1peGlucyhDb2xvcmFibGUsIFRoZW1lYWJsZSkuZXh0ZW5kKHsKICBuYW1lOiAndi1tZXNzYWdlcycsCiAgcHJvcHM6IHsKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICAiZGVmYXVsdCI6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2VuQ2hpbGRyZW46IGZ1bmN0aW9uIGdlbkNoaWxkcmVuKCkgewogICAgICB2YXIgX2NvbnRleHQ7CiAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KCd0cmFuc2l0aW9uLWdyb3VwJywgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1tZXNzYWdlc19fd3JhcHBlcicsCiAgICAgICAgYXR0cnM6IHsKICAgICAgICAgIG5hbWU6ICdtZXNzYWdlLXRyYW5zaXRpb24nLAogICAgICAgICAgdGFnOiAnZGl2JwogICAgICAgIH0KICAgICAgfSwgX21hcEluc3RhbmNlUHJvcGVydHkoX2NvbnRleHQgPSB0aGlzLnZhbHVlKS5jYWxsKF9jb250ZXh0LCB0aGlzLmdlbk1lc3NhZ2UpKTsKICAgIH0sCiAgICBnZW5NZXNzYWdlOiBmdW5jdGlvbiBnZW5NZXNzYWdlKG1lc3NhZ2UsIGtleSkgewogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1tZXNzYWdlc19fbWVzc2FnZScsCiAgICAgICAga2V5OiBrZXkKICAgICAgfSwgZ2V0U2xvdCh0aGlzLCAnZGVmYXVsdCcsIHsKICAgICAgICBtZXNzYWdlOiBtZXNzYWdlLAogICAgICAgIGtleToga2V5CiAgICAgIH0pIHx8IFttZXNzYWdlXSk7CiAgICB9CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICByZXR1cm4gaCgnZGl2JywgdGhpcy5zZXRUZXh0Q29sb3IodGhpcy5jb2xvciwgewogICAgICBzdGF0aWNDbGFzczogJ3YtbWVzc2FnZXMnLAogICAgICAiY2xhc3MiOiB0aGlzLnRoZW1lQ2xhc3NlcwogICAgfSksIFt0aGlzLmdlbkNoaWxkcmVuKCldKTsKICB9Cn0pOw=="}, {"version": 3, "names": ["Colorable", "Themeable", "mixins", "getSlot", "extend", "name", "props", "value", "type", "Array", "default", "methods", "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "_context", "$createElement", "staticClass", "attrs", "tag", "_mapInstanceProperty", "call", "genMessage", "message", "key", "render", "h", "setTextColor", "color", "themeClasses"], "sources": ["../../../src/components/VMessages/VMessages.ts"], "sourcesContent": ["// Styles\nimport './VMessages.sass'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport Themeable from '../../mixins/themeable'\n\n// Types\nimport { VNode } from 'vue'\nimport { PropValidator } from 'vue/types/options'\nimport mixins from '../../util/mixins'\n\n// Utilities\nimport { getSlot } from '../../util/helpers'\n\n/* @vue/component */\nexport default mixins(Colorable, Themeable).extend({\n  name: 'v-messages',\n\n  props: {\n    value: {\n      type: Array,\n      default: () => ([]),\n    } as PropValidator<string[]>,\n  },\n\n  methods: {\n    genChildren () {\n      return this.$createElement('transition-group', {\n        staticClass: 'v-messages__wrapper',\n        attrs: {\n          name: 'message-transition',\n          tag: 'div',\n        },\n      }, this.value.map(this.genMessage))\n    },\n    genMessage (message: string, key: number) {\n      return this.$createElement('div', {\n        staticClass: 'v-messages__message',\n        key,\n      }, getSlot(this, 'default', { message, key }) || [message])\n    },\n  },\n\n  render (h): VNode {\n    return h('div', this.setTextColor(this.color, {\n      staticClass: 'v-messages',\n      class: this.themeClasses,\n    }), [this.genChildren()])\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAO,kDAAP,C,CAEA;;AACA,OAAOA,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AAKA,OAAOC,MAAP,MAAmB,mBAAnB,C,CAEA;;AACA,SAASC,OAAT,QAAwB,oBAAxB;AAEA;;AACA,eAAeD,MAAM,CAACF,SAAD,EAAYC,SAAZ,CAAN,CAA6BG,MAA7B,CAAoC;EACjDC,IAAI,EAAE,YAD2C;EAGjDC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAEC,KADD;MAEL,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAS;MAAA;IAFX;EADF,CAH0C;EAUjDC,OAAO,EAAE;IACPC,WAAW,WAAXA,WAAWA,CAAA;MAAA,IAAAC,QAAA;MACT,OAAO,KAAKC,cAAL,CAAoB,kBAApB,EAAwC;QAC7CC,WAAW,EAAE,qBADgC;QAE7CC,KAAK,EAAE;UACLX,IAAI,EAAE,oBADD;UAELY,GAAG,EAAE;QAFA;MAFsC,CAAxC,EAMJC,oBAAA,CAAAL,QAAA,QAAKN,KAAL,EAAAY,IAAA,CAAAN,QAAA,EAAe,KAAKO,UAApB,CANI,CAAP;IAOD,CATM;IAUPA,UAAU,WAAVA,UAAUA,CAAEC,OAAF,EAAmBC,GAAnB,EAA8B;MACtC,OAAO,KAAKR,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,qBADmB;QAEhCO,GAAA,EAAAA;MAFgC,CAA3B,EAGJnB,OAAO,CAAC,IAAD,EAAO,SAAP,EAAkB;QAAEkB,OAAF,EAAEA,OAAF;QAAWC,GAAA,EAAAA;MAAX,CAAlB,CAAP,IAA8C,CAACD,OAAD,CAH1C,CAAP;IAID;EAfM,CAVwC;EA4BjDE,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ,KAAKC,YAAL,CAAkB,KAAKC,KAAvB,EAA8B;MAC5CX,WAAW,EAAE,YAD+B;MAE5C,SAAO,KAAKY;IAFgC,CAA9B,CAAR,EAGJ,CAAC,KAAKf,WAAL,EAAD,CAHI,CAAR;EAID;AAjCgD,CAApC,CAAf", "ignoreList": []}]}