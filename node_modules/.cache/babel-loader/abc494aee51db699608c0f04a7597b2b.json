{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/menuable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/menuable/index.js", "mtime": 1757335237143}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Stackable", "factory", "positionableFactory", "Activatable", "Detachable", "mixins", "convertToUnit", "baseMixins", "extend", "name", "props", "allowOverflow", "Boolean", "light", "dark", "max<PERSON><PERSON><PERSON>", "type", "Number", "String", "min<PERSON><PERSON><PERSON>", "nudgeBottom", "nudgeLeft", "nudgeRight", "nudgeTop", "nudgeWidth", "offsetOverflow", "positionX", "positionY", "zIndex", "data", "activatorNode", "absoluteX", "absoluteY", "activatedBy", "activatorFixed", "dimensions", "activator", "top", "left", "bottom", "right", "width", "height", "offsetTop", "scrollHeight", "offsetLeft", "content", "relativeYOffset", "hasJustFocused", "hasW<PERSON>ow", "inputActivator", "isContentActive", "pageWidth", "pageYOffset", "stackClass", "stackMinZIndex", "computed", "computedLeft", "a", "c", "activatorLeft", "attach", "Math", "max", "$vuetify", "rtl", "offsetX", "isNaN", "min", "_parseInt", "computedTop", "offsetY", "hasActivator", "$slots", "$scopedSlots", "absoluteYOffset", "watch", "disabled", "val", "callDeactivate", "isActive", "callActivate", "beforeMount", "window", "addEventListener", "updateDimensions", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "absolutePosition", "activate", "calcLeft", "menuWidth", "calcXOverflow", "calcTop", "calcYOverflow", "xOverflow", "getOffsetLeft", "documentHeight", "getInnerHeight", "toTop", "contentHeight", "totalHeight", "isOverflowing", "deactivate", "checkForPageYOffset", "getOffsetTop", "checkActivatorFixed", "el", "getActivator", "getComputedStyle", "position", "offsetParent", "genActivatorListeners", "_this", "listeners", "options", "call", "onClick", "click", "e", "openOnClick", "clientX", "clientY", "innerHeight", "document", "documentElement", "clientHeight", "pageXOffset", "scrollLeft", "scrollTop", "getRoundedBoundedClientRect", "rect", "getBoundingClientRect", "round", "measure", "style", "marginLeft", "marginTop", "sneakPeek", "cb", "_this2", "requestAnimationFrame", "$refs", "display", "startTransition", "_this3", "_Promise", "resolve", "_this4", "clientWidth", "_objectSpread", "absolute", "offsetRect"], "sources": ["../../../src/mixins/menuable/index.ts"], "sourcesContent": ["// Mixins\nimport Stackable from '../stackable'\nimport { factory as positionableFactory } from '../positionable'\nimport Activatable from '../activatable'\nimport Detachable from '../detachable'\n\n// Utilities\nimport mixins, { ExtractVue } from '../../util/mixins'\nimport { convertToUnit } from '../../util/helpers'\n\n// Types\nimport { VNode } from 'vue'\n\nconst baseMixins = mixins(\n  Stackable,\n  positionableFactory(['top', 'right', 'bottom', 'left', 'absolute']),\n  Activatable,\n  Detachable,\n)\n\ninterface dimensions {\n  top: number\n  left: number\n  bottom: number\n  right: number\n  width: number\n  height: number\n  offsetTop: number\n  scrollHeight: number\n  offsetLeft: number\n}\n\ninterface options extends ExtractVue<typeof baseMixins> {\n  attach: boolean | string | Element\n  offsetY: boolean\n  offsetX: boolean\n  dimensions: {\n    activator: dimensions\n    content: dimensions\n  }\n  $refs: {\n    content: HTMLElement\n    activator: HTMLElement\n  }\n}\n\n/* @vue/component */\nexport default baseMixins.extend<options>().extend({\n  name: 'menuable',\n\n  props: {\n    allowOverflow: Boolean,\n    light: Boolean,\n    dark: Boolean,\n    maxWidth: {\n      type: [Number, String],\n      default: 'auto',\n    },\n    minWidth: [Number, String],\n    nudgeBottom: {\n      type: [Number, String],\n      default: 0,\n    },\n    nudgeLeft: {\n      type: [Number, String],\n      default: 0,\n    },\n    nudgeRight: {\n      type: [Number, String],\n      default: 0,\n    },\n    nudgeTop: {\n      type: [Number, String],\n      default: 0,\n    },\n    nudgeWidth: {\n      type: [Number, String],\n      default: 0,\n    },\n    offsetOverflow: Boolean,\n    positionX: {\n      type: Number,\n      default: null,\n    },\n    positionY: {\n      type: Number,\n      default: null,\n    },\n    zIndex: {\n      type: [Number, String],\n      default: null,\n    },\n  },\n\n  data: () => ({\n    activatorNode: [] as VNode[],\n    absoluteX: 0,\n    absoluteY: 0,\n    activatedBy: null as EventTarget | null,\n    activatorFixed: false,\n    dimensions: {\n      activator: {\n        top: 0,\n        left: 0,\n        bottom: 0,\n        right: 0,\n        width: 0,\n        height: 0,\n        offsetTop: 0,\n        scrollHeight: 0,\n        offsetLeft: 0,\n      },\n      content: {\n        top: 0,\n        left: 0,\n        bottom: 0,\n        right: 0,\n        width: 0,\n        height: 0,\n        offsetTop: 0,\n        scrollHeight: 0,\n      },\n    },\n    relativeYOffset: 0,\n    hasJustFocused: false,\n    hasWindow: false,\n    inputActivator: false,\n    isContentActive: false,\n    pageWidth: 0,\n    pageYOffset: 0,\n    stackClass: 'v-menu__content--active',\n    stackMinZIndex: 6,\n  }),\n\n  computed: {\n    computedLeft () {\n      const a = this.dimensions.activator\n      const c = this.dimensions.content\n      const activatorLeft = (this.attach !== false ? a.offsetLeft : a.left) || 0\n      const minWidth = Math.max(a.width, c.width)\n      let left = 0\n      left += activatorLeft\n      if (this.left || (this.$vuetify.rtl && !this.right)) left -= (minWidth - a.width)\n      if (this.offsetX) {\n        const maxWidth = isNaN(Number(this.maxWidth))\n          ? a.width\n          : Math.min(a.width, Number(this.maxWidth))\n\n        left += this.left ? -maxWidth : a.width\n      }\n      if (this.nudgeLeft) left -= parseInt(this.nudgeLeft)\n      if (this.nudgeRight) left += parseInt(this.nudgeRight)\n\n      return left\n    },\n    computedTop () {\n      const a = this.dimensions.activator\n      const c = this.dimensions.content\n      let top = 0\n\n      if (this.top) top += a.height - c.height\n      if (this.attach !== false) top += a.offsetTop\n      else top += a.top + this.pageYOffset\n      if (this.offsetY) top += this.top ? -a.height : a.height\n      if (this.nudgeTop) top -= parseInt(this.nudgeTop)\n      if (this.nudgeBottom) top += parseInt(this.nudgeBottom)\n\n      return top\n    },\n    hasActivator (): boolean {\n      return !!this.$slots.activator || !!this.$scopedSlots.activator || !!this.activator || !!this.inputActivator\n    },\n    absoluteYOffset (): number {\n      return this.pageYOffset - this.relativeYOffset\n    },\n  },\n\n  watch: {\n    disabled (val) {\n      val && this.callDeactivate()\n    },\n    isActive (val) {\n      if (this.disabled) return\n\n      val ? this.callActivate() : this.callDeactivate()\n    },\n    positionX: 'updateDimensions',\n    positionY: 'updateDimensions',\n  },\n\n  beforeMount () {\n    this.hasWindow = typeof window !== 'undefined'\n\n    if (this.hasWindow) {\n      window.addEventListener('resize', this.updateDimensions, false)\n    }\n  },\n\n  beforeDestroy () {\n    if (this.hasWindow) {\n      window.removeEventListener('resize', this.updateDimensions, false)\n    }\n  },\n\n  methods: {\n    absolutePosition () {\n      return {\n        offsetTop: this.positionY || this.absoluteY,\n        offsetLeft: this.positionX || this.absoluteX,\n        scrollHeight: 0,\n        top: this.positionY || this.absoluteY,\n        bottom: this.positionY || this.absoluteY,\n        left: this.positionX || this.absoluteX,\n        right: this.positionX || this.absoluteX,\n        height: 0,\n        width: 0,\n      }\n    },\n    activate () {},\n    calcLeft (menuWidth: number) {\n      return convertToUnit(this.attach !== false\n        ? this.computedLeft\n        : this.calcXOverflow(this.computedLeft, menuWidth))\n    },\n    calcTop () {\n      return convertToUnit(this.attach !== false\n        ? this.computedTop\n        : this.calcYOverflow(this.computedTop))\n    },\n    calcXOverflow (left: number, menuWidth: number) {\n      const xOverflow = left + menuWidth - this.pageWidth + 12\n\n      if ((!this.left || this.right) && xOverflow > 0) {\n        left = Math.max(left - xOverflow, 0)\n      } else {\n        left = Math.max(left, 12)\n      }\n\n      return left + this.getOffsetLeft()\n    },\n    calcYOverflow (top: number) {\n      const documentHeight = this.getInnerHeight()\n      const toTop = this.absoluteYOffset + documentHeight\n      const activator = this.dimensions.activator\n      const contentHeight = this.dimensions.content.height\n      const totalHeight = top + contentHeight\n      const isOverflowing = toTop < totalHeight\n\n      // If overflowing bottom and offset\n      // TODO: set 'bottom' position instead of 'top'\n      if (isOverflowing &&\n        this.offsetOverflow &&\n        // If we don't have enough room to offset\n        // the overflow, don't offset\n        activator.top > contentHeight\n      ) {\n        top = this.pageYOffset + (activator.top - contentHeight)\n      // If overflowing bottom\n      } else if (isOverflowing && !this.allowOverflow) {\n        top = toTop - contentHeight - 12\n      // If overflowing top\n      } else if (top < this.absoluteYOffset && !this.allowOverflow) {\n        top = this.absoluteYOffset + 12\n      }\n\n      return top < 12 ? 12 : top\n    },\n    callActivate () {\n      if (!this.hasWindow) return\n\n      this.activate()\n    },\n    callDeactivate () {\n      this.isContentActive = false\n\n      this.deactivate()\n    },\n    checkForPageYOffset () {\n      if (this.hasWindow) {\n        this.pageYOffset = this.activatorFixed ? 0 : this.getOffsetTop()\n      }\n    },\n    checkActivatorFixed () {\n      if (this.attach !== false) {\n        this.activatorFixed = false\n        return\n      }\n      let el = this.getActivator()\n      while (el) {\n        if (window.getComputedStyle(el).position === 'fixed') {\n          this.activatorFixed = true\n          return\n        }\n        el = el.offsetParent as HTMLElement\n      }\n      this.activatorFixed = false\n    },\n    deactivate () {},\n    genActivatorListeners () {\n      const listeners = Activatable.options.methods.genActivatorListeners.call(this)\n\n      const onClick = listeners.click\n\n      if (onClick) {\n        listeners.click = (e: MouseEvent & KeyboardEvent & FocusEvent) => {\n          if (this.openOnClick) {\n            onClick && onClick(e)\n          }\n\n          this.absoluteX = e.clientX\n          this.absoluteY = e.clientY\n        }\n      }\n\n      return listeners\n    },\n    getInnerHeight () {\n      if (!this.hasWindow) return 0\n\n      return window.innerHeight ||\n        document.documentElement.clientHeight\n    },\n    getOffsetLeft () {\n      if (!this.hasWindow) return 0\n\n      return window.pageXOffset ||\n        document.documentElement.scrollLeft\n    },\n    getOffsetTop () {\n      if (!this.hasWindow) return 0\n\n      return window.pageYOffset ||\n        document.documentElement.scrollTop\n    },\n    getRoundedBoundedClientRect (el: Element) {\n      const rect = el.getBoundingClientRect()\n      return {\n        top: Math.round(rect.top),\n        left: Math.round(rect.left),\n        bottom: Math.round(rect.bottom),\n        right: Math.round(rect.right),\n        width: Math.round(rect.width),\n        height: Math.round(rect.height),\n      }\n    },\n    measure (el: HTMLElement) {\n      if (!el || !this.hasWindow) return null\n\n      const rect = this.getRoundedBoundedClientRect(el)\n\n      // Account for activator margin\n      if (this.attach !== false) {\n        const style = window.getComputedStyle(el)\n\n        rect.left = parseInt(style.marginLeft!)\n        rect.top = parseInt(style.marginTop!)\n      }\n\n      return rect\n    },\n    sneakPeek (cb: () => void) {\n      requestAnimationFrame(() => {\n        const el = this.$refs.content\n\n        if (!el || el.style.display !== 'none') {\n          cb()\n          return\n        }\n\n        el.style.display = 'inline-block'\n        cb()\n        el.style.display = 'none'\n      })\n    },\n    startTransition () {\n      return new Promise<void>(resolve => requestAnimationFrame(() => {\n        this.isContentActive = this.hasJustFocused = this.isActive\n        resolve()\n      }))\n    },\n    updateDimensions () {\n      this.hasWindow = typeof window !== 'undefined'\n      this.checkActivatorFixed()\n      this.checkForPageYOffset()\n      this.pageWidth = document.documentElement.clientWidth\n\n      const dimensions: any = {\n        activator: { ...this.dimensions.activator },\n        content: { ...this.dimensions.content },\n      }\n\n      // Activator should already be shown\n      if (!this.hasActivator || this.absolute) {\n        dimensions.activator = this.absolutePosition()\n      } else {\n        const activator = this.getActivator()\n        if (!activator) return\n\n        dimensions.activator = this.measure(activator)\n        dimensions.activator.offsetLeft = activator.offsetLeft\n        if (this.attach !== false) {\n          // account for css padding causing things to not line up\n          // this is mostly for v-autocomplete, hopefully it won't break anything\n          dimensions.activator.offsetTop = activator.offsetTop\n        } else {\n          dimensions.activator.offsetTop = 0\n        }\n      }\n\n      // Display and hide to get dimensions\n      this.sneakPeek(() => {\n        if (this.$refs.content) {\n          if (this.$refs.content.offsetParent) {\n            const offsetRect = this.getRoundedBoundedClientRect(this.$refs.content.offsetParent)\n\n            this.relativeYOffset = window.pageYOffset + offsetRect.top\n            dimensions.activator.top -= this.relativeYOffset\n            dimensions.activator.left -= window.pageXOffset + offsetRect.left\n          }\n\n          dimensions.content = this.measure(this.$refs.content)\n        }\n\n        this.dimensions = dimensions\n      })\n    },\n  },\n})\n"], "mappings": ";;;;AAAA;AACA,OAAOA,SAAP,MAAsB,cAAtB;AACA,SAASC,OAAO,IAAIC,mBAApB,QAA+C,iBAA/C;AACA,OAAOC,WAAP,MAAwB,gBAAxB;AACA,OAAOC,UAAP,MAAuB,eAAvB,C,CAEA;;AACA,OAAOC,MAAP,MAAmC,mBAAnC;AACA,SAASC,aAAT,QAA8B,oBAA9B;AAKA,IAAMC,UAAU,GAAGF,MAAM,CACvBL,SADuB,EAEvBE,mBAAmB,CAAC,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,EAAmC,UAAnC,CAAD,CAFI,EAGvBC,WAHuB,EAIvBC,UAJuB,CAAzB;AAiCA;;AACA,eAAeG,UAAU,CAACC,MAAX,GAA6BA,MAA7B,CAAoC;EACjDC,IAAI,EAAE,UAD2C;EAGjDC,KAAK,EAAE;IACLC,aAAa,EAAEC,OADV;IAELC,KAAK,EAAED,OAFF;IAGLE,IAAI,EAAEF,OAHD;IAILG,QAAQ,EAAE;MACRC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADE;MAER,WAAS;IAFD,CAJL;IAQLC,QAAQ,EAAE,CAACF,MAAD,EAASC,MAAT,CARL;IASLE,WAAW,EAAE;MACXJ,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADK;MAEX,WAAS;IAFE,CATR;IAaLG,SAAS,EAAE;MACTL,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADG;MAET,WAAS;IAFA,CAbN;IAiBLI,UAAU,EAAE;MACVN,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADI;MAEV,WAAS;IAFC,CAjBP;IAqBLK,QAAQ,EAAE;MACRP,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADE;MAER,WAAS;IAFD,CArBL;IAyBLM,UAAU,EAAE;MACVR,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADI;MAEV,WAAS;IAFC,CAzBP;IA6BLO,cAAc,EAAEb,OA7BX;IA8BLc,SAAS,EAAE;MACTV,IAAI,EAAEC,MADG;MAET,WAAS;IAFA,CA9BN;IAkCLU,SAAS,EAAE;MACTX,IAAI,EAAEC,MADG;MAET,WAAS;IAFA,CAlCN;IAsCLW,MAAM,EAAE;MACNZ,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADA;MAEN,WAAS;IAFH;EAtCH,CAH0C;EA+CjDW,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,aAAa,EAAE,EADJ;MAEXC,SAAS,EAAE,CAFA;MAGXC,SAAS,EAAE,CAHA;MAIXC,WAAW,EAAE,IAJF;MAKXC,cAAc,EAAE,KALL;MAMXC,UAAU,EAAE;QACVC,SAAS,EAAE;UACTC,GAAG,EAAE,CADI;UAETC,IAAI,EAAE,CAFG;UAGTC,MAAM,EAAE,CAHC;UAITC,KAAK,EAAE,CAJE;UAKTC,KAAK,EAAE,CALE;UAMTC,MAAM,EAAE,CANC;UAOTC,SAAS,EAAE,CAPF;UAQTC,YAAY,EAAE,CARL;UASTC,UAAU,EAAE;QATH,CADD;QAYVC,OAAO,EAAE;UACPT,GAAG,EAAE,CADE;UAEPC,IAAI,EAAE,CAFC;UAGPC,MAAM,EAAE,CAHD;UAIPC,KAAK,EAAE,CAJA;UAKPC,KAAK,EAAE,CALA;UAMPC,MAAM,EAAE,CAND;UAOPC,SAAS,EAAE,CAPJ;UAQPC,YAAY,EAAE;QARP;MAZC,CAND;MA6BXG,eAAe,EAAE,CA7BN;MA8BXC,cAAc,EAAE,KA9BL;MA+BXC,SAAS,EAAE,KA/BA;MAgCXC,cAAc,EAAE,KAhCL;MAiCXC,eAAe,EAAE,KAjCN;MAkCXC,SAAS,EAAE,CAlCA;MAmCXC,WAAW,EAAE,CAnCF;MAoCXC,UAAU,EAAE,yBApCD;MAqCXC,cAAc,EAAE;IArCL,CAAP;EAAA,CA/C2C;EAuFjDC,QAAQ,EAAE;IACRC,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAMC,CAAC,GAAG,KAAKvB,UAAL,CAAgBC,SAA1B;MACA,IAAMuB,CAAC,GAAG,KAAKxB,UAAL,CAAgBW,OAA1B;MACA,IAAMc,aAAa,GAAG,CAAC,KAAKC,MAAL,KAAgB,KAAhB,GAAwBH,CAAC,CAACb,UAA1B,GAAuCa,CAAC,CAACpB,IAA1C,KAAmD,CAAzE;MACA,IAAMnB,QAAQ,GAAG2C,IAAI,CAACC,GAAL,CAASL,CAAC,CAACjB,KAAX,EAAkBkB,CAAC,CAAClB,KAApB,CAAjB;MACA,IAAIH,IAAI,GAAG,CAAX;MACAA,IAAI,IAAIsB,aAAR;MACA,IAAI,KAAKtB,IAAL,IAAc,KAAK0B,QAAL,CAAcC,GAAd,IAAqB,CAAC,KAAKzB,KAA7C,EAAqDF,IAAI,IAAKnB,QAAQ,GAAGuC,CAAC,CAACjB,KAAtB;MACrD,IAAI,KAAKyB,OAAT,EAAkB;QAChB,IAAMnD,QAAQ,GAAGoD,KAAK,CAAClD,MAAM,CAAC,KAAKF,QAAN,CAAP,CAAL,GACb2C,CAAC,CAACjB,KADW,GAEbqB,IAAI,CAACM,GAAL,CAASV,CAAC,CAACjB,KAAX,EAAkBxB,MAAM,CAAC,KAAKF,QAAN,CAAxB,CAFJ;QAIAuB,IAAI,IAAI,KAAKA,IAAL,GAAY,CAACvB,QAAb,GAAwB2C,CAAC,CAACjB,KAAlC;MACD;MACD,IAAI,KAAKpB,SAAT,EAAoBiB,IAAI,IAAI+B,SAAA,CAAS,KAAKhD,SAAN,CAAhB;MACpB,IAAI,KAAKC,UAAT,EAAqBgB,IAAI,IAAI+B,SAAA,CAAS,KAAK/C,UAAN,CAAhB;MAErB,OAAOgB,IAAP;IACD,CApBO;IAqBRgC,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAMZ,CAAC,GAAG,KAAKvB,UAAL,CAAgBC,SAA1B;MACA,IAAMuB,CAAC,GAAG,KAAKxB,UAAL,CAAgBW,OAA1B;MACA,IAAIT,GAAG,GAAG,CAAV;MAEA,IAAI,KAAKA,GAAT,EAAcA,GAAG,IAAIqB,CAAC,CAAChB,MAAF,GAAWiB,CAAC,CAACjB,MAApB;MACd,IAAI,KAAKmB,MAAL,KAAgB,KAApB,EAA2BxB,GAAG,IAAIqB,CAAC,CAACf,SAAT,CAA3B,KACKN,GAAG,IAAIqB,CAAC,CAACrB,GAAF,GAAQ,KAAKgB,WAApB;MACL,IAAI,KAAKkB,OAAT,EAAkBlC,GAAG,IAAI,KAAKA,GAAL,GAAW,CAACqB,CAAC,CAAChB,MAAd,GAAuBgB,CAAC,CAAChB,MAAhC;MAClB,IAAI,KAAKnB,QAAT,EAAmBc,GAAG,IAAIgC,SAAA,CAAS,KAAK9C,QAAN,CAAf;MACnB,IAAI,KAAKH,WAAT,EAAsBiB,GAAG,IAAIgC,SAAA,CAAS,KAAKjD,WAAN,CAAf;MAEtB,OAAOiB,GAAP;IACD,CAlCO;IAmCRmC,YAAY,WAAZA,YAAYA,CAAA;MACV,OAAO,CAAC,CAAC,KAAKC,MAAL,CAAYrC,SAAd,IAA2B,CAAC,CAAC,KAAKsC,YAAL,CAAkBtC,SAA/C,IAA4D,CAAC,CAAC,KAAKA,SAAnE,IAAgF,CAAC,CAAC,KAAKc,cAA9F;IACD,CArCO;IAsCRyB,eAAe,WAAfA,eAAeA,CAAA;MACb,OAAO,KAAKtB,WAAL,GAAmB,KAAKN,eAA/B;IACD;EAxCO,CAvFuC;EAkIjD6B,KAAK,EAAE;IACLC,QAAQ,WAARA,QAAQA,CAAEC,GAAF,EAAK;MACXA,GAAG,IAAI,KAAKC,cAAL,EAAP;IACD,CAHI;IAILC,QAAQ,WAARA,QAAQA,CAAEF,GAAF,EAAK;MACX,IAAI,KAAKD,QAAT,EAAmB;MAEnBC,GAAG,GAAG,KAAKG,YAAL,EAAH,GAAyB,KAAKF,cAAL,EAA5B;IACD,CARI;IASLrD,SAAS,EAAE,kBATN;IAULC,SAAS,EAAE;EAVN,CAlI0C;EA+IjDuD,WAAW,WAAXA,WAAWA,CAAA;IACT,KAAKjC,SAAL,GAAiB,OAAOkC,MAAP,KAAkB,WAAnC;IAEA,IAAI,KAAKlC,SAAT,EAAoB;MAClBkC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKC,gBAAvC,EAAyD,KAAzD;IACD;EACF,CArJgD;EAuJjDC,aAAa,WAAbA,aAAaA,CAAA;IACX,IAAI,KAAKrC,SAAT,EAAoB;MAClBkC,MAAM,CAACI,mBAAP,CAA2B,QAA3B,EAAqC,KAAKF,gBAA1C,EAA4D,KAA5D;IACD;EACF,CA3JgD;EA6JjDG,OAAO,EAAE;IACPC,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,OAAO;QACL9C,SAAS,EAAE,KAAKhB,SAAL,IAAkB,KAAKK,SAD7B;QAELa,UAAU,EAAE,KAAKnB,SAAL,IAAkB,KAAKK,SAF9B;QAGLa,YAAY,EAAE,CAHT;QAILP,GAAG,EAAE,KAAKV,SAAL,IAAkB,KAAKK,SAJvB;QAKLO,MAAM,EAAE,KAAKZ,SAAL,IAAkB,KAAKK,SAL1B;QAMLM,IAAI,EAAE,KAAKZ,SAAL,IAAkB,KAAKK,SANxB;QAOLS,KAAK,EAAE,KAAKd,SAAL,IAAkB,KAAKK,SAPzB;QAQLW,MAAM,EAAE,CARH;QASLD,KAAK,EAAE;MATF,CAAP;IAWD,CAbM;IAcPiD,QAAQ,WAARA,QAAQA,CAAA,GAAM,CAdP;IAePC,QAAQ,WAARA,QAAQA,CAAEC,SAAF,EAAmB;MACzB,OAAOtF,aAAa,CAAC,KAAKuD,MAAL,KAAgB,KAAhB,GACjB,KAAKJ,YADY,GAEjB,KAAKoC,aAAL,CAAmB,KAAKpC,YAAxB,EAAsCmC,SAAtC,CAFgB,CAApB;IAGD,CAnBM;IAoBPE,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAOxF,aAAa,CAAC,KAAKuD,MAAL,KAAgB,KAAhB,GACjB,KAAKS,WADY,GAEjB,KAAKyB,aAAL,CAAmB,KAAKzB,WAAxB,CAFgB,CAApB;IAGD,CAxBM;IAyBPuB,aAAa,WAAbA,aAAaA,CAAEvD,IAAF,EAAgBsD,SAAhB,EAAiC;MAC5C,IAAMI,SAAS,GAAG1D,IAAI,GAAGsD,SAAP,GAAmB,KAAKxC,SAAxB,GAAoC,EAAtD;MAEA,IAAI,CAAC,CAAC,KAAKd,IAAN,IAAc,KAAKE,KAApB,KAA8BwD,SAAS,GAAG,CAA9C,EAAiD;QAC/C1D,IAAI,GAAGwB,IAAI,CAACC,GAAL,CAASzB,IAAI,GAAG0D,SAAhB,EAA2B,CAA3B,CAAP;MACD,CAFD,MAEO;QACL1D,IAAI,GAAGwB,IAAI,CAACC,GAAL,CAASzB,IAAT,EAAe,EAAf,CAAP;MACD;MAED,OAAOA,IAAI,GAAG,KAAK2D,aAAL,EAAd;IACD,CAnCM;IAoCPF,aAAa,WAAbA,aAAaA,CAAE1D,GAAF,EAAa;MACxB,IAAM6D,cAAc,GAAG,KAAKC,cAAL,EAAvB;MACA,IAAMC,KAAK,GAAG,KAAKzB,eAAL,GAAuBuB,cAArC;MACA,IAAM9D,SAAS,GAAG,KAAKD,UAAL,CAAgBC,SAAlC;MACA,IAAMiE,aAAa,GAAG,KAAKlE,UAAL,CAAgBW,OAAhB,CAAwBJ,MAA9C;MACA,IAAM4D,WAAW,GAAGjE,GAAG,GAAGgE,aAA1B;MACA,IAAME,aAAa,GAAGH,KAAK,GAAGE,WAA9B,CANwB,CAQxB;MACA;;MACA,IAAIC,aAAa,IACf,KAAK9E,cADH;MAEF;MACA;MACAW,SAAS,CAACC,GAAV,GAAgBgE,aAJlB,EAKE;QACAhE,GAAG,GAAG,KAAKgB,WAAL,IAAoBjB,SAAS,CAACC,GAAV,GAAgBgE,aAApC,CAAN,CADA,CAEF;MACC,CARD,MAQO,IAAIE,aAAa,IAAI,CAAC,KAAK5F,aAA3B,EAA0C;QAC/C0B,GAAG,GAAG+D,KAAK,GAAGC,aAAR,GAAwB,EAA9B,CAD+C,CAEjD;MACC,CAHM,MAGA,IAAIhE,GAAG,GAAG,KAAKsC,eAAX,IAA8B,CAAC,KAAKhE,aAAxC,EAAuD;QAC5D0B,GAAG,GAAG,KAAKsC,eAAL,GAAuB,EAA7B;MACD;MAED,OAAOtC,GAAG,GAAG,EAAN,GAAW,EAAX,GAAgBA,GAAvB;IACD,CA9DM;IA+DP4C,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAI,CAAC,KAAKhC,SAAV,EAAqB;MAErB,KAAKyC,QAAL;IACD,CAnEM;IAoEPX,cAAc,WAAdA,cAAcA,CAAA;MACZ,KAAK5B,eAAL,GAAuB,KAAvB;MAEA,KAAKqD,UAAL;IACD,CAxEM;IAyEPC,mBAAmB,WAAnBA,mBAAmBA,CAAA;MACjB,IAAI,KAAKxD,SAAT,EAAoB;QAClB,KAAKI,WAAL,GAAmB,KAAKnB,cAAL,GAAsB,CAAtB,GAA0B,KAAKwE,YAAL,EAA7C;MACD;IACF,CA7EM;IA8EPC,mBAAmB,WAAnBA,mBAAmBA,CAAA;MACjB,IAAI,KAAK9C,MAAL,KAAgB,KAApB,EAA2B;QACzB,KAAK3B,cAAL,GAAsB,KAAtB;QACA;MACD;MACD,IAAI0E,EAAE,GAAG,KAAKC,YAAL,EAAT;MACA,OAAOD,EAAP,EAAW;QACT,IAAIzB,MAAM,CAAC2B,gBAAP,CAAwBF,EAAxB,EAA4BG,QAA5B,KAAyC,OAA7C,EAAsD;UACpD,KAAK7E,cAAL,GAAsB,IAAtB;UACA;QACD;QACD0E,EAAE,GAAGA,EAAE,CAACI,YAAR;MACD;MACD,KAAK9E,cAAL,GAAsB,KAAtB;IACD,CA5FM;IA6FPsE,UAAU,WAAVA,UAAUA,CAAA,GAAM,CA7FT;IA8FPS,qBAAqB,WAArBA,qBAAqBA,CAAA;MAAA,IAAAC,KAAA;MACnB,IAAMC,SAAS,GAAGhH,WAAW,CAACiH,OAAZ,CAAoB5B,OAApB,CAA4ByB,qBAA5B,CAAkDI,IAAlD,CAAuD,IAAvD,CAAlB;MAEA,IAAMC,OAAO,GAAGH,SAAS,CAACI,KAA1B;MAEA,IAAID,OAAJ,EAAa;QACXH,SAAS,CAACI,KAAV,GAAmB,UAAAC,CAAD,EAA+C;UAC/D,IAAIN,KAAA,CAAKO,WAAT,EAAsB;YACpBH,OAAO,IAAIA,OAAO,CAACE,CAAD,CAAlB;UACD;UAEDN,KAAA,CAAKnF,SAAL,GAAiByF,CAAC,CAACE,OAAnB;UACAR,KAAA,CAAKlF,SAAL,GAAiBwF,CAAC,CAACG,OAAnB;QACD,CAPD;MAQD;MAED,OAAOR,SAAP;IACD,CA/GM;IAgHPhB,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAI,CAAC,KAAKlD,SAAV,EAAqB,OAAO,CAAP;MAErB,OAAOkC,MAAM,CAACyC,WAAP,IACLC,QAAQ,CAACC,eAAT,CAAyBC,YAD3B;IAED,CArHM;IAsHP9B,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAI,CAAC,KAAKhD,SAAV,EAAqB,OAAO,CAAP;MAErB,OAAOkC,MAAM,CAAC6C,WAAP,IACLH,QAAQ,CAACC,eAAT,CAAyBG,UAD3B;IAED,CA3HM;IA4HPvB,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAI,CAAC,KAAKzD,SAAV,EAAqB,OAAO,CAAP;MAErB,OAAOkC,MAAM,CAAC9B,WAAP,IACLwE,QAAQ,CAACC,eAAT,CAAyBI,SAD3B;IAED,CAjIM;IAkIPC,2BAA2B,WAA3BA,2BAA2BA,CAAEvB,EAAF,EAAa;MACtC,IAAMwB,IAAI,GAAGxB,EAAE,CAACyB,qBAAH,EAAb;MACA,OAAO;QACLhG,GAAG,EAAEyB,IAAI,CAACwE,KAAL,CAAWF,IAAI,CAAC/F,GAAhB,CADA;QAELC,IAAI,EAAEwB,IAAI,CAACwE,KAAL,CAAWF,IAAI,CAAC9F,IAAhB,CAFD;QAGLC,MAAM,EAAEuB,IAAI,CAACwE,KAAL,CAAWF,IAAI,CAAC7F,MAAhB,CAHH;QAILC,KAAK,EAAEsB,IAAI,CAACwE,KAAL,CAAWF,IAAI,CAAC5F,KAAhB,CAJF;QAKLC,KAAK,EAAEqB,IAAI,CAACwE,KAAL,CAAWF,IAAI,CAAC3F,KAAhB,CALF;QAMLC,MAAM,EAAEoB,IAAI,CAACwE,KAAL,CAAWF,IAAI,CAAC1F,MAAhB;MANH,CAAP;IAQD,CA5IM;IA6IP6F,OAAO,WAAPA,OAAOA,CAAE3B,EAAF,EAAiB;MACtB,IAAI,CAACA,EAAD,IAAO,CAAC,KAAK3D,SAAjB,EAA4B,OAAO,IAAP;MAE5B,IAAMmF,IAAI,GAAG,KAAKD,2BAAL,CAAiCvB,EAAjC,CAAb,CAHsB,CAKtB;;MACA,IAAI,KAAK/C,MAAL,KAAgB,KAApB,EAA2B;QACzB,IAAM2E,KAAK,GAAGrD,MAAM,CAAC2B,gBAAP,CAAwBF,EAAxB,CAAd;QAEAwB,IAAI,CAAC9F,IAAL,GAAY+B,SAAA,CAASmE,KAAK,CAACC,UAAP,CAApB;QACAL,IAAI,CAAC/F,GAAL,GAAWgC,SAAA,CAASmE,KAAK,CAACE,SAAP,CAAnB;MACD;MAED,OAAON,IAAP;IACD,CA3JM;IA4JPO,SAAS,WAATA,SAASA,CAAEC,EAAF,EAAgB;MAAA,IAAAC,MAAA;MACvBC,qBAAqB,CAAC,YAAK;QACzB,IAAMlC,EAAE,GAAGiC,MAAA,CAAKE,KAAL,CAAWjG,OAAtB;QAEA,IAAI,CAAC8D,EAAD,IAAOA,EAAE,CAAC4B,KAAH,CAASQ,OAAT,KAAqB,MAAhC,EAAwC;UACtCJ,EAAE;UACF;QACD;QAEDhC,EAAE,CAAC4B,KAAH,CAASQ,OAAT,GAAmB,cAAnB;QACAJ,EAAE;QACFhC,EAAE,CAAC4B,KAAH,CAASQ,OAAT,GAAmB,MAAnB;MACD,CAXoB,CAArB;IAYD,CAzKM;IA0KPC,eAAe,WAAfA,eAAeA,CAAA;MAAA,IAAAC,MAAA;MACb,OAAO,IAAAC,QAAA,CAAkB,UAAAC,OAAO;QAAA,OAAIN,qBAAqB,CAAC,YAAK;UAC7DI,MAAA,CAAK/F,eAAL,GAAuB+F,MAAA,CAAKlG,cAAL,GAAsBkG,MAAA,CAAKlE,QAAlD;UACAoE,OAAO;QACR,CAHwD,CAAlD;MAAA,EAAP;IAID,CA/KM;IAgLP/D,gBAAgB,WAAhBA,gBAAgBA,CAAA;MAAA,IAAAgE,MAAA;MACd,KAAKpG,SAAL,GAAiB,OAAOkC,MAAP,KAAkB,WAAnC;MACA,KAAKwB,mBAAL;MACA,KAAKF,mBAAL;MACA,KAAKrD,SAAL,GAAiByE,QAAQ,CAACC,eAAT,CAAyBwB,WAA1C;MAEA,IAAMnH,UAAU,GAAQ;QACtBC,SAAS,EAAAmH,aAAA,KAAO,KAAKpH,UAAL,CAAgBC,SAAA,CADV;QAEtBU,OAAO,EAAAyG,aAAA,KAAO,KAAKpH,UAAL,CAAgBW,OAAA;MAFR,CAAxB,CANc,CAWd;;MACA,IAAI,CAAC,KAAK0B,YAAN,IAAsB,KAAKgF,QAA/B,EAAyC;QACvCrH,UAAU,CAACC,SAAX,GAAuB,KAAKqD,gBAAL,EAAvB;MACD,CAFD,MAEO;QACL,IAAMrD,SAAS,GAAG,KAAKyE,YAAL,EAAlB;QACA,IAAI,CAACzE,SAAL,EAAgB;QAEhBD,UAAU,CAACC,SAAX,GAAuB,KAAKmG,OAAL,CAAanG,SAAb,CAAvB;QACAD,UAAU,CAACC,SAAX,CAAqBS,UAArB,GAAkCT,SAAS,CAACS,UAA5C;QACA,IAAI,KAAKgB,MAAL,KAAgB,KAApB,EAA2B;UACzB;UACA;UACA1B,UAAU,CAACC,SAAX,CAAqBO,SAArB,GAAiCP,SAAS,CAACO,SAA3C;QACD,CAJD,MAIO;UACLR,UAAU,CAACC,SAAX,CAAqBO,SAArB,GAAiC,CAAjC;QACD;MACF,CA3Ba,CA6Bd;;MACA,KAAKgG,SAAL,CAAe,YAAK;QAClB,IAAIU,MAAA,CAAKN,KAAL,CAAWjG,OAAf,EAAwB;UACtB,IAAIuG,MAAA,CAAKN,KAAL,CAAWjG,OAAX,CAAmBkE,YAAvB,EAAqC;YACnC,IAAMyC,UAAU,GAAGJ,MAAA,CAAKlB,2BAAL,CAAiCkB,MAAA,CAAKN,KAAL,CAAWjG,OAAX,CAAmBkE,YAApD,CAAnB;YAEAqC,MAAA,CAAKtG,eAAL,GAAuBoC,MAAM,CAAC9B,WAAP,GAAqBoG,UAAU,CAACpH,GAAvD;YACAF,UAAU,CAACC,SAAX,CAAqBC,GAArB,IAA4BgH,MAAA,CAAKtG,eAAjC;YACAZ,UAAU,CAACC,SAAX,CAAqBE,IAArB,IAA6B6C,MAAM,CAAC6C,WAAP,GAAqByB,UAAU,CAACnH,IAA7D;UACD;UAEDH,UAAU,CAACW,OAAX,GAAqBuG,MAAA,CAAKd,OAAL,CAAac,MAAA,CAAKN,KAAL,CAAWjG,OAAxB,CAArB;QACD;QAEDuG,MAAA,CAAKlH,UAAL,GAAkBA,UAAlB;MACD,CAdD;IAeD;EA7NM;AA7JwC,CAApC,CAAf", "ignoreList": []}]}