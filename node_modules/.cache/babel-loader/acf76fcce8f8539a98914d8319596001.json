{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/fa.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/fa.js", "mtime": 1757335235774}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGljb25zID0gewogIGNvbXBsZXRlOiAnZmFzIGZhLWNoZWNrJywKICBjYW5jZWw6ICdmYXMgZmEtdGltZXMtY2lyY2xlJywKICBjbG9zZTogJ2ZhcyBmYS10aW1lcycsCiAgImRlbGV0ZSI6ICdmYXMgZmEtdGltZXMtY2lyY2xlJywKICBjbGVhcjogJ2ZhcyBmYS10aW1lcy1jaXJjbGUnLAogIHN1Y2Nlc3M6ICdmYXMgZmEtY2hlY2stY2lyY2xlJywKICBpbmZvOiAnZmFzIGZhLWluZm8tY2lyY2xlJywKICB3YXJuaW5nOiAnZmFzIGZhLWV4Y2xhbWF0aW9uJywKICBlcnJvcjogJ2ZhcyBmYS1leGNsYW1hdGlvbi10cmlhbmdsZScsCiAgcHJldjogJ2ZhcyBmYS1jaGV2cm9uLWxlZnQnLAogIG5leHQ6ICdmYXMgZmEtY2hldnJvbi1yaWdodCcsCiAgY2hlY2tib3hPbjogJ2ZhcyBmYS1jaGVjay1zcXVhcmUnLAogIGNoZWNrYm94T2ZmOiAnZmFyIGZhLXNxdWFyZScsCiAgY2hlY2tib3hJbmRldGVybWluYXRlOiAnZmFzIGZhLW1pbnVzLXNxdWFyZScsCiAgZGVsaW1pdGVyOiAnZmFzIGZhLWNpcmNsZScsCiAgc29ydDogJ2ZhcyBmYS1zb3J0LXVwJywKICBleHBhbmQ6ICdmYXMgZmEtY2hldnJvbi1kb3duJywKICBtZW51OiAnZmFzIGZhLWJhcnMnLAogIHN1Ymdyb3VwOiAnZmFzIGZhLWNhcmV0LWRvd24nLAogIGRyb3Bkb3duOiAnZmFzIGZhLWNhcmV0LWRvd24nLAogIHJhZGlvT246ICdmYXIgZmEtZG90LWNpcmNsZScsCiAgcmFkaW9PZmY6ICdmYXIgZmEtY2lyY2xlJywKICBlZGl0OiAnZmFzIGZhLWVkaXQnLAogIHJhdGluZ0VtcHR5OiAnZmFyIGZhLXN0YXInLAogIHJhdGluZ0Z1bGw6ICdmYXMgZmEtc3RhcicsCiAgcmF0aW5nSGFsZjogJ2ZhcyBmYS1zdGFyLWhhbGYnLAogIGxvYWRpbmc6ICdmYXMgZmEtc3luYycsCiAgZmlyc3Q6ICdmYXMgZmEtc3RlcC1iYWNrd2FyZCcsCiAgbGFzdDogJ2ZhcyBmYS1zdGVwLWZvcndhcmQnLAogIHVuZm9sZDogJ2ZhcyBmYS1hcnJvd3MtYWx0LXYnLAogIGZpbGU6ICdmYXMgZmEtcGFwZXJjbGlwJywKICBwbHVzOiAnZmFzIGZhLXBsdXMnLAogIG1pbnVzOiAnZmFzIGZhLW1pbnVzJwp9OwpleHBvcnQgZGVmYXVsdCBpY29uczs="}, {"version": 3, "names": ["icons", "complete", "cancel", "close", "clear", "success", "info", "warning", "error", "prev", "next", "checkboxOn", "checkboxOff", "checkboxIndeterminate", "delimiter", "sort", "expand", "menu", "subgroup", "dropdown", "radioOn", "radioOff", "edit", "ratingEmpty", "ratingFull", "ratingHalf", "loading", "first", "last", "unfold", "file", "plus", "minus"], "sources": ["../../../../src/services/icons/presets/fa.ts"], "sourcesContent": ["import { VuetifyIcons } from 'vuetify/types/services/icons'\n\nconst icons: VuetifyIcons = {\n  complete: 'fas fa-check',\n  cancel: 'fas fa-times-circle',\n  close: 'fas fa-times',\n  delete: 'fas fa-times-circle', // delete (e.g. v-chip close)\n  clear: 'fas fa-times-circle', // delete (e.g. v-chip close)\n  success: 'fas fa-check-circle',\n  info: 'fas fa-info-circle',\n  warning: 'fas fa-exclamation',\n  error: 'fas fa-exclamation-triangle',\n  prev: 'fas fa-chevron-left',\n  next: 'fas fa-chevron-right',\n  checkboxOn: 'fas fa-check-square',\n  checkboxOff: 'far fa-square', // note 'far'\n  checkboxIndeterminate: 'fas fa-minus-square',\n  delimiter: 'fas fa-circle', // for carousel\n  sort: 'fas fa-sort-up',\n  expand: 'fas fa-chevron-down',\n  menu: 'fas fa-bars',\n  subgroup: 'fas fa-caret-down',\n  dropdown: 'fas fa-caret-down',\n  radioOn: 'far fa-dot-circle',\n  radioOff: 'far fa-circle',\n  edit: 'fas fa-edit',\n  ratingEmpty: 'far fa-star',\n  ratingFull: 'fas fa-star',\n  ratingHalf: 'fas fa-star-half',\n  loading: 'fas fa-sync',\n  first: 'fas fa-step-backward',\n  last: 'fas fa-step-forward',\n  unfold: 'fas fa-arrows-alt-v',\n  file: 'fas fa-paperclip',\n  plus: 'fas fa-plus',\n  minus: 'fas fa-minus',\n}\n\nexport default icons\n"], "mappings": "AAEA,IAAMA,KAAK,GAAiB;EAC1BC,QAAQ,EAAE,cADgB;EAE1BC,MAAM,EAAE,qBAFkB;EAG1BC,KAAK,EAAE,cAHmB;EAI1B,UAAQ,qBAJkB;EAK1BC,KAAK,EAAE,qBALmB;EAM1BC,OAAO,EAAE,qBANiB;EAO1BC,IAAI,EAAE,oBAPoB;EAQ1BC,OAAO,EAAE,oBARiB;EAS1BC,KAAK,EAAE,6BATmB;EAU1BC,IAAI,EAAE,qBAVoB;EAW1BC,IAAI,EAAE,sBAXoB;EAY1BC,UAAU,EAAE,qBAZc;EAa1BC,WAAW,EAAE,eAba;EAc1BC,qBAAqB,EAAE,qBAdG;EAe1BC,SAAS,EAAE,eAfe;EAgB1BC,IAAI,EAAE,gBAhBoB;EAiB1BC,MAAM,EAAE,qBAjBkB;EAkB1BC,IAAI,EAAE,aAlBoB;EAmB1BC,QAAQ,EAAE,mBAnBgB;EAoB1BC,QAAQ,EAAE,mBApBgB;EAqB1BC,OAAO,EAAE,mBArBiB;EAsB1BC,QAAQ,EAAE,eAtBgB;EAuB1BC,IAAI,EAAE,aAvBoB;EAwB1BC,WAAW,EAAE,aAxBa;EAyB1BC,UAAU,EAAE,aAzBc;EA0B1BC,UAAU,EAAE,kBA1Bc;EA2B1BC,OAAO,EAAE,aA3BiB;EA4B1BC,KAAK,EAAE,sBA5BmB;EA6B1BC,IAAI,EAAE,qBA7BoB;EA8B1BC,MAAM,EAAE,qBA9BkB;EA+B1BC,IAAI,EAAE,kBA/BoB;EAgC1BC,IAAI,EAAE,aAhCoB;EAiC1BC,KAAK,EAAE;AAjCmB,CAA5B;AAoCA,eAAehC,KAAf", "ignoreList": []}]}