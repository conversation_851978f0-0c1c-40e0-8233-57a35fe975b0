{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCounter/VCounter.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCounter/VCounter.js", "mtime": 1757335237789}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0IF9wYXJzZUludCBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL3BhcnNlLWludCI7CmltcG9ydCBfY29uY2F0SW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL2NvbmNhdCI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLm51bWJlci5jb25zdHJ1Y3Rvci5qcyI7Ci8vIFN0eWxlcwppbXBvcnQgIi4uLy4uLy4uL3NyYy9jb21wb25lbnRzL1ZDb3VudGVyL1ZDb3VudGVyLnNhc3MiOyAvLyBNaXhpbnMKCmltcG9ydCBUaGVtZWFibGUsIHsgZnVuY3Rpb25hbFRoZW1lQ2xhc3NlcyB9IGZyb20gJy4uLy4uL21peGlucy90aGVtZWFibGUnOwppbXBvcnQgbWl4aW5zIGZyb20gJy4uLy4uL3V0aWwvbWl4aW5zJzsKLyogQHZ1ZS9jb21wb25lbnQgKi8KCmV4cG9ydCBkZWZhdWx0IG1peGlucyhUaGVtZWFibGUpLmV4dGVuZCh7CiAgbmFtZTogJ3YtY291bnRlcicsCiAgZnVuY3Rpb25hbDogdHJ1ZSwKICBwcm9wczogewogICAgdmFsdWU6IHsKICAgICAgdHlwZTogW051bWJlciwgU3RyaW5nXSwKICAgICAgImRlZmF1bHQiOiAnJwogICAgfSwKICAgIG1heDogW051bWJlciwgU3RyaW5nXQogIH0sCiAgcmVuZGVyOiBmdW5jdGlvbiByZW5kZXIoaCwgY3R4KSB7CiAgICB2YXIgX2NvbnRleHQ7CiAgICB2YXIgcHJvcHMgPSBjdHgucHJvcHM7CiAgICB2YXIgbWF4ID0gX3BhcnNlSW50KHByb3BzLm1heCwgMTApOwogICAgdmFyIHZhbHVlID0gX3BhcnNlSW50KHByb3BzLnZhbHVlLCAxMCk7CiAgICB2YXIgY29udGVudCA9IG1heCA/IF9jb25jYXRJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0ID0gIiIuY29uY2F0KHZhbHVlLCAiIC8gIikpLmNhbGwoX2NvbnRleHQsIG1heCkgOiBTdHJpbmcocHJvcHMudmFsdWUpOwogICAgdmFyIGlzR3JlYXRlciA9IG1heCAmJiB2YWx1ZSA+IG1heDsKICAgIHJldHVybiBoKCdkaXYnLCB7CiAgICAgIHN0YXRpY0NsYXNzOiAndi1jb3VudGVyJywKICAgICAgImNsYXNzIjogX29iamVjdFNwcmVhZCh7CiAgICAgICAgJ2Vycm9yLS10ZXh0JzogaXNHcmVhdGVyCiAgICAgIH0sIGZ1bmN0aW9uYWxUaGVtZUNsYXNzZXMoY3R4KSkKICAgIH0sIGNvbnRlbnQpOwogIH0KfSk7"}, {"version": 3, "names": ["Themeable", "functionalThemeClasses", "mixins", "extend", "name", "functional", "props", "value", "type", "Number", "String", "max", "render", "h", "ctx", "_context", "_parseInt", "content", "_concatInstanceProperty", "concat", "call", "isGreater", "staticClass", "_objectSpread"], "sources": ["../../../src/components/VCounter/VCounter.ts"], "sourcesContent": ["// Styles\nimport './VCounter.sass'\n\n// Mixins\nimport Themeable, { functionalThemeClasses } from '../../mixins/themeable'\n\n// Types\nimport { VNode } from 'vue'\nimport mixins from '../../util/mixins'\n\n/* @vue/component */\nexport default mixins(Themeable).extend({\n  name: 'v-counter',\n\n  functional: true,\n\n  props: {\n    value: {\n      type: [Number, String],\n      default: '',\n    },\n    max: [Number, String],\n  },\n\n  render (h, ctx): VNode {\n    const { props } = ctx\n    const max = parseInt(props.max, 10)\n    const value = parseInt(props.value, 10)\n    const content = max ? `${value} / ${max}` : String(props.value)\n    const isGreater = max && (value > max)\n\n    return h('div', {\n      staticClass: 'v-counter',\n      class: {\n        'error--text': isGreater,\n        ...functionalThemeClasses(ctx),\n      },\n    }, content)\n  },\n})\n"], "mappings": ";;;;AAAA;AACA,OAAO,gDAAP,C,CAEA;;AACA,OAAOA,SAAP,IAAoBC,sBAApB,QAAkD,wBAAlD;AAIA,OAAOC,MAAP,MAAmB,mBAAnB;AAEA;;AACA,eAAeA,MAAM,CAACF,SAAD,CAAN,CAAkBG,MAAlB,CAAyB;EACtCC,IAAI,EAAE,WADgC;EAGtCC,UAAU,EAAE,IAH0B;EAKtCC,KAAK,EAAE;IACLC,KAAK,EAAE;MACLC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADD;MAEL,WAAS;IAFJ,CADF;IAKLC,GAAG,EAAE,CAACF,MAAD,EAASC,MAAT;EALA,CAL+B;EAatCE,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAKC,GAAL,EAAQ;IAAA,IAAAC,QAAA;IACZ,IAAQT,KAAA,GAAUQ,GAAlB,CAAQR,KAAA;IACR,IAAMK,GAAG,GAAGK,SAAA,CAASV,KAAK,CAACK,GAAP,EAAY,EAAZ,CAApB;IACA,IAAMJ,KAAK,GAAGS,SAAA,CAASV,KAAK,CAACC,KAAP,EAAc,EAAd,CAAtB;IACA,IAAMU,OAAO,GAAGN,GAAG,GAAAO,uBAAA,CAAAH,QAAA,MAAAI,MAAA,CAAMZ,KAAK,UAAAa,IAAA,CAAAL,QAAA,EAAMJ,GAAG,IAAKD,MAAM,CAACJ,KAAK,CAACC,KAAP,CAAlD;IACA,IAAMc,SAAS,GAAGV,GAAG,IAAKJ,KAAK,GAAGI,GAAlC;IAEA,OAAOE,CAAC,CAAC,KAAD,EAAQ;MACdS,WAAW,EAAE,WADC;MAEd,SAAAC,aAAA;QACE,eAAeF;MADV,GAEFpB,sBAAsB,CAACa,GAAD;IAJb,CAAR,EAMLG,OANK,CAAR;EAOD;AA3BqC,CAAzB,CAAf", "ignoreList": []}]}