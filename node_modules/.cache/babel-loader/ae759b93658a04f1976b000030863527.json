{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/mdi.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/mdi.js", "mtime": 1757335237357}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGljb25zID0gewogIGNvbXBsZXRlOiAnbWRpLWNoZWNrJywKICBjYW5jZWw6ICdtZGktY2xvc2UtY2lyY2xlJywKICBjbG9zZTogJ21kaS1jbG9zZScsCiAgImRlbGV0ZSI6ICdtZGktY2xvc2UtY2lyY2xlJywKICBjbGVhcjogJ21kaS1jbG9zZScsCiAgc3VjY2VzczogJ21kaS1jaGVjay1jaXJjbGUnLAogIGluZm86ICdtZGktaW5mb3JtYXRpb24nLAogIHdhcm5pbmc6ICdtZGktZXhjbGFtYXRpb24nLAogIGVycm9yOiAnbWRpLWFsZXJ0JywKICBwcmV2OiAnbWRpLWNoZXZyb24tbGVmdCcsCiAgbmV4dDogJ21kaS1jaGV2cm9uLXJpZ2h0JywKICBjaGVja2JveE9uOiAnbWRpLWNoZWNrYm94LW1hcmtlZCcsCiAgY2hlY2tib3hPZmY6ICdtZGktY2hlY2tib3gtYmxhbmstb3V0bGluZScsCiAgY2hlY2tib3hJbmRldGVybWluYXRlOiAnbWRpLW1pbnVzLWJveCcsCiAgZGVsaW1pdGVyOiAnbWRpLWNpcmNsZScsCiAgc29ydDogJ21kaS1hcnJvdy11cCcsCiAgZXhwYW5kOiAnbWRpLWNoZXZyb24tZG93bicsCiAgbWVudTogJ21kaS1tZW51JywKICBzdWJncm91cDogJ21kaS1tZW51LWRvd24nLAogIGRyb3Bkb3duOiAnbWRpLW1lbnUtZG93bicsCiAgcmFkaW9PbjogJ21kaS1yYWRpb2JveC1tYXJrZWQnLAogIHJhZGlvT2ZmOiAnbWRpLXJhZGlvYm94LWJsYW5rJywKICBlZGl0OiAnbWRpLXBlbmNpbCcsCiAgcmF0aW5nRW1wdHk6ICdtZGktc3Rhci1vdXRsaW5lJywKICByYXRpbmdGdWxsOiAnbWRpLXN0YXInLAogIHJhdGluZ0hhbGY6ICdtZGktc3Rhci1oYWxmLWZ1bGwnLAogIGxvYWRpbmc6ICdtZGktY2FjaGVkJywKICBmaXJzdDogJ21kaS1wYWdlLWZpcnN0JywKICBsYXN0OiAnbWRpLXBhZ2UtbGFzdCcsCiAgdW5mb2xkOiAnbWRpLXVuZm9sZC1tb3JlLWhvcml6b250YWwnLAogIGZpbGU6ICdtZGktcGFwZXJjbGlwJywKICBwbHVzOiAnbWRpLXBsdXMnLAogIG1pbnVzOiAnbWRpLW1pbnVzJwp9OwpleHBvcnQgZGVmYXVsdCBpY29uczs="}, {"version": 3, "names": ["icons", "complete", "cancel", "close", "clear", "success", "info", "warning", "error", "prev", "next", "checkboxOn", "checkboxOff", "checkboxIndeterminate", "delimiter", "sort", "expand", "menu", "subgroup", "dropdown", "radioOn", "radioOff", "edit", "ratingEmpty", "ratingFull", "ratingHalf", "loading", "first", "last", "unfold", "file", "plus", "minus"], "sources": ["../../../../src/services/icons/presets/mdi.ts"], "sourcesContent": ["import { VuetifyIcons } from 'vuetify/types/services/icons'\n\nconst icons: VuetifyIcons = {\n  complete: 'mdi-check',\n  cancel: 'mdi-close-circle',\n  close: 'mdi-close',\n  delete: 'mdi-close-circle', // delete (e.g. v-chip close)\n  clear: 'mdi-close',\n  success: 'mdi-check-circle',\n  info: 'mdi-information',\n  warning: 'mdi-exclamation',\n  error: 'mdi-alert',\n  prev: 'mdi-chevron-left',\n  next: 'mdi-chevron-right',\n  checkboxOn: 'mdi-checkbox-marked',\n  checkboxOff: 'mdi-checkbox-blank-outline',\n  checkboxIndeterminate: 'mdi-minus-box',\n  delimiter: 'mdi-circle', // for carousel\n  sort: 'mdi-arrow-up',\n  expand: 'mdi-chevron-down',\n  menu: 'mdi-menu',\n  subgroup: 'mdi-menu-down',\n  dropdown: 'mdi-menu-down',\n  radioOn: 'mdi-radiobox-marked',\n  radioOff: 'mdi-radiobox-blank',\n  edit: 'mdi-pencil',\n  ratingEmpty: 'mdi-star-outline',\n  ratingFull: 'mdi-star',\n  ratingHalf: 'mdi-star-half-full',\n  loading: 'mdi-cached',\n  first: 'mdi-page-first',\n  last: 'mdi-page-last',\n  unfold: 'mdi-unfold-more-horizontal',\n  file: 'mdi-paperclip',\n  plus: 'mdi-plus',\n  minus: 'mdi-minus',\n}\n\nexport default icons\n"], "mappings": "AAEA,IAAMA,KAAK,GAAiB;EAC1BC,QAAQ,EAAE,WADgB;EAE1BC,MAAM,EAAE,kBAFkB;EAG1BC,KAAK,EAAE,WAHmB;EAI1B,UAAQ,kBAJkB;EAK1BC,KAAK,EAAE,WALmB;EAM1BC,OAAO,EAAE,kBANiB;EAO1BC,IAAI,EAAE,iBAPoB;EAQ1BC,OAAO,EAAE,iBARiB;EAS1BC,KAAK,EAAE,WATmB;EAU1BC,IAAI,EAAE,kBAVoB;EAW1BC,IAAI,EAAE,mBAXoB;EAY1BC,UAAU,EAAE,qBAZc;EAa1BC,WAAW,EAAE,4BAba;EAc1BC,qBAAqB,EAAE,eAdG;EAe1BC,SAAS,EAAE,YAfe;EAgB1BC,IAAI,EAAE,cAhBoB;EAiB1BC,MAAM,EAAE,kBAjBkB;EAkB1BC,IAAI,EAAE,UAlBoB;EAmB1BC,QAAQ,EAAE,eAnBgB;EAoB1BC,QAAQ,EAAE,eApBgB;EAqB1BC,OAAO,EAAE,qBArBiB;EAsB1BC,QAAQ,EAAE,oBAtBgB;EAuB1BC,IAAI,EAAE,YAvBoB;EAwB1BC,WAAW,EAAE,kBAxBa;EAyB1BC,UAAU,EAAE,UAzBc;EA0B1BC,UAAU,EAAE,oBA1Bc;EA2B1BC,OAAO,EAAE,YA3BiB;EA4B1BC,KAAK,EAAE,gBA5BmB;EA6B1BC,IAAI,EAAE,eA7BoB;EA8B1BC,MAAM,EAAE,4BA9BkB;EA+B1BC,IAAI,EAAE,eA/BoB;EAgC1BC,IAAI,EAAE,UAhCoB;EAiC1BC,KAAK,EAAE;AAjCmB,CAA5B;AAoCA,eAAehC,KAAf", "ignoreList": []}]}