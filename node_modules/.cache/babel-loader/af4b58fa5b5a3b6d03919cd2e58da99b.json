{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/VContainer.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VGrid/VContainer.js", "mtime": 1757335237779}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Grid", "mergeData", "extend", "name", "functional", "props", "id", "String", "tag", "type", "fluid", "Boolean", "render", "h", "_ref", "_context2", "data", "children", "classes", "attrs", "_context", "_filterInstanceProperty", "_Object$keys", "call", "key", "value", "_startsWithInstanceProperty", "domProps", "staticClass", "_concatInstanceProperty", "Array"], "sources": ["../../../src/components/VGrid/VContainer.ts"], "sourcesContent": ["import './_grid.sass'\nimport './VGrid.sass'\n\nimport Grid from './grid'\n\nimport mergeData from '../../util/mergeData'\n\n/* @vue/component */\nexport default Grid('container').extend({\n  name: 'v-container',\n  functional: true,\n  props: {\n    id: String,\n    tag: {\n      type: String,\n      default: 'div',\n    },\n    fluid: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  render (h, { props, data, children }) {\n    let classes\n    const { attrs } = data\n    if (attrs) {\n      // reset attrs to extract utility clases like pa-3\n      data.attrs = {}\n      classes = Object.keys(attrs).filter(key => {\n        // TODO: Remove once resolved\n        // https://github.com/vuejs/vue/issues/7841\n        if (key === 'slot') return false\n\n        const value = attrs[key]\n\n        // add back data attributes like data-test=\"foo\" but do not\n        // add them as classes\n        if (key.startsWith('data-')) {\n          data.attrs![key] = value\n          return false\n        }\n\n        return value || typeof value === 'string'\n      })\n    }\n\n    if (props.id) {\n      data.domProps = data.domProps || {}\n      data.domProps.id = props.id\n    }\n\n    return h(\n      props.tag,\n      mergeData(data, {\n        staticClass: 'container',\n        class: Array<any>({\n          'container--fluid': props.fluid,\n        }).concat(classes || []),\n      }),\n      children\n    )\n  },\n})\n"], "mappings": ";;;;AAAA,OAAO,0CAAP;AACA,OAAO,0CAAP;AAEA,OAAOA,IAAP,MAAiB,QAAjB;AAEA,OAAOC,SAAP,MAAsB,sBAAtB;AAEA;;AACA,eAAeD,IAAI,CAAC,WAAD,CAAJ,CAAkBE,MAAlB,CAAyB;EACtCC,IAAI,EAAE,aADgC;EAEtCC,UAAU,EAAE,IAF0B;EAGtCC,KAAK,EAAE;IACLC,EAAE,EAAEC,MADC;IAELC,GAAG,EAAE;MACHC,IAAI,EAAEF,MADH;MAEH,WAAS;IAFN,CAFA;IAMLG,KAAK,EAAE;MACLD,IAAI,EAAEE,OADD;MAEL,WAAS;IAFJ;EANF,CAH+B;EActCC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAAC,IAAA,EAA8B;IAAA,IAAAC,SAAA;IAAA,IAAvBV,KAAF,GAAAS,IAAA,CAAET,KAAF;MAASW,IAAT,GAAAF,IAAA,CAASE,IAAT;MAAeC,QAAA,GAAAH,IAAA,CAAAG,QAAA;IACxB,IAAIC,OAAJ;IACA,IAAQC,KAAA,GAAUH,IAAlB,CAAQG,KAAA;IACR,IAAIA,KAAJ,EAAW;MAAA,IAAAC,QAAA;MACT;MACAJ,IAAI,CAACG,KAAL,GAAa,EAAb;MACAD,OAAO,GAAGG,uBAAA,CAAAD,QAAA,GAAAE,YAAA,CAAYH,KAAZ,GAAAI,IAAA,CAAAH,QAAA,EAA0B,UAAAI,GAAG,EAAG;QACxC;QACA;QACA,IAAIA,GAAG,KAAK,MAAZ,EAAoB,OAAO,KAAP;QAEpB,IAAMC,KAAK,GAAGN,KAAK,CAACK,GAAD,CAAnB,CALwC,CAOxC;QACA;;QACA,IAAIE,2BAAA,CAAAF,GAAG,EAAAD,IAAA,CAAHC,GAAG,EAAY,OAAf,CAAJ,EAA6B;UAC3BR,IAAI,CAACG,KAAL,CAAYK,GAAZ,IAAmBC,KAAnB;UACA,OAAO,KAAP;QACD;QAED,OAAOA,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAAjC;MACD,CAfS,CAAV;IAgBD;IAED,IAAIpB,KAAK,CAACC,EAAV,EAAc;MACZU,IAAI,CAACW,QAAL,GAAgBX,IAAI,CAACW,QAAL,IAAiB,EAAjC;MACAX,IAAI,CAACW,QAAL,CAAcrB,EAAd,GAAmBD,KAAK,CAACC,EAAzB;IACD;IAED,OAAOO,CAAC,CACNR,KAAK,CAACG,GADA,EAENP,SAAS,CAACe,IAAD,EAAO;MACdY,WAAW,EAAE,WADC;MAEd,SAAOC,uBAAA,CAAAd,SAAA,GAAAe,KAAK,CAAM;QAChB,oBAAoBzB,KAAK,CAACK;MADV,CAAN,CAAL,EAAAa,IAAA,CAAAR,SAAA,EAEGG,OAAO,IAAI,EAFd;IAFO,CAAP,CAFH,EAQND,QARM,CAAR;EAUD;AArDqC,CAAzB,CAAf", "ignoreList": []}]}