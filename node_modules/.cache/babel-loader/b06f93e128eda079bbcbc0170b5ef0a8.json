{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/transitionable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/transitionable/index.js", "mtime": 1757335237267}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwpleHBvcnQgZGVmYXVsdCBWdWUuZXh0ZW5kKHsKICBuYW1lOiAndHJhbnNpdGlvbmFibGUnLAogIHByb3BzOiB7CiAgICBtb2RlOiBTdHJpbmcsCiAgICBvcmlnaW46IFN0cmluZywKICAgIHRyYW5zaXRpb246IFN0cmluZwogIH0KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "extend", "name", "props", "mode", "String", "origin", "transition"], "sources": ["../../../src/mixins/transitionable/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\nexport default Vue.extend({\n  name: 'transitionable',\n\n  props: {\n    mode: String,\n    origin: String,\n    transition: String,\n  },\n})\n"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,gBADkB;EAGxBC,KAAK,EAAE;IACLC,IAAI,EAAEC,MADD;IAELC,MAAM,EAAED,MAFH;IAGLE,UAAU,EAAEF;EAHP;AAHiB,CAAX,CAAf", "ignoreList": []}]}