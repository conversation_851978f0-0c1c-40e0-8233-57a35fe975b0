{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VChip/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VChip/index.js", "mtime": 1757335236816}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZDaGlwIGZyb20gJy4vVkNoaXAnOwpleHBvcnQgeyBWQ2hpcCB9OwpleHBvcnQgZGVmYXVsdCBWQ2hpcDs="}, {"version": 3, "names": ["VChip"], "sources": ["../../../src/components/VChip/index.ts"], "sourcesContent": ["import VChip from './VChip'\n\nexport { VChip }\nexport default VChip\n"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,SAAlB;AAEA,SAASA,KAAT;AACA,eAAeA,KAAf", "ignoreList": []}]}