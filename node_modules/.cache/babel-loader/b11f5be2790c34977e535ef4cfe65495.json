{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VStepper/VStepper.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VStepper/VStepper.js", "mtime": 1757335239113}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VSheet", "provide", "RegistrableProvide", "Proxyable", "mixins", "breaking", "baseMixins", "extend", "name", "step<PERSON>lick", "isVertical", "vertical", "props", "altLabels", "Boolean", "nonLinear", "flat", "data", "isBooted", "steps", "content", "isReverse", "internalLazyValue", "value", "step", "computed", "classes", "_objectSpread", "_flatInstanceProperty", "options", "call", "styles", "watch", "internalValue", "val", "oldVal", "Number", "updateView", "created", "$listeners", "input", "mounted", "methods", "register", "item", "$options", "push", "unregister", "_context", "_filterInstanceProperty", "i", "_context2", "_this", "$nextTick", "index", "length", "toggle", "render", "h", "tag", "staticClass", "style", "$slots"], "sources": ["../../../src/components/VStepper/VStepper.ts"], "sourcesContent": ["// Styles\nimport './VStepper.sass'\n\n// Extensions\nimport VSheet from '../VSheet'\n\n// Components\nimport VStepperStep from './VStepperStep'\nimport VStepperContent from './VStepperContent'\n\n// Mixins\nimport { provide as RegistrableProvide } from '../../mixins/registrable'\nimport Proxyable from '../../mixins/proxyable'\n\n// Utilities\nimport mixins from '../../util/mixins'\nimport { breaking } from '../../util/console'\n\n// Types\nimport { VNode } from 'vue'\n\nconst baseMixins = mixins(\n  VSheet,\n  RegistrableProvide('stepper'),\n  Proxyable,\n)\n\ntype VStepperStepInstance = InstanceType<typeof VStepperStep>\ntype VStepperContentInstance = InstanceType<typeof VStepperContent>\n\n/* @vue/component */\nexport default baseMixins.extend({\n  name: 'v-stepper',\n\n  provide (): object {\n    return {\n      stepClick: this.stepClick,\n      isVertical: this.vertical,\n    }\n  },\n\n  props: {\n    altLabels: <PERSON><PERSON>an,\n    nonLinear: Boolean,\n    flat: Boolean,\n    vertical: <PERSON>olean,\n  },\n\n  data () {\n    const data: Dictionary<any> = {\n      isBooted: false,\n      steps: [] as VStepperStepInstance[],\n      content: [] as VStepperContentInstance[],\n      isReverse: false,\n    }\n\n    data.internalLazyValue = this.value != null\n      ? this.value\n      : (data[0] || {}).step || 1\n\n    return data\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        'v-stepper--flat': this.flat,\n        'v-stepper--is-booted': this.isBooted,\n        'v-stepper--vertical': this.vertical,\n        'v-stepper--alt-labels': this.altLabels,\n        'v-stepper--non-linear': this.nonLinear,\n        ...VSheet.options.computed.classes.call(this),\n      }\n    },\n    styles (): object {\n      return {\n        ...VSheet.options.computed.styles.call(this),\n      }\n    },\n  },\n\n  watch: {\n    internalValue (val, oldVal) {\n      this.isReverse = Number(val) < Number(oldVal)\n\n      oldVal && (this.isBooted = true)\n\n      this.updateView()\n    },\n  },\n\n  created () {\n    /* istanbul ignore next */\n    if (this.$listeners.input) {\n      breaking('@input', '@change', this)\n    }\n  },\n\n  mounted () {\n    this.updateView()\n  },\n\n  methods: {\n    register (item: VStepperStepInstance | VStepperContentInstance) {\n      if (item.$options.name === 'v-stepper-step') {\n        this.steps.push(item as VStepperStepInstance)\n      } else if (item.$options.name === 'v-stepper-content') {\n        (item as VStepperContentInstance).isVertical = this.vertical\n        this.content.push(item as VStepperContentInstance)\n      }\n    },\n    unregister (item: VStepperStepInstance | VStepperContentInstance) {\n      if (item.$options.name === 'v-stepper-step') {\n        this.steps = this.steps.filter((i: VStepperStepInstance) => i !== item)\n      } else if (item.$options.name === 'v-stepper-content') {\n        (item as VStepperContentInstance).isVertical = this.vertical\n        this.content = this.content.filter((i: VStepperContentInstance) => i !== item)\n      }\n    },\n    stepClick (step: string | number) {\n      this.$nextTick(() => (this.internalValue = step))\n    },\n    updateView () {\n      for (let index = this.steps.length; --index >= 0;) {\n        this.steps[index].toggle(this.internalValue as any)\n      }\n      for (let index = this.content.length; --index >= 0;) {\n        this.content[index].toggle(this.internalValue as any, this.isReverse)\n      }\n    },\n  },\n\n  render (h): VNode {\n    return h(this.tag, {\n      staticClass: 'v-stepper',\n      class: this.classes,\n      style: this.styles,\n    }, this.$slots.default)\n  },\n})\n"], "mappings": ";;;;;;AAAA;AACA,OAAO,gDAAP,C,CAEA;;AACA,OAAOA,MAAP,MAAmB,WAAnB,C,CAMA;;AACA,SAASC,OAAO,IAAIC,kBAApB,QAA8C,0BAA9C;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,QAAT,QAAyB,oBAAzB;AAKA,IAAMC,UAAU,GAAGF,MAAM,CACvBJ,MADuB,EAEvBE,kBAAkB,CAAC,SAAD,CAFK,EAGvBC,SAHuB,CAAzB;AASA;;AACA,eAAeG,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,WADyB;EAG/BP,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLQ,SAAS,EAAE,KAAKA,SADX;MAELC,UAAU,EAAE,KAAKC;IAFZ,CAAP;EAID,CAR8B;EAU/BC,KAAK,EAAE;IACLC,SAAS,EAAEC,OADN;IAELC,SAAS,EAAED,OAFN;IAGLE,IAAI,EAAEF,OAHD;IAILH,QAAQ,EAAEG;EAJL,CAVwB;EAiB/BG,IAAI,WAAJA,IAAIA,CAAA;IACF,IAAMA,IAAI,GAAoB;MAC5BC,QAAQ,EAAE,KADkB;MAE5BC,KAAK,EAAE,EAFqB;MAG5BC,OAAO,EAAE,EAHmB;MAI5BC,SAAS,EAAE;IAJiB,CAA9B;IAOAJ,IAAI,CAACK,iBAAL,GAAyB,KAAKC,KAAL,IAAc,IAAd,GACrB,KAAKA,KADgB,GAErB,CAACN,IAAI,CAAC,CAAD,CAAJ,IAAW,EAAZ,EAAgBO,IAAhB,IAAwB,CAF5B;IAIA,OAAOP,IAAP;EACD,CA9B8B;EAgC/BQ,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA;QACE,mBAAAC,qBAAA,CAAmB,KADd;QAEL,wBAAwB,KAAKV,QAFxB;QAGL,uBAAuB,KAAKP,QAHvB;QAIL,yBAAyB,KAAKE,SAJzB;QAKL,yBAAyB,KAAKE;MALzB,GAMFf,MAAM,CAAC6B,OAAP,CAAeJ,QAAf,CAAwBC,OAAxB,CAAgCI,IAAhC,CAAqC,IAArC;IAEN,CAVO;IAWRC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAAJ,aAAA,KACK3B,MAAM,CAAC6B,OAAP,CAAeJ,QAAf,CAAwBM,MAAxB,CAA+BD,IAA/B,CAAoC,IAApC;IAEN;EAfO,CAhCqB;EAkD/BE,KAAK,EAAE;IACLC,aAAa,WAAbA,aAAaA,CAAEC,GAAF,EAAOC,MAAP,EAAa;MACxB,KAAKd,SAAL,GAAiBe,MAAM,CAACF,GAAD,CAAN,GAAcE,MAAM,CAACD,MAAD,CAArC;MAEAA,MAAM,KAAK,KAAKjB,QAAL,GAAgB,IAArB,CAAN;MAEA,KAAKmB,UAAL;IACD;EAPI,CAlDwB;EA4D/BC,OAAO,WAAPA,OAAOA,CAAA;IACL;IACA,IAAI,KAAKC,UAAL,CAAgBC,KAApB,EAA2B;MACzBnC,QAAQ,CAAC,QAAD,EAAW,SAAX,EAAsB,IAAtB,CAAR;IACD;EACF,CAjE8B;EAmE/BoC,OAAO,WAAPA,OAAOA,CAAA;IACL,KAAKJ,UAAL;EACD,CArE8B;EAuE/BK,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAEC,IAAF,EAAsD;MAC5D,IAAIA,IAAI,CAACC,QAAL,CAAcrC,IAAd,KAAuB,gBAA3B,EAA6C;QAC3C,KAAKW,KAAL,CAAW2B,IAAX,CAAgBF,IAAhB;MACD,CAFD,MAEO,IAAIA,IAAI,CAACC,QAAL,CAAcrC,IAAd,KAAuB,mBAA3B,EAAgD;QACpDoC,IAAgC,CAAClC,UAAjC,GAA8C,KAAKC,QAAnD;QACD,KAAKS,OAAL,CAAa0B,IAAb,CAAkBF,IAAlB;MACD;IACF,CARM;IASPG,UAAU,WAAVA,UAAUA,CAAEH,IAAF,EAAsD;MAC9D,IAAIA,IAAI,CAACC,QAAL,CAAcrC,IAAd,KAAuB,gBAA3B,EAA6C;QAAA,IAAAwC,QAAA;QAC3C,KAAK7B,KAAL,GAAa8B,uBAAA,CAAAD,QAAA,QAAK7B,KAAL,EAAAW,IAAA,CAAAkB,QAAA,EAAmB,UAAAE,CAAD;UAAA,OAA6BA,CAAC,KAAKN,IAArD;QAAA,EAAb;MACD,CAFD,MAEO,IAAIA,IAAI,CAACC,QAAL,CAAcrC,IAAd,KAAuB,mBAA3B,EAAgD;QAAA,IAAA2C,SAAA;QACpDP,IAAgC,CAAClC,UAAjC,GAA8C,KAAKC,QAAnD;QACD,KAAKS,OAAL,GAAe6B,uBAAA,CAAAE,SAAA,QAAK/B,OAAL,EAAAU,IAAA,CAAAqB,SAAA,EAAqB,UAAAD,CAAD;UAAA,OAAgCA,CAAC,KAAKN,IAA1D;QAAA,EAAf;MACD;IACF,CAhBM;IAiBPnC,SAAS,WAATA,SAASA,CAAEe,IAAF,EAAuB;MAAA,IAAA4B,KAAA;MAC9B,KAAKC,SAAL,CAAe;QAAA,OAAOD,KAAA,CAAKnB,aAAL,GAAqBT,IAA3C;MAAA;IACD,CAnBM;IAoBPa,UAAU,WAAVA,UAAUA,CAAA;MACR,KAAK,IAAIiB,KAAK,GAAG,KAAKnC,KAAL,CAAWoC,MAA5B,EAAoC,EAAED,KAAF,IAAW,CAA/C,GAAmD;QACjD,KAAKnC,KAAL,CAAWmC,KAAX,EAAkBE,MAAlB,CAAyB,KAAKvB,aAA9B;MACD;MACD,KAAK,IAAIqB,MAAK,GAAG,KAAKlC,OAAL,CAAamC,MAA9B,EAAsC,EAAED,MAAF,IAAW,CAAjD,GAAqD;QACnD,KAAKlC,OAAL,CAAakC,MAAb,EAAoBE,MAApB,CAA2B,KAAKvB,aAAhC,EAAsD,KAAKZ,SAA3D;MACD;IACF;EA3BM,CAvEsB;EAqG/BoC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAKC,GAAN,EAAW;MACjBC,WAAW,EAAE,WADI;MAEjB,SAAO,KAAKlC,OAFK;MAGjBmC,KAAK,EAAE,KAAK9B;IAHK,CAAX,EAIL,KAAK+B,MAAL,WAJK,CAAR;EAKD;AA3G8B,CAAlB,CAAf", "ignoreList": []}]}