{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sv.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sv.js", "mtime": 1757335237534}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/sv.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON>',\n  close: '<PERSON>äng',\n  dataIterator: {\n    noResultsText: 'Inga poster funna',\n    loadingText: 'Laddar data...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Rader per sida:',\n    ariaLabel: {\n      sortDescending: 'Sorterat fallande.',\n      sortAscending: 'Sorterat stigande.',\n      sortNone: 'Osorterat.',\n      activateNone: 'Aktivera för att ta bort sortering.',\n      activateDescending: 'Aktivera för sortering fallande.',\n      activateAscending: 'Aktivera för sortering stigande.',\n    },\n    sortBy: 'Sortera efter',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Objekt per sida:',\n    itemsPerPageAll: 'Alla',\n    nextPage: 'Nästa sida',\n    prevPage: 'Föregående sida',\n    firstPage: 'Första sidan',\n    lastPage: 'Sista sidan',\n    pageText: '{0}-{1} av {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} markerade',\n    nextMonthAriaLabel: 'Nästa månad',\n    nextYearAriaLabel: 'Nästa år',\n    prevMonthAriaLabel: 'Förra månaden',\n    prevYearAriaLabel: 'Förra året',\n  },\n  noDataText: 'Ingen data tillgänglig',\n  carousel: {\n    prev: 'Föregående vy',\n    next: 'Nästa vy',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} fler',\n  },\n  fileInput: {\n    counter: '{0} filer',\n    counterSize: '{0} filer (av {1} totalt)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Pagination Navigation',\n      next: 'Nästa sida',\n      previous: 'Föregående sida',\n      page: 'Gå till sidan {0}',\n      currentPage: 'Aktuell sida, sida {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,mBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,iBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,oBAFN;MAGTC,QAAQ,EAAE,YAHD;MAITC,YAAY,EAAE,qCAJL;MAKTC,kBAAkB,EAAE,kCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,kBADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,YAHA;IAIVC,QAAQ,EAAE,iBAJA;IAKVC,SAAS,EAAE,cALD;IAMVC,QAAQ,EAAE,aANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,eADL;IAEVC,kBAAkB,EAAE,aAFV;IAGVC,iBAAiB,EAAE,UAHT;IAIVC,kBAAkB,EAAE,eAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,wBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,eADE;IAERC,IAAI,EAAE,UAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,YAFG;MAGTY,QAAQ,EAAE,iBAHD;MAITC,IAAI,EAAE,mBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}