{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VDivider/VDivider.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VDivider/VDivider.js", "mtime": 1757335238246}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKLy8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL2NvbXBvbmVudHMvVkRpdmlkZXIvVkRpdmlkZXIuc2FzcyI7IC8vIE1peGlucwoKaW1wb3J0IFRoZW1lYWJsZSBmcm9tICcuLi8uLi9taXhpbnMvdGhlbWVhYmxlJzsKZXhwb3J0IGRlZmF1bHQgVGhlbWVhYmxlLmV4dGVuZCh7CiAgbmFtZTogJ3YtZGl2aWRlcicsCiAgcHJvcHM6IHsKICAgIGluc2V0OiBCb29sZWFuLAogICAgdmVydGljYWw6IEJvb2xlYW4KICB9LAogIHJlbmRlcjogZnVuY3Rpb24gcmVuZGVyKGgpIHsKICAgIC8vIFdBSS1BUklBIGF0dHJpYnV0ZXMKICAgIHZhciBvcmllbnRhdGlvbjsKICAgIGlmICghdGhpcy4kYXR0cnMucm9sZSB8fCB0aGlzLiRhdHRycy5yb2xlID09PSAnc2VwYXJhdG9yJykgewogICAgICBvcmllbnRhdGlvbiA9IHRoaXMudmVydGljYWwgPyAndmVydGljYWwnIDogJ2hvcml6b250YWwnOwogICAgfQogICAgcmV0dXJuIGgoJ2hyJywgewogICAgICAiY2xhc3MiOiBfb2JqZWN0U3ByZWFkKHsKICAgICAgICAndi1kaXZpZGVyJzogdHJ1ZSwKICAgICAgICAndi1kaXZpZGVyLS1pbnNldCc6IHRoaXMuaW5zZXQsCiAgICAgICAgJ3YtZGl2aWRlci0tdmVydGljYWwnOiB0aGlzLnZlcnRpY2FsCiAgICAgIH0sIHRoaXMudGhlbWVDbGFzc2VzKSwKICAgICAgYXR0cnM6IF9vYmplY3RTcHJlYWQoewogICAgICAgIHJvbGU6ICdzZXBhcmF0b3InLAogICAgICAgICdhcmlhLW9yaWVudGF0aW9uJzogb3JpZW50YXRpb24KICAgICAgfSwgdGhpcy4kYXR0cnMpLAogICAgICBvbjogdGhpcy4kbGlzdGVuZXJzCiAgICB9KTsKICB9Cn0pOw=="}, {"version": 3, "names": ["Themeable", "extend", "name", "props", "inset", "Boolean", "vertical", "render", "h", "orientation", "$attrs", "role", "_objectSpread", "themeClasses", "attrs", "on", "$listeners"], "sources": ["../../../src/components/VDivider/VDivider.ts"], "sourcesContent": ["// Styles\nimport './VDivider.sass'\n\n// Types\nimport { VNode } from 'vue'\n\n// Mixins\nimport Themeable from '../../mixins/themeable'\n\nexport default Themeable.extend({\n  name: 'v-divider',\n\n  props: {\n    inset: Boolean,\n    vertical: Boolean,\n  },\n\n  render (h): VNode {\n    // WAI-ARIA attributes\n    let orientation\n    if (!this.$attrs.role || this.$attrs.role === 'separator') {\n      orientation = this.vertical ? 'vertical' : 'horizontal'\n    }\n    return h('hr', {\n      class: {\n        'v-divider': true,\n        'v-divider--inset': this.inset,\n        'v-divider--vertical': this.vertical,\n        ...this.themeClasses,\n      },\n      attrs: {\n        role: 'separator',\n        'aria-orientation': orientation,\n        ...this.$attrs,\n      },\n      on: this.$listeners,\n    })\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAO,gDAAP,C,CAKA;;AACA,OAAOA,SAAP,MAAsB,wBAAtB;AAEA,eAAeA,SAAS,CAACC,MAAV,CAAiB;EAC9BC,IAAI,EAAE,WADwB;EAG9BC,KAAK,EAAE;IACLC,KAAK,EAAEC,OADF;IAELC,QAAQ,EAAED;EAFL,CAHuB;EAQ9BE,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP;IACA,IAAIC,WAAJ;IACA,IAAI,CAAC,KAAKC,MAAL,CAAYC,IAAb,IAAqB,KAAKD,MAAL,CAAYC,IAAZ,KAAqB,WAA9C,EAA2D;MACzDF,WAAW,GAAG,KAAKH,QAAL,GAAgB,UAAhB,GAA6B,YAA3C;IACD;IACD,OAAOE,CAAC,CAAC,IAAD,EAAO;MACb,SAAAI,aAAA;QACE,aAAa,IADR;QAEL,oBAAoB,KAAKR,KAFpB;QAGL,uBAAuB,KAAKE;MAHvB,GAIF,KAAKO,YAAA,CALG;MAObC,KAAK,EAAAF,aAAA;QACHD,IAAI,EAAE,WADD;QAEL,oBAAoBF;MAFf,GAGF,KAAKC,MAAA,CAVG;MAYbK,EAAE,EAAE,KAAKC;IAZI,CAAP,CAAR;EAcD;AA5B6B,CAAjB,CAAf", "ignoreList": []}]}