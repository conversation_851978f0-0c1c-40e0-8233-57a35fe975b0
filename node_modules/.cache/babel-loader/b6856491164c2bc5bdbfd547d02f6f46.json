{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VProgressCircular/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VProgressCircular/index.js", "mtime": 1757335236921}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZQcm9ncmVzc0NpcmN1bGFyIGZyb20gJy4vVlByb2dyZXNzQ2lyY3VsYXInOwpleHBvcnQgeyBWUHJvZ3Jlc3NDaXJjdWxhciB9OwpleHBvcnQgZGVmYXVsdCBWUHJvZ3Jlc3NDaXJjdWxhcjs="}, {"version": 3, "names": ["VProgressCircular"], "sources": ["../../../src/components/VProgressCircular/index.ts"], "sourcesContent": ["import VProgressCircular from './VProgressCircular'\n\nexport { VProgressCircular }\nexport default VProgressCircular\n"], "mappings": "AAAA,OAAOA,iBAAP,MAA8B,qBAA9B;AAEA,SAASA,iBAAT;AACA,eAAeA,iBAAf", "ignoreList": []}]}