{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/fa.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/fa.js", "mtime": 1757335235772}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/fa.ts"], "sourcesContent": ["export default {\n  badge: 'نشان',\n  close: 'بستن',\n  dataIterator: {\n    noResultsText: 'نتیجه‌ای یافت نشد',\n    loadingText: 'در حال بارگذاری...',\n  },\n  dataTable: {\n    itemsPerPageText: 'ردیف در صفحه:',\n    ariaLabel: {\n      sortDescending: 'مرتب‌سازی نزولی',\n      sortAscending: 'مرتب‌سازی صعودی',\n      sortNone: 'بدون مرتب‌سازی',\n      activateNone: 'غیرفعال‌سازی مرتب‌سازی',\n      activateDescending: 'غیرفعال‌سازی مرتب‌سازی نزولی',\n      activateAscending: 'غیرفعال‌سازی مرتب‌سازی صعودی',\n    },\n    sortBy: 'مرتب‌سازی براساس',\n  },\n  dataFooter: {\n    itemsPerPageText: 'ردیف در صفحه:',\n    itemsPerPageAll: 'همه',\n    nextPage: 'صفحه‌ی بعد',\n    prevPage: 'صفحه‌ی قبل',\n    firstPage: 'صفحه‌ی یکم',\n    lastPage: 'صفحه‌ی آخر',\n    pageText: '{0} تا {1} از {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} انتخاب شده',\n    nextMonthAriaLabel: 'ماه بعد',\n    nextYearAriaLabel: 'سال بعد',\n    prevMonthAriaLabel: 'ماه قبل',\n    prevYearAriaLabel: 'سال قبل',\n  },\n  noDataText: 'داده‌ای موجود نیست',\n  carousel: {\n    prev: 'اسلاید قبلی',\n    next: 'اسلاید بعدی',\n    ariaLabel: {\n      delimiter: 'اسلاید {0} از {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{بیشتر {0',\n  },\n  fileInput: {\n    counter: '{0} پرونده',\n    counterSize: '{0} پرونده ({1} در کل)',\n  },\n  timePicker: {\n    am: 'قبل از ظهر',\n    pm: 'بعد از ظهر',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'جهت یابی صفحه',\n      next: 'صفحه بعد',\n      previous: 'صفحه قبلی',\n      page: 'برو صفحه {0}',\n      currentPage: '{0} صفحه فعلی ، صفحه',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,MADM;EAEbC,KAAK,EAAE,MAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,mBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,eADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,iBADP;MAETC,aAAa,EAAE,iBAFN;MAGTC,QAAQ,EAAE,gBAHD;MAITC,YAAY,EAAE,wBAJL;MAKTC,kBAAkB,EAAE,8BALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,eADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,YAHA;IAIVC,QAAQ,EAAE,YAJA;IAKVC,SAAS,EAAE,YALD;IAMVC,QAAQ,EAAE,YANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,gBADL;IAEVC,kBAAkB,EAAE,SAFV;IAGVC,iBAAiB,EAAE,SAHT;IAIVC,kBAAkB,EAAE,SAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,oBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,aADE;IAERC,IAAI,EAAE,aAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,YADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,YADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,eADA;MAETX,IAAI,EAAE,UAFG;MAGTY,QAAQ,EAAE,WAHD;MAITC,IAAI,EAAE,cAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}