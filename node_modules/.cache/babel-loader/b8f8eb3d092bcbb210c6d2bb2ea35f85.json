{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ja.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ja.js", "mtime": 1757335237317}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/ja.ts"], "sourcesContent": ["export default {\n  badge: 'バッジ',\n  close: '閉じる',\n  dataIterator: {\n    noResultsText: '検索結果が見つかりません。',\n    loadingText: '項目をロード中です...',\n  },\n  dataTable: {\n    itemsPerPageText: '1ページあたりの行数：',\n    ariaLabel: {\n      sortDescending: '降順の並び替え。',\n      sortAscending: '昇順の並び替え。',\n      sortNone: 'ソートされていません。',\n      activateNone: 'ソートを削除するには有効にしてください。',\n      activateDescending: '降順の並び替えのためには有効にしてください。',\n      activateAscending: '昇順のソートのためには有効にしてください。',\n    },\n    sortBy: 'ソート方式',\n  },\n  dataFooter: {\n    itemsPerPageText: '1ページあたりの件数：',\n    itemsPerPageAll: 'すべて',\n    nextPage: '次のページ',\n    prevPage: '前のページ',\n    firstPage: '最初のページ',\n    lastPage: '最後のページ',\n    pageText: '{0}-{1} 件目 / {2}件',\n  },\n  datePicker: {\n    itemsSelected: '{0}日付選択',\n    nextMonthAriaLabel: '来月',\n    nextYearAriaLabel: '来年',\n    prevMonthAriaLabel: '前月',\n    prevYearAriaLabel: '前年',\n  },\n  noDataText: 'データはありません。',\n  carousel: {\n    prev: '前のビジュアル',\n    next: '次のビジュアル',\n    ariaLabel: {\n      delimiter: 'カルーセルのスライド {0}件目 / {1}件',\n    },\n  },\n  calendar: {\n    moreEvents: 'さらに{0}',\n  },\n  fileInput: {\n    counter: '{0} ファイル',\n    counterSize: '{0} ファイル (合計 {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'ページネーションナビゲーション',\n      next: '次のページ',\n      previous: '前のページ',\n      page: '{0}ページ目に移動',\n      currentPage: '現在のページ、ページ {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: '評価 {1} のうち {0}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,KADM;EAEbC,KAAK,EAAE,KAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,eADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,aADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,UADP;MAETC,aAAa,EAAE,UAFN;MAGTC,QAAQ,EAAE,aAHD;MAITC,YAAY,EAAE,sBAJL;MAKTC,kBAAkB,EAAE,wBALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,aADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,OAHA;IAIVC,QAAQ,EAAE,OAJA;IAKVC,SAAS,EAAE,QALD;IAMVC,QAAQ,EAAE,QANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,SADL;IAEVC,kBAAkB,EAAE,IAFV;IAGVC,iBAAiB,EAAE,IAHT;IAIVC,kBAAkB,EAAE,IAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,YAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,SADE;IAERC,IAAI,EAAE,SAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,UADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,iBADA;MAETX,IAAI,EAAE,OAFG;MAGTY,QAAQ,EAAE,OAHD;MAITC,IAAI,EAAE,YAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}