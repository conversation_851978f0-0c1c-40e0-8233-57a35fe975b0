{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VProgressLinear/VProgressLinear.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VProgressLinear/VProgressLinear.js", "mtime": 1757335238824}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VFadeTransition", "VSlideXTransition", "intersect", "Colorable", "factory", "PositionableFactory", "Proxyable", "Themeable", "convertToUnit", "getSlot", "mixins", "baseMixins", "extend", "name", "directives", "props", "active", "type", "Boolean", "backgroundColor", "String", "backgroundOpacity", "Number", "bufferValue", "color", "height", "indeterminate", "query", "reverse", "rounded", "stream", "striped", "value", "data", "internalLazyValue", "isVisible", "computed", "__cachedBackground", "$createElement", "setBackgroundColor", "staticClass", "style", "backgroundStyle", "__cachedBar", "computedTransition", "__cachedBarType", "__cachedIndeterminate", "__cachedDeterminate", "__cachedBuffer", "styles", "width", "normalizedValue", "genProgressBar", "__cachedStream", "setTextColor", "normalizedBuffer", "_parseFloat", "_defineProperty", "opacity", "isReversed", "Math", "max", "classes", "_objectSpread", "absolute", "fixed", "reactive", "themeClasses", "$vuetify", "rtl", "_reverseInstanceProperty", "normalize", "$listeners", "change", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slot", "genListeners", "listeners", "click", "onClick", "e", "_this$$el$getBounding", "$el", "getBoundingClientRect", "internalValue", "offsetX", "onObserve", "entries", "observer", "isIntersecting", "render", "h", "attrs", "role", "undefined", "bottom", "top", "on"], "sources": ["../../../src/components/VProgressLinear/VProgressLinear.ts"], "sourcesContent": ["import './VProgressLinear.sass'\n\n// Components\nimport {\n  VFadeTransition,\n  VSlideXTransition,\n} from '../transitions'\n\n// Directives\nimport intersect from '../../directives/intersect'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport { factory as PositionableFactory } from '../../mixins/positionable'\nimport Proxyable from '../../mixins/proxyable'\nimport Themeable from '../../mixins/themeable'\n\n// Utilities\nimport { convertToUnit, getSlot } from '../../util/helpers'\nimport mixins from '../../util/mixins'\n\n// Types\nimport { FunctionalComponentOptions } from 'vue/types'\nimport { VNode } from 'vue'\n\nconst baseMixins = mixins(\n  Colorable,\n  PositionableFactory(['absolute', 'fixed', 'top', 'bottom']),\n  Proxyable,\n  Themeable\n)\n\n/* @vue/component */\nexport default baseMixins.extend({\n  name: 'v-progress-linear',\n\n  directives: { intersect },\n\n  props: {\n    active: {\n      type: Boolean,\n      default: true,\n    },\n    backgroundColor: {\n      type: String,\n      default: null,\n    },\n    backgroundOpacity: {\n      type: [Number, String],\n      default: null,\n    },\n    bufferValue: {\n      type: [Number, String],\n      default: 100,\n    },\n    color: {\n      type: String,\n      default: 'primary',\n    },\n    height: {\n      type: [Number, String],\n      default: 4,\n    },\n    indeterminate: Boolean,\n    query: Boolean,\n    reverse: Boolean,\n    rounded: Boolean,\n    stream: Boolean,\n    striped: Boolean,\n    value: {\n      type: [Number, String],\n      default: 0,\n    },\n  },\n\n  data () {\n    return {\n      internalLazyValue: this.value || 0,\n      isVisible: true,\n    }\n  },\n\n  computed: {\n    __cachedBackground (): VNode {\n      return this.$createElement('div', this.setBackgroundColor(this.backgroundColor || this.color, {\n        staticClass: 'v-progress-linear__background',\n        style: this.backgroundStyle,\n      }))\n    },\n    __cachedBar (): VNode {\n      return this.$createElement(this.computedTransition, [this.__cachedBarType])\n    },\n    __cachedBarType (): VNode {\n      return this.indeterminate ? this.__cachedIndeterminate : this.__cachedDeterminate\n    },\n    __cachedBuffer (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-progress-linear__buffer',\n        style: this.styles,\n      })\n    },\n    __cachedDeterminate (): VNode {\n      return this.$createElement('div', this.setBackgroundColor(this.color, {\n        staticClass: `v-progress-linear__determinate`,\n        style: {\n          width: convertToUnit(this.normalizedValue, '%'),\n        },\n      }))\n    },\n    __cachedIndeterminate (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-progress-linear__indeterminate',\n        class: {\n          'v-progress-linear__indeterminate--active': this.active,\n        },\n      }, [\n        this.genProgressBar('long'),\n        this.genProgressBar('short'),\n      ])\n    },\n    __cachedStream (): VNode | null {\n      if (!this.stream) return null\n\n      return this.$createElement('div', this.setTextColor(this.color, {\n        staticClass: 'v-progress-linear__stream',\n        style: {\n          width: convertToUnit(100 - this.normalizedBuffer, '%'),\n        },\n      }))\n    },\n    backgroundStyle (): object {\n      const backgroundOpacity = this.backgroundOpacity == null\n        ? (this.backgroundColor ? 1 : 0.3)\n        : parseFloat(this.backgroundOpacity)\n\n      return {\n        opacity: backgroundOpacity,\n        [this.isReversed ? 'right' : 'left']: convertToUnit(this.normalizedValue, '%'),\n        width: convertToUnit(Math.max(0, this.normalizedBuffer - this.normalizedValue), '%'),\n      }\n    },\n    classes (): object {\n      return {\n        'v-progress-linear--absolute': this.absolute,\n        'v-progress-linear--fixed': this.fixed,\n        'v-progress-linear--query': this.query,\n        'v-progress-linear--reactive': this.reactive,\n        'v-progress-linear--reverse': this.isReversed,\n        'v-progress-linear--rounded': this.rounded,\n        'v-progress-linear--striped': this.striped,\n        'v-progress-linear--visible': this.isVisible,\n        ...this.themeClasses,\n      }\n    },\n    computedTransition (): FunctionalComponentOptions {\n      return this.indeterminate ? VFadeTransition : VSlideXTransition\n    },\n    isReversed (): boolean {\n      return this.$vuetify.rtl !== this.reverse\n    },\n    normalizedBuffer (): number {\n      return this.normalize(this.bufferValue)\n    },\n    normalizedValue (): number {\n      return this.normalize(this.internalLazyValue)\n    },\n    reactive (): boolean {\n      return Boolean(this.$listeners.change)\n    },\n    styles (): object {\n      const styles: Record<string, any> = {}\n\n      if (!this.active) {\n        styles.height = 0\n      }\n\n      if (!this.indeterminate && parseFloat(this.normalizedBuffer) !== 100) {\n        styles.width = convertToUnit(this.normalizedBuffer, '%')\n      }\n\n      return styles\n    },\n  },\n\n  methods: {\n    genContent () {\n      const slot = getSlot(this, 'default', { value: this.internalLazyValue })\n\n      if (!slot) return null\n\n      return this.$createElement('div', {\n        staticClass: 'v-progress-linear__content',\n      }, slot)\n    },\n    genListeners () {\n      const listeners = this.$listeners\n\n      if (this.reactive) {\n        listeners.click = this.onClick\n      }\n\n      return listeners\n    },\n    genProgressBar (name: 'long' | 'short') {\n      return this.$createElement('div', this.setBackgroundColor(this.color, {\n        staticClass: 'v-progress-linear__indeterminate',\n        class: {\n          [name]: true,\n        },\n      }))\n    },\n    onClick (e: MouseEvent) {\n      if (!this.reactive) return\n\n      const { width } = this.$el.getBoundingClientRect()\n\n      this.internalValue = e.offsetX / width * 100\n    },\n    onObserve (entries: IntersectionObserverEntry[], observer: IntersectionObserver, isIntersecting: boolean) {\n      this.isVisible = isIntersecting\n    },\n    normalize (value: string | number) {\n      if (value < 0) return 0\n      if (value > 100) return 100\n      return parseFloat(value)\n    },\n  },\n\n  render (h): VNode {\n    const data = {\n      staticClass: 'v-progress-linear',\n      attrs: {\n        role: 'progressbar',\n        'aria-valuemin': 0,\n        'aria-valuemax': this.normalizedBuffer,\n        'aria-valuenow': this.indeterminate ? undefined : this.normalizedValue,\n      },\n      class: this.classes,\n      directives: [{\n        name: 'intersect',\n        value: this.onObserve,\n      }],\n      style: {\n        bottom: this.bottom ? 0 : undefined,\n        height: this.active ? convertToUnit(this.height) : 0,\n        top: this.top ? 0 : undefined,\n      },\n      on: this.genListeners(),\n    }\n\n    return h('div', data, [\n      this.__cachedStream,\n      this.__cachedBackground,\n      this.__cachedBuffer,\n      this.__cachedBar,\n      this.genContent(),\n    ])\n  },\n})\n"], "mappings": ";;;;;;AAAA,OAAO,8DAAP,C,CAEA;;AACA,SACEA,eADF,EAEEC,iBAFF,QAGO,gBAHP,C,CAKA;;AACA,OAAOC,SAAP,MAAsB,4BAAtB,C,CAEA;;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,SAASC,OAAO,IAAIC,mBAApB,QAA+C,2BAA/C;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,SAASC,aAAT,EAAwBC,OAAxB,QAAuC,oBAAvC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAMA,IAAMC,UAAU,GAAGD,MAAM,CACvBP,SADuB,EAEvBE,mBAAmB,CAAC,CAAC,UAAD,EAAa,OAAb,EAAsB,KAAtB,EAA6B,QAA7B,CAAD,CAFI,EAGvBC,SAHuB,EAIvBC,SAJuB,CAAzB;AAOA;;AACA,eAAeI,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,mBADyB;EAG/BC,UAAU,EAAE;IAAEZ,SAAA,EAAAA;EAAF,CAHmB;EAK/Ba,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,OADA;MAEN,WAAS;IAFH,CADH;IAKLC,eAAe,EAAE;MACfF,IAAI,EAAEG,MADS;MAEf,WAAS;IAFM,CALZ;IASLC,iBAAiB,EAAE;MACjBJ,IAAI,EAAE,CAACK,MAAD,EAASF,MAAT,CADW;MAEjB,WAAS;IAFQ,CATd;IAaLG,WAAW,EAAE;MACXN,IAAI,EAAE,CAACK,MAAD,EAASF,MAAT,CADK;MAEX,WAAS;IAFE,CAbR;IAiBLI,KAAK,EAAE;MACLP,IAAI,EAAEG,MADD;MAEL,WAAS;IAFJ,CAjBF;IAqBLK,MAAM,EAAE;MACNR,IAAI,EAAE,CAACK,MAAD,EAASF,MAAT,CADA;MAEN,WAAS;IAFH,CArBH;IAyBLM,aAAa,EAAER,OAzBV;IA0BLS,KAAK,EAAET,OA1BF;IA2BLU,OAAO,EAAEV,OA3BJ;IA4BLW,OAAO,EAAEX,OA5BJ;IA6BLY,MAAM,EAAEZ,OA7BH;IA8BLa,OAAO,EAAEb,OA9BJ;IA+BLc,KAAK,EAAE;MACLf,IAAI,EAAE,CAACK,MAAD,EAASF,MAAT,CADD;MAEL,WAAS;IAFJ;EA/BF,CALwB;EA0C/Ba,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,iBAAiB,EAAE,KAAKF,KAAL,IAAc,CAD5B;MAELG,SAAS,EAAE;IAFN,CAAP;EAID,CA/C8B;EAiD/BC,QAAQ,EAAE;IACRC,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B,KAAKC,kBAAL,CAAwB,KAAKpB,eAAL,IAAwB,KAAKK,KAArD,EAA4D;QAC5FgB,WAAW,EAAE,+BAD+E;QAE5FC,KAAK,EAAE,KAAKC;MAFgF,CAA5D,CAA3B,CAAP;IAID,CANO;IAORC,WAAW,WAAXA,WAAWA,CAAA;MACT,OAAO,KAAKL,cAAL,CAAoB,KAAKM,kBAAzB,EAA6C,CAAC,KAAKC,eAAN,CAA7C,CAAP;IACD,CATO;IAURA,eAAe,WAAfA,eAAeA,CAAA;MACb,OAAO,KAAKnB,aAAL,GAAqB,KAAKoB,qBAA1B,GAAkD,KAAKC,mBAA9D;IACD,CAZO;IAaRC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,KAAKV,cAAL,CAAoB,KAApB,EAA2B;QAChCE,WAAW,EAAE,2BADmB;QAEhCC,KAAK,EAAE,KAAKQ;MAFoB,CAA3B,CAAP;IAID,CAlBO;IAmBRF,mBAAmB,WAAnBA,mBAAmBA,CAAA;MACjB,OAAO,KAAKT,cAAL,CAAoB,KAApB,EAA2B,KAAKC,kBAAL,CAAwB,KAAKf,KAA7B,EAAoC;QACpEgB,WAAW,kCADyD;QAEpEC,KAAK,EAAE;UACLS,KAAK,EAAE1C,aAAa,CAAC,KAAK2C,eAAN,EAAuB,GAAvB;QADf;MAF6D,CAApC,CAA3B,CAAP;IAMD,CA1BO;IA2BRL,qBAAqB,WAArBA,qBAAqBA,CAAA;MACnB,OAAO,KAAKR,cAAL,CAAoB,KAApB,EAA2B;QAChCE,WAAW,EAAE,kCADmB;QAEhC,SAAO;UACL,4CAA4C,KAAKxB;QAD5C;MAFyB,CAA3B,EAKJ,CACD,KAAKoC,cAAL,CAAoB,MAApB,CADC,EAED,KAAKA,cAAL,CAAoB,OAApB,CAFC,CALI,CAAP;IASD,CArCO;IAsCRC,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAI,CAAC,KAAKvB,MAAV,EAAkB,OAAO,IAAP;MAElB,OAAO,KAAKQ,cAAL,CAAoB,KAApB,EAA2B,KAAKgB,YAAL,CAAkB,KAAK9B,KAAvB,EAA8B;QAC9DgB,WAAW,EAAE,2BADiD;QAE9DC,KAAK,EAAE;UACLS,KAAK,EAAE1C,aAAa,CAAC,MAAM,KAAK+C,gBAAZ,EAA8B,GAA9B;QADf;MAFuD,CAA9B,CAA3B,CAAP;IAMD,CA/CO;IAgDRb,eAAe,WAAfA,eAAeA,CAAA;MACb,IAAMrB,iBAAiB,GAAG,KAAKA,iBAAL,IAA0B,IAA1B,GACrB,KAAKF,eAAL,GAAuB,CAAvB,GAA2B,GADN,GAEtBqC,WAAA,CAAW,KAAKnC,iBAAN,CAFd;MAIA,OAAAoC,eAAA,CAAAA,eAAA;QACEC,OAAO,EAAErC;MADJ,GAEJ,KAAKsC,UAAL,GAAkB,OAAlB,GAA4B,MAA7B,EAAsCnD,aAAa,CAAC,KAAK2C,eAAN,EAAuB,GAAvB,CAF9C,YAGE3C,aAAa,CAACoD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKN,gBAAL,GAAwB,KAAKJ,eAAzC,CAAD,EAA4D,GAA5D;IAEvB,CA1DO;IA2DRW,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA;QACE,+BAA+B,KAAKC,QAD/B;QAEL,4BAA4B,KAAKC,KAF5B;QAGL,4BAA4B,KAAKtC,KAH5B;QAIL,+BAA+B,KAAKuC,QAJ/B;QAKL,8BAA8B,KAAKP,UAL9B;QAML,8BAA8B,KAAK9B,OAN9B;QAOL,8BAA8B,KAAKE,OAP9B;QAQL,8BAA8B,KAAKI;MAR9B,GASF,KAAKgC,YAAA;IAEX,CAvEO;IAwERvB,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,OAAO,KAAKlB,aAAL,GAAqB1B,eAArB,GAAuCC,iBAA9C;IACD,CA1EO;IA2ER0D,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKS,QAAL,CAAcC,GAAd,KAAAC,wBAAA,CAAsB,KAA7B;IACD,CA7EO;IA8ERf,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,OAAO,KAAKgB,SAAL,CAAe,KAAKhD,WAApB,CAAP;IACD,CAhFO;IAiFR4B,eAAe,WAAfA,eAAeA,CAAA;MACb,OAAO,KAAKoB,SAAL,CAAe,KAAKrC,iBAApB,CAAP;IACD,CAnFO;IAoFRgC,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAOhD,OAAO,CAAC,KAAKsD,UAAL,CAAgBC,MAAjB,CAAd;IACD,CAtFO;IAuFRxB,MAAM,WAANA,MAAMA,CAAA;MACJ,IAAMA,MAAM,GAAwB,EAApC;MAEA,IAAI,CAAC,KAAKjC,MAAV,EAAkB;QAChBiC,MAAM,CAACxB,MAAP,GAAgB,CAAhB;MACD;MAED,IAAI,CAAC,KAAKC,aAAN,IAAuB8B,WAAA,CAAW,KAAKD,gBAAN,CAAV,KAAsC,GAAjE,EAAsE;QACpEN,MAAM,CAACC,KAAP,GAAe1C,aAAa,CAAC,KAAK+C,gBAAN,EAAwB,GAAxB,CAA5B;MACD;MAED,OAAON,MAAP;IACD;EAnGO,CAjDqB;EAuJ/ByB,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAMC,IAAI,GAAGnE,OAAO,CAAC,IAAD,EAAO,SAAP,EAAkB;QAAEuB,KAAK,EAAE,KAAKE;MAAd,CAAlB,CAApB;MAEA,IAAI,CAAC0C,IAAL,EAAW,OAAO,IAAP;MAEX,OAAO,KAAKtC,cAAL,CAAoB,KAApB,EAA2B;QAChCE,WAAW,EAAE;MADmB,CAA3B,EAEJoC,IAFI,CAAP;IAGD,CATM;IAUPC,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAMC,SAAS,GAAG,KAAKN,UAAvB;MAEA,IAAI,KAAKN,QAAT,EAAmB;QACjBY,SAAS,CAACC,KAAV,GAAkB,KAAKC,OAAvB;MACD;MAED,OAAOF,SAAP;IACD,CAlBM;IAmBP1B,cAAc,WAAdA,cAAcA,CAAEvC,IAAF,EAAwB;MACpC,OAAO,KAAKyB,cAAL,CAAoB,KAApB,EAA2B,KAAKC,kBAAL,CAAwB,KAAKf,KAA7B,EAAoC;QACpEgB,WAAW,EAAE,kCADuD;QAEpE,SAAAiB,eAAA,KACG5C,IAAD,EAAQ;MAH0D,CAApC,CAA3B,CAAP;IAMD,CA1BM;IA2BPmE,OAAO,WAAPA,OAAOA,CAAEC,CAAF,EAAe;MACpB,IAAI,CAAC,KAAKf,QAAV,EAAoB;MAEpB,IAAAgB,qBAAA,GAAkB,KAAKC,GAAL,CAASC,qBAAT,EAAlB;QAAQlC,KAAA,GAAAgC,qBAAA,CAAAhC,KAAA;MAER,KAAKmC,aAAL,GAAqBJ,CAAC,CAACK,OAAF,GAAYpC,KAAZ,GAAoB,GAAzC;IACD,CAjCM;IAkCPqC,SAAS,WAATA,SAASA,CAAEC,OAAF,EAAwCC,QAAxC,EAAwEC,cAAxE,EAA+F;MACtG,KAAKvD,SAAL,GAAiBuD,cAAjB;IACD,CApCM;IAqCPnB,SAAS,WAATA,SAASA,CAAEvC,KAAF,EAAwB;MAC/B,IAAIA,KAAK,GAAG,CAAZ,EAAe,OAAO,CAAP;MACf,IAAIA,KAAK,GAAG,GAAZ,EAAiB,OAAO,GAAP;MACjB,OAAOwB,WAAA,CAAWxB,KAAD,CAAjB;IACD;EAzCM,CAvJsB;EAmM/B2D,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAM3D,IAAI,GAAG;MACXO,WAAW,EAAE,mBADF;MAEXqD,KAAK,EAAE;QACLC,IAAI,EAAE,aADD;QAEL,iBAAiB,CAFZ;QAGL,iBAAiB,KAAKvC,gBAHjB;QAIL,iBAAiB,KAAK7B,aAAL,GAAqBqE,SAArB,GAAiC,KAAK5C;MAJlD,CAFI;MAQX,SAAO,KAAKW,OARD;MASXhD,UAAU,EAAE,CAAC;QACXD,IAAI,EAAE,WADK;QAEXmB,KAAK,EAAE,KAAKuD;MAFD,CAAD,CATD;MAaX9C,KAAK,EAAE;QACLuD,MAAM,EAAE,KAAKA,MAAL,GAAc,CAAd,GAAkBD,SADrB;QAELtE,MAAM,EAAE,KAAKT,MAAL,GAAcR,aAAa,CAAC,KAAKiB,MAAN,CAA3B,GAA2C,CAF9C;QAGLwE,GAAG,EAAE,KAAKA,GAAL,GAAW,CAAX,GAAeF;MAHf,CAbI;MAkBXG,EAAE,EAAE,KAAKrB,YAAL;IAlBO,CAAb;IAqBA,OAAOe,CAAC,CAAC,KAAD,EAAQ3D,IAAR,EAAc,CACpB,KAAKoB,cADe,EAEpB,KAAKhB,kBAFe,EAGpB,KAAKW,cAHe,EAIpB,KAAKL,WAJe,EAKpB,KAAKgC,UAAL,EALoB,CAAd,CAAR;EAOD;AAhO8B,CAAlB,CAAf", "ignoreList": []}]}