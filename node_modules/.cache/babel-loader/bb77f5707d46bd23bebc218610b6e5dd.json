{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/et.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/et.js", "mtime": 1757335235655}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/et.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Vastavaid kirjeid ei leitud',\n    loadingText: 'Andmeid laaditakse...',\n  },\n  dataTable: {\n    itemsPerPageText: '<PERSON>id<PERSON> le<PERSON>:',\n    ariaLabel: {\n      sortDescending: 'Kahanevalt sorteeritud.',\n      sortAscending: 'Kasvavalt sorteeritud.',\n      sortNone: 'Ei ole sorteeritud.',\n      activateNone: 'Vajuta uuesti sorteerimise eemaldamiseks.',\n      activateDescending: 'Vajuta uuesti, et sorteerida kahanevalt.',\n      activateAscending: 'Vajuta kasvavalt sorteerimiseks.',\n    },\n    sortBy: 'Sorteerimise alus',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Kir<PERSON><PERSON> lehe<PERSON>l:',\n    itemsPerPageAll: 'Kõik',\n    nextPage: 'Järgmine lehekülg',\n    prevPage: 'Eelmine lehekülg',\n    firstPage: '<PERSON><PERSON><PERSON><PERSON> le<PERSON>',\n    lastPage: '<PERSON><PERSON><PERSON><PERSON> le<PERSON>',\n    pageText: '{0}-{1} {2}st',\n  },\n  datePicker: {\n    itemsSelected: '{0} valitud',\n    nextMonthAriaLabel: 'Järgmine kuu',\n    nextYearAriaLabel: 'Järgmine aasta',\n    prevMonthAriaLabel: 'Eelmine kuu',\n    prevYearAriaLabel: 'Eelmine aasta',\n  },\n  noDataText: 'Andmed puuduvad',\n  carousel: {\n    prev: 'Eelmine visuaalne',\n    next: 'Järgmine visuaalne',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} veel',\n  },\n  fileInput: {\n    counter: '{0} faili',\n    counterSize: '{0} faili (kokku {1})',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Pagination Navigation',\n      next: 'Järgmine lehekülg',\n      previous: 'Eelmine lehekülg',\n      page: 'Mine lehele {0}',\n      currentPage: 'Praegune leht, leht {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,MADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,6BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,kBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,yBADP;MAETC,aAAa,EAAE,wBAFN;MAGTC,QAAQ,EAAE,qBAHD;MAITC,YAAY,EAAE,2CAJL;MAKTC,kBAAkB,EAAE,0CALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,mBAHA;IAIVC,QAAQ,EAAE,kBAJA;IAKVC,SAAS,EAAE,kBALD;IAMVC,QAAQ,EAAE,kBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,aADL;IAEVC,kBAAkB,EAAE,cAFV;IAGVC,iBAAiB,EAAE,gBAHT;IAIVC,kBAAkB,EAAE,aAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,iBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,mBADE;IAERC,IAAI,EAAE,oBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,uBADA;MAETX,IAAI,EAAE,mBAFG;MAGTY,QAAQ,EAAE,kBAHD;MAITC,IAAI,EAAE,iBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}