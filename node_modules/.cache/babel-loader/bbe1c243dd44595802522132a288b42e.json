{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/BackgroundDialog.vue?vue&type=template&id=833f155c", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/BackgroundDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "showDialog", "attrs", "model", "value", "callback", "$$v", "expression", "staticClass", "_v", "backgroundTab", "cols", "md", "label", "elementParams", "url", "$set", "angle", "_n", "color", "text", "on", "click", "addBackground", "_e", "outlined", "setBackgroundImageAngle", "$event", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/BackgroundDialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.showDialog\n    ? _c(\n        \"v-dialog\",\n        {\n          attrs: { \"max-width\": \"1000\" },\n          model: {\n            value: _vm.showDialog,\n            callback: function ($$v) {\n              _vm.showDialog = $$v\n            },\n            expression: \"showDialog\",\n          },\n        },\n        [\n          _c(\n            \"v-card\",\n            [\n              _c(\"v-card-title\", { staticClass: \"headline lighten-2\" }, [\n                _vm._v(\" 背景 \"),\n              ]),\n              _c(\n                \"v-card-text\",\n                [\n                  _c(\n                    \"v-tabs\",\n                    {\n                      model: {\n                        value: _vm.backgroundTab,\n                        callback: function ($$v) {\n                          _vm.backgroundTab = $$v\n                        },\n                        expression: \"backgroundTab\",\n                      },\n                    },\n                    [\n                      _c(\"v-tab\", [_vm._v(\"H5背景\")]),\n                      _c(\"v-tab\", [_vm._v(\"图片背景\")]),\n                      _c(\"v-tab\", [_vm._v(\"修改图片背景角度\")]),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"v-row\",\n                    [\n                      _c(\n                        \"v-col\",\n                        { attrs: { cols: \"12\", md: \"12\" } },\n                        [\n                          _c(\n                            \"v-tabs-items\",\n                            {\n                              model: {\n                                value: _vm.backgroundTab,\n                                callback: function ($$v) {\n                                  _vm.backgroundTab = $$v\n                                },\n                                expression: \"backgroundTab\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"v-tab-item\",\n                                [\n                                  _c(\"v-text-field\", {\n                                    attrs: {\n                                      label: \"H5背景URL\",\n                                      \"prepend-icon\": \"mdi-language-html5\",\n                                    },\n                                    model: {\n                                      value:\n                                        _vm.elementParams[\"backgroundH5\"].url,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.elementParams[\"backgroundH5\"],\n                                          \"url\",\n                                          $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"elementParams['backgroundH5'].url\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"v-tab-item\",\n                                [\n                                  _c(\"v-text-field\", {\n                                    attrs: {\n                                      label: \"背景图片URL\",\n                                      \"prepend-icon\": \"mdi-image\",\n                                    },\n                                    model: {\n                                      value:\n                                        _vm.elementParams[\"backgroundImg\"].url,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.elementParams[\"backgroundImg\"],\n                                          \"url\",\n                                          $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"elementParams['backgroundImg'].url\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"v-tab-item\",\n                                [\n                                  _c(\"v-text-field\", {\n                                    attrs: { label: \"背景图片角度\" },\n                                    model: {\n                                      value:\n                                        _vm.elementParams[\"backgroundImg\"]\n                                          .angle,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.elementParams[\"backgroundImg\"],\n                                          \"angle\",\n                                          _vm._n($$v)\n                                        )\n                                      },\n                                      expression:\n                                        \"elementParams['backgroundImg'].angle\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _c(\"v-divider\"),\n              _c(\n                \"v-card-actions\",\n                [\n                  _c(\"v-spacer\"),\n                  _vm.backgroundTab < 2\n                    ? _c(\n                        \"v-btn\",\n                        {\n                          attrs: { color: \"primary\", text: \"\" },\n                          on: { click: _vm.addBackground },\n                        },\n                        [_vm._v(\"添加\")]\n                      )\n                    : _vm._e(),\n                  _vm.backgroundTab === 2\n                    ? _c(\n                        \"v-btn\",\n                        {\n                          staticClass: \"ma-2\",\n                          attrs: { outlined: \"\", color: \"indigo\" },\n                          on: { click: _vm.setBackgroundImageAngle },\n                        },\n                        [_vm._v(\" 设置背景图片角度 \")]\n                      )\n                    : _vm._e(),\n                  _c(\n                    \"v-btn\",\n                    {\n                      attrs: { text: \"\" },\n                      on: {\n                        click: function ($event) {\n                          _vm.showDialog = false\n                        },\n                      },\n                    },\n                    [_vm._v(\"关闭\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,UAAU,GACjBF,EAAE,CACA,UAAU,EACV;IACEG,KAAK,EAAE;MAAE,WAAW,EAAE;IAAO,CAAC;IAC9BC,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACG,UAAU;MACrBI,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBR,GAAG,CAACG,UAAU,GAAGK,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACER,EAAE,CACA,QAAQ,EACR,CACEA,EAAE,CAAC,cAAc,EAAE;IAAES,WAAW,EAAE;EAAqB,CAAC,EAAE,CACxDV,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFV,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CACA,QAAQ,EACR;IACEI,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACY,aAAa;MACxBL,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBR,GAAG,CAACY,aAAa,GAAGJ,GAAG;MACzB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACER,EAAE,CAAC,OAAO,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC7BV,EAAE,CAAC,OAAO,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC7BV,EAAE,CAAC,OAAO,EAAE,CAACD,GAAG,CAACW,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAClC,EACD,CACF,CAAC,EACDV,EAAE,CACA,OAAO,EACP,CACEA,EAAE,CACA,OAAO,EACP;IAAEG,KAAK,EAAE;MAAES,IAAI,EAAE,IAAI;MAAEC,EAAE,EAAE;IAAK;EAAE,CAAC,EACnC,CACEb,EAAE,CACA,cAAc,EACd;IACEI,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACY,aAAa;MACxBL,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBR,GAAG,CAACY,aAAa,GAAGJ,GAAG;MACzB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACER,EAAE,CACA,YAAY,EACZ,CACEA,EAAE,CAAC,cAAc,EAAE;IACjBG,KAAK,EAAE;MACLW,KAAK,EAAE,SAAS;MAChB,cAAc,EAAE;IAClB,CAAC;IACDV,KAAK,EAAE;MACLC,KAAK,EACHN,GAAG,CAACgB,aAAa,CAAC,cAAc,CAAC,CAACC,GAAG;MACvCV,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBR,GAAG,CAACkB,IAAI,CACNlB,GAAG,CAACgB,aAAa,CAAC,cAAc,CAAC,EACjC,KAAK,EACLR,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDR,EAAE,CACA,YAAY,EACZ,CACEA,EAAE,CAAC,cAAc,EAAE;IACjBG,KAAK,EAAE;MACLW,KAAK,EAAE,SAAS;MAChB,cAAc,EAAE;IAClB,CAAC;IACDV,KAAK,EAAE;MACLC,KAAK,EACHN,GAAG,CAACgB,aAAa,CAAC,eAAe,CAAC,CAACC,GAAG;MACxCV,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBR,GAAG,CAACkB,IAAI,CACNlB,GAAG,CAACgB,aAAa,CAAC,eAAe,CAAC,EAClC,KAAK,EACLR,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDR,EAAE,CACA,YAAY,EACZ,CACEA,EAAE,CAAC,cAAc,EAAE;IACjBG,KAAK,EAAE;MAAEW,KAAK,EAAE;IAAS,CAAC;IAC1BV,KAAK,EAAE;MACLC,KAAK,EACHN,GAAG,CAACgB,aAAa,CAAC,eAAe,CAAC,CAC/BG,KAAK;MACVZ,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBR,GAAG,CAACkB,IAAI,CACNlB,GAAG,CAACgB,aAAa,CAAC,eAAe,CAAC,EAClC,OAAO,EACPhB,GAAG,CAACoB,EAAE,CAACZ,GAAG,CACZ,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CAAC,WAAW,CAAC,EACfA,EAAE,CACA,gBAAgB,EAChB,CACEA,EAAE,CAAC,UAAU,CAAC,EACdD,GAAG,CAACY,aAAa,GAAG,CAAC,GACjBX,EAAE,CACA,OAAO,EACP;IACEG,KAAK,EAAE;MAAEiB,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAG,CAAC;IACrCC,EAAE,EAAE;MAAEC,KAAK,EAAExB,GAAG,CAACyB;IAAc;EACjC,CAAC,EACD,CAACzB,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,GACDX,GAAG,CAAC0B,EAAE,CAAC,CAAC,EACZ1B,GAAG,CAACY,aAAa,KAAK,CAAC,GACnBX,EAAE,CACA,OAAO,EACP;IACES,WAAW,EAAE,MAAM;IACnBN,KAAK,EAAE;MAAEuB,QAAQ,EAAE,EAAE;MAAEN,KAAK,EAAE;IAAS,CAAC;IACxCE,EAAE,EAAE;MAAEC,KAAK,EAAExB,GAAG,CAAC4B;IAAwB;EAC3C,CAAC,EACD,CAAC5B,GAAG,CAACW,EAAE,CAAC,YAAY,CAAC,CACvB,CAAC,GACDX,GAAG,CAAC0B,EAAE,CAAC,CAAC,EACZzB,EAAE,CACA,OAAO,EACP;IACEG,KAAK,EAAE;MAAEkB,IAAI,EAAE;IAAG,CAAC;IACnBC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYK,MAAM,EAAE;QACvB7B,GAAG,CAACG,UAAU,GAAG,KAAK;MACxB;IACF;EACF,CAAC,EACD,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDX,GAAG,CAAC0B,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AACxB/B,MAAM,CAACgC,aAAa,GAAG,IAAI;AAE3B,SAAShC,MAAM,EAAE+B,eAAe", "ignoreList": []}]}