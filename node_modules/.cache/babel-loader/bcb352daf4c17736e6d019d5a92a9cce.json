{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/el.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/el.js", "mtime": 1757335235499}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/el.ts"], "sourcesContent": ["export default {\n  badge: 'Σήμα',\n  close: 'Close',\n  dataIterator: {\n    noResultsText: 'Δε βρέθηκαν αποτελέσματα',\n    loadingText: 'Loading item...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Γραμμές ανά σελίδα:',\n    ariaLabel: {\n      sortDescending: 'Sorted descending.',\n      sortAscending: 'Sorted ascending.',\n      sortNone: 'Not sorted.',\n      activateNone: 'Activate to remove sorting.',\n      activateDescending: 'Activate to sort descending.',\n      activateAscending: 'Activate to sort ascending.',\n    },\n    sortBy: 'Sort by',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Αντικείμενα ανά σελίδα:',\n    itemsPerPageAll: 'Όλα',\n    nextPage: 'Επόμενη σελίδα',\n    prevPage: 'Προηγούμενη σελίδα',\n    firstPage: 'Πρώτη σελίδα',\n    lastPage: 'Τελευταία σελίδα',\n    pageText: '{0}-{1} από {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} επιλεγμένα',\n    nextMonthAriaLabel: 'Τον επόμενο μήνα',\n    nextYearAriaLabel: 'Του χρόνου',\n    prevMonthAriaLabel: 'Προηγούμενος μήνας',\n    prevYearAriaLabel: 'Προηγούμενο έτος',\n  },\n  noDataText: 'Χωρίς δεδομένα',\n  carousel: {\n    prev: 'הקודם חזותי',\n    next: 'הבא חזותי',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} ακόμη',\n  },\n  fileInput: {\n    counter: '{0} files',\n    counterSize: '{0} files ({1} in total)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Πλοήγηση με προορισμούς',\n      next: 'Επόμενη σελίδα',\n      previous: 'Προηγούμενη σελίδα',\n      page: 'Πήγαινε στην σελίδα {0}',\n      currentPage: 'Τρέχουσα σελίδα, σελίδα {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,MADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,0BADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,qBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,mBAFN;MAGTC,QAAQ,EAAE,aAHD;MAITC,YAAY,EAAE,6BAJL;MAKTC,kBAAkB,EAAE,8BALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,yBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,gBAHA;IAIVC,QAAQ,EAAE,oBAJA;IAKVC,SAAS,EAAE,cALD;IAMVC,QAAQ,EAAE,kBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,gBADL;IAEVC,kBAAkB,EAAE,kBAFV;IAGVC,iBAAiB,EAAE,YAHT;IAIVC,kBAAkB,EAAE,oBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,gBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,aADE;IAERC,IAAI,EAAE,WAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,yBADA;MAETX,IAAI,EAAE,gBAFG;MAGTY,QAAQ,EAAE,oBAHD;MAITC,IAAI,EAAE,yBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}