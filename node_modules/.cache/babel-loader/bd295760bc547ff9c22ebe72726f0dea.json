{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/eslint-loader/index.js??ref--14-0!/Users/<USER>/Downloads/web-demo/src/util/TiwTranscode.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/util/TiwTranscode.js", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/eslint-loader/index.js", "mtime": 1757335229508}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["transcodeIntervalMap", "taskIdUserDataMap", "instance", "getInstance", "window", "axios", "create", "baseURL", "timeout", "createTranscodeTask", "_ref", "callback", "_this", "Url", "IsStaticPPT", "post", "cmd", "content", "SdkAppId", "ThumbnailResolution", "then", "res", "data", "Error", "code", "Code", "message", "Message", "status", "taskId", "TaskId", "_setInterval", "describeTranscodeTask", "err", "concat", "_this2", "userData", "fileData", "clearInterval", "Status"], "sources": ["/Users/<USER>/Downloads/web-demo/src/util/TiwTranscode.js"], "sourcesContent": ["export default {\n\n  transcodeIntervalMap: {},\n  taskIdUserDataMap: {},\n  instance: null,\n\n  getInstance() {\n    if (!this.instance) {\n      this.instance = window.axios.create({\n        baseURL: 'https://tiw-3gflb4tn0d7f550f-1259648581.ap-shanghai.app.tcloudbase.com',\n        timeout: 5000,\n      });\n    }\n    return this.instance;\n  },\n\n  createTranscodeTask({ Url, IsStaticPPT }, callback) {\n    this.getInstance().post('/tiw_transcode', {\n      cmd: 'createTask',\n      content: {\n        SdkAppId: 1400127140,\n        Url,\n        IsStaticPPT,\n        ThumbnailResolution: '200x200',\n      },\n    })\n      .then((res) => {\n        if (res.data.Error) {\n          callback({\n            code: res.data.Error.Code,\n            message: res.data.Error.Message,\n            status: 'ERROR',\n          });\n        } else {\n          const taskId = res.data.TaskId;\n          this.transcodeIntervalMap[taskId] = setInterval(() => {\n          // 查询进度\n            this.describeTranscodeTask(taskId, callback);\n          }, 5000);\n          this.taskIdUserDataMap[taskId] = {};\n        }\n      })\n      .catch((err) => {\n        callback({\n          status: 'ERROR',\n          code: 'FailedOperation.SdkModule',\n          message: `upload file failure, ${err.message}`,\n        });\n      });\n  },\n\n  describeTranscodeTask(taskId, callback) {\n    this.getInstance().post('/tiw_transcode', {\n      cmd: 'describeTask',\n      content: {\n        SdkAppId: 1400127140,\n        TaskId: taskId,\n      },\n    })\n      .then((res) => {\n        if (res.data.Error) {\n          callback({\n            status: 'ERROR',\n            code: res.data.Error.Code,\n            message: res.data.Error.Message,\n            taskId,\n            userData: this.taskIdUserDataMap[taskId],\n            fileData: {}\n          });\n          clearInterval(this.transcodeIntervalMap[taskId]);\n        } else {\n          callback({\n            status: 'SUCCESS',\n            code: 0,\n            message: '',\n            taskId,\n            userData: this.taskIdUserDataMap[taskId],\n            fileData: res.data,\n          });\n          if (res.data.Status === 'FINISHED') {\n            clearInterval(this.transcodeIntervalMap[taskId]);\n          }\n        }\n      })\n      .catch((err) => {\n        callback({\n          status: 'ERROR',\n          code: 'FailedOperation.SdkModule',\n          message: `transcode failure, ${err.message}`,\n          taskId,\n          userData: this.taskIdUserDataMap[taskId],\n          fileData: {}\n        });\n        clearInterval(this.transcodeIntervalMap[taskId]);\n      });\n  },\n};\n"], "mappings": ";AAAA,eAAe;EAEbA,oBAAoB,EAAE,CAAC,CAAC;EACxBC,iBAAiB,EAAE,CAAC,CAAC;EACrBC,QAAQ,EAAE,IAAI;EAEdC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAGE,MAAM,CAACC,KAAK,CAACC,MAAM,CAAC;QAClCC,OAAO,EAAE,wEAAwE;QACjFC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IACA,OAAO,IAAI,CAACN,QAAQ;EACtB,CAAC;EAEDO,mBAAmB,WAAnBA,mBAAmBA,CAAAC,IAAA,EAAuBC,QAAQ,EAAE;IAAA,IAAAC,KAAA;IAAA,IAA9BC,GAAG,GAAAH,IAAA,CAAHG,GAAG;MAAEC,WAAW,GAAAJ,IAAA,CAAXI,WAAW;IACpC,IAAI,CAACX,WAAW,CAAC,CAAC,CAACY,IAAI,CAAC,gBAAgB,EAAE;MACxCC,GAAG,EAAE,YAAY;MACjBC,OAAO,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBL,GAAG,EAAHA,GAAG;QACHC,WAAW,EAAXA,WAAW;QACXK,mBAAmB,EAAE;MACvB;IACF,CAAC,CAAC,CACCC,IAAI,CAAC,UAACC,GAAG,EAAK;MACb,IAAIA,GAAG,CAACC,IAAI,CAACC,KAAK,EAAE;QAClBZ,QAAQ,CAAC;UACPa,IAAI,EAAEH,GAAG,CAACC,IAAI,CAACC,KAAK,CAACE,IAAI;UACzBC,OAAO,EAAEL,GAAG,CAACC,IAAI,CAACC,KAAK,CAACI,OAAO;UAC/BC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAMC,MAAM,GAAGR,GAAG,CAACC,IAAI,CAACQ,MAAM;QAC9BlB,KAAI,CAACZ,oBAAoB,CAAC6B,MAAM,CAAC,GAAGE,YAAA,CAAY,YAAM;UACtD;UACEnB,KAAI,CAACoB,qBAAqB,CAACH,MAAM,EAAElB,QAAQ,CAAC;QAC9C,CAAC,EAAE,IAAI,CAAC;QACRC,KAAI,CAACX,iBAAiB,CAAC4B,MAAM,CAAC,GAAG,CAAC,CAAC;MACrC;IACF,CAAC,CAAC,SACI,CAAC,UAACI,GAAG,EAAK;MACdtB,QAAQ,CAAC;QACPiB,MAAM,EAAE,OAAO;QACfJ,IAAI,EAAE,2BAA2B;QACjCE,OAAO,0BAAAQ,MAAA,CAA0BD,GAAG,CAACP,OAAO;MAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;EACN,CAAC;EAEDM,qBAAqB,WAArBA,qBAAqBA,CAACH,MAAM,EAAElB,QAAQ,EAAE;IAAA,IAAAwB,MAAA;IACtC,IAAI,CAAChC,WAAW,CAAC,CAAC,CAACY,IAAI,CAAC,gBAAgB,EAAE;MACxCC,GAAG,EAAE,cAAc;MACnBC,OAAO,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpBY,MAAM,EAAED;MACV;IACF,CAAC,CAAC,CACCT,IAAI,CAAC,UAACC,GAAG,EAAK;MACb,IAAIA,GAAG,CAACC,IAAI,CAACC,KAAK,EAAE;QAClBZ,QAAQ,CAAC;UACPiB,MAAM,EAAE,OAAO;UACfJ,IAAI,EAAEH,GAAG,CAACC,IAAI,CAACC,KAAK,CAACE,IAAI;UACzBC,OAAO,EAAEL,GAAG,CAACC,IAAI,CAACC,KAAK,CAACI,OAAO;UAC/BE,MAAM,EAANA,MAAM;UACNO,QAAQ,EAAED,MAAI,CAAClC,iBAAiB,CAAC4B,MAAM,CAAC;UACxCQ,QAAQ,EAAE,CAAC;QACb,CAAC,CAAC;QACFC,aAAa,CAACH,MAAI,CAACnC,oBAAoB,CAAC6B,MAAM,CAAC,CAAC;MAClD,CAAC,MAAM;QACLlB,QAAQ,CAAC;UACPiB,MAAM,EAAE,SAAS;UACjBJ,IAAI,EAAE,CAAC;UACPE,OAAO,EAAE,EAAE;UACXG,MAAM,EAANA,MAAM;UACNO,QAAQ,EAAED,MAAI,CAAClC,iBAAiB,CAAC4B,MAAM,CAAC;UACxCQ,QAAQ,EAAEhB,GAAG,CAACC;QAChB,CAAC,CAAC;QACF,IAAID,GAAG,CAACC,IAAI,CAACiB,MAAM,KAAK,UAAU,EAAE;UAClCD,aAAa,CAACH,MAAI,CAACnC,oBAAoB,CAAC6B,MAAM,CAAC,CAAC;QAClD;MACF;IACF,CAAC,CAAC,SACI,CAAC,UAACI,GAAG,EAAK;MACdtB,QAAQ,CAAC;QACPiB,MAAM,EAAE,OAAO;QACfJ,IAAI,EAAE,2BAA2B;QACjCE,OAAO,wBAAAQ,MAAA,CAAwBD,GAAG,CAACP,OAAO,CAAE;QAC5CG,MAAM,EAANA,MAAM;QACNO,QAAQ,EAAED,MAAI,CAAClC,iBAAiB,CAAC4B,MAAM,CAAC;QACxCQ,QAAQ,EAAE,CAAC;MACb,CAAC,CAAC;MACFC,aAAa,CAACH,MAAI,CAACnC,oBAAoB,CAAC6B,MAAM,CAAC,CAAC;IAClD,CAAC,CAAC;EACN;AACF,CAAC", "ignoreList": []}]}