{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabsBar.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VTabs/VTabsBar.js", "mtime": 1757335239363}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["BaseSlideGroup", "Themeable", "SSRBootable", "mixins", "extend", "name", "provide", "tabsBar", "computed", "classes", "_objectSpread", "options", "call", "isMobile", "showArrows", "themeClasses", "watch", "items", "internalValue", "$route", "methods", "callSlider", "isBooted", "$emit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "data", "staticClass", "onRouteChange", "val", "oldVal", "mandatory", "newPath", "path", "old<PERSON><PERSON>", "hasNew", "hasOld", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "item", "value", "to", "err", "e", "f", "undefined", "h", "attrs", "role"], "sources": ["../../../src/components/VTabs/VTabsBar.ts"], "sourcesContent": ["// Extensions\nimport { BaseSlideGroup } from '../VSlideGroup/VSlideGroup'\n\n// Components\nimport VTab from './VTab'\n\n// Mixins\nimport Themeable from '../../mixins/themeable'\nimport SSRBootable from '../../mixins/ssr-bootable'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\n// Types\nimport { Route } from 'vue-router'\nimport { VNode } from 'vue'\n\ntype VTabInstance = InstanceType<typeof VTab>\n\nexport default mixins(\n  BaseSlideGroup,\n  SSRBootable,\n  Themeable\n  /* @vue/component */\n).extend({\n  name: 'v-tabs-bar',\n\n  provide () {\n    return {\n      tabsBar: this,\n    }\n  },\n\n  computed: {\n    classes () {\n      return {\n        ...BaseSlideGroup.options.computed.classes.call(this),\n        'v-tabs-bar': true,\n        'v-tabs-bar--is-mobile': this.isMobile,\n        // TODO: Remove this and move to v-slide-group\n        'v-tabs-bar--show-arrows': this.showArrows,\n        ...this.themeClasses,\n      }\n    },\n  },\n\n  watch: {\n    items: 'callSlider',\n    internalValue: 'callSlider',\n    $route: 'onRouteChange',\n  },\n\n  methods: {\n    callSlider () {\n      if (!this.isBooted) return\n\n      this.$emit('call:slider')\n    },\n    genContent () {\n      const render = BaseSlideGroup.options.methods.genContent.call(this)\n\n      render.data = render.data || {}\n      render.data.staticClass += ' v-tabs-bar__content'\n\n      return render\n    },\n    onRouteChange (val: Route, oldVal: Route) {\n      /* istanbul ignore next */\n      if (this.mandatory) return\n\n      const items = this.items as unknown as VTabInstance[]\n      const newPath = val.path\n      const oldPath = oldVal.path\n\n      let hasNew = false\n      let hasOld = false\n\n      for (const item of items) {\n        if (item.to === oldPath) hasOld = true\n        else if (item.to === newPath) hasNew = true\n\n        if (hasNew && hasOld) break\n      }\n\n      // If we have an old item and not a new one\n      // it's assumed that the user navigated to\n      // a path that is not present in the items\n      if (!hasNew && hasOld) this.internalValue = undefined\n    },\n  },\n\n  render (h): VNode {\n    const render = BaseSlideGroup.options.render.call(this, h)\n\n    render.data!.attrs = {\n      role: 'tablist',\n    }\n\n    return render\n  },\n})\n"], "mappings": ";;AAAA;AACA,SAASA,cAAT,QAA+B,4BAA/B,C,CAKA;;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,WAAP,MAAwB,2BAAxB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAQA,eAAeA,MAAM,CACnBH,cADmB,EAEnBE,WAFmB,EAGnBD;AACA,oBAJmB,CAAN,CAKbG,MALa,CAKN;EACPC,IAAI,EAAE,YADC;EAGPC,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,OAAO,EAAE;IADJ,CAAP;EAGD,CAPM;EASPC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKV,cAAc,CAACW,OAAf,CAAuBH,QAAvB,CAAgCC,OAAhC,CAAwCG,IAAxC,CAA6C,IAA7C,CADE;QAEL,cAAc,IAFT;QAGL,yBAAyB,KAAKC,QAHzB;QAIL;QACA,2BAA2B,KAAKC;MAL3B,GAMF,KAAKC,YAAA;IAEX;EAVO,CATH;EAsBPC,KAAK,EAAE;IACLC,KAAK,EAAE,YADF;IAELC,aAAa,EAAE,YAFV;IAGLC,MAAM,EAAE;EAHH,CAtBA;EA4BPC,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAI,CAAC,KAAKC,QAAV,EAAoB;MAEpB,KAAKC,KAAL,CAAW,aAAX;IACD,CALM;IAMPC,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAMC,MAAM,GAAGzB,cAAc,CAACW,OAAf,CAAuBS,OAAvB,CAA+BI,UAA/B,CAA0CZ,IAA1C,CAA+C,IAA/C,CAAf;MAEAa,MAAM,CAACC,IAAP,GAAcD,MAAM,CAACC,IAAP,IAAe,EAA7B;MACAD,MAAM,CAACC,IAAP,CAAYC,WAAZ,IAA2B,sBAA3B;MAEA,OAAOF,MAAP;IACD,CAbM;IAcPG,aAAa,WAAbA,aAAaA,CAAEC,GAAF,EAAcC,MAAd,EAA2B;MACtC;MACA,IAAI,KAAKC,SAAT,EAAoB;MAEpB,IAAMd,KAAK,GAAG,KAAKA,KAAnB;MACA,IAAMe,OAAO,GAAGH,GAAG,CAACI,IAApB;MACA,IAAMC,OAAO,GAAGJ,MAAM,CAACG,IAAvB;MAEA,IAAIE,MAAM,GAAG,KAAb;MACA,IAAIC,MAAM,GAAG,KAAb;MAAA,IAAAC,SAAA,GAAAC,0BAAA,CAEmBrB,KAAnB;QAAAsB,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA,GAA0B;UAAA,IAAfC,IAAX,GAAAJ,KAAA,CAAAK,KAAA;UACE,IAAID,IAAI,CAACE,EAAL,KAAYX,OAAhB,EAAyBE,MAAM,GAAG,IAAT,CAAzB,KACK,IAAIO,IAAI,CAACE,EAAL,KAAYb,OAAhB,EAAyBG,MAAM,GAAG,IAAT;UAE9B,IAAIA,MAAM,IAAIC,MAAd,EAAsB;QACvB,CAhBqC,CAkBtC;QACA;QACA;MAAA,SAAAU,GAAA;QAAAT,SAAA,CAAAU,CAAA,CAAAD,GAAA;MAAA;QAAAT,SAAA,CAAAW,CAAA;MAAA;MACA,IAAI,CAACb,MAAD,IAAWC,MAAf,EAAuB,KAAKlB,aAAL,GAAqB+B,SAArB;IACxB;EApCM,CA5BF;EAmEPxB,MAAM,WAANA,MAAMA,CAAEyB,CAAF,EAAG;IACP,IAAMzB,MAAM,GAAGzB,cAAc,CAACW,OAAf,CAAuBc,MAAvB,CAA8Bb,IAA9B,CAAmC,IAAnC,EAAyCsC,CAAzC,CAAf;IAEAzB,MAAM,CAACC,IAAP,CAAayB,KAAb,GAAqB;MACnBC,IAAI,EAAE;IADa,CAArB;IAIA,OAAO3B,MAAP;EACD;AA3EM,CALM,CAAf", "ignoreList": []}]}