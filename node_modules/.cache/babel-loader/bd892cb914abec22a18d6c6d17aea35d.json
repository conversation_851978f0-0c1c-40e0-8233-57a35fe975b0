{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/themeable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/themeable/index.js", "mtime": 1757335237260}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "Themeable", "extend", "name", "provide", "theme", "themeableProvide", "inject", "isDark", "props", "dark", "type", "Boolean", "light", "data", "computed", "appIsDark", "$vuetify", "themeClasses", "rootIsDark", "rootThemeClasses", "watch", "handler", "newVal", "oldVal", "immediate", "functionalThemeClasses", "context", "vm", "_objectSpread", "injections", "options", "call"], "sources": ["../../../src/mixins/themeable/index.ts"], "sourcesContent": ["import Vue from 'vue'\nimport { PropType, RenderContext } from 'vue/types/options'\n\ninterface options extends Vue {\n  theme: {\n    isDark: boolean\n  }\n}\n\n/* @vue/component */\nconst Themeable = Vue.extend<options>().extend({\n  name: 'themeable',\n\n  provide (): object {\n    return {\n      theme: this.themeableProvide,\n    }\n  },\n\n  inject: {\n    theme: {\n      default: {\n        isDark: false,\n      },\n    },\n  },\n\n  props: {\n    dark: {\n      type: Boolean as PropType<boolean | null>,\n      default: null,\n    },\n    light: {\n      type: Boolean as PropType<boolean | null>,\n      default: null,\n    },\n  },\n\n  data () {\n    return {\n      themeableProvide: {\n        isDark: false,\n      },\n    }\n  },\n\n  computed: {\n    appIsDark (): boolean {\n      return this.$vuetify.theme.dark || false\n    },\n    isDark (): boolean {\n      if (this.dark === true) {\n        // explicitly dark\n        return true\n      } else if (this.light === true) {\n        // explicitly light\n        return false\n      } else {\n        // inherit from parent, or default false if there is none\n        return this.theme.isDark\n      }\n    },\n    themeClasses (): object {\n      return {\n        'theme--dark': this.isDark,\n        'theme--light': !this.isDark,\n      }\n    },\n    /** Used by menus and dialogs, inherits from v-app instead of the parent */\n    rootIsDark (): boolean {\n      if (this.dark === true) {\n        // explicitly dark\n        return true\n      } else if (this.light === true) {\n        // explicitly light\n        return false\n      } else {\n        // inherit from v-app\n        return this.appIsDark\n      }\n    },\n    rootThemeClasses (): Dictionary<boolean> {\n      return {\n        'theme--dark': this.rootIsDark,\n        'theme--light': !this.rootIsDark,\n      }\n    },\n  },\n\n  watch: {\n    isDark: {\n      handler (newVal, oldVal) {\n        if (newVal !== oldVal) {\n          this.themeableProvide.isDark = this.isDark\n        }\n      },\n      immediate: true,\n    },\n  },\n})\n\nexport default Themeable\n\nexport function functionalThemeClasses (context: RenderContext): object {\n  const vm = {\n    ...context.props,\n    ...context.injections,\n  }\n  const isDark = Themeable.options.computed.isDark.call(vm)\n  return Themeable.options.computed.themeClasses.call({ isDark })\n}\n"], "mappings": ";AAAA,OAAOA,GAAP,MAAgB,KAAhB;AASA;;AACA,IAAMC,SAAS,GAAGD,GAAG,CAACE,MAAJ,GAAsBA,MAAtB,CAA6B;EAC7CC,IAAI,EAAE,WADuC;EAG7CC,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,KAAK,EAAE,KAAKC;IADP,CAAP;EAGD,CAP4C;EAS7CC,MAAM,EAAE;IACNF,KAAK,EAAE;MACL,WAAS;QACPG,MAAM,EAAE;MADD;IADJ;EADD,CATqC;EAiB7CC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,OADF;MAEJ,WAAS;IAFL,CADD;IAKLC,KAAK,EAAE;MACLF,IAAI,EAAEC,OADD;MAEL,WAAS;IAFJ;EALF,CAjBsC;EA4B7CE,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLR,gBAAgB,EAAE;QAChBE,MAAM,EAAE;MADQ;IADb,CAAP;EAKD,CAlC4C;EAoC7CO,QAAQ,EAAE;IACRC,SAAS,WAATA,SAASA,CAAA;MACP,OAAO,KAAKC,QAAL,CAAcZ,KAAd,CAAoBK,IAApB,IAA4B,KAAnC;IACD,CAHO;IAIRF,MAAM,WAANA,MAAMA,CAAA;MACJ,IAAI,KAAKE,IAAL,KAAc,IAAlB,EAAwB;QACtB;QACA,OAAO,IAAP;MACD,CAHD,MAGO,IAAI,KAAKG,KAAL,KAAe,IAAnB,EAAyB;QAC9B;QACA,OAAO,KAAP;MACD,CAHM,MAGA;QACL;QACA,OAAO,KAAKR,KAAL,CAAWG,MAAlB;MACD;IACF,CAfO;IAgBRU,YAAY,WAAZA,YAAYA,CAAA;MACV,OAAO;QACL,eAAe,KAAKV,MADf;QAEL,gBAAgB,CAAC,KAAKA;MAFjB,CAAP;IAID,CArBO;IAsBR,2EACAW,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAI,KAAKT,IAAL,KAAc,IAAlB,EAAwB;QACtB;QACA,OAAO,IAAP;MACD,CAHD,MAGO,IAAI,KAAKG,KAAL,KAAe,IAAnB,EAAyB;QAC9B;QACA,OAAO,KAAP;MACD,CAHM,MAGA;QACL;QACA,OAAO,KAAKG,SAAZ;MACD;IACF,CAlCO;IAmCRI,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,OAAO;QACL,eAAe,KAAKD,UADf;QAEL,gBAAgB,CAAC,KAAKA;MAFjB,CAAP;IAID;EAxCO,CApCmC;EA+E7CE,KAAK,EAAE;IACLb,MAAM,EAAE;MACNc,OAAO,WAAPA,OAAOA,CAAEC,MAAF,EAAUC,MAAV,EAAgB;QACrB,IAAID,MAAM,KAAKC,MAAf,EAAuB;UACrB,KAAKlB,gBAAL,CAAsBE,MAAtB,GAA+B,KAAKA,MAApC;QACD;MACF,CALK;MAMNiB,SAAS,EAAE;IANL;EADH;AA/EsC,CAA7B,CAAlB;AA2FA,eAAexB,SAAf;AAEA,OAAM,SAAUyB,sBAAVA,CAAkCC,OAAlC,EAAwD;EAC5D,IAAMC,EAAE,GAAAC,aAAA,CAAAA,aAAA,KACHF,OAAO,CAAClB,KADF,GAENkB,OAAO,CAACG,UAAA,CAFb;EAIA,IAAMtB,MAAM,GAAGP,SAAS,CAAC8B,OAAV,CAAkBhB,QAAlB,CAA2BP,MAA3B,CAAkCwB,IAAlC,CAAuCJ,EAAvC,CAAf;EACA,OAAO3B,SAAS,CAAC8B,OAAV,CAAkBhB,QAAlB,CAA2BG,YAA3B,CAAwCc,IAAxC,CAA6C;IAAExB,MAAA,EAAAA;EAAF,CAA7C,CAAP;AACD", "ignoreList": []}]}