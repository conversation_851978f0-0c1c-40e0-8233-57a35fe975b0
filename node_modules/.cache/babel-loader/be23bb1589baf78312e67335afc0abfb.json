{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/setPrototypeOf.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/setPrototypeOf.js", "mtime": 1757335237678}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9PYmplY3Qkc2V0UHJvdG90eXBlT2YgZnJvbSAiY29yZS1qcy1wdXJlL2ZlYXR1cmVzL29iamVjdC9zZXQtcHJvdG90eXBlLW9mLmpzIjsKaW1wb3J0IF9iaW5kSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJjb3JlLWpzLXB1cmUvZmVhdHVyZXMvaW5zdGFuY2UvYmluZC5qcyI7CmZ1bmN0aW9uIF9zZXRQcm90b3R5cGVPZih0LCBlKSB7CiAgdmFyIF9jb250ZXh0OwogIHJldHVybiBfc2V0UHJvdG90eXBlT2YgPSBfT2JqZWN0JHNldFByb3RvdHlwZU9mID8gX2JpbmRJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0ID0gX09iamVjdCRzZXRQcm90b3R5cGVPZikuY2FsbChfY29udGV4dCkgOiBmdW5jdGlvbiAodCwgZSkgewogICAgcmV0dXJuIHQuX19wcm90b19fID0gZSwgdDsKICB9LCBfc2V0UHJvdG90eXBlT2YodCwgZSk7Cn0KZXhwb3J0IHsgX3NldFByb3RvdHlwZU9mIGFzIGRlZmF1bHQgfTs="}, {"version": 3, "names": ["_Object$setPrototypeOf", "_bindInstanceProperty", "_setPrototypeOf", "t", "e", "_context", "call", "__proto__", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/setPrototypeOf.js"], "sourcesContent": ["import _Object$setPrototypeOf from \"core-js-pure/features/object/set-prototype-of.js\";\nimport _bindInstanceProperty from \"core-js-pure/features/instance/bind.js\";\nfunction _setPrototypeOf(t, e) {\n  var _context;\n  return _setPrototypeOf = _Object$setPrototypeOf ? _bindInstanceProperty(_context = _Object$setPrototypeOf).call(_context) : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,qBAAqB,MAAM,wCAAwC;AAC1E,SAASC,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAIC,QAAQ;EACZ,OAAOH,eAAe,GAAGF,sBAAsB,GAAGC,qBAAqB,CAACI,QAAQ,GAAGL,sBAAsB,CAAC,CAACM,IAAI,CAACD,QAAQ,CAAC,GAAG,UAAUF,CAAC,EAAEC,CAAC,EAAE;IAC1I,OAAOD,CAAC,CAACI,SAAS,GAAGH,CAAC,EAAED,CAAC;EAC3B,CAAC,EAAED,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC1B;AACA,SAASF,eAAe,IAAIM,OAAO", "ignoreList": []}]}