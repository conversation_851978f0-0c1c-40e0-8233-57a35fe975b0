{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/rippleable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/rippleable/index.js", "mtime": 1757335237218}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7Ci8vIERpcmVjdGl2ZXMKaW1wb3J0IHJpcHBsZSBmcm9tICcuLi8uLi9kaXJlY3RpdmVzL3JpcHBsZSc7IC8vIFR5cGVzCgppbXBvcnQgVnVlIGZyb20gJ3Z1ZSc7CmV4cG9ydCBkZWZhdWx0IFZ1ZS5leHRlbmQoewogIG5hbWU6ICdyaXBwbGVhYmxlJywKICBkaXJlY3RpdmVzOiB7CiAgICByaXBwbGU6IHJpcHBsZQogIH0sCiAgcHJvcHM6IHsKICAgIHJpcHBsZTogewogICAgICB0eXBlOiBbQm9vbGVhbiwgT2JqZWN0XSwKICAgICAgImRlZmF1bHQiOiB0cnVlCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZW5SaXBwbGU6IGZ1bmN0aW9uIGdlblJpcHBsZSgpIHsKICAgICAgdmFyIGRhdGEgPSBhcmd1bWVudHMubGVuZ3RoID4gMCAmJiBhcmd1bWVudHNbMF0gIT09IHVuZGVmaW5lZCA/IGFyZ3VtZW50c1swXSA6IHt9OwogICAgICBpZiAoIXRoaXMucmlwcGxlKSByZXR1cm4gbnVsbDsKICAgICAgZGF0YS5zdGF0aWNDbGFzcyA9ICd2LWlucHV0LS1zZWxlY3Rpb24tY29udHJvbHNfX3JpcHBsZSc7CiAgICAgIGRhdGEuZGlyZWN0aXZlcyA9IGRhdGEuZGlyZWN0aXZlcyB8fCBbXTsKICAgICAgZGF0YS5kaXJlY3RpdmVzLnB1c2goewogICAgICAgIG5hbWU6ICdyaXBwbGUnLAogICAgICAgIHZhbHVlOiB7CiAgICAgICAgICBjZW50ZXI6IHRydWUKICAgICAgICB9CiAgICAgIH0pOwogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgZGF0YSk7CiAgICB9CiAgfQp9KTs="}, {"version": 3, "names": ["ripple", "<PERSON><PERSON>", "extend", "name", "directives", "props", "type", "Boolean", "Object", "methods", "gen<PERSON><PERSON><PERSON>", "data", "arguments", "length", "undefined", "staticClass", "push", "value", "center", "$createElement"], "sources": ["../../../src/mixins/rippleable/index.ts"], "sourcesContent": ["// Directives\nimport ripple from '../../directives/ripple'\n\n// Types\nimport Vue, { VNode, VNodeData, VNodeDirective } from 'vue'\n\nexport default Vue.extend({\n  name: 'rippleable',\n\n  directives: { ripple },\n\n  props: {\n    ripple: {\n      type: [Boolean, Object],\n      default: true,\n    },\n  },\n\n  methods: {\n    genRipple (data: VNodeData = {}): VNode | null {\n      if (!this.ripple) return null\n\n      data.staticClass = 'v-input--selection-controls__ripple'\n\n      data.directives = data.directives || []\n      data.directives.push({\n        name: 'ripple',\n        value: { center: true },\n      } as VNodeDirective)\n\n      return this.$createElement('div', data)\n    },\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAOA,MAAP,MAAmB,yBAAnB,C,CAEA;;AACA,OAAOC,GAAP,MAAsD,KAAtD;AAEA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,YADkB;EAGxBC,UAAU,EAAE;IAAEJ,MAAA,EAAAA;EAAF,CAHY;EAKxBK,KAAK,EAAE;IACLL,MAAM,EAAE;MACNM,IAAI,EAAE,CAACC,OAAD,EAAUC,MAAV,CADA;MAEN,WAAS;IAFH;EADH,CALiB;EAYxBC,OAAO,EAAE;IACPC,SAAS,WAATA,SAASA,CAAA,EAAsB;MAAA,IAApBC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAkB,EAApB;MACP,IAAI,CAAC,KAAKZ,MAAV,EAAkB,OAAO,IAAP;MAElBW,IAAI,CAACI,WAAL,GAAmB,qCAAnB;MAEAJ,IAAI,CAACP,UAAL,GAAkBO,IAAI,CAACP,UAAL,IAAmB,EAArC;MACAO,IAAI,CAACP,UAAL,CAAgBY,IAAhB,CAAqB;QACnBb,IAAI,EAAE,QADa;QAEnBc,KAAK,EAAE;UAAEC,MAAM,EAAE;QAAV;MAFY,CAArB;MAKA,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2BR,IAA3B,CAAP;IACD;EAbM;AAZe,CAAX,CAAf", "ignoreList": []}]}