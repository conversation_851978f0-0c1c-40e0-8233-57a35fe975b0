{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSlider/VSlider.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSlider/VSlider.js", "mtime": 1757335239093}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBfcGFyc2VGbG9hdCBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL3BhcnNlLWZsb2F0IjsKaW1wb3J0IF9yZXZlcnNlSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL3JldmVyc2UiOwppbXBvcnQgX21hcEluc3RhbmNlUHJvcGVydHkgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS9tYXAiOwppbXBvcnQgX2NvbmNhdEluc3RhbmNlUHJvcGVydHkgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS9jb25jYXQiOwppbXBvcnQgX3NldFRpbWVvdXQgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9zZXQtdGltZW91dCI7CmltcG9ydCBfaW5jbHVkZXNJbnN0YW5jZVByb3BlcnR5IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvaW5zdGFuY2UvaW5jbHVkZXMiOwppbXBvcnQgX3RyaW1JbnN0YW5jZVByb3BlcnR5IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvaW5zdGFuY2UvdHJpbSI7CmltcG9ydCBfaW5kZXhPZkluc3RhbmNlUHJvcGVydHkgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS9pbmRleC1vZiI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmRhdGUudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLnRvLWZpeGVkLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC50by1zdHJpbmcuanMiOwppbXBvcnQgIi4uLy4uLy4uL3NyYy9jb21wb25lbnRzL1ZTbGlkZXIvVlNsaWRlci5zYXNzIjsgLy8gQ29tcG9uZW50cwoKaW1wb3J0IFZJbnB1dCBmcm9tICcuLi9WSW5wdXQnOwppbXBvcnQgeyBWU2NhbGVUcmFuc2l0aW9uIH0gZnJvbSAnLi4vdHJhbnNpdGlvbnMnOyAvLyBNaXhpbnMKCmltcG9ydCBtaXhpbnMgZnJvbSAnLi4vLi4vdXRpbC9taXhpbnMnOwppbXBvcnQgTG9hZGFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL2xvYWRhYmxlJzsgLy8gRGlyZWN0aXZlcwoKaW1wb3J0IENsaWNrT3V0c2lkZSBmcm9tICcuLi8uLi9kaXJlY3RpdmVzL2NsaWNrLW91dHNpZGUnOyAvLyBIZWxwZXJzCgppbXBvcnQgeyBhZGRPbmNlRXZlbnRMaXN0ZW5lciwgZGVlcEVxdWFsLCBrZXlDb2RlcywgY3JlYXRlUmFuZ2UsIGNvbnZlcnRUb1VuaXQsIHBhc3NpdmVTdXBwb3J0ZWQgfSBmcm9tICcuLi8uLi91dGlsL2hlbHBlcnMnOwppbXBvcnQgeyBjb25zb2xlV2FybiB9IGZyb20gJy4uLy4uL3V0aWwvY29uc29sZSc7CmV4cG9ydCBkZWZhdWx0IG1peGlucyhWSW5wdXQsIExvYWRhYmxlCi8qIEB2dWUvY29tcG9uZW50ICovKS5leHRlbmQoewogIG5hbWU6ICd2LXNsaWRlcicsCiAgZGlyZWN0aXZlczogewogICAgQ2xpY2tPdXRzaWRlOiBDbGlja091dHNpZGUKICB9LAogIG1peGluczogW0xvYWRhYmxlXSwKICBwcm9wczogewogICAgZGlzYWJsZWQ6IEJvb2xlYW4sCiAgICBpbnZlcnNlTGFiZWw6IEJvb2xlYW4sCiAgICBtYXg6IHsKICAgICAgdHlwZTogW051bWJlciwgU3RyaW5nXSwKICAgICAgImRlZmF1bHQiOiAxMDAKICAgIH0sCiAgICBtaW46IHsKICAgICAgdHlwZTogW051bWJlciwgU3RyaW5nXSwKICAgICAgImRlZmF1bHQiOiAwCiAgICB9LAogICAgc3RlcDogewogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6IDEKICAgIH0sCiAgICB0aHVtYkNvbG9yOiBTdHJpbmcsCiAgICB0aHVtYkxhYmVsOiB7CiAgICAgIHR5cGU6IFtCb29sZWFuLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6IHVuZGVmaW5lZCwKICAgICAgdmFsaWRhdG9yOiBmdW5jdGlvbiB2YWxpZGF0b3IodikgewogICAgICAgIHJldHVybiB0eXBlb2YgdiA9PT0gJ2Jvb2xlYW4nIHx8IHYgPT09ICdhbHdheXMnOwogICAgICB9CiAgICB9LAogICAgdGh1bWJTaXplOiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICAgICJkZWZhdWx0IjogMzIKICAgIH0sCiAgICB0aWNrTGFiZWxzOiB7CiAgICAgIHR5cGU6IEFycmF5LAogICAgICAiZGVmYXVsdCI6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgfSwKICAgIHRpY2tzOiB7CiAgICAgIHR5cGU6IFtCb29sZWFuLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6IGZhbHNlLAogICAgICB2YWxpZGF0b3I6IGZ1bmN0aW9uIHZhbGlkYXRvcih2KSB7CiAgICAgICAgcmV0dXJuIHR5cGVvZiB2ID09PSAnYm9vbGVhbicgfHwgdiA9PT0gJ2Fsd2F5cyc7CiAgICAgIH0KICAgIH0sCiAgICB0aWNrU2l6ZTogewogICAgICB0eXBlOiBbTnVtYmVyLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6IDIKICAgIH0sCiAgICB0cmFja0NvbG9yOiBTdHJpbmcsCiAgICB0cmFja0ZpbGxDb2xvcjogU3RyaW5nLAogICAgdmFsdWU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICB2ZXJ0aWNhbDogQm9vbGVhbgogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFwcDogbnVsbCwKICAgICAgb2xkVmFsdWU6IG51bGwsCiAgICAgIHRodW1iUHJlc3NlZDogZmFsc2UsCiAgICAgIG1vdXNlVGltZW91dDogLTEsCiAgICAgIGlzRm9jdXNlZDogZmFsc2UsCiAgICAgIGlzQWN0aXZlOiBmYWxzZSwKICAgICAgbm9DbGljazogZmFsc2UsCiAgICAgIHN0YXJ0T2Zmc2V0OiAwCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNsYXNzZXM6IGZ1bmN0aW9uIGNsYXNzZXMoKSB7CiAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIFZJbnB1dC5vcHRpb25zLmNvbXB1dGVkLmNsYXNzZXMuY2FsbCh0aGlzKSksIHt9LCB7CiAgICAgICAgJ3YtaW5wdXRfX3NsaWRlcic6IHRydWUsCiAgICAgICAgJ3YtaW5wdXRfX3NsaWRlci0tdmVydGljYWwnOiB0aGlzLnZlcnRpY2FsLAogICAgICAgICd2LWlucHV0X19zbGlkZXItLWludmVyc2UtbGFiZWwnOiB0aGlzLmludmVyc2VMYWJlbAogICAgICB9KTsKICAgIH0sCiAgICBpbnRlcm5hbFZhbHVlOiB7CiAgICAgIGdldDogZnVuY3Rpb24gZ2V0KCkgewogICAgICAgIHJldHVybiB0aGlzLmxhenlWYWx1ZTsKICAgICAgfSwKICAgICAgc2V0OiBmdW5jdGlvbiBzZXQodmFsKSB7CiAgICAgICAgdmFsID0gaXNOYU4odmFsKSA/IHRoaXMubWluVmFsdWUgOiB2YWw7IC8vIFJvdW5kIHZhbHVlIHRvIGVuc3VyZSB0aGUKICAgICAgICAvLyBlbnRpcmUgc2xpZGVyIHJhbmdlIGNhbgogICAgICAgIC8vIGJlIHNlbGVjdGVkIHdpdGggc3RlcAoKICAgICAgICB2YXIgdmFsdWUgPSB0aGlzLnJvdW5kVmFsdWUoTWF0aC5taW4oTWF0aC5tYXgodmFsLCB0aGlzLm1pblZhbHVlKSwgdGhpcy5tYXhWYWx1ZSkpOwogICAgICAgIGlmICh2YWx1ZSA9PT0gdGhpcy5sYXp5VmFsdWUpIHJldHVybjsKICAgICAgICB0aGlzLmxhenlWYWx1ZSA9IHZhbHVlOwogICAgICAgIHRoaXMuJGVtaXQoJ2lucHV0JywgdmFsdWUpOwogICAgICB9CiAgICB9LAogICAgdHJhY2tUcmFuc2l0aW9uOiBmdW5jdGlvbiB0cmFja1RyYW5zaXRpb24oKSB7CiAgICAgIHJldHVybiB0aGlzLnRodW1iUHJlc3NlZCA/IHRoaXMuc2hvd1RpY2tzIHx8IHRoaXMuc3RlcE51bWVyaWMgPyAnMC4xcyBjdWJpYy1iZXppZXIoMC4yNSwgMC44LCAwLjUsIDEpJyA6ICdub25lJyA6ICcnOwogICAgfSwKICAgIG1pblZhbHVlOiBmdW5jdGlvbiBtaW5WYWx1ZSgpIHsKICAgICAgcmV0dXJuIF9wYXJzZUZsb2F0KHRoaXMubWluKTsKICAgIH0sCiAgICBtYXhWYWx1ZTogZnVuY3Rpb24gbWF4VmFsdWUoKSB7CiAgICAgIHJldHVybiBfcGFyc2VGbG9hdCh0aGlzLm1heCk7CiAgICB9LAogICAgc3RlcE51bWVyaWM6IGZ1bmN0aW9uIHN0ZXBOdW1lcmljKCkgewogICAgICByZXR1cm4gdGhpcy5zdGVwID4gMCA/IF9wYXJzZUZsb2F0KHRoaXMuc3RlcCkgOiAwOwogICAgfSwKICAgIGlucHV0V2lkdGg6IGZ1bmN0aW9uIGlucHV0V2lkdGgoKSB7CiAgICAgIHZhciBpbnB1dFdpZHRoID0gKHRoaXMucm91bmRWYWx1ZSh0aGlzLmludGVybmFsVmFsdWUpIC0gdGhpcy5taW5WYWx1ZSkgLyAodGhpcy5tYXhWYWx1ZSAtIHRoaXMubWluVmFsdWUpICogMTAwOwogICAgICByZXR1cm4gaXNOYU4oaW5wdXRXaWR0aCkgPyAwIDogaW5wdXRXaWR0aDsKICAgIH0sCiAgICB0cmFja0ZpbGxTdHlsZXM6IGZ1bmN0aW9uIHRyYWNrRmlsbFN0eWxlcygpIHsKICAgICAgdmFyIHN0YXJ0RGlyID0gdGhpcy52ZXJ0aWNhbCA/ICdib3R0b20nIDogJ2xlZnQnOwogICAgICB2YXIgZW5kRGlyID0gdGhpcy52ZXJ0aWNhbCA/ICd0b3AnIDogJ3JpZ2h0JzsKICAgICAgdmFyIHZhbHVlRGlyID0gdGhpcy52ZXJ0aWNhbCA/ICdoZWlnaHQnIDogJ3dpZHRoJzsKICAgICAgdmFyIHN0YXJ0ID0gdGhpcy4kdnVldGlmeS5ydGwgPyAnYXV0bycgOiAnMCc7CiAgICAgIHZhciBlbmQgPSB0aGlzLiR2dWV0aWZ5LnJ0bCA/ICcwJyA6ICdhdXRvJzsKICAgICAgdmFyIHZhbHVlID0gdGhpcy5pc0Rpc2FibGVkID8gImNhbGMoIi5jb25jYXQodGhpcy5pbnB1dFdpZHRoLCAiJSAtIDEwcHgpIikgOiAiIi5jb25jYXQodGhpcy5pbnB1dFdpZHRoLCAiJSIpOwogICAgICByZXR1cm4gX2RlZmluZVByb3BlcnR5KF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoewogICAgICAgIHRyYW5zaXRpb246IHRoaXMudHJhY2tUcmFuc2l0aW9uCiAgICAgIH0sIHN0YXJ0RGlyLCBzdGFydCksIGVuZERpciwgZW5kKSwgdmFsdWVEaXIsIHZhbHVlKTsKICAgIH0sCiAgICB0cmFja1N0eWxlczogZnVuY3Rpb24gdHJhY2tTdHlsZXMoKSB7CiAgICAgIHZhciBzdGFydERpciA9IHRoaXMudmVydGljYWwgPyB0aGlzLiR2dWV0aWZ5LnJ0bCA/ICdib3R0b20nIDogJ3RvcCcgOiB0aGlzLiR2dWV0aWZ5LnJ0bCA/ICdsZWZ0JyA6ICdyaWdodCc7CiAgICAgIHZhciBlbmREaXIgPSB0aGlzLnZlcnRpY2FsID8gJ2hlaWdodCcgOiAnd2lkdGgnOwogICAgICB2YXIgc3RhcnQgPSAnMHB4JzsKICAgICAgdmFyIGVuZCA9IHRoaXMuaXNEaXNhYmxlZCA/ICJjYWxjKCIuY29uY2F0KDEwMCAtIHRoaXMuaW5wdXRXaWR0aCwgIiUgLSAxMHB4KSIpIDogImNhbGMoIi5jb25jYXQoMTAwIC0gdGhpcy5pbnB1dFdpZHRoLCAiJSkiKTsKICAgICAgcmV0dXJuIF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoewogICAgICAgIHRyYW5zaXRpb246IHRoaXMudHJhY2tUcmFuc2l0aW9uCiAgICAgIH0sIHN0YXJ0RGlyLCBzdGFydCksIGVuZERpciwgZW5kKTsKICAgIH0sCiAgICBzaG93VGlja3M6IGZ1bmN0aW9uIHNob3dUaWNrcygpIHsKICAgICAgcmV0dXJuIHRoaXMudGlja0xhYmVscy5sZW5ndGggPiAwIHx8ICEhKCF0aGlzLmlzRGlzYWJsZWQgJiYgdGhpcy5zdGVwTnVtZXJpYyAmJiB0aGlzLnRpY2tzKTsKICAgIH0sCiAgICBudW1UaWNrczogZnVuY3Rpb24gbnVtVGlja3MoKSB7CiAgICAgIHJldHVybiBNYXRoLmNlaWwoKHRoaXMubWF4VmFsdWUgLSB0aGlzLm1pblZhbHVlKSAvIHRoaXMuc3RlcE51bWVyaWMpOwogICAgfSwKICAgIHNob3dUaHVtYkxhYmVsOiBmdW5jdGlvbiBzaG93VGh1bWJMYWJlbCgpIHsKICAgICAgcmV0dXJuICF0aGlzLmlzRGlzYWJsZWQgJiYgISEodGhpcy50aHVtYkxhYmVsIHx8IHRoaXMuJHNjb3BlZFNsb3RzWyd0aHVtYi1sYWJlbCddKTsKICAgIH0sCiAgICBjb21wdXRlZFRyYWNrQ29sb3I6IGZ1bmN0aW9uIGNvbXB1dGVkVHJhY2tDb2xvcigpIHsKICAgICAgaWYgKHRoaXMuaXNEaXNhYmxlZCkgcmV0dXJuIHVuZGVmaW5lZDsKICAgICAgaWYgKHRoaXMudHJhY2tDb2xvcikgcmV0dXJuIHRoaXMudHJhY2tDb2xvcjsKICAgICAgaWYgKHRoaXMuaXNEYXJrKSByZXR1cm4gdGhpcy52YWxpZGF0aW9uU3RhdGU7CiAgICAgIHJldHVybiB0aGlzLnZhbGlkYXRpb25TdGF0ZSB8fCAncHJpbWFyeSBsaWdodGVuLTMnOwogICAgfSwKICAgIGNvbXB1dGVkVHJhY2tGaWxsQ29sb3I6IGZ1bmN0aW9uIGNvbXB1dGVkVHJhY2tGaWxsQ29sb3IoKSB7CiAgICAgIGlmICh0aGlzLmlzRGlzYWJsZWQpIHJldHVybiB1bmRlZmluZWQ7CiAgICAgIGlmICh0aGlzLnRyYWNrRmlsbENvbG9yKSByZXR1cm4gdGhpcy50cmFja0ZpbGxDb2xvcjsKICAgICAgcmV0dXJuIHRoaXMudmFsaWRhdGlvblN0YXRlIHx8IHRoaXMuY29tcHV0ZWRDb2xvcjsKICAgIH0sCiAgICBjb21wdXRlZFRodW1iQ29sb3I6IGZ1bmN0aW9uIGNvbXB1dGVkVGh1bWJDb2xvcigpIHsKICAgICAgaWYgKHRoaXMudGh1bWJDb2xvcikgcmV0dXJuIHRoaXMudGh1bWJDb2xvcjsKICAgICAgcmV0dXJuIHRoaXMudmFsaWRhdGlvblN0YXRlIHx8IHRoaXMuY29tcHV0ZWRDb2xvcjsKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBtaW46IGZ1bmN0aW9uIG1pbih2YWwpIHsKICAgICAgdmFyIHBhcnNlZCA9IF9wYXJzZUZsb2F0KHZhbCk7CiAgICAgIHBhcnNlZCA+IHRoaXMuaW50ZXJuYWxWYWx1ZSAmJiB0aGlzLiRlbWl0KCdpbnB1dCcsIHBhcnNlZCk7CiAgICB9LAogICAgbWF4OiBmdW5jdGlvbiBtYXgodmFsKSB7CiAgICAgIHZhciBwYXJzZWQgPSBfcGFyc2VGbG9hdCh2YWwpOwogICAgICBwYXJzZWQgPCB0aGlzLmludGVybmFsVmFsdWUgJiYgdGhpcy4kZW1pdCgnaW5wdXQnLCBwYXJzZWQpOwogICAgfSwKICAgIHZhbHVlOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIodikgewogICAgICAgIHRoaXMuaW50ZXJuYWxWYWx1ZSA9IHY7CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8vIFdpdGhvdXQgYSB2LWFwcCwgaU9TIGRvZXMgbm90IHdvcmsgd2l0aCBib2R5IHNlbGVjdG9ycwogICAgdGhpcy5hcHAgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdbZGF0YS1hcHBdJykgfHwgY29uc29sZVdhcm4oJ01pc3Npbmcgdi1hcHAgb3IgYSBub24tYm9keSB3cmFwcGluZyBlbGVtZW50IHdpdGggdGhlIFtkYXRhLWFwcF0gYXR0cmlidXRlJywgdGhpcyk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZW5EZWZhdWx0U2xvdDogZnVuY3Rpb24gZ2VuRGVmYXVsdFNsb3QoKSB7CiAgICAgIHZhciBjaGlsZHJlbiA9IFt0aGlzLmdlbkxhYmVsKCldOwogICAgICB2YXIgc2xpZGVyID0gdGhpcy5nZW5TbGlkZXIoKTsKICAgICAgdGhpcy5pbnZlcnNlTGFiZWwgPyBjaGlsZHJlbi51bnNoaWZ0KHNsaWRlcikgOiBjaGlsZHJlbi5wdXNoKHNsaWRlcik7CiAgICAgIGNoaWxkcmVuLnB1c2godGhpcy5nZW5Qcm9ncmVzcygpKTsKICAgICAgcmV0dXJuIGNoaWxkcmVuOwogICAgfSwKICAgIGdlblNsaWRlcjogZnVuY3Rpb24gZ2VuU2xpZGVyKCkgewogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgewogICAgICAgICJjbGFzcyI6IF9vYmplY3RTcHJlYWQoewogICAgICAgICAgJ3Ytc2xpZGVyJzogdHJ1ZSwKICAgICAgICAgICd2LXNsaWRlci0taG9yaXpvbnRhbCc6ICF0aGlzLnZlcnRpY2FsLAogICAgICAgICAgJ3Ytc2xpZGVyLS12ZXJ0aWNhbCc6IHRoaXMudmVydGljYWwsCiAgICAgICAgICAndi1zbGlkZXItLWZvY3VzZWQnOiB0aGlzLmlzRm9jdXNlZCwKICAgICAgICAgICd2LXNsaWRlci0tYWN0aXZlJzogdGhpcy5pc0FjdGl2ZSwKICAgICAgICAgICd2LXNsaWRlci0tZGlzYWJsZWQnOiB0aGlzLmlzRGlzYWJsZWQsCiAgICAgICAgICAndi1zbGlkZXItLXJlYWRvbmx5JzogdGhpcy5pc1JlYWRvbmx5CiAgICAgICAgfSwgdGhpcy50aGVtZUNsYXNzZXMpLAogICAgICAgIGRpcmVjdGl2ZXM6IFt7CiAgICAgICAgICBuYW1lOiAnY2xpY2stb3V0c2lkZScsCiAgICAgICAgICB2YWx1ZTogdGhpcy5vbkJsdXIKICAgICAgICB9XSwKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IHRoaXMub25TbGlkZXJDbGljaywKICAgICAgICAgIG1vdXNlZG93bjogdGhpcy5vblNsaWRlck1vdXNlRG93biwKICAgICAgICAgIHRvdWNoc3RhcnQ6IHRoaXMub25TbGlkZXJNb3VzZURvd24KICAgICAgICB9CiAgICAgIH0sIHRoaXMuZ2VuQ2hpbGRyZW4oKSk7CiAgICB9LAogICAgZ2VuQ2hpbGRyZW46IGZ1bmN0aW9uIGdlbkNoaWxkcmVuKCkgewogICAgICByZXR1cm4gW3RoaXMuZ2VuSW5wdXQoKSwgdGhpcy5nZW5UcmFja0NvbnRhaW5lcigpLCB0aGlzLmdlblN0ZXBzKCksIHRoaXMuZ2VuVGh1bWJDb250YWluZXIodGhpcy5pbnRlcm5hbFZhbHVlLCB0aGlzLmlucHV0V2lkdGgsIHRoaXMuaXNBY3RpdmUsIHRoaXMuaXNGb2N1c2VkLCB0aGlzLm9uRm9jdXMsIHRoaXMub25CbHVyKV07CiAgICB9LAogICAgZ2VuSW5wdXQ6IGZ1bmN0aW9uIGdlbklucHV0KCkgewogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnaW5wdXQnLCB7CiAgICAgICAgYXR0cnM6IF9vYmplY3RTcHJlYWQoewogICAgICAgICAgdmFsdWU6IHRoaXMuaW50ZXJuYWxWYWx1ZSwKICAgICAgICAgIGlkOiB0aGlzLmNvbXB1dGVkSWQsCiAgICAgICAgICBkaXNhYmxlZDogdHJ1ZSwKICAgICAgICAgIHJlYWRvbmx5OiB0cnVlLAogICAgICAgICAgdGFiaW5kZXg6IC0xCiAgICAgICAgfSwgdGhpcy4kYXR0cnMpIC8vIG9uOiB0aGlzLmdlbkxpc3RlbmVycygpLCAvLyBUT0RPOiBkbyB3ZSBuZWVkIHRvIGF0dGFjaCB0aGUgbGlzdGVuZXJzIHRvIGlucHV0PwogICAgICB9KTsKICAgIH0sCiAgICBnZW5UcmFja0NvbnRhaW5lcjogZnVuY3Rpb24gZ2VuVHJhY2tDb250YWluZXIoKSB7CiAgICAgIHZhciBjaGlsZHJlbiA9IFt0aGlzLiRjcmVhdGVFbGVtZW50KCdkaXYnLCB0aGlzLnNldEJhY2tncm91bmRDb2xvcih0aGlzLmNvbXB1dGVkVHJhY2tDb2xvciwgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1zbGlkZXJfX3RyYWNrLWJhY2tncm91bmQnLAogICAgICAgIHN0eWxlOiB0aGlzLnRyYWNrU3R5bGVzCiAgICAgIH0pKSwgdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb21wdXRlZFRyYWNrRmlsbENvbG9yLCB7CiAgICAgICAgc3RhdGljQ2xhc3M6ICd2LXNsaWRlcl9fdHJhY2stZmlsbCcsCiAgICAgICAgc3R5bGU6IHRoaXMudHJhY2tGaWxsU3R5bGVzCiAgICAgIH0pKV07CiAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KCdkaXYnLCB7CiAgICAgICAgc3RhdGljQ2xhc3M6ICd2LXNsaWRlcl9fdHJhY2stY29udGFpbmVyJywKICAgICAgICByZWY6ICd0cmFjaycKICAgICAgfSwgY2hpbGRyZW4pOwogICAgfSwKICAgIGdlblN0ZXBzOiBmdW5jdGlvbiBnZW5TdGVwcygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgaWYgKCF0aGlzLnN0ZXAgfHwgIXRoaXMuc2hvd1RpY2tzKSByZXR1cm4gbnVsbDsKICAgICAgdmFyIHRpY2tTaXplID0gX3BhcnNlRmxvYXQodGhpcy50aWNrU2l6ZSk7CiAgICAgIHZhciByYW5nZSA9IGNyZWF0ZVJhbmdlKHRoaXMubnVtVGlja3MgKyAxKTsKICAgICAgdmFyIGRpcmVjdGlvbiA9IHRoaXMudmVydGljYWwgPyAnYm90dG9tJyA6IHRoaXMuJHZ1ZXRpZnkucnRsID8gJ3JpZ2h0JyA6ICdsZWZ0JzsKICAgICAgdmFyIG9mZnNldERpcmVjdGlvbiA9IHRoaXMudmVydGljYWwgPyB0aGlzLiR2dWV0aWZ5LnJ0bCA/ICdsZWZ0JyA6ICdyaWdodCcgOiAndG9wJzsKICAgICAgaWYgKHRoaXMudmVydGljYWwpIF9yZXZlcnNlSW5zdGFuY2VQcm9wZXJ0eShyYW5nZSkuY2FsbChyYW5nZSk7CiAgICAgIHZhciB0aWNrcyA9IF9tYXBJbnN0YW5jZVByb3BlcnR5KHJhbmdlKS5jYWxsKHJhbmdlLCBmdW5jdGlvbiAoaW5kZXgpIHsKICAgICAgICB2YXIgX2NvbnRleHQ7CiAgICAgICAgdmFyIGNoaWxkcmVuID0gW107CiAgICAgICAgaWYgKF90aGlzLnRpY2tMYWJlbHNbaW5kZXhdKSB7CiAgICAgICAgICBjaGlsZHJlbi5wdXNoKF90aGlzLiRjcmVhdGVFbGVtZW50KCdkaXYnLCB7CiAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAndi1zbGlkZXJfX3RpY2stbGFiZWwnCiAgICAgICAgICB9LCBfdGhpcy50aWNrTGFiZWxzW2luZGV4XSkpOwogICAgICAgIH0KICAgICAgICB2YXIgd2lkdGggPSBpbmRleCAqICgxMDAgLyBfdGhpcy5udW1UaWNrcyk7CiAgICAgICAgdmFyIGZpbGxlZCA9IF90aGlzLiR2dWV0aWZ5LnJ0bCA/IDEwMCAtIF90aGlzLmlucHV0V2lkdGggPCB3aWR0aCA6IHdpZHRoIDwgX3RoaXMuaW5wdXRXaWR0aDsKICAgICAgICByZXR1cm4gX3RoaXMuJGNyZWF0ZUVsZW1lbnQoJ3NwYW4nLCB7CiAgICAgICAgICBrZXk6IGluZGV4LAogICAgICAgICAgc3RhdGljQ2xhc3M6ICd2LXNsaWRlcl9fdGljaycsCiAgICAgICAgICAiY2xhc3MiOiB7CiAgICAgICAgICAgICd2LXNsaWRlcl9fdGljay0tZmlsbGVkJzogZmlsbGVkCiAgICAgICAgICB9LAogICAgICAgICAgc3R5bGU6IF9kZWZpbmVQcm9wZXJ0eShfZGVmaW5lUHJvcGVydHkoewogICAgICAgICAgICB3aWR0aDogIiIuY29uY2F0KHRpY2tTaXplLCAicHgiKSwKICAgICAgICAgICAgaGVpZ2h0OiAiIi5jb25jYXQodGlja1NpemUsICJweCIpCiAgICAgICAgICB9LCBkaXJlY3Rpb24sIF9jb25jYXRJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0ID0gImNhbGMoIi5jb25jYXQod2lkdGgsICIlIC0gIikpLmNhbGwoX2NvbnRleHQsIHRpY2tTaXplIC8gMiwgInB4KSIpKSwgb2Zmc2V0RGlyZWN0aW9uLCAiY2FsYyg1MCUgLSAiLmNvbmNhdCh0aWNrU2l6ZSAvIDIsICJweCkiKSkKICAgICAgICB9LCBjaGlsZHJlbik7CiAgICAgIH0pOwogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1zbGlkZXJfX3RpY2tzLWNvbnRhaW5lcicsCiAgICAgICAgImNsYXNzIjogewogICAgICAgICAgJ3Ytc2xpZGVyX190aWNrcy1jb250YWluZXItLWFsd2F5cy1zaG93JzogdGhpcy50aWNrcyA9PT0gJ2Fsd2F5cycgfHwgdGhpcy50aWNrTGFiZWxzLmxlbmd0aCA+IDAKICAgICAgICB9CiAgICAgIH0sIHRpY2tzKTsKICAgIH0sCiAgICBnZW5UaHVtYkNvbnRhaW5lcjogZnVuY3Rpb24gZ2VuVGh1bWJDb250YWluZXIodmFsdWUsIHZhbHVlV2lkdGgsIGlzQWN0aXZlLCBpc0ZvY3VzZWQsIG9uRm9jdXMsIG9uQmx1cikgewogICAgICB2YXIgcmVmID0gYXJndW1lbnRzLmxlbmd0aCA+IDYgJiYgYXJndW1lbnRzWzZdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbNl0gOiAndGh1bWInOwogICAgICB2YXIgY2hpbGRyZW4gPSBbdGhpcy5nZW5UaHVtYigpXTsKICAgICAgdmFyIHRodW1iTGFiZWxDb250ZW50ID0gdGhpcy5nZW5UaHVtYkxhYmVsQ29udGVudCh2YWx1ZSk7CiAgICAgIHRoaXMuc2hvd1RodW1iTGFiZWwgJiYgY2hpbGRyZW4ucHVzaCh0aGlzLmdlblRodW1iTGFiZWwodGh1bWJMYWJlbENvbnRlbnQpKTsKICAgICAgcmV0dXJuIHRoaXMuJGNyZWF0ZUVsZW1lbnQoJ2RpdicsIHRoaXMuc2V0VGV4dENvbG9yKHRoaXMuY29tcHV0ZWRUaHVtYkNvbG9yLCB7CiAgICAgICAgcmVmOiByZWYsCiAgICAgICAga2V5OiByZWYsCiAgICAgICAgc3RhdGljQ2xhc3M6ICd2LXNsaWRlcl9fdGh1bWItY29udGFpbmVyJywKICAgICAgICAiY2xhc3MiOiB7CiAgICAgICAgICAndi1zbGlkZXJfX3RodW1iLWNvbnRhaW5lci0tYWN0aXZlJzogaXNBY3RpdmUsCiAgICAgICAgICAndi1zbGlkZXJfX3RodW1iLWNvbnRhaW5lci0tZm9jdXNlZCc6IGlzRm9jdXNlZCwKICAgICAgICAgICd2LXNsaWRlcl9fdGh1bWItY29udGFpbmVyLS1zaG93LWxhYmVsJzogdGhpcy5zaG93VGh1bWJMYWJlbAogICAgICAgIH0sCiAgICAgICAgc3R5bGU6IHRoaXMuZ2V0VGh1bWJDb250YWluZXJTdHlsZXModmFsdWVXaWR0aCksCiAgICAgICAgYXR0cnM6IHsKICAgICAgICAgIHJvbGU6ICdzbGlkZXInLAogICAgICAgICAgdGFiaW5kZXg6IHRoaXMuaXNEaXNhYmxlZCA/IC0xIDogdGhpcy4kYXR0cnMudGFiaW5kZXggPyB0aGlzLiRhdHRycy50YWJpbmRleCA6IDAsCiAgICAgICAgICAnYXJpYS1sYWJlbCc6IHRoaXMuJGF0dHJzWydhcmlhLWxhYmVsJ10gfHwgdGhpcy5sYWJlbCwKICAgICAgICAgICdhcmlhLXZhbHVlbWluJzogdGhpcy5taW4sCiAgICAgICAgICAnYXJpYS12YWx1ZW1heCc6IHRoaXMubWF4LAogICAgICAgICAgJ2FyaWEtdmFsdWVub3cnOiB0aGlzLmludGVybmFsVmFsdWUsCiAgICAgICAgICAnYXJpYS1yZWFkb25seSc6IFN0cmluZyh0aGlzLmlzUmVhZG9ubHkpLAogICAgICAgICAgJ2FyaWEtb3JpZW50YXRpb24nOiB0aGlzLnZlcnRpY2FsID8gJ3ZlcnRpY2FsJyA6ICdob3Jpem9udGFsJwogICAgICAgIH0sCiAgICAgICAgb246IHsKICAgICAgICAgIGZvY3VzOiBvbkZvY3VzLAogICAgICAgICAgYmx1cjogb25CbHVyLAogICAgICAgICAga2V5ZG93bjogdGhpcy5vbktleURvd24KICAgICAgICB9CiAgICAgIH0pLCBjaGlsZHJlbik7CiAgICB9LAogICAgZ2VuVGh1bWJMYWJlbENvbnRlbnQ6IGZ1bmN0aW9uIGdlblRodW1iTGFiZWxDb250ZW50KHZhbHVlKSB7CiAgICAgIHJldHVybiB0aGlzLiRzY29wZWRTbG90c1sndGh1bWItbGFiZWwnXSA/IHRoaXMuJHNjb3BlZFNsb3RzWyd0aHVtYi1sYWJlbCddKHsKICAgICAgICB2YWx1ZTogdmFsdWUKICAgICAgfSkgOiBbdGhpcy4kY3JlYXRlRWxlbWVudCgnc3BhbicsIFtTdHJpbmcodmFsdWUpXSldOwogICAgfSwKICAgIGdlblRodW1iTGFiZWw6IGZ1bmN0aW9uIGdlblRodW1iTGFiZWwoY29udGVudCkgewogICAgICB2YXIgc2l6ZSA9IGNvbnZlcnRUb1VuaXQodGhpcy50aHVtYlNpemUpOwogICAgICB2YXIgdHJhbnNmb3JtID0gdGhpcy52ZXJ0aWNhbCA/ICJ0cmFuc2xhdGVZKDIwJSkgdHJhbnNsYXRlWSgiLmNvbmNhdChOdW1iZXIodGhpcy50aHVtYlNpemUpIC8gMyAtIDEsICJweCkgdHJhbnNsYXRlWCg1NSUpIHJvdGF0ZSgxMzVkZWcpIikgOiAidHJhbnNsYXRlWSgtMjAlKSB0cmFuc2xhdGVZKC0xMnB4KSB0cmFuc2xhdGVYKC01MCUpIHJvdGF0ZSg0NWRlZykiOwogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudChWU2NhbGVUcmFuc2l0aW9uLCB7CiAgICAgICAgcHJvcHM6IHsKICAgICAgICAgIG9yaWdpbjogJ2JvdHRvbSBjZW50ZXInCiAgICAgICAgfQogICAgICB9LCBbdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgewogICAgICAgIHN0YXRpY0NsYXNzOiAndi1zbGlkZXJfX3RodW1iLWxhYmVsLWNvbnRhaW5lcicsCiAgICAgICAgZGlyZWN0aXZlczogW3sKICAgICAgICAgIG5hbWU6ICdzaG93JywKICAgICAgICAgIHZhbHVlOiB0aGlzLmlzRm9jdXNlZCB8fCB0aGlzLmlzQWN0aXZlIHx8IHRoaXMudGh1bWJMYWJlbCA9PT0gJ2Fsd2F5cycKICAgICAgICB9XQogICAgICB9LCBbdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb21wdXRlZFRodW1iQ29sb3IsIHsKICAgICAgICBzdGF0aWNDbGFzczogJ3Ytc2xpZGVyX190aHVtYi1sYWJlbCcsCiAgICAgICAgc3R5bGU6IHsKICAgICAgICAgIGhlaWdodDogc2l6ZSwKICAgICAgICAgIHdpZHRoOiBzaXplLAogICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2Zvcm0KICAgICAgICB9CiAgICAgIH0pLCBbdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgY29udGVudCldKV0pXSk7CiAgICB9LAogICAgZ2VuVGh1bWI6IGZ1bmN0aW9uIGdlblRodW1iKCkgewogICAgICByZXR1cm4gdGhpcy4kY3JlYXRlRWxlbWVudCgnZGl2JywgdGhpcy5zZXRCYWNrZ3JvdW5kQ29sb3IodGhpcy5jb21wdXRlZFRodW1iQ29sb3IsIHsKICAgICAgICBzdGF0aWNDbGFzczogJ3Ytc2xpZGVyX190aHVtYicKICAgICAgfSkpOwogICAgfSwKICAgIGdldFRodW1iQ29udGFpbmVyU3R5bGVzOiBmdW5jdGlvbiBnZXRUaHVtYkNvbnRhaW5lclN0eWxlcyh3aWR0aCkgewogICAgICB2YXIgZGlyZWN0aW9uID0gdGhpcy52ZXJ0aWNhbCA/ICd0b3AnIDogJ2xlZnQnOwogICAgICB2YXIgdmFsdWUgPSB0aGlzLiR2dWV0aWZ5LnJ0bCA/IDEwMCAtIHdpZHRoIDogd2lkdGg7CiAgICAgIHZhbHVlID0gdGhpcy52ZXJ0aWNhbCA/IDEwMCAtIHZhbHVlIDogdmFsdWU7CiAgICAgIHJldHVybiBfZGVmaW5lUHJvcGVydHkoewogICAgICAgIHRyYW5zaXRpb246IHRoaXMudHJhY2tUcmFuc2l0aW9uCiAgICAgIH0sIGRpcmVjdGlvbiwgIiIuY29uY2F0KHZhbHVlLCAiJSIpKTsKICAgIH0sCiAgICBvblNsaWRlck1vdXNlRG93bjogZnVuY3Rpb24gb25TbGlkZXJNb3VzZURvd24oZSkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdmFyIF9hOwogICAgICBlLnByZXZlbnREZWZhdWx0KCk7CiAgICAgIHRoaXMub2xkVmFsdWUgPSB0aGlzLmludGVybmFsVmFsdWU7CiAgICAgIHRoaXMuaXNBY3RpdmUgPSB0cnVlOwogICAgICBpZiAoKF9hID0gZS50YXJnZXQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5tYXRjaGVzKCcudi1zbGlkZXJfX3RodW1iLWNvbnRhaW5lciwgLnYtc2xpZGVyX190aHVtYi1jb250YWluZXIgKicpKSB7CiAgICAgICAgdGhpcy50aHVtYlByZXNzZWQgPSB0cnVlOwogICAgICAgIHZhciBkb21SZWN0ID0gZS50YXJnZXQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7CiAgICAgICAgdmFyIHRvdWNoID0gJ3RvdWNoZXMnIGluIGUgPyBlLnRvdWNoZXNbMF0gOiBlOwogICAgICAgIHRoaXMuc3RhcnRPZmZzZXQgPSB0aGlzLnZlcnRpY2FsID8gdG91Y2guY2xpZW50WSAtIChkb21SZWN0LnRvcCArIGRvbVJlY3QuaGVpZ2h0IC8gMikgOiB0b3VjaC5jbGllbnRYIC0gKGRvbVJlY3QubGVmdCArIGRvbVJlY3Qud2lkdGggLyAyKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnN0YXJ0T2Zmc2V0ID0gMDsKICAgICAgICB3aW5kb3cuY2xlYXJUaW1lb3V0KHRoaXMubW91c2VUaW1lb3V0KTsKICAgICAgICB0aGlzLm1vdXNlVGltZW91dCA9IF9zZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzMi50aHVtYlByZXNzZWQgPSB0cnVlOwogICAgICAgIH0sIDMwMCk7CiAgICAgIH0KICAgICAgdmFyIG1vdXNlVXBPcHRpb25zID0gcGFzc2l2ZVN1cHBvcnRlZCA/IHsKICAgICAgICBwYXNzaXZlOiB0cnVlLAogICAgICAgIGNhcHR1cmU6IHRydWUKICAgICAgfSA6IHRydWU7CiAgICAgIHZhciBtb3VzZU1vdmVPcHRpb25zID0gcGFzc2l2ZVN1cHBvcnRlZCA/IHsKICAgICAgICBwYXNzaXZlOiB0cnVlCiAgICAgIH0gOiBmYWxzZTsKICAgICAgdmFyIGlzVG91Y2hFdmVudCA9ICd0b3VjaGVzJyBpbiBlOwogICAgICB0aGlzLm9uTW91c2VNb3ZlKGUpOwogICAgICB0aGlzLmFwcC5hZGRFdmVudExpc3RlbmVyKGlzVG91Y2hFdmVudCA/ICd0b3VjaG1vdmUnIDogJ21vdXNlbW92ZScsIHRoaXMub25Nb3VzZU1vdmUsIG1vdXNlTW92ZU9wdGlvbnMpOwogICAgICBhZGRPbmNlRXZlbnRMaXN0ZW5lcih0aGlzLmFwcCwgaXNUb3VjaEV2ZW50ID8gJ3RvdWNoZW5kJyA6ICdtb3VzZXVwJywgdGhpcy5vblNsaWRlck1vdXNlVXAsIG1vdXNlVXBPcHRpb25zKTsKICAgICAgdGhpcy4kZW1pdCgnc3RhcnQnLCB0aGlzLmludGVybmFsVmFsdWUpOwogICAgfSwKICAgIG9uU2xpZGVyTW91c2VVcDogZnVuY3Rpb24gb25TbGlkZXJNb3VzZVVwKGUpIHsKICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTsKICAgICAgd2luZG93LmNsZWFyVGltZW91dCh0aGlzLm1vdXNlVGltZW91dCk7CiAgICAgIHRoaXMudGh1bWJQcmVzc2VkID0gZmFsc2U7CiAgICAgIHZhciBtb3VzZU1vdmVPcHRpb25zID0gcGFzc2l2ZVN1cHBvcnRlZCA/IHsKICAgICAgICBwYXNzaXZlOiB0cnVlCiAgICAgIH0gOiBmYWxzZTsKICAgICAgdGhpcy5hcHAucmVtb3ZlRXZlbnRMaXN0ZW5lcigndG91Y2htb3ZlJywgdGhpcy5vbk1vdXNlTW92ZSwgbW91c2VNb3ZlT3B0aW9ucyk7CiAgICAgIHRoaXMuYXBwLnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlbW92ZScsIHRoaXMub25Nb3VzZU1vdmUsIG1vdXNlTW92ZU9wdGlvbnMpOwogICAgICB0aGlzLiRlbWl0KCdtb3VzZXVwJywgZSk7CiAgICAgIHRoaXMuJGVtaXQoJ2VuZCcsIHRoaXMuaW50ZXJuYWxWYWx1ZSk7CiAgICAgIGlmICghZGVlcEVxdWFsKHRoaXMub2xkVmFsdWUsIHRoaXMuaW50ZXJuYWxWYWx1ZSkpIHsKICAgICAgICB0aGlzLiRlbWl0KCdjaGFuZ2UnLCB0aGlzLmludGVybmFsVmFsdWUpOwogICAgICAgIHRoaXMubm9DbGljayA9IHRydWU7CiAgICAgIH0KICAgICAgdGhpcy5pc0FjdGl2ZSA9IGZhbHNlOwogICAgfSwKICAgIG9uTW91c2VNb3ZlOiBmdW5jdGlvbiBvbk1vdXNlTW92ZShlKSB7CiAgICAgIGlmIChlLnR5cGUgPT09ICdtb3VzZW1vdmUnKSB7CiAgICAgICAgdGhpcy50aHVtYlByZXNzZWQgPSB0cnVlOwogICAgICB9CiAgICAgIHRoaXMuaW50ZXJuYWxWYWx1ZSA9IHRoaXMucGFyc2VNb3VzZU1vdmUoZSk7CiAgICB9LAogICAgb25LZXlEb3duOiBmdW5jdGlvbiBvbktleURvd24oZSkgewogICAgICBpZiAoIXRoaXMuaXNJbnRlcmFjdGl2ZSkgcmV0dXJuOwogICAgICB2YXIgdmFsdWUgPSB0aGlzLnBhcnNlS2V5RG93bihlLCB0aGlzLmludGVybmFsVmFsdWUpOwogICAgICBpZiAodmFsdWUgPT0gbnVsbCB8fCB2YWx1ZSA8IHRoaXMubWluVmFsdWUgfHwgdmFsdWUgPiB0aGlzLm1heFZhbHVlKSByZXR1cm47CiAgICAgIHRoaXMuaW50ZXJuYWxWYWx1ZSA9IHZhbHVlOwogICAgICB0aGlzLiRlbWl0KCdjaGFuZ2UnLCB2YWx1ZSk7CiAgICB9LAogICAgb25TbGlkZXJDbGljazogZnVuY3Rpb24gb25TbGlkZXJDbGljayhlKSB7CiAgICAgIGlmICh0aGlzLm5vQ2xpY2spIHsKICAgICAgICB0aGlzLm5vQ2xpY2sgPSBmYWxzZTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdmFyIHRodW1iID0gdGhpcy4kcmVmcy50aHVtYjsKICAgICAgdGh1bWIuZm9jdXMoKTsKICAgICAgdGhpcy5vbk1vdXNlTW92ZShlKTsKICAgICAgdGhpcy4kZW1pdCgnY2hhbmdlJywgdGhpcy5pbnRlcm5hbFZhbHVlKTsKICAgIH0sCiAgICBvbkJsdXI6IGZ1bmN0aW9uIG9uQmx1cihlKSB7CiAgICAgIHRoaXMuaXNGb2N1c2VkID0gZmFsc2U7CiAgICAgIHRoaXMuJGVtaXQoJ2JsdXInLCBlKTsKICAgIH0sCiAgICBvbkZvY3VzOiBmdW5jdGlvbiBvbkZvY3VzKGUpIHsKICAgICAgdGhpcy5pc0ZvY3VzZWQgPSB0cnVlOwogICAgICB0aGlzLiRlbWl0KCdmb2N1cycsIGUpOwogICAgfSwKICAgIHBhcnNlTW91c2VNb3ZlOiBmdW5jdGlvbiBwYXJzZU1vdXNlTW92ZShlKSB7CiAgICAgIHZhciBzdGFydCA9IHRoaXMudmVydGljYWwgPyAndG9wJyA6ICdsZWZ0JzsKICAgICAgdmFyIGxlbmd0aCA9IHRoaXMudmVydGljYWwgPyAnaGVpZ2h0JyA6ICd3aWR0aCc7CiAgICAgIHZhciBjbGljayA9IHRoaXMudmVydGljYWwgPyAnY2xpZW50WScgOiAnY2xpZW50WCc7CiAgICAgIHZhciBfdGhpcyQkcmVmcyR0cmFjayRnZXQgPSB0aGlzLiRyZWZzLnRyYWNrLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLAogICAgICAgIHRyYWNrU3RhcnQgPSBfdGhpcyQkcmVmcyR0cmFjayRnZXRbc3RhcnRdLAogICAgICAgIHRyYWNrTGVuZ3RoID0gX3RoaXMkJHJlZnMkdHJhY2skZ2V0W2xlbmd0aF07CiAgICAgIHZhciBjbGlja09mZnNldCA9ICd0b3VjaGVzJyBpbiBlID8gZS50b3VjaGVzWzBdW2NsaWNrXSA6IGVbY2xpY2tdOyAvLyBJdCBpcyBwb3NzaWJsZSBmb3IgbGVmdCB0byBiZSBOYU4sIGZvcmNlIHRvIG51bWJlcgoKICAgICAgdmFyIGNsaWNrUG9zID0gTWF0aC5taW4oTWF0aC5tYXgoKGNsaWNrT2Zmc2V0IC0gdHJhY2tTdGFydCAtIHRoaXMuc3RhcnRPZmZzZXQpIC8gdHJhY2tMZW5ndGgsIDApLCAxKSB8fCAwOwogICAgICBpZiAodGhpcy52ZXJ0aWNhbCkgY2xpY2tQb3MgPSAxIC0gY2xpY2tQb3M7CiAgICAgIGlmICh0aGlzLiR2dWV0aWZ5LnJ0bCkgY2xpY2tQb3MgPSAxIC0gY2xpY2tQb3M7CiAgICAgIHJldHVybiBfcGFyc2VGbG9hdCh0aGlzLm1pbikgKyBjbGlja1BvcyAqICh0aGlzLm1heFZhbHVlIC0gdGhpcy5taW5WYWx1ZSk7CiAgICB9LAogICAgcGFyc2VLZXlEb3duOiBmdW5jdGlvbiBwYXJzZUtleURvd24oZSwgdmFsdWUpIHsKICAgICAgdmFyIF9jb250ZXh0MiwgX2NvbnRleHQzOwogICAgICBpZiAoIXRoaXMuaXNJbnRlcmFjdGl2ZSkgcmV0dXJuOwogICAgICB2YXIgcGFnZXVwID0ga2V5Q29kZXMucGFnZXVwLAogICAgICAgIHBhZ2Vkb3duID0ga2V5Q29kZXMucGFnZWRvd24sCiAgICAgICAgZW5kID0ga2V5Q29kZXMuZW5kLAogICAgICAgIGhvbWUgPSBrZXlDb2Rlcy5ob21lLAogICAgICAgIGxlZnQgPSBrZXlDb2Rlcy5sZWZ0LAogICAgICAgIHJpZ2h0ID0ga2V5Q29kZXMucmlnaHQsCiAgICAgICAgZG93biA9IGtleUNvZGVzLmRvd24sCiAgICAgICAgdXAgPSBrZXlDb2Rlcy51cDsKICAgICAgaWYgKCFfaW5jbHVkZXNJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0MiA9IFtwYWdldXAsIHBhZ2Vkb3duLCBlbmQsIGhvbWUsIGxlZnQsIHJpZ2h0LCBkb3duLCB1cF0pLmNhbGwoX2NvbnRleHQyLCBlLmtleUNvZGUpKSByZXR1cm47CiAgICAgIGUucHJldmVudERlZmF1bHQoKTsKICAgICAgdmFyIHN0ZXAgPSB0aGlzLnN0ZXBOdW1lcmljIHx8IDE7CiAgICAgIHZhciBzdGVwcyA9ICh0aGlzLm1heFZhbHVlIC0gdGhpcy5taW5WYWx1ZSkgLyBzdGVwOwogICAgICBpZiAoX2luY2x1ZGVzSW5zdGFuY2VQcm9wZXJ0eShfY29udGV4dDMgPSBbbGVmdCwgcmlnaHQsIGRvd24sIHVwXSkuY2FsbChfY29udGV4dDMsIGUua2V5Q29kZSkpIHsKICAgICAgICB2YXIgaW5jcmVhc2UgPSB0aGlzLiR2dWV0aWZ5LnJ0bCA/IFtsZWZ0LCB1cF0gOiBbcmlnaHQsIHVwXTsKICAgICAgICB2YXIgZGlyZWN0aW9uID0gX2luY2x1ZGVzSW5zdGFuY2VQcm9wZXJ0eShpbmNyZWFzZSkuY2FsbChpbmNyZWFzZSwgZS5rZXlDb2RlKSA/IDEgOiAtMTsKICAgICAgICB2YXIgbXVsdGlwbGllciA9IGUuc2hpZnRLZXkgPyAzIDogZS5jdHJsS2V5ID8gMiA6IDE7CiAgICAgICAgdmFsdWUgPSB2YWx1ZSArIGRpcmVjdGlvbiAqIHN0ZXAgKiBtdWx0aXBsaWVyOwogICAgICB9IGVsc2UgaWYgKGUua2V5Q29kZSA9PT0gaG9tZSkgewogICAgICAgIHZhbHVlID0gdGhpcy5taW5WYWx1ZTsKICAgICAgfSBlbHNlIGlmIChlLmtleUNvZGUgPT09IGVuZCkgewogICAgICAgIHZhbHVlID0gdGhpcy5tYXhWYWx1ZTsKICAgICAgfSBlbHNlIHsKICAgICAgICB2YXIgX2RpcmVjdGlvbiA9IGUua2V5Q29kZSA9PT0gcGFnZWRvd24gPyAxIDogLTE7CiAgICAgICAgdmFsdWUgPSB2YWx1ZSAtIF9kaXJlY3Rpb24gKiBzdGVwICogKHN0ZXBzID4gMTAwID8gc3RlcHMgLyAxMCA6IDEwKTsKICAgICAgfQogICAgICByZXR1cm4gdmFsdWU7CiAgICB9LAogICAgcm91bmRWYWx1ZTogZnVuY3Rpb24gcm91bmRWYWx1ZSh2YWx1ZSkgewogICAgICB2YXIgX2NvbnRleHQ0OwogICAgICBpZiAoIXRoaXMuc3RlcE51bWVyaWMpIHJldHVybiB2YWx1ZTsgLy8gRm9ybWF0IGlucHV0IHZhbHVlIHVzaW5nIHRoZSBzYW1lIG51bWJlcgogICAgICAvLyBvZiBkZWNpbWFscyBwbGFjZXMgYXMgaW4gdGhlIHN0ZXAgcHJvcAoKICAgICAgdmFyIHRyaW1tZWRTdGVwID0gX3RyaW1JbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0NCA9IHRoaXMuc3RlcC50b1N0cmluZygpKS5jYWxsKF9jb250ZXh0NCk7CiAgICAgIHZhciBkZWNpbWFscyA9IF9pbmRleE9mSW5zdGFuY2VQcm9wZXJ0eSh0cmltbWVkU3RlcCkuY2FsbCh0cmltbWVkU3RlcCwgJy4nKSA+IC0xID8gdHJpbW1lZFN0ZXAubGVuZ3RoIC0gX2luZGV4T2ZJbnN0YW5jZVByb3BlcnR5KHRyaW1tZWRTdGVwKS5jYWxsKHRyaW1tZWRTdGVwLCAnLicpIC0gMSA6IDA7CiAgICAgIHZhciBvZmZzZXQgPSB0aGlzLm1pblZhbHVlICUgdGhpcy5zdGVwTnVtZXJpYzsKICAgICAgdmFyIG5ld1ZhbHVlID0gTWF0aC5yb3VuZCgodmFsdWUgLSBvZmZzZXQpIC8gdGhpcy5zdGVwTnVtZXJpYykgKiB0aGlzLnN0ZXBOdW1lcmljICsgb2Zmc2V0OwogICAgICByZXR1cm4gX3BhcnNlRmxvYXQoTWF0aC5taW4obmV3VmFsdWUsIHRoaXMubWF4VmFsdWUpLnRvRml4ZWQoZGVjaW1hbHMpKTsKICAgIH0KICB9Cn0pOw=="}, {"version": 3, "names": ["VInput", "VScaleTransition", "mixins", "Loadable", "ClickOutside", "addOnceEventListener", "deepEqual", "keyCodes", "createRange", "convertToUnit", "passiveSupported", "console<PERSON>arn", "extend", "name", "directives", "props", "disabled", "Boolean", "inverse<PERSON>abel", "max", "type", "Number", "String", "min", "step", "thumbColor", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "validator", "v", "thumbSize", "tick<PERSON><PERSON><PERSON>", "Array", "default", "ticks", "tickSize", "trackColor", "trackFillColor", "value", "vertical", "data", "app", "oldValue", "thumbPressed", "mouseTimeout", "isFocused", "isActive", "noClick", "startOffset", "computed", "classes", "_objectSpread", "options", "call", "internalValue", "get", "lazyValue", "set", "val", "isNaN", "minValue", "roundValue", "Math", "maxValue", "$emit", "trackTransition", "showTicks", "stepNumeric", "_parseFloat", "inputWidth", "trackFillStyles", "startDir", "endDir", "valueDir", "start", "$vuetify", "rtl", "end", "isDisabled", "concat", "_defineProperty", "transition", "trackStyles", "length", "numTicks", "ceil", "showThumbLabel", "$scopedSlots", "computedTrackColor", "isDark", "validationState", "computedTrackFillColor", "computedColor", "computedThumbColor", "watch", "parsed", "handler", "immediate", "mounted", "document", "querySelector", "methods", "genDefaultSlot", "children", "gen<PERSON><PERSON><PERSON>", "slider", "genSlider", "unshift", "push", "genProgress", "$createElement", "is<PERSON><PERSON><PERSON>ly", "themeClasses", "onBlur", "on", "click", "onSliderClick", "mousedown", "onSliderMouseDown", "touchstart", "g<PERSON><PERSON><PERSON><PERSON><PERSON>", "genInput", "genTrackContainer", "genSteps", "gen<PERSON><PERSON>bContainer", "onFocus", "attrs", "id", "computedId", "readonly", "tabindex", "$attrs", "setBackgroundColor", "staticClass", "style", "ref", "_this", "range", "direction", "offsetDirection", "_reverseInstanceProperty", "_mapInstanceProperty", "index", "_context", "width", "filled", "key", "height", "_concatInstanceProperty", "valueWidth", "arguments", "gen<PERSON><PERSON>b", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gen<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "genT<PERSON>b<PERSON><PERSON>l", "setTextColor", "getThumbContainerStyles", "role", "label", "focus", "blur", "keydown", "onKeyDown", "content", "size", "transform", "origin", "e", "_this2", "preventDefault", "_a", "target", "matches", "domRect", "getBoundingClientRect", "touch", "touches", "clientY", "top", "clientX", "left", "window", "clearTimeout", "_setTimeout", "mouseUpOptions", "passive", "capture", "mouseMoveOptions", "isTouchEvent", "onMouseMove", "addEventListener", "onSliderMouseUp", "stopPropagation", "removeEventListener", "parseMouseMove", "isInteractive", "parseKeyDown", "thumb", "$refs", "_this$$refs$track$get", "track", "trackStart", "trackLength", "clickOffset", "clickPos", "_context2", "_context3", "pageup", "pagedown", "home", "right", "down", "up", "_includesInstanceProperty", "keyCode", "steps", "increase", "multiplier", "shift<PERSON>ey", "ctrl<PERSON>ey", "_context4", "trimmedStep", "_trimInstanceProperty", "toString", "decimals", "_indexOfInstanceProperty", "offset", "newValue", "round", "toFixed"], "sources": ["../../../src/components/VSlider/VSlider.ts"], "sourcesContent": ["import './VSlider.sass'\n\n// Components\nimport VInput from '../VInput'\nimport { VScaleTransition } from '../transitions'\n\n// Mixins\nimport mixins, { ExtractVue } from '../../util/mixins'\nimport Loadable from '../../mixins/loadable'\n\n// Directives\nimport ClickOutside from '../../directives/click-outside'\n\n// Helpers\nimport { addOnceEventListener, deepEqual, keyCodes, createRange, convertToUnit, passiveSupported } from '../../util/helpers'\nimport { consoleWarn } from '../../util/console'\n\n// Types\nimport Vue, { VNode, VNodeChildrenArrayContents, PropType } from 'vue'\nimport { ScopedSlotChildren } from 'vue/types/vnode'\nimport { PropValidator } from 'vue/types/options'\n\ninterface options extends Vue {\n  $refs: {\n    track: HTMLElement\n  }\n}\n\nexport default mixins<options &\n/* eslint-disable indent */\n  ExtractVue<[\n    typeof VInput,\n    typeof Loadable\n  ]>\n/* eslint-enable indent */\n>(\n  VInput,\n  Loadable\n/* @vue/component */\n).extend({\n  name: 'v-slider',\n\n  directives: {\n    ClickOutside,\n  },\n\n  mixins: [Loadable],\n\n  props: {\n    disabled: Boolean,\n    inverseLabel: Boolean,\n    max: {\n      type: [Number, String],\n      default: 100,\n    },\n    min: {\n      type: [Number, String],\n      default: 0,\n    },\n    step: {\n      type: [Number, String],\n      default: 1,\n    },\n    thumbColor: String,\n    thumbLabel: {\n      type: [Boolean, String] as PropType<boolean | 'always' | undefined>,\n      default: undefined,\n      validator: v => typeof v === 'boolean' || v === 'always',\n    },\n    thumbSize: {\n      type: [Number, String],\n      default: 32,\n    },\n    tickLabels: {\n      type: Array,\n      default: () => ([]),\n    } as PropValidator<string[]>,\n    ticks: {\n      type: [Boolean, String] as PropType<boolean | 'always'>,\n      default: false,\n      validator: v => typeof v === 'boolean' || v === 'always',\n    },\n    tickSize: {\n      type: [Number, String],\n      default: 2,\n    },\n    trackColor: String,\n    trackFillColor: String,\n    value: [Number, String],\n    vertical: Boolean,\n  },\n\n  data: () => ({\n    app: null as any,\n    oldValue: null as any,\n    thumbPressed: false,\n    mouseTimeout: -1,\n    isFocused: false,\n    isActive: false,\n    noClick: false, // Prevent click event if dragging took place, hack for #7915\n    startOffset: 0,\n  }),\n\n  computed: {\n    classes (): object {\n      return {\n        ...VInput.options.computed.classes.call(this),\n        'v-input__slider': true,\n        'v-input__slider--vertical': this.vertical,\n        'v-input__slider--inverse-label': this.inverseLabel,\n      }\n    },\n    internalValue: {\n      get (): number {\n        return this.lazyValue\n      },\n      set (val: number) {\n        val = isNaN(val) ? this.minValue : val\n        // Round value to ensure the\n        // entire slider range can\n        // be selected with step\n        const value = this.roundValue(Math.min(Math.max(val, this.minValue), this.maxValue))\n\n        if (value === this.lazyValue) return\n\n        this.lazyValue = value\n\n        this.$emit('input', value)\n      },\n    },\n    trackTransition (): string {\n      return this.thumbPressed\n        ? this.showTicks || this.stepNumeric\n          ? '0.1s cubic-bezier(0.25, 0.8, 0.5, 1)'\n          : 'none'\n        : ''\n    },\n    minValue (): number {\n      return parseFloat(this.min)\n    },\n    maxValue (): number {\n      return parseFloat(this.max)\n    },\n    stepNumeric (): number {\n      return this.step > 0 ? parseFloat(this.step) : 0\n    },\n    inputWidth (): number {\n      const inputWidth = (this.roundValue(this.internalValue) - this.minValue) / (this.maxValue - this.minValue) * 100\n\n      return isNaN(inputWidth) ? 0 : inputWidth\n    },\n    trackFillStyles (): Partial<CSSStyleDeclaration> {\n      const startDir = this.vertical ? 'bottom' : 'left'\n      const endDir = this.vertical ? 'top' : 'right'\n      const valueDir = this.vertical ? 'height' : 'width'\n\n      const start = this.$vuetify.rtl ? 'auto' : '0'\n      const end = this.$vuetify.rtl ? '0' : 'auto'\n      const value = this.isDisabled ? `calc(${this.inputWidth}% - 10px)` : `${this.inputWidth}%`\n\n      return {\n        transition: this.trackTransition,\n        [startDir]: start,\n        [endDir]: end,\n        [valueDir]: value,\n      }\n    },\n    trackStyles (): Partial<CSSStyleDeclaration> {\n      const startDir = this.vertical ? this.$vuetify.rtl ? 'bottom' : 'top' : this.$vuetify.rtl ? 'left' : 'right'\n      const endDir = this.vertical ? 'height' : 'width'\n\n      const start = '0px'\n      const end = this.isDisabled ? `calc(${100 - this.inputWidth}% - 10px)` : `calc(${100 - this.inputWidth}%)`\n\n      return {\n        transition: this.trackTransition,\n        [startDir]: start,\n        [endDir]: end,\n      }\n    },\n    showTicks (): boolean {\n      return this.tickLabels.length > 0 ||\n        !!(!this.isDisabled && this.stepNumeric && this.ticks)\n    },\n    numTicks (): number {\n      return Math.ceil((this.maxValue - this.minValue) / this.stepNumeric)\n    },\n    showThumbLabel (): boolean {\n      return !this.isDisabled && !!(\n        this.thumbLabel ||\n        this.$scopedSlots['thumb-label']\n      )\n    },\n    computedTrackColor (): string | undefined {\n      if (this.isDisabled) return undefined\n      if (this.trackColor) return this.trackColor\n      if (this.isDark) return this.validationState\n      return this.validationState || 'primary lighten-3'\n    },\n    computedTrackFillColor (): string | undefined {\n      if (this.isDisabled) return undefined\n      if (this.trackFillColor) return this.trackFillColor\n      return this.validationState || this.computedColor\n    },\n    computedThumbColor (): string | undefined {\n      if (this.thumbColor) return this.thumbColor\n      return this.validationState || this.computedColor\n    },\n  },\n\n  watch: {\n    min (val) {\n      const parsed = parseFloat(val)\n      parsed > this.internalValue && this.$emit('input', parsed)\n    },\n    max (val) {\n      const parsed = parseFloat(val)\n      parsed < this.internalValue && this.$emit('input', parsed)\n    },\n    value: {\n      handler (v: number) {\n        this.internalValue = v\n      },\n      immediate: true,\n    },\n  },\n\n  mounted () {\n    // Without a v-app, iOS does not work with body selectors\n    this.app = document.querySelector('[data-app]') ||\n      consoleWarn('Missing v-app or a non-body wrapping element with the [data-app] attribute', this)\n  },\n\n  methods: {\n    genDefaultSlot (): VNodeChildrenArrayContents {\n      const children: VNodeChildrenArrayContents = [this.genLabel()]\n      const slider = this.genSlider()\n      this.inverseLabel\n        ? children.unshift(slider)\n        : children.push(slider)\n\n      children.push(this.genProgress())\n\n      return children\n    },\n    genSlider (): VNode {\n      return this.$createElement('div', {\n        class: {\n          'v-slider': true,\n          'v-slider--horizontal': !this.vertical,\n          'v-slider--vertical': this.vertical,\n          'v-slider--focused': this.isFocused,\n          'v-slider--active': this.isActive,\n          'v-slider--disabled': this.isDisabled,\n          'v-slider--readonly': this.isReadonly,\n          ...this.themeClasses,\n        },\n        directives: [{\n          name: 'click-outside',\n          value: this.onBlur,\n        }],\n        on: {\n          click: this.onSliderClick,\n          mousedown: this.onSliderMouseDown,\n          touchstart: this.onSliderMouseDown,\n        },\n      }, this.genChildren())\n    },\n    genChildren (): VNodeChildrenArrayContents {\n      return [\n        this.genInput(),\n        this.genTrackContainer(),\n        this.genSteps(),\n        this.genThumbContainer(\n          this.internalValue,\n          this.inputWidth,\n          this.isActive,\n          this.isFocused,\n          this.onFocus,\n          this.onBlur,\n        ),\n      ]\n    },\n    genInput (): VNode {\n      return this.$createElement('input', {\n        attrs: {\n          value: this.internalValue,\n          id: this.computedId,\n          disabled: true,\n          readonly: true,\n          tabindex: -1,\n          ...this.$attrs,\n        },\n        // on: this.genListeners(), // TODO: do we need to attach the listeners to input?\n      })\n    },\n    genTrackContainer (): VNode {\n      const children = [\n        this.$createElement('div', this.setBackgroundColor(this.computedTrackColor, {\n          staticClass: 'v-slider__track-background',\n          style: this.trackStyles,\n        })),\n        this.$createElement('div', this.setBackgroundColor(this.computedTrackFillColor, {\n          staticClass: 'v-slider__track-fill',\n          style: this.trackFillStyles,\n        })),\n      ]\n\n      return this.$createElement('div', {\n        staticClass: 'v-slider__track-container',\n        ref: 'track',\n      }, children)\n    },\n    genSteps (): VNode | null {\n      if (!this.step || !this.showTicks) return null\n\n      const tickSize = parseFloat(this.tickSize)\n      const range = createRange(this.numTicks + 1)\n      const direction = this.vertical ? 'bottom' : (this.$vuetify.rtl ? 'right' : 'left')\n      const offsetDirection = this.vertical ? (this.$vuetify.rtl ? 'left' : 'right') : 'top'\n\n      if (this.vertical) range.reverse()\n\n      const ticks = range.map(index => {\n        const children = []\n\n        if (this.tickLabels[index]) {\n          children.push(this.$createElement('div', {\n            staticClass: 'v-slider__tick-label',\n          }, this.tickLabels[index]))\n        }\n\n        const width = index * (100 / this.numTicks)\n        const filled = this.$vuetify.rtl ? (100 - this.inputWidth) < width : width < this.inputWidth\n\n        return this.$createElement('span', {\n          key: index,\n          staticClass: 'v-slider__tick',\n          class: {\n            'v-slider__tick--filled': filled,\n          },\n          style: {\n            width: `${tickSize}px`,\n            height: `${tickSize}px`,\n            [direction]: `calc(${width}% - ${tickSize / 2}px)`,\n            [offsetDirection]: `calc(50% - ${tickSize / 2}px)`,\n          },\n        }, children)\n      })\n\n      return this.$createElement('div', {\n        staticClass: 'v-slider__ticks-container',\n        class: {\n          'v-slider__ticks-container--always-show': this.ticks === 'always' || this.tickLabels.length > 0,\n        },\n      }, ticks)\n    },\n    genThumbContainer (\n      value: number,\n      valueWidth: number,\n      isActive: boolean,\n      isFocused: boolean,\n      onFocus: Function,\n      onBlur: Function,\n      ref = 'thumb'\n    ): VNode {\n      const children = [this.genThumb()]\n\n      const thumbLabelContent = this.genThumbLabelContent(value)\n      this.showThumbLabel && children.push(this.genThumbLabel(thumbLabelContent))\n\n      return this.$createElement('div', this.setTextColor(this.computedThumbColor, {\n        ref,\n        key: ref,\n        staticClass: 'v-slider__thumb-container',\n        class: {\n          'v-slider__thumb-container--active': isActive,\n          'v-slider__thumb-container--focused': isFocused,\n          'v-slider__thumb-container--show-label': this.showThumbLabel,\n        },\n        style: this.getThumbContainerStyles(valueWidth),\n        attrs: {\n          role: 'slider',\n          tabindex: this.isDisabled ? -1 : this.$attrs.tabindex ? this.$attrs.tabindex : 0,\n          'aria-label': this.$attrs['aria-label'] || this.label,\n          'aria-valuemin': this.min,\n          'aria-valuemax': this.max,\n          'aria-valuenow': this.internalValue,\n          'aria-readonly': String(this.isReadonly),\n          'aria-orientation': this.vertical ? 'vertical' : 'horizontal',\n        },\n        on: {\n          focus: onFocus,\n          blur: onBlur,\n          keydown: this.onKeyDown,\n        },\n      }), children)\n    },\n    genThumbLabelContent (value: number | string): ScopedSlotChildren {\n      return this.$scopedSlots['thumb-label']\n        ? this.$scopedSlots['thumb-label']!({ value })\n        : [this.$createElement('span', [String(value)])]\n    },\n    genThumbLabel (content: ScopedSlotChildren): VNode {\n      const size = convertToUnit(this.thumbSize)\n\n      const transform = this.vertical\n        ? `translateY(20%) translateY(${(Number(this.thumbSize) / 3) - 1}px) translateX(55%) rotate(135deg)`\n        : `translateY(-20%) translateY(-12px) translateX(-50%) rotate(45deg)`\n\n      return this.$createElement(VScaleTransition, {\n        props: { origin: 'bottom center' },\n      }, [\n        this.$createElement('div', {\n          staticClass: 'v-slider__thumb-label-container',\n          directives: [{\n            name: 'show',\n            value: this.isFocused || this.isActive || this.thumbLabel === 'always',\n          }],\n        }, [\n          this.$createElement('div', this.setBackgroundColor(this.computedThumbColor, {\n            staticClass: 'v-slider__thumb-label',\n            style: {\n              height: size,\n              width: size,\n              transform,\n            },\n          }), [this.$createElement('div', content)]),\n        ]),\n      ])\n    },\n    genThumb (): VNode {\n      return this.$createElement('div', this.setBackgroundColor(this.computedThumbColor, {\n        staticClass: 'v-slider__thumb',\n      }))\n    },\n    getThumbContainerStyles (width: number): object {\n      const direction = this.vertical ? 'top' : 'left'\n      let value = this.$vuetify.rtl ? 100 - width : width\n      value = this.vertical ? 100 - value : value\n\n      return {\n        transition: this.trackTransition,\n        [direction]: `${value}%`,\n      }\n    },\n    onSliderMouseDown (e: MouseEvent | TouchEvent) {\n      e.preventDefault()\n\n      this.oldValue = this.internalValue\n      this.isActive = true\n\n      if ((e.target as Element)?.matches('.v-slider__thumb-container, .v-slider__thumb-container *')) {\n        this.thumbPressed = true\n        const domRect = (e.target as Element).getBoundingClientRect()\n        const touch = 'touches' in e ? e.touches[0] : e\n        this.startOffset = this.vertical\n          ? touch.clientY - (domRect.top + domRect.height / 2)\n          : touch.clientX - (domRect.left + domRect.width / 2)\n      } else {\n        this.startOffset = 0\n        window.clearTimeout(this.mouseTimeout)\n        this.mouseTimeout = window.setTimeout(() => {\n          this.thumbPressed = true\n        }, 300)\n      }\n\n      const mouseUpOptions = passiveSupported ? { passive: true, capture: true } : true\n      const mouseMoveOptions = passiveSupported ? { passive: true } : false\n\n      const isTouchEvent = 'touches' in e\n\n      this.onMouseMove(e)\n      this.app.addEventListener(isTouchEvent ? 'touchmove' : 'mousemove', this.onMouseMove, mouseMoveOptions)\n      addOnceEventListener(this.app, isTouchEvent ? 'touchend' : 'mouseup', this.onSliderMouseUp, mouseUpOptions)\n\n      this.$emit('start', this.internalValue)\n    },\n    onSliderMouseUp (e: Event) {\n      e.stopPropagation()\n      window.clearTimeout(this.mouseTimeout)\n      this.thumbPressed = false\n      const mouseMoveOptions = passiveSupported ? { passive: true } : false\n      this.app.removeEventListener('touchmove', this.onMouseMove, mouseMoveOptions)\n      this.app.removeEventListener('mousemove', this.onMouseMove, mouseMoveOptions)\n\n      this.$emit('mouseup', e)\n      this.$emit('end', this.internalValue)\n      if (!deepEqual(this.oldValue, this.internalValue)) {\n        this.$emit('change', this.internalValue)\n        this.noClick = true\n      }\n\n      this.isActive = false\n    },\n    onMouseMove (e: MouseEvent | TouchEvent) {\n      if (e.type === 'mousemove') {\n        this.thumbPressed = true\n      }\n      this.internalValue = this.parseMouseMove(e)\n    },\n    onKeyDown (e: KeyboardEvent) {\n      if (!this.isInteractive) return\n\n      const value = this.parseKeyDown(e, this.internalValue)\n\n      if (\n        value == null ||\n        value < this.minValue ||\n        value > this.maxValue\n      ) return\n\n      this.internalValue = value\n      this.$emit('change', value)\n    },\n    onSliderClick (e: MouseEvent) {\n      if (this.noClick) {\n        this.noClick = false\n        return\n      }\n      const thumb = this.$refs.thumb as HTMLElement\n      thumb.focus()\n\n      this.onMouseMove(e)\n      this.$emit('change', this.internalValue)\n    },\n    onBlur (e: Event) {\n      this.isFocused = false\n\n      this.$emit('blur', e)\n    },\n    onFocus (e: Event) {\n      this.isFocused = true\n\n      this.$emit('focus', e)\n    },\n    parseMouseMove (e: MouseEvent | TouchEvent) {\n      const start = this.vertical ? 'top' : 'left'\n      const length = this.vertical ? 'height' : 'width'\n      const click = this.vertical ? 'clientY' : 'clientX'\n\n      const {\n        [start]: trackStart,\n        [length]: trackLength,\n      } = this.$refs.track.getBoundingClientRect()\n      const clickOffset = 'touches' in e ? e.touches[0][click] : e[click]\n\n      // It is possible for left to be NaN, force to number\n      let clickPos = Math.min(Math.max((clickOffset - trackStart - this.startOffset) / trackLength, 0), 1) || 0\n\n      if (this.vertical) clickPos = 1 - clickPos\n      if (this.$vuetify.rtl) clickPos = 1 - clickPos\n\n      return parseFloat(this.min) + clickPos * (this.maxValue - this.minValue)\n    },\n    parseKeyDown (e: KeyboardEvent, value: number) {\n      if (!this.isInteractive) return\n\n      const { pageup, pagedown, end, home, left, right, down, up } = keyCodes\n\n      if (![pageup, pagedown, end, home, left, right, down, up].includes(e.keyCode)) return\n\n      e.preventDefault()\n      const step = this.stepNumeric || 1\n      const steps = (this.maxValue - this.minValue) / step\n      if ([left, right, down, up].includes(e.keyCode)) {\n        const increase = this.$vuetify.rtl ? [left, up] : [right, up]\n        const direction = increase.includes(e.keyCode) ? 1 : -1\n        const multiplier = e.shiftKey ? 3 : (e.ctrlKey ? 2 : 1)\n\n        value = value + (direction * step * multiplier)\n      } else if (e.keyCode === home) {\n        value = this.minValue\n      } else if (e.keyCode === end) {\n        value = this.maxValue\n      } else {\n        const direction = e.keyCode === pagedown ? 1 : -1\n        value = value - (direction * step * (steps > 100 ? steps / 10 : 10))\n      }\n\n      return value\n    },\n    roundValue (value: number): number {\n      if (!this.stepNumeric) return value\n      // Format input value using the same number\n      // of decimals places as in the step prop\n      const trimmedStep = this.step.toString().trim()\n      const decimals = trimmedStep.indexOf('.') > -1\n        ? (trimmedStep.length - trimmedStep.indexOf('.') - 1)\n        : 0\n      const offset = this.minValue % this.stepNumeric\n\n      const newValue = Math.round((value - offset) / this.stepNumeric) * this.stepNumeric + offset\n\n      return parseFloat(Math.min(newValue, this.maxValue).toFixed(decimals))\n    },\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,8CAAP,C,CAEA;;AACA,OAAOA,MAAP,MAAmB,WAAnB;AACA,SAASC,gBAAT,QAAiC,gBAAjC,C,CAEA;;AACA,OAAOC,MAAP,MAAmC,mBAAnC;AACA,OAAOC,QAAP,MAAqB,uBAArB,C,CAEA;;AACA,OAAOC,YAAP,MAAyB,gCAAzB,C,CAEA;;AACA,SAASC,oBAAT,EAA+BC,SAA/B,EAA0CC,QAA1C,EAAoDC,WAApD,EAAiEC,aAAjE,EAAgFC,gBAAhF,QAAwG,oBAAxG;AACA,SAASC,WAAT,QAA4B,oBAA5B;AAaA,eAAeT,MAAM,CAQnBF,MARmB,EASnBG;AACF,oBAVqB,CAAN,CAWbS,MAXa,CAWN;EACPC,IAAI,EAAE,UADC;EAGPC,UAAU,EAAE;IACVV,YAAA,EAAAA;EADU,CAHL;EAOPF,MAAM,EAAE,CAACC,QAAD,CAPD;EASPY,KAAK,EAAE;IACLC,QAAQ,EAAEC,OADL;IAELC,YAAY,EAAED,OAFT;IAGLE,GAAG,EAAE;MACHC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADH;MAEH,WAAS;IAFN,CAHA;IAOLC,GAAG,EAAE;MACHH,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADH;MAEH,WAAS;IAFN,CAPA;IAWLE,IAAI,EAAE;MACJJ,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADF;MAEJ,WAAS;IAFL,CAXD;IAeLG,UAAU,EAAEH,MAfP;IAgBLI,UAAU,EAAE;MACVN,IAAI,EAAE,CAACH,OAAD,EAAUK,MAAV,CADI;MAEV,WAASK,SAFC;MAGVC,SAAS,EAAE,SAAXA,SAASA,CAAEC,CAAC;QAAA,OAAI,OAAOA,CAAP,KAAa,SAAb,IAA0BA,CAAC,KAAK;MAAA;IAHtC,CAhBP;IAqBLC,SAAS,EAAE;MACTV,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADG;MAET,WAAS;IAFA,CArBN;IAyBLS,UAAU,EAAE;MACVX,IAAI,EAAEY,KADI;MAEV,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAS;MAAA;IAFN,CAzBP;IA6BLC,KAAK,EAAE;MACLd,IAAI,EAAE,CAACH,OAAD,EAAUK,MAAV,CADD;MAEL,WAAS,KAFJ;MAGLM,SAAS,EAAE,SAAXA,SAASA,CAAEC,CAAC;QAAA,OAAI,OAAOA,CAAP,KAAa,SAAb,IAA0BA,CAAC,KAAK;MAAA;IAH3C,CA7BF;IAkCLM,QAAQ,EAAE;MACRf,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADE;MAER,WAAS;IAFD,CAlCL;IAsCLc,UAAU,EAAEd,MAtCP;IAuCLe,cAAc,EAAEf,MAvCX;IAwCLgB,KAAK,EAAE,CAACjB,MAAD,EAASC,MAAT,CAxCF;IAyCLiB,QAAQ,EAAEtB;EAzCL,CATA;EAqDPuB,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,GAAG,EAAE,IADM;MAEXC,QAAQ,EAAE,IAFC;MAGXC,YAAY,EAAE,KAHH;MAIXC,YAAY,EAAE,CAAC,CAJJ;MAKXC,SAAS,EAAE,KALA;MAMXC,QAAQ,EAAE,KANC;MAOXC,OAAO,EAAE,KAPE;MAQXC,WAAW,EAAE;IARF,CAAP;EAAA,CArDC;EAgEPC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKnD,MAAM,CAACoD,OAAP,CAAeH,QAAf,CAAwBC,OAAxB,CAAgCG,IAAhC,CAAqC,IAArC,CADE;QAEL,mBAAmB,IAFd;QAGL,6BAA6B,KAAKd,QAH7B;QAIL,kCAAkC,KAAKrB;MAAA;IAE1C,CARO;IASRoC,aAAa,EAAE;MACbC,GAAG,WAAHA,GAAGA,CAAA;QACD,OAAO,KAAKC,SAAZ;MACD,CAHY;MAIbC,GAAG,WAAHA,GAAGA,CAAEC,GAAF,EAAa;QACdA,GAAG,GAAGC,KAAK,CAACD,GAAD,CAAL,GAAa,KAAKE,QAAlB,GAA6BF,GAAnC,CADc,CAEd;QACA;QACA;;QACA,IAAMpB,KAAK,GAAG,KAAKuB,UAAL,CAAgBC,IAAI,CAACvC,GAAL,CAASuC,IAAI,CAAC3C,GAAL,CAASuC,GAAT,EAAc,KAAKE,QAAnB,CAAT,EAAuC,KAAKG,QAA5C,CAAhB,CAAd;QAEA,IAAIzB,KAAK,KAAK,KAAKkB,SAAnB,EAA8B;QAE9B,KAAKA,SAAL,GAAiBlB,KAAjB;QAEA,KAAK0B,KAAL,CAAW,OAAX,EAAoB1B,KAApB;MACD;IAhBY,CATP;IA2BR2B,eAAe,WAAfA,eAAeA,CAAA;MACb,OAAO,KAAKtB,YAAL,GACH,KAAKuB,SAAL,IAAkB,KAAKC,WAAvB,GACE,sCADF,GAEE,MAHC,GAIH,EAJJ;IAKD,CAjCO;IAkCRP,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAOQ,WAAA,CAAW,KAAK7C,GAAN,CAAjB;IACD,CApCO;IAqCRwC,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAOK,WAAA,CAAW,KAAKjD,GAAN,CAAjB;IACD,CAvCO;IAwCRgD,WAAW,WAAXA,WAAWA,CAAA;MACT,OAAO,KAAK3C,IAAL,GAAY,CAAZ,GAAgB4C,WAAA,CAAW,KAAK5C,IAAN,CAA1B,GAAwC,CAA/C;IACD,CA1CO;IA2CR6C,UAAU,WAAVA,UAAUA,CAAA;MACR,IAAMA,UAAU,GAAG,CAAC,KAAKR,UAAL,CAAgB,KAAKP,aAArB,IAAsC,KAAKM,QAA5C,KAAyD,KAAKG,QAAL,GAAgB,KAAKH,QAA9E,IAA0F,GAA7G;MAEA,OAAOD,KAAK,CAACU,UAAD,CAAL,GAAoB,CAApB,GAAwBA,UAA/B;IACD,CA/CO;IAgDRC,eAAe,WAAfA,eAAeA,CAAA;MACb,IAAMC,QAAQ,GAAG,KAAKhC,QAAL,GAAgB,QAAhB,GAA2B,MAA5C;MACA,IAAMiC,MAAM,GAAG,KAAKjC,QAAL,GAAgB,KAAhB,GAAwB,OAAvC;MACA,IAAMkC,QAAQ,GAAG,KAAKlC,QAAL,GAAgB,QAAhB,GAA2B,OAA5C;MAEA,IAAMmC,KAAK,GAAG,KAAKC,QAAL,CAAcC,GAAd,GAAoB,MAApB,GAA6B,GAA3C;MACA,IAAMC,GAAG,GAAG,KAAKF,QAAL,CAAcC,GAAd,GAAoB,GAApB,GAA0B,MAAtC;MACA,IAAMtC,KAAK,GAAG,KAAKwC,UAAL,WAAAC,MAAA,CAA0B,KAAKV,UAAU,oBAAAU,MAAA,CAAiB,KAAKV,UAAU,MAAvF;MAEA,OAAAW,eAAA,CAAAA,eAAA,CAAAA,eAAA;QACEC,UAAU,EAAE,KAAKhB;MADZ,GAEJM,QAAD,EAAYG,KAFP,GAGJF,MAAD,EAAUK,GAHL,GAIJJ,QAAD,EAAYnC,KAAA;IAEf,CA/DO;IAgER4C,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAMX,QAAQ,GAAG,KAAKhC,QAAL,GAAgB,KAAKoC,QAAL,CAAcC,GAAd,GAAoB,QAApB,GAA+B,KAA/C,GAAuD,KAAKD,QAAL,CAAcC,GAAd,GAAoB,MAApB,GAA6B,OAArG;MACA,IAAMJ,MAAM,GAAG,KAAKjC,QAAL,GAAgB,QAAhB,GAA2B,OAA1C;MAEA,IAAMmC,KAAK,GAAG,KAAd;MACA,IAAMG,GAAG,GAAG,KAAKC,UAAL,WAAAC,MAAA,CAA0B,MAAM,KAAKV,UAAU,yBAAAU,MAAA,CAAsB,MAAM,KAAKV,UAAU,OAAtG;MAEA,OAAAW,eAAA,CAAAA,eAAA;QACEC,UAAU,EAAE,KAAKhB;MADZ,GAEJM,QAAD,EAAYG,KAFP,GAGJF,MAAD,EAAUK,GAAA;IAEb,CA5EO;IA6ERX,SAAS,WAATA,SAASA,CAAA;MACP,OAAO,KAAKnC,UAAL,CAAgBoD,MAAhB,GAAyB,CAAzB,IACL,CAAC,EAAE,CAAC,KAAKL,UAAN,IAAoB,KAAKX,WAAzB,IAAwC,KAAKjC,KAA/C,CADH;IAED,CAhFO;IAiFRkD,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAOtB,IAAI,CAACuB,IAAL,CAAU,CAAC,KAAKtB,QAAL,GAAgB,KAAKH,QAAtB,IAAkC,KAAKO,WAAjD,CAAP;IACD,CAnFO;IAoFRmB,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,CAAC,KAAKR,UAAN,IAAoB,CAAC,EAC1B,KAAKpD,UAAL,IACA,KAAK6D,YAAL,CAAkB,aAAlB,CAF0B,CAA5B;IAID,CAzFO;IA0FRC,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAI,KAAKV,UAAT,EAAqB,OAAOnD,SAAP;MACrB,IAAI,KAAKS,UAAT,EAAqB,OAAO,KAAKA,UAAZ;MACrB,IAAI,KAAKqD,MAAT,EAAiB,OAAO,KAAKC,eAAZ;MACjB,OAAO,KAAKA,eAAL,IAAwB,mBAA/B;IACD,CA/FO;IAgGRC,sBAAsB,WAAtBA,sBAAsBA,CAAA;MACpB,IAAI,KAAKb,UAAT,EAAqB,OAAOnD,SAAP;MACrB,IAAI,KAAKU,cAAT,EAAyB,OAAO,KAAKA,cAAZ;MACzB,OAAO,KAAKqD,eAAL,IAAwB,KAAKE,aAApC;IACD,CApGO;IAqGRC,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAI,KAAKpE,UAAT,EAAqB,OAAO,KAAKA,UAAZ;MACrB,OAAO,KAAKiE,eAAL,IAAwB,KAAKE,aAApC;IACD;EAxGO,CAhEH;EA2KPE,KAAK,EAAE;IACLvE,GAAG,WAAHA,GAAGA,CAAEmC,GAAF,EAAK;MACN,IAAMqC,MAAM,GAAG3B,WAAA,CAAWV,GAAD,CAAzB;MACAqC,MAAM,GAAG,KAAKzC,aAAd,IAA+B,KAAKU,KAAL,CAAW,OAAX,EAAoB+B,MAApB,CAA/B;IACD,CAJI;IAKL5E,GAAG,WAAHA,GAAGA,CAAEuC,GAAF,EAAK;MACN,IAAMqC,MAAM,GAAG3B,WAAA,CAAWV,GAAD,CAAzB;MACAqC,MAAM,GAAG,KAAKzC,aAAd,IAA+B,KAAKU,KAAL,CAAW,OAAX,EAAoB+B,MAApB,CAA/B;IACD,CARI;IASLzD,KAAK,EAAE;MACL0D,OAAO,WAAPA,OAAOA,CAAEnE,CAAF,EAAW;QAChB,KAAKyB,aAAL,GAAqBzB,CAArB;MACD,CAHI;MAILoE,SAAS,EAAE;IAJN;EATF,CA3KA;EA4LPC,OAAO,WAAPA,OAAOA,CAAA;IACL;IACA,KAAKzD,GAAL,GAAW0D,QAAQ,CAACC,aAAT,CAAuB,YAAvB,KACTzF,WAAW,CAAC,4EAAD,EAA+E,IAA/E,CADb;EAED,CAhMM;EAkMP0F,OAAO,EAAE;IACPC,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAMC,QAAQ,GAA+B,CAAC,KAAKC,QAAL,EAAD,CAA7C;MACA,IAAMC,MAAM,GAAG,KAAKC,SAAL,EAAf;MACA,KAAKxF,YAAL,GACIqF,QAAQ,CAACI,OAAT,CAAiBF,MAAjB,CADJ,GAEIF,QAAQ,CAACK,IAAT,CAAcH,MAAd,CAFJ;MAIAF,QAAQ,CAACK,IAAT,CAAc,KAAKC,WAAL,EAAd;MAEA,OAAON,QAAP;IACD,CAXM;IAYPG,SAAS,WAATA,SAASA,CAAA;MACP,OAAO,KAAKI,cAAL,CAAoB,KAApB,EAA2B;QAChC,SAAA3D,aAAA;UACE,YAAY,IADP;UAEL,wBAAwB,CAAC,KAAKZ,QAFzB;UAGL,sBAAsB,KAAKA,QAHtB;UAIL,qBAAqB,KAAKM,SAJrB;UAKL,oBAAoB,KAAKC,QALpB;UAML,sBAAsB,KAAKgC,UANtB;UAOL,sBAAsB,KAAKiC;QAPtB,GAQF,KAAKC,YAAA,CATsB;QAWhClG,UAAU,EAAE,CAAC;UACXD,IAAI,EAAE,eADK;UAEXyB,KAAK,EAAE,KAAK2E;QAFD,CAAD,CAXoB;QAehCC,EAAE,EAAE;UACFC,KAAK,EAAE,KAAKC,aADV;UAEFC,SAAS,EAAE,KAAKC,iBAFd;UAGFC,UAAU,EAAE,KAAKD;QAHf;MAf4B,CAA3B,EAoBJ,KAAKE,WAAL,EApBI,CAAP;IAqBD,CAlCM;IAmCPA,WAAW,WAAXA,WAAWA,CAAA;MACT,OAAO,CACL,KAAKC,QAAL,EADK,EAEL,KAAKC,iBAAL,EAFK,EAGL,KAAKC,QAAL,EAHK,EAIL,KAAKC,iBAAL,CACE,KAAKtE,aADP,EAEE,KAAKe,UAFP,EAGE,KAAKvB,QAHP,EAIE,KAAKD,SAJP,EAKE,KAAKgF,OALP,EAME,KAAKZ,MANP,CAJK,CAAP;IAaD,CAjDM;IAkDPQ,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAO,KAAKX,cAAL,CAAoB,OAApB,EAA6B;QAClCgB,KAAK,EAAA3E,aAAA;UACHb,KAAK,EAAE,KAAKgB,aADP;UAELyE,EAAE,EAAE,KAAKC,UAFJ;UAGLhH,QAAQ,EAAE,IAHL;UAILiH,QAAQ,EAAE,IAJL;UAKLC,QAAQ,EAAE,CAAC;QALN,GAMF,KAAKC,MAAA,CAPwB,CASlC;MATkC,CAA7B,CAAP;IAWD,CA9DM;IA+DPT,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IAAMnB,QAAQ,GAAG,CACf,KAAKO,cAAL,CAAoB,KAApB,EAA2B,KAAKsB,kBAAL,CAAwB,KAAK5C,kBAA7B,EAAiD;QAC1E6C,WAAW,EAAE,4BAD6D;QAE1EC,KAAK,EAAE,KAAKpD;MAF8D,CAAjD,CAA3B,CADe,EAKf,KAAK4B,cAAL,CAAoB,KAApB,EAA2B,KAAKsB,kBAAL,CAAwB,KAAKzC,sBAA7B,EAAqD;QAC9E0C,WAAW,EAAE,sBADiE;QAE9EC,KAAK,EAAE,KAAKhE;MAFkE,CAArD,CAA3B,CALe,CAAjB;MAWA,OAAO,KAAKwC,cAAL,CAAoB,KAApB,EAA2B;QAChCuB,WAAW,EAAE,2BADmB;QAEhCE,GAAG,EAAE;MAF2B,CAA3B,EAGJhC,QAHI,CAAP;IAID,CA/EM;IAgFPoB,QAAQ,WAARA,QAAQA,CAAA;MAAA,IAAAa,KAAA;MACN,IAAI,CAAC,KAAKhH,IAAN,IAAc,CAAC,KAAK0C,SAAxB,EAAmC,OAAO,IAAP;MAEnC,IAAM/B,QAAQ,GAAGiC,WAAA,CAAW,KAAKjC,QAAN,CAA3B;MACA,IAAMsG,KAAK,GAAGjI,WAAW,CAAC,KAAK4E,QAAL,GAAgB,CAAjB,CAAzB;MACA,IAAMsD,SAAS,GAAG,KAAKnG,QAAL,GAAgB,QAAhB,GAA4B,KAAKoC,QAAL,CAAcC,GAAd,GAAoB,OAApB,GAA8B,MAA5E;MACA,IAAM+D,eAAe,GAAG,KAAKpG,QAAL,GAAiB,KAAKoC,QAAL,CAAcC,GAAd,GAAoB,MAApB,GAA6B,OAA9C,GAAyD,KAAjF;MAEA,IAAI,KAAKrC,QAAT,EAAmBqG,wBAAA,CAAAH,KAAK,EAAApF,IAAA,CAALoF,KAAA;MAEnB,IAAMvG,KAAK,GAAG2G,oBAAA,CAAAJ,KAAK,EAAApF,IAAA,CAALoF,KAAK,EAAK,UAAAK,KAAK,EAAG;QAAA,IAAAC,QAAA;QAC9B,IAAMxC,QAAQ,GAAG,EAAjB;QAEA,IAAIiC,KAAA,CAAKzG,UAAL,CAAgB+G,KAAhB,CAAJ,EAA4B;UAC1BvC,QAAQ,CAACK,IAAT,CAAc4B,KAAA,CAAK1B,cAAL,CAAoB,KAApB,EAA2B;YACvCuB,WAAW,EAAE;UAD0B,CAA3B,EAEXG,KAAA,CAAKzG,UAAL,CAAgB+G,KAAhB,CAFW,CAAd;QAGD;QAED,IAAME,KAAK,GAAGF,KAAK,IAAI,MAAMN,KAAA,CAAKpD,QAAf,CAAnB;QACA,IAAM6D,MAAM,GAAGT,KAAA,CAAK7D,QAAL,CAAcC,GAAd,GAAqB,MAAM4D,KAAA,CAAKnE,UAAZ,GAA0B2E,KAA9C,GAAsDA,KAAK,GAAGR,KAAA,CAAKnE,UAAlF;QAEA,OAAOmE,KAAA,CAAK1B,cAAL,CAAoB,MAApB,EAA4B;UACjCoC,GAAG,EAAEJ,KAD4B;UAEjCT,WAAW,EAAE,gBAFoB;UAGjC,SAAO;YACL,0BAA0BY;UADrB,CAH0B;UAMjCX,KAAK,EAAAtD,eAAA,CAAAA,eAAA;YACHgE,KAAK,KAAAjE,MAAA,CAAK5C,QAAQ,OADb;YAELgH,MAAM,KAAApE,MAAA,CAAK5C,QAAQ;UAFd,GAGJuG,SAAD,EAAAU,uBAAA,CAAAL,QAAA,WAAAhE,MAAA,CAAqBiE,KAAK,WAAA3F,IAAA,CAAA0F,QAAA,EAAO5G,QAAQ,GAAG,CAAC,WAC5CwG,eAAD,gBAAA5D,MAAA,CAAiC5C,QAAQ,GAAG,CAAC;QAVd,CAA5B,EAYJoE,QAZI,CAAP;MAaD,CAzBa,CAAd;MA2BA,OAAO,KAAKO,cAAL,CAAoB,KAApB,EAA2B;QAChCuB,WAAW,EAAE,2BADmB;QAEhC,SAAO;UACL,0CAA0C,KAAKnG,KAAL,KAAe,QAAf,IAA2B,KAAKH,UAAL,CAAgBoD,MAAhB,GAAyB;QADzF;MAFyB,CAA3B,EAKJjD,KALI,CAAP;IAMD,CA3HM;IA4HP0F,iBAAiB,WAAjBA,iBAAiBA,CACftF,KADe,EAEf+G,UAFe,EAGfvG,QAHe,EAIfD,SAJe,EAKfgF,OALe,EAMfZ,MANe,EAOF;MAAA,IAAbsB,GAAG,GAAAe,SAAA,CAAAnE,MAAA,QAAAmE,SAAA,QAAA3H,SAAA,GAAA2H,SAAA,MAAG,OAPS;MASf,IAAM/C,QAAQ,GAAG,CAAC,KAAKgD,QAAL,EAAD,CAAjB;MAEA,IAAMC,iBAAiB,GAAG,KAAKC,oBAAL,CAA0BnH,KAA1B,CAA1B;MACA,KAAKgD,cAAL,IAAuBiB,QAAQ,CAACK,IAAT,CAAc,KAAK8C,aAAL,CAAmBF,iBAAnB,CAAd,CAAvB;MAEA,OAAO,KAAK1C,cAAL,CAAoB,KAApB,EAA2B,KAAK6C,YAAL,CAAkB,KAAK9D,kBAAvB,EAA2C;QAC3E0C,GAD2E,EAC3EA,GAD2E;QAE3EW,GAAG,EAAEX,GAFsE;QAG3EF,WAAW,EAAE,2BAH8D;QAI3E,SAAO;UACL,qCAAqCvF,QADhC;UAEL,sCAAsCD,SAFjC;UAGL,yCAAyC,KAAKyC;QAHzC,CAJoE;QAS3EgD,KAAK,EAAE,KAAKsB,uBAAL,CAA6BP,UAA7B,CAToE;QAU3EvB,KAAK,EAAE;UACL+B,IAAI,EAAE,QADD;UAEL3B,QAAQ,EAAE,KAAKpD,UAAL,GAAkB,CAAC,CAAnB,GAAuB,KAAKqD,MAAL,CAAYD,QAAZ,GAAuB,KAAKC,MAAL,CAAYD,QAAnC,GAA8C,CAF1E;UAGL,cAAc,KAAKC,MAAL,CAAY,YAAZ,KAA6B,KAAK2B,KAH3C;UAIL,iBAAiB,KAAKvI,GAJjB;UAKL,iBAAiB,KAAKJ,GALjB;UAML,iBAAiB,KAAKmC,aANjB;UAOL,iBAAiBhC,MAAM,CAAC,KAAKyF,UAAN,CAPlB;UAQL,oBAAoB,KAAKxE,QAAL,GAAgB,UAAhB,GAA6B;QAR5C,CAVoE;QAoB3E2E,EAAE,EAAE;UACF6C,KAAK,EAAElC,OADL;UAEFmC,IAAI,EAAE/C,MAFJ;UAGFgD,OAAO,EAAE,KAAKC;QAHZ;MApBuE,CAA3C,CAA3B,EAyBH3D,QAzBG,CAAP;IA0BD,CApKM;IAqKPkD,oBAAoB,WAApBA,oBAAoBA,CAAEnH,KAAF,EAAwB;MAC1C,OAAO,KAAKiD,YAAL,CAAkB,aAAlB,IACH,KAAKA,YAAL,CAAkB,aAAlB,EAAkC;QAAEjD,KAAA,EAAAA;MAAF,CAAlC,CADG,GAEH,CAAC,KAAKwE,cAAL,CAAoB,MAApB,EAA4B,CAACxF,MAAM,CAACgB,KAAD,CAAP,CAA5B,CAAD,CAFJ;IAGD,CAzKM;IA0KPoH,aAAa,WAAbA,aAAaA,CAAES,OAAF,EAA6B;MACxC,IAAMC,IAAI,GAAG3J,aAAa,CAAC,KAAKqB,SAAN,CAA1B;MAEA,IAAMuI,SAAS,GAAG,KAAK9H,QAAL,iCAAAwC,MAAA,CACiB1D,MAAM,CAAC,KAAKS,SAAN,CAAN,GAAyB,CAA1B,GAA+B,CAAC,6GADlE;MAIA,OAAO,KAAKgF,cAAL,CAAoB7G,gBAApB,EAAsC;QAC3Cc,KAAK,EAAE;UAAEuJ,MAAM,EAAE;QAAV;MADoC,CAAtC,EAEJ,CACD,KAAKxD,cAAL,CAAoB,KAApB,EAA2B;QACzBuB,WAAW,EAAE,iCADY;QAEzBvH,UAAU,EAAE,CAAC;UACXD,IAAI,EAAE,MADK;UAEXyB,KAAK,EAAE,KAAKO,SAAL,IAAkB,KAAKC,QAAvB,IAAmC,KAAKpB,UAAL,KAAoB;QAFnD,CAAD;MAFa,CAA3B,EAMG,CACD,KAAKoF,cAAL,CAAoB,KAApB,EAA2B,KAAKsB,kBAAL,CAAwB,KAAKvC,kBAA7B,EAAiD;QAC1EwC,WAAW,EAAE,uBAD6D;QAE1EC,KAAK,EAAE;UACLa,MAAM,EAAEiB,IADH;UAELpB,KAAK,EAAEoB,IAFF;UAGLC,SAAA,EAAAA;QAHK;MAFmE,CAAjD,CAA3B,EAOI,CAAC,KAAKvD,cAAL,CAAoB,KAApB,EAA2BqD,OAA3B,CAAD,CAPJ,CADC,CANH,CADC,CAFI,CAAP;IAoBD,CArMM;IAsMPZ,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAO,KAAKzC,cAAL,CAAoB,KAApB,EAA2B,KAAKsB,kBAAL,CAAwB,KAAKvC,kBAA7B,EAAiD;QACjFwC,WAAW,EAAE;MADoE,CAAjD,CAA3B,CAAP;IAGD,CA1MM;IA2MPuB,uBAAuB,WAAvBA,uBAAuBA,CAAEZ,KAAF,EAAe;MACpC,IAAMN,SAAS,GAAG,KAAKnG,QAAL,GAAgB,KAAhB,GAAwB,MAA1C;MACA,IAAID,KAAK,GAAG,KAAKqC,QAAL,CAAcC,GAAd,GAAoB,MAAMoE,KAA1B,GAAkCA,KAA9C;MACA1G,KAAK,GAAG,KAAKC,QAAL,GAAgB,MAAMD,KAAtB,GAA8BA,KAAtC;MAEA,OAAA0C,eAAA;QACEC,UAAU,EAAE,KAAKhB;MADZ,GAEJyE,SAAD,KAAA3D,MAAA,CAAgBzC,KAAK;IAExB,CApNM;IAqNPgF,iBAAiB,WAAjBA,iBAAiBA,CAAEiD,CAAF,EAA4B;MAAA,IAAAC,MAAA;;MAC3CD,CAAC,CAACE,cAAF;MAEA,KAAK/H,QAAL,GAAgB,KAAKY,aAArB;MACA,KAAKR,QAAL,GAAgB,IAAhB;MAEA,IAAI,CAAA4H,EAAA,GAACH,CAAC,CAACI,MAAH,MAAqB,IAArB,IAAqBD,EAAA,WAArB,GAAqB,MAArB,GAAqBA,EAAA,CAAEE,OAAF,CAAU,0DAAV,CAAzB,EAAgG;QAC9F,KAAKjI,YAAL,GAAoB,IAApB;QACA,IAAMkI,OAAO,GAAIN,CAAC,CAACI,MAAF,CAAqBG,qBAArB,EAAjB;QACA,IAAMC,KAAK,GAAG,aAAaR,CAAb,GAAiBA,CAAC,CAACS,OAAF,CAAU,CAAV,CAAjB,GAAgCT,CAA9C;QACA,KAAKvH,WAAL,GAAmB,KAAKT,QAAL,GACfwI,KAAK,CAACE,OAAN,IAAiBJ,OAAO,CAACK,GAAR,GAAcL,OAAO,CAAC1B,MAAR,GAAiB,CAAhD,CADe,GAEf4B,KAAK,CAACI,OAAN,IAAiBN,OAAO,CAACO,IAAR,GAAeP,OAAO,CAAC7B,KAAR,GAAgB,CAAhD,CAFJ;MAGD,CAPD,MAOO;QACL,KAAKhG,WAAL,GAAmB,CAAnB;QACAqI,MAAM,CAACC,YAAP,CAAoB,KAAK1I,YAAzB;QACA,KAAKA,YAAL,GAAoB2I,WAAA,CAAkB,YAAK;UACzCf,MAAA,CAAK7H,YAAL,GAAoB,IAApB;QACD,CAFmB,EAEjB,GAFiB,CAApB;MAGD;MAED,IAAM6I,cAAc,GAAG9K,gBAAgB,GAAG;QAAE+K,OAAO,EAAE,IAAX;QAAiBC,OAAO,EAAE;MAA1B,CAAH,GAAsC,IAA7E;MACA,IAAMC,gBAAgB,GAAGjL,gBAAgB,GAAG;QAAE+K,OAAO,EAAE;MAAX,CAAH,GAAuB,KAAhE;MAEA,IAAMG,YAAY,GAAG,aAAarB,CAAlC;MAEA,KAAKsB,WAAL,CAAiBtB,CAAjB;MACA,KAAK9H,GAAL,CAASqJ,gBAAT,CAA0BF,YAAY,GAAG,WAAH,GAAiB,WAAvD,EAAoE,KAAKC,WAAzE,EAAsFF,gBAAtF;MACAtL,oBAAoB,CAAC,KAAKoC,GAAN,EAAWmJ,YAAY,GAAG,UAAH,GAAgB,SAAvC,EAAkD,KAAKG,eAAvD,EAAwEP,cAAxE,CAApB;MAEA,KAAKxH,KAAL,CAAW,OAAX,EAAoB,KAAKV,aAAzB;IACD,CApPM;IAqPPyI,eAAe,WAAfA,eAAeA,CAAExB,CAAF,EAAU;MACvBA,CAAC,CAACyB,eAAF;MACAX,MAAM,CAACC,YAAP,CAAoB,KAAK1I,YAAzB;MACA,KAAKD,YAAL,GAAoB,KAApB;MACA,IAAMgJ,gBAAgB,GAAGjL,gBAAgB,GAAG;QAAE+K,OAAO,EAAE;MAAX,CAAH,GAAuB,KAAhE;MACA,KAAKhJ,GAAL,CAASwJ,mBAAT,CAA6B,WAA7B,EAA0C,KAAKJ,WAA/C,EAA4DF,gBAA5D;MACA,KAAKlJ,GAAL,CAASwJ,mBAAT,CAA6B,WAA7B,EAA0C,KAAKJ,WAA/C,EAA4DF,gBAA5D;MAEA,KAAK3H,KAAL,CAAW,SAAX,EAAsBuG,CAAtB;MACA,KAAKvG,KAAL,CAAW,KAAX,EAAkB,KAAKV,aAAvB;MACA,IAAI,CAAChD,SAAS,CAAC,KAAKoC,QAAN,EAAgB,KAAKY,aAArB,CAAd,EAAmD;QACjD,KAAKU,KAAL,CAAW,QAAX,EAAqB,KAAKV,aAA1B;QACA,KAAKP,OAAL,GAAe,IAAf;MACD;MAED,KAAKD,QAAL,GAAgB,KAAhB;IACD,CArQM;IAsQP+I,WAAW,WAAXA,WAAWA,CAAEtB,CAAF,EAA4B;MACrC,IAAIA,CAAC,CAACnJ,IAAF,KAAW,WAAf,EAA4B;QAC1B,KAAKuB,YAAL,GAAoB,IAApB;MACD;MACD,KAAKW,aAAL,GAAqB,KAAK4I,cAAL,CAAoB3B,CAApB,CAArB;IACD,CA3QM;IA4QPL,SAAS,WAATA,SAASA,CAAEK,CAAF,EAAkB;MACzB,IAAI,CAAC,KAAK4B,aAAV,EAAyB;MAEzB,IAAM7J,KAAK,GAAG,KAAK8J,YAAL,CAAkB7B,CAAlB,EAAqB,KAAKjH,aAA1B,CAAd;MAEA,IACEhB,KAAK,IAAI,IAAT,IACAA,KAAK,GAAG,KAAKsB,QADb,IAEAtB,KAAK,GAAG,KAAKyB,QAHf,EAIE;MAEF,KAAKT,aAAL,GAAqBhB,KAArB;MACA,KAAK0B,KAAL,CAAW,QAAX,EAAqB1B,KAArB;IACD,CAzRM;IA0RP8E,aAAa,WAAbA,aAAaA,CAAEmD,CAAF,EAAe;MAC1B,IAAI,KAAKxH,OAAT,EAAkB;QAChB,KAAKA,OAAL,GAAe,KAAf;QACA;MACD;MACD,IAAMsJ,KAAK,GAAG,KAAKC,KAAL,CAAWD,KAAzB;MACAA,KAAK,CAACtC,KAAN;MAEA,KAAK8B,WAAL,CAAiBtB,CAAjB;MACA,KAAKvG,KAAL,CAAW,QAAX,EAAqB,KAAKV,aAA1B;IACD,CApSM;IAqSP2D,MAAM,WAANA,MAAMA,CAAEsD,CAAF,EAAU;MACd,KAAK1H,SAAL,GAAiB,KAAjB;MAEA,KAAKmB,KAAL,CAAW,MAAX,EAAmBuG,CAAnB;IACD,CAzSM;IA0SP1C,OAAO,WAAPA,OAAOA,CAAE0C,CAAF,EAAU;MACf,KAAK1H,SAAL,GAAiB,IAAjB;MAEA,KAAKmB,KAAL,CAAW,OAAX,EAAoBuG,CAApB;IACD,CA9SM;IA+SP2B,cAAc,WAAdA,cAAcA,CAAE3B,CAAF,EAA4B;MACxC,IAAM7F,KAAK,GAAG,KAAKnC,QAAL,GAAgB,KAAhB,GAAwB,MAAtC;MACA,IAAM4C,MAAM,GAAG,KAAK5C,QAAL,GAAgB,QAAhB,GAA2B,OAA1C;MACA,IAAM4E,KAAK,GAAG,KAAK5E,QAAL,GAAgB,SAAhB,GAA4B,SAA1C;MAEA,IAAAgK,qBAAA,GAGI,KAAKD,KAAL,CAAWE,KAAX,CAAiB1B,qBAAjB,EAHJ;QACW2B,UADL,GAAAF,qBAAA,CACH7H,KAAD;QACUgI,WAAA,GAAAH,qBAAA,CAATpH,MAAD;MAEF,IAAMwH,WAAW,GAAG,aAAapC,CAAb,GAAiBA,CAAC,CAACS,OAAF,CAAU,CAAV,EAAa7D,KAAb,CAAjB,GAAuCoD,CAAC,CAACpD,KAAD,CAA5D,CATwC,CAWxC;;MACA,IAAIyF,QAAQ,GAAG9I,IAAI,CAACvC,GAAL,CAASuC,IAAI,CAAC3C,GAAL,CAAS,CAACwL,WAAW,GAAGF,UAAd,GAA2B,KAAKzJ,WAAjC,IAAgD0J,WAAzD,EAAsE,CAAtE,CAAT,EAAmF,CAAnF,KAAyF,CAAxG;MAEA,IAAI,KAAKnK,QAAT,EAAmBqK,QAAQ,GAAG,IAAIA,QAAf;MACnB,IAAI,KAAKjI,QAAL,CAAcC,GAAlB,EAAuBgI,QAAQ,GAAG,IAAIA,QAAf;MAEvB,OAAOxI,WAAA,CAAW,KAAK7C,GAAN,CAAV,GAAuBqL,QAAQ,IAAI,KAAK7I,QAAL,GAAgB,KAAKH,QAAzB,CAAtC;IACD,CAjUM;IAkUPwI,YAAY,WAAZA,YAAYA,CAAE7B,CAAF,EAAoBjI,KAApB,EAAiC;MAAA,IAAAuK,SAAA,EAAAC,SAAA;MAC3C,IAAI,CAAC,KAAKX,aAAV,EAAyB;MAEzB,IAAQY,MAAF,GAAyDxM,QAA/D,CAAQwM,MAAF;QAAUC,QAAV,GAAyDzM,QAA/D,CAAgByM,QAAV;QAAoBnI,GAApB,GAAyDtE,QAA/D,CAA0BsE,GAApB;QAAyBoI,IAAzB,GAAyD1M,QAA/D,CAA+B0M,IAAzB;QAA+B7B,IAA/B,GAAyD7K,QAA/D,CAAqC6K,IAA/B;QAAqC8B,KAArC,GAAyD3M,QAA/D,CAA2C2M,KAArC;QAA4CC,IAA5C,GAAyD5M,QAA/D,CAAkD4M,IAA5C;QAAkDC,EAAA,GAAO7M,QAA/D,CAAwD6M,EAAA;MAExD,IAAI,CAACC,yBAAA,CAAAR,SAAA,IAACE,MAAD,EAASC,QAAT,EAAmBnI,GAAnB,EAAwBoI,IAAxB,EAA8B7B,IAA9B,EAAoC8B,KAApC,EAA2CC,IAA3C,EAAiDC,EAAjD,GAAA/J,IAAA,CAAAwJ,SAAA,EAA8DtC,CAAC,CAAC+C,OAAhE,CAAL,EAA+E;MAE/E/C,CAAC,CAACE,cAAF;MACA,IAAMjJ,IAAI,GAAG,KAAK2C,WAAL,IAAoB,CAAjC;MACA,IAAMoJ,KAAK,GAAG,CAAC,KAAKxJ,QAAL,GAAgB,KAAKH,QAAtB,IAAkCpC,IAAhD;MACA,IAAI6L,yBAAA,CAAAP,SAAA,IAAC1B,IAAD,EAAO8B,KAAP,EAAcC,IAAd,EAAoBC,EAApB,GAAA/J,IAAA,CAAAyJ,SAAA,EAAiCvC,CAAC,CAAC+C,OAAnC,CAAJ,EAAiD;QAC/C,IAAME,QAAQ,GAAG,KAAK7I,QAAL,CAAcC,GAAd,GAAoB,CAACwG,IAAD,EAAOgC,EAAP,CAApB,GAAiC,CAACF,KAAD,EAAQE,EAAR,CAAlD;QACA,IAAM1E,SAAS,GAAG2E,yBAAA,CAAAG,QAAQ,EAAAnK,IAAA,CAARmK,QAAQ,EAAUjD,CAAC,CAAC+C,OAApB,IAA+B,CAA/B,GAAmC,CAAC,CAAtD;QACA,IAAMG,UAAU,GAAGlD,CAAC,CAACmD,QAAF,GAAa,CAAb,GAAkBnD,CAAC,CAACoD,OAAF,GAAY,CAAZ,GAAgB,CAArD;QAEArL,KAAK,GAAGA,KAAK,GAAIoG,SAAS,GAAGlH,IAAZ,GAAmBiM,UAApC;MACD,CAND,MAMO,IAAIlD,CAAC,CAAC+C,OAAF,KAAcL,IAAlB,EAAwB;QAC7B3K,KAAK,GAAG,KAAKsB,QAAb;MACD,CAFM,MAEA,IAAI2G,CAAC,CAAC+C,OAAF,KAAczI,GAAlB,EAAuB;QAC5BvC,KAAK,GAAG,KAAKyB,QAAb;MACD,CAFM,MAEA;QACL,IAAM2E,UAAS,GAAG6B,CAAC,CAAC+C,OAAF,KAAcN,QAAd,GAAyB,CAAzB,GAA6B,CAAC,CAAhD;QACA1K,KAAK,GAAGA,KAAK,GAAIoG,UAAS,GAAGlH,IAAZ,IAAoB+L,KAAK,GAAG,GAAR,GAAcA,KAAK,GAAG,EAAtB,GAA2B,EAA/C,CAAjB;MACD;MAED,OAAOjL,KAAP;IACD,CA5VM;IA6VPuB,UAAU,WAAVA,UAAUA,CAAEvB,KAAF,EAAe;MAAA,IAAAsL,SAAA;MACvB,IAAI,CAAC,KAAKzJ,WAAV,EAAuB,OAAO7B,KAAP,CADA,CAEvB;MACA;;MACA,IAAMuL,WAAW,GAAGC,qBAAA,CAAAF,SAAA,QAAKpM,IAAL,CAAUuM,QAAV,IAAA1K,IAAA,CAAAuK,SAAA,CAApB;MACA,IAAMI,QAAQ,GAAGC,wBAAA,CAAAJ,WAAW,EAAAxK,IAAA,CAAXwK,WAAW,EAAS,GAApB,IAA2B,CAAC,CAA5B,GACZA,WAAW,CAAC1I,MAAZ,GAAqB8I,wBAAA,CAAAJ,WAAW,EAAAxK,IAAA,CAAXwK,WAAW,EAAS,GAApB,CAArB,GAAgD,CADpC,GAEb,CAFJ;MAGA,IAAMK,MAAM,GAAG,KAAKtK,QAAL,GAAgB,KAAKO,WAApC;MAEA,IAAMgK,QAAQ,GAAGrK,IAAI,CAACsK,KAAL,CAAW,CAAC9L,KAAK,GAAG4L,MAAT,IAAmB,KAAK/J,WAAnC,IAAkD,KAAKA,WAAvD,GAAqE+J,MAAtF;MAEA,OAAO9J,WAAA,CAAWN,IAAI,CAACvC,GAAL,CAAS4M,QAAT,EAAmB,KAAKpK,QAAxB,EAAkCsK,OAAlC,CAA0CL,QAA1C,CAAD,CAAjB;IACD;EA1WM;AAlMF,CAXM,CAAf", "ignoreList": []}]}