{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/validatable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/validatable/index.js", "mtime": 1757335237276}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Colorable", "Themeable", "inject", "RegistrableInject", "deepEqual", "consoleError", "mixins", "baseMixins", "extend", "name", "props", "disabled", "Boolean", "error", "errorCount", "type", "Number", "String", "errorMessages", "Array", "default", "messages", "readonly", "rules", "success", "successMessages", "validateOnBlur", "value", "required", "data", "errorBucket", "hasColor", "hasFocused", "hasInput", "isFocused", "isResetting", "lazyValue", "valid", "computed", "computedColor", "isDisabled", "undefined", "color", "isDark", "appIsDark", "<PERSON><PERSON><PERSON><PERSON>", "internalErrorMessages", "length", "hasSuccess", "internalSuccessMessages", "externalError", "hasMessages", "validationTarget", "hasState", "shouldValidate", "genInternalMessages", "internalMessages", "internalValue", "get", "set", "val", "$emit", "form", "isInteractive", "is<PERSON><PERSON><PERSON>ly", "validations", "_context", "_sliceInstanceProperty", "call", "validationState", "watch", "handler", "newVal", "oldVal", "validate", "deep", "$nextTick", "_this", "_setTimeout", "beforeMount", "created", "register", "<PERSON><PERSON><PERSON><PERSON>", "unregister", "methods", "_Array$isArray", "reset", "resetValidation", "force", "arguments", "index", "rule", "push", "concat", "_typeof"], "sources": ["../../../src/mixins/validatable/index.ts"], "sourcesContent": ["// Mixins\nimport Colorable from '../colorable'\nimport Themeable from '../themeable'\nimport { inject as RegistrableInject } from '../registrable'\n\n// Utilities\nimport { deepEqual } from '../../util/helpers'\nimport { consoleError } from '../../util/console'\nimport mixins from '../../util/mixins'\n\n// Types\nimport { PropValidator } from 'vue/types/options'\nimport { InputMessage, InputValidationRules } from 'vuetify/types'\n\nconst baseMixins = mixins(\n  Colorable,\n  RegistrableInject<'form', any>('form'),\n  Themeable,\n)\n\n/* @vue/component */\nexport default baseMixins.extend({\n  name: 'validatable',\n\n  props: {\n    disabled: Boolean,\n    error: Boolean,\n    errorCount: {\n      type: [Number, String],\n      default: 1,\n    },\n    errorMessages: {\n      type: [String, Array],\n      default: () => [],\n    } as PropValidator<InputMessage | null>,\n    messages: {\n      type: [String, Array],\n      default: () => [],\n    } as PropValidator<InputMessage | null>,\n    readonly: Boolean,\n    rules: {\n      type: Array,\n      default: () => [],\n    } as PropValidator<InputValidationRules>,\n    success: Boolean,\n    successMessages: {\n      type: [String, Array],\n      default: () => [],\n    } as PropValidator<InputMessage | null>,\n    validateOnBlur: Boolean,\n    value: { required: false },\n  },\n\n  data () {\n    return {\n      errorBucket: [] as string[],\n      hasColor: false,\n      hasFocused: false,\n      hasInput: false,\n      isFocused: false,\n      isResetting: false,\n      lazyValue: this.value,\n      valid: false,\n    }\n  },\n\n  computed: {\n    computedColor (): string | undefined {\n      if (this.isDisabled) return undefined\n      if (this.color) return this.color\n      // It's assumed that if the input is on a\n      // dark background, the user will want to\n      // have a white color. If the entire app\n      // is setup to be dark, then they will\n      // like want to use their primary color\n      if (this.isDark && !this.appIsDark) return 'white'\n      else return 'primary'\n    },\n    hasError (): boolean {\n      return (\n        this.internalErrorMessages.length > 0 ||\n        this.errorBucket.length > 0 ||\n        this.error\n      )\n    },\n    // TODO: Add logic that allows the user to enable based\n    // upon a good validation\n    hasSuccess (): boolean {\n      return (\n        this.internalSuccessMessages.length > 0 ||\n        this.success\n      )\n    },\n    externalError (): boolean {\n      return this.internalErrorMessages.length > 0 || this.error\n    },\n    hasMessages (): boolean {\n      return this.validationTarget.length > 0\n    },\n    hasState (): boolean {\n      if (this.isDisabled) return false\n\n      return (\n        this.hasSuccess ||\n        (this.shouldValidate && this.hasError)\n      )\n    },\n    internalErrorMessages (): InputValidationRules {\n      return this.genInternalMessages(this.errorMessages)\n    },\n    internalMessages (): InputValidationRules {\n      return this.genInternalMessages(this.messages)\n    },\n    internalSuccessMessages (): InputValidationRules {\n      return this.genInternalMessages(this.successMessages)\n    },\n    internalValue: {\n      get (): unknown {\n        return this.lazyValue\n      },\n      set (val: any) {\n        this.lazyValue = val\n\n        this.$emit('input', val)\n      },\n    },\n    isDisabled (): boolean {\n      return this.disabled || (\n        !!this.form &&\n        this.form.disabled\n      )\n    },\n    isInteractive (): boolean {\n      return !this.isDisabled && !this.isReadonly\n    },\n    isReadonly (): boolean {\n      return this.readonly || (\n        !!this.form &&\n        this.form.readonly\n      )\n    },\n    shouldValidate (): boolean {\n      if (this.externalError) return true\n      if (this.isResetting) return false\n\n      return this.validateOnBlur\n        ? this.hasFocused && !this.isFocused\n        : (this.hasInput || this.hasFocused)\n    },\n    validations (): InputValidationRules {\n      return this.validationTarget.slice(0, Number(this.errorCount))\n    },\n    validationState (): string | undefined {\n      if (this.isDisabled) return undefined\n      if (this.hasError && this.shouldValidate) return 'error'\n      if (this.hasSuccess) return 'success'\n      if (this.hasColor) return this.computedColor\n      return undefined\n    },\n    validationTarget (): InputValidationRules {\n      if (this.internalErrorMessages.length > 0) {\n        return this.internalErrorMessages\n      } else if (this.successMessages && this.successMessages.length > 0) {\n        return this.internalSuccessMessages\n      } else if (this.messages && this.messages.length > 0) {\n        return this.internalMessages\n      } else if (this.shouldValidate) {\n        return this.errorBucket\n      } else return []\n    },\n  },\n\n  watch: {\n    rules: {\n      handler (newVal, oldVal) {\n        if (deepEqual(newVal, oldVal)) return\n        this.validate()\n      },\n      deep: true,\n    },\n    internalValue () {\n      // If it's the first time we're setting input,\n      // mark it with hasInput\n      this.hasInput = true\n      this.validateOnBlur || this.$nextTick(this.validate)\n    },\n    isFocused (val) {\n      // Should not check validation\n      // if disabled\n      if (\n        !val &&\n        !this.isDisabled\n      ) {\n        this.hasFocused = true\n        this.validateOnBlur && this.$nextTick(this.validate)\n      }\n    },\n    isResetting () {\n      setTimeout(() => {\n        this.hasInput = false\n        this.hasFocused = false\n        this.isResetting = false\n        this.validate()\n      }, 0)\n    },\n    hasError (val) {\n      if (this.shouldValidate) {\n        this.$emit('update:error', val)\n      }\n    },\n    value (val) {\n      this.lazyValue = val\n    },\n  },\n\n  beforeMount () {\n    this.validate()\n  },\n\n  created () {\n    this.form && this.form.register(this)\n  },\n\n  beforeDestroy () {\n    this.form && this.form.unregister(this)\n  },\n\n  methods: {\n    genInternalMessages (messages: InputMessage | null): InputValidationRules {\n      if (!messages) return []\n      else if (Array.isArray(messages)) return messages\n      else return [messages]\n    },\n    /** @public */\n    reset () {\n      this.isResetting = true\n      this.internalValue = Array.isArray(this.internalValue)\n        ? []\n        : null\n    },\n    /** @public */\n    resetValidation () {\n      this.isResetting = true\n    },\n    /** @public */\n    validate (force = false, value?: any): boolean {\n      const errorBucket = []\n      value = value || this.internalValue\n\n      if (force) this.hasInput = this.hasFocused = true\n\n      for (let index = 0; index < this.rules.length; index++) {\n        const rule = this.rules[index]\n        const valid = typeof rule === 'function' ? rule(value) : rule\n\n        if (valid === false || typeof valid === 'string') {\n          errorBucket.push(valid || '')\n        } else if (typeof valid !== 'boolean') {\n          consoleError(`Rules should return a string or boolean, received '${typeof valid}' instead`, this)\n        }\n      }\n\n      this.errorBucket = errorBucket\n      this.valid = errorBucket.length === 0\n\n      return this.valid\n    },\n  },\n})\n"], "mappings": ";;;;;;AAAA;AACA,OAAOA,SAAP,MAAsB,cAAtB;AACA,OAAOC,SAAP,MAAsB,cAAtB;AACA,SAASC,MAAM,IAAIC,iBAAnB,QAA4C,gBAA5C,C,CAEA;;AACA,SAASC,SAAT,QAA0B,oBAA1B;AACA,SAASC,YAAT,QAA6B,oBAA7B;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAMA,IAAMC,UAAU,GAAGD,MAAM,CACvBN,SADuB,EAEvBG,iBAAiB,CAAc,MAAd,CAFM,EAGvBF,SAHuB,CAAzB;AAMA;;AACA,eAAeM,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,aADyB;EAG/BC,KAAK,EAAE;IACLC,QAAQ,EAAEC,OADL;IAELC,KAAK,EAAED,OAFF;IAGLE,UAAU,EAAE;MACVC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADI;MAEV,WAAS;IAFC,CAHP;IAOLC,aAAa,EAAE;MACbH,IAAI,EAAE,CAACE,MAAD,EAASE,KAAT,CADO;MAEb,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ;MAAA;IAFF,CAPV;IAWLC,QAAQ,EAAE;MACRN,IAAI,EAAE,CAACE,MAAD,EAASE,KAAT,CADE;MAER,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ;MAAA;IAFP,CAXL;IAeLE,QAAQ,EAAEV,OAfL;IAgBLW,KAAK,EAAE;MACLR,IAAI,EAAEI,KADD;MAEL,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ;MAAA;IAFV,CAhBF;IAoBLI,OAAO,EAAEZ,OApBJ;IAqBLa,eAAe,EAAE;MACfV,IAAI,EAAE,CAACE,MAAD,EAASE,KAAT,CADS;MAEf,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ;MAAA;IAFA,CArBZ;IAyBLM,cAAc,EAAEd,OAzBX;IA0BLe,KAAK,EAAE;MAAEC,QAAQ,EAAE;IAAZ;EA1BF,CAHwB;EAgC/BC,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,WAAW,EAAE,EADR;MAELC,QAAQ,EAAE,KAFL;MAGLC,UAAU,EAAE,KAHP;MAILC,QAAQ,EAAE,KAJL;MAKLC,SAAS,EAAE,KALN;MAMLC,WAAW,EAAE,KANR;MAOLC,SAAS,EAAE,KAAKT,KAPX;MAQLU,KAAK,EAAE;IARF,CAAP;EAUD,CA3C8B;EA6C/BC,QAAQ,EAAE;IACRC,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAI,KAAKC,UAAT,EAAqB,OAAOC,SAAP;MACrB,IAAI,KAAKC,KAAT,EAAgB,OAAO,KAAKA,KAAZ,CAFL,CAGX;MACA;MACA;MACA;MACA;;MACA,IAAI,KAAKC,MAAL,IAAe,CAAC,KAAKC,SAAzB,EAAoC,OAAO,OAAP,CAApC,KACK,OAAO,SAAP;IACN,CAXO;IAYRC,QAAQ,WAARA,QAAQA,CAAA;MACN,OACE,KAAKC,qBAAL,CAA2BC,MAA3B,GAAoC,CAApC,IACA,KAAKjB,WAAL,CAAiBiB,MAAjB,GAA0B,CAD1B,IAEA,KAAKlC,KAHP;IAKD,CAlBO;IAmBR;IACA;IACAmC,UAAU,WAAVA,UAAUA,CAAA;MACR,OACE,KAAKC,uBAAL,CAA6BF,MAA7B,GAAsC,CAAtC,IACA,KAAKvB,OAFP;IAID,CA1BO;IA2BR0B,aAAa,WAAbA,aAAaA,CAAA;MACX,OAAO,KAAKJ,qBAAL,CAA2BC,MAA3B,GAAoC,CAApC,IAAyC,KAAKlC,KAArD;IACD,CA7BO;IA8BRsC,WAAW,WAAXA,WAAWA,CAAA;MACT,OAAO,KAAKC,gBAAL,CAAsBL,MAAtB,GAA+B,CAAtC;IACD,CAhCO;IAiCRM,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAI,KAAKb,UAAT,EAAqB,OAAO,KAAP;MAErB,OACE,KAAKQ,UAAL,IACC,KAAKM,cAAL,IAAuB,KAAKT,QAF/B;IAID,CAxCO;IAyCRC,qBAAqB,WAArBA,qBAAqBA,CAAA;MACnB,OAAO,KAAKS,mBAAL,CAAyB,KAAKrC,aAA9B,CAAP;IACD,CA3CO;IA4CRsC,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,OAAO,KAAKD,mBAAL,CAAyB,KAAKlC,QAA9B,CAAP;IACD,CA9CO;IA+CR4B,uBAAuB,WAAvBA,uBAAuBA,CAAA;MACrB,OAAO,KAAKM,mBAAL,CAAyB,KAAK9B,eAA9B,CAAP;IACD,CAjDO;IAkDRgC,aAAa,EAAE;MACbC,GAAG,WAAHA,GAAGA,CAAA;QACD,OAAO,KAAKtB,SAAZ;MACD,CAHY;MAIbuB,GAAG,WAAHA,GAAGA,CAAEC,GAAF,EAAU;QACX,KAAKxB,SAAL,GAAiBwB,GAAjB;QAEA,KAAKC,KAAL,CAAW,OAAX,EAAoBD,GAApB;MACD;IARY,CAlDP;IA4DRpB,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAK7B,QAAL,IACL,CAAC,CAAC,KAAKmD,IAAP,IACA,KAAKA,IAAL,CAAUnD,QAFZ;IAID,CAjEO;IAkERoD,aAAa,WAAbA,aAAaA,CAAA;MACX,OAAO,CAAC,KAAKvB,UAAN,IAAoB,CAAC,KAAKwB,UAAjC;IACD,CApEO;IAqERA,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAK1C,QAAL,IACL,CAAC,CAAC,KAAKwC,IAAP,IACA,KAAKA,IAAL,CAAUxC,QAFZ;IAID,CA1EO;IA2ERgC,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAI,KAAKJ,aAAT,EAAwB,OAAO,IAAP;MACxB,IAAI,KAAKf,WAAT,EAAsB,OAAO,KAAP;MAEtB,OAAO,KAAKT,cAAL,GACH,KAAKM,UAAL,IAAmB,CAAC,KAAKE,SADtB,GAEF,KAAKD,QAAL,IAAiB,KAAKD,UAF3B;IAGD,CAlFO;IAmFRiC,WAAW,WAAXA,WAAWA,CAAA;MAAA,IAAAC,QAAA;MACT,OAAOC,sBAAA,CAAAD,QAAA,QAAKd,gBAAL,EAAAgB,IAAA,CAAAF,QAAA,EAA4B,CAA5B,EAA+BlD,MAAM,CAAC,KAAKF,UAAN,CAArC,CAAP;IACD,CArFO;IAsFRuD,eAAe,WAAfA,eAAeA,CAAA;MACb,IAAI,KAAK7B,UAAT,EAAqB,OAAOC,SAAP;MACrB,IAAI,KAAKI,QAAL,IAAiB,KAAKS,cAA1B,EAA0C,OAAO,OAAP;MAC1C,IAAI,KAAKN,UAAT,EAAqB,OAAO,SAAP;MACrB,IAAI,KAAKjB,QAAT,EAAmB,OAAO,KAAKQ,aAAZ;MACnB,OAAOE,SAAP;IACD,CA5FO;IA6FRW,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,IAAI,KAAKN,qBAAL,CAA2BC,MAA3B,GAAoC,CAAxC,EAA2C;QACzC,OAAO,KAAKD,qBAAZ;MACD,CAFD,MAEO,IAAI,KAAKrB,eAAL,IAAwB,KAAKA,eAAL,CAAqBsB,MAArB,GAA8B,CAA1D,EAA6D;QAClE,OAAO,KAAKE,uBAAZ;MACD,CAFM,MAEA,IAAI,KAAK5B,QAAL,IAAiB,KAAKA,QAAL,CAAc0B,MAAd,GAAuB,CAA5C,EAA+C;QACpD,OAAO,KAAKS,gBAAZ;MACD,CAFM,MAEA,IAAI,KAAKF,cAAT,EAAyB;QAC9B,OAAO,KAAKxB,WAAZ;MACD,CAFM,MAEA,OAAO,EAAP;IACR;EAvGO,CA7CqB;EAuJ/BwC,KAAK,EAAE;IACL/C,KAAK,EAAE;MACLgD,OAAO,WAAPA,OAAOA,CAAEC,MAAF,EAAUC,MAAV,EAAgB;QACrB,IAAIrE,SAAS,CAACoE,MAAD,EAASC,MAAT,CAAb,EAA+B;QAC/B,KAAKC,QAAL;MACD,CAJI;MAKLC,IAAI,EAAE;IALD,CADF;IAQLlB,aAAa,WAAbA,aAAaA,CAAA;MACX;MACA;MACA,KAAKxB,QAAL,GAAgB,IAAhB;MACA,KAAKP,cAAL,IAAuB,KAAKkD,SAAL,CAAe,KAAKF,QAApB,CAAvB;IACD,CAbI;IAcLxC,SAAS,WAATA,SAASA,CAAE0B,GAAF,EAAK;MACZ;MACA;MACA,IACE,CAACA,GAAD,IACA,CAAC,KAAKpB,UAFR,EAGE;QACA,KAAKR,UAAL,GAAkB,IAAlB;QACA,KAAKN,cAAL,IAAuB,KAAKkD,SAAL,CAAe,KAAKF,QAApB,CAAvB;MACD;IACF,CAxBI;IAyBLvC,WAAW,WAAXA,WAAWA,CAAA;MAAA,IAAA0C,KAAA;MACTC,WAAA,CAAW,YAAK;QACdD,KAAA,CAAK5C,QAAL,GAAgB,KAAhB;QACA4C,KAAA,CAAK7C,UAAL,GAAkB,KAAlB;QACA6C,KAAA,CAAK1C,WAAL,GAAmB,KAAnB;QACA0C,KAAA,CAAKH,QAAL;MACD,CALS,EAKP,CALO,CAAV;IAMD,CAhCI;IAiCL7B,QAAQ,WAARA,QAAQA,CAAEe,GAAF,EAAK;MACX,IAAI,KAAKN,cAAT,EAAyB;QACvB,KAAKO,KAAL,CAAW,cAAX,EAA2BD,GAA3B;MACD;IACF,CArCI;IAsCLjC,KAAK,WAALA,KAAKA,CAAEiC,GAAF,EAAK;MACR,KAAKxB,SAAL,GAAiBwB,GAAjB;IACD;EAxCI,CAvJwB;EAkM/BmB,WAAW,WAAXA,WAAWA,CAAA;IACT,KAAKL,QAAL;EACD,CApM8B;EAsM/BM,OAAO,WAAPA,OAAOA,CAAA;IACL,KAAKlB,IAAL,IAAa,KAAKA,IAAL,CAAUmB,QAAV,CAAmB,IAAnB,CAAb;EACD,CAxM8B;EA0M/BC,aAAa,WAAbA,aAAaA,CAAA;IACX,KAAKpB,IAAL,IAAa,KAAKA,IAAL,CAAUqB,UAAV,CAAqB,IAArB,CAAb;EACD,CA5M8B;EA8M/BC,OAAO,EAAE;IACP7B,mBAAmB,WAAnBA,mBAAmBA,CAAElC,QAAF,EAA+B;MAChD,IAAI,CAACA,QAAL,EAAe,OAAO,EAAP,CAAf,KACK,IAAIgE,cAAA,CAAchE,QAAd,CAAJ,EAA6B,OAAOA,QAAP,CAA7B,KACA,OAAO,CAACA,QAAD,CAAP;IACN,CALM;IAMP,cACAiE,KAAK,WAALA,KAAKA,CAAA;MACH,KAAKnD,WAAL,GAAmB,IAAnB;MACA,KAAKsB,aAAL,GAAqB4B,cAAA,CAAc,KAAK5B,aAAnB,IACjB,EADiB,GAEjB,IAFJ;IAGD,CAZM;IAaP,cACA8B,eAAe,WAAfA,eAAeA,CAAA;MACb,KAAKpD,WAAL,GAAmB,IAAnB;IACD,CAhBM;IAiBP,cACAuC,QAAQ,WAARA,QAAQA,CAAA,EAA4B;MAAA,IAA1Bc,KAAK,GAAAC,SAAA,CAAA1C,MAAA,QAAA0C,SAAA,QAAAhD,SAAA,GAAAgD,SAAA,MAAG,KAAV;MAAA,IAAiB9D,KAAjB,GAAA8D,SAAA,CAAA1C,MAAA,OAAA0C,SAAA,MAAAhD,SAAA;MACN,IAAMX,WAAW,GAAG,EAApB;MACAH,KAAK,GAAGA,KAAK,IAAI,KAAK8B,aAAtB;MAEA,IAAI+B,KAAJ,EAAW,KAAKvD,QAAL,GAAgB,KAAKD,UAAL,GAAkB,IAAlC;MAEX,KAAK,IAAI0D,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAG,KAAKnE,KAAL,CAAWwB,MAAvC,EAA+C2C,KAAK,EAApD,EAAwD;QACtD,IAAMC,IAAI,GAAG,KAAKpE,KAAL,CAAWmE,KAAX,CAAb;QACA,IAAMrD,KAAK,GAAG,OAAOsD,IAAP,KAAgB,UAAhB,GAA6BA,IAAI,CAAChE,KAAD,CAAjC,GAA2CgE,IAAzD;QAEA,IAAItD,KAAK,KAAK,KAAV,IAAmB,OAAOA,KAAP,KAAiB,QAAxC,EAAkD;UAChDP,WAAW,CAAC8D,IAAZ,CAAiBvD,KAAK,IAAI,EAA1B;QACD,CAFD,MAEO,IAAI,OAAOA,KAAP,KAAiB,SAArB,EAAgC;UACrChC,YAAY,uDAAAwF,MAAA,CAAAC,OAAA,CAA8DzD,KAAK,iBAAa,IAAhF,CAAZ;QACD;MACF;MAED,KAAKP,WAAL,GAAmBA,WAAnB;MACA,KAAKO,KAAL,GAAaP,WAAW,CAACiB,MAAZ,KAAuB,CAApC;MAEA,OAAO,KAAKV,KAAZ;IACD;EAvCM;AA9MsB,CAAlB,CAAf", "ignoreList": []}]}