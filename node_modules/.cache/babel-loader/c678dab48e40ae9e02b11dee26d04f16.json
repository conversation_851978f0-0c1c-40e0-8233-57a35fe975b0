{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/hu.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/hu.js", "mtime": 1757335235903}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGJhZGdlOiAnSmVsdsOpbnknLAogIGNsb3NlOiAnQmV6w6Fyw6FzJywKICBkYXRhSXRlcmF0b3I6IHsKICAgIG5vUmVzdWx0c1RleHQ6ICdOaW5jcyBlZ3llesWRIHRhbMOhbGF0JywKICAgIGxvYWRpbmdUZXh0OiAnQmV0w7ZsdMOpcy4uLicKICB9LAogIGRhdGFUYWJsZTogewogICAgaXRlbXNQZXJQYWdlVGV4dDogJ0VsZW0gb2xkYWxhbmvDqW50OicsCiAgICBhcmlhTGFiZWw6IHsKICAgICAgc29ydERlc2NlbmRpbmc6ICdDc8O2a2tlbsWRIHNvcnJlbmRiZSByZW5kZXp2ZS4nLAogICAgICBzb3J0QXNjZW5kaW5nOiAnTsO2dmVrdsWRIHNvcnJlbmRiZSByZW5kZXp2ZS4nLAogICAgICBzb3J0Tm9uZTogJ1JlbmRlemV0bGVuLicsCiAgICAgIGFjdGl2YXRlTm9uZTogJ1JlbmRlesOpcyB0w7ZybMOpc2UuJywKICAgICAgYWN0aXZhdGVEZXNjZW5kaW5nOiAnQWt0aXbDoWxkIGEgY3PDtmtrZW7FkSByZW5kZXrDqXPDqXJ0LicsCiAgICAgIGFjdGl2YXRlQXNjZW5kaW5nOiAnQWt0aXbDoWxkIGEgbsO2dmVrdsWRIHJlbmRlesOpc8OpcnQuJwogICAgfSwKICAgIHNvcnRCeTogJ1JlbmRlesOpcycKICB9LAogIGRhdGFGb290ZXI6IHsKICAgIGl0ZW1zUGVyUGFnZVRleHQ6ICdFbGVtIG9sZGFsYW5rw6ludDonLAogICAgaXRlbXNQZXJQYWdlQWxsOiAnTWluZCcsCiAgICBuZXh0UGFnZTogJ0vDtnZldGtlesWRIG9sZGFsJywKICAgIHByZXZQYWdlOiAnRWzFkXrFkSBvbGRhbCcsCiAgICBmaXJzdFBhZ2U6ICdFbHPFkSBvbGRhbCcsCiAgICBsYXN0UGFnZTogJ1V0b2xzw7Mgb2xkYWwnLAogICAgcGFnZVRleHQ6ICd7MH0tezF9IC8gezJ9JwogIH0sCiAgZGF0ZVBpY2tlcjogewogICAgaXRlbXNTZWxlY3RlZDogJ3swfSBraXbDoWxhc3p0dmEnLAogICAgbmV4dE1vbnRoQXJpYUxhYmVsOiAnS8O2dmV0a2V6xZEgaMOzbmFwJywKICAgIG5leHRZZWFyQXJpYUxhYmVsOiAnS8O2dmV0a2V6xZEgw6l2JywKICAgIHByZXZNb250aEFyaWFMYWJlbDogJ0VsxZF6xZEgaMOzbmFwJywKICAgIHByZXZZZWFyQXJpYUxhYmVsOiAnRWzFkXrFkSDDqXYnCiAgfSwKICBub0RhdGFUZXh0OiAnTmluY3MgZWzDqXJoZXTFkSBhZGF0JywKICBjYXJvdXNlbDogewogICAgcHJldjogJ0VsxZF6xZEnLAogICAgbmV4dDogJ0vDtnZldGtlesWRJywKICAgIGFyaWFMYWJlbDogewogICAgICBkZWxpbWl0ZXI6ICdEaWEgezB9L3sxfScKICAgIH0KICB9LAogIGNhbGVuZGFyOiB7CiAgICBtb3JlRXZlbnRzOiAnezB9IHRvdsOhYmJpJwogIH0sCiAgZmlsZUlucHV0OiB7CiAgICBjb3VudGVyOiAnezB9IGbDoWpsJywKICAgIGNvdW50ZXJTaXplOiAnezB9IGbDoWpsICh7MX0gw7Zzc3plc2VuKScKICB9LAogIHRpbWVQaWNrZXI6IHsKICAgIGFtOiAnZGUnLAogICAgcG06ICdkdScKICB9LAogIHBhZ2luYXRpb246IHsKICAgIGFyaWFMYWJlbDogewogICAgICB3cmFwcGVyOiAnT2xkYWwgbmF2aWfDoWNpw7MnLAogICAgICBuZXh0OiAnS8O2dmV0a2V6xZEgb2xkYWwnLAogICAgICBwcmV2aW91czogJ0VsxZF6xZEgb2xkYWwnLAogICAgICBwYWdlOiAnTWVuaiBhKHopIHswfS4gb2xkYWxyYScsCiAgICAgIGN1cnJlbnRQYWdlOiAnQWt0dcOhbGlzIG9sZGFsOiB7MH0nCiAgICB9CiAgfSwKICByYXRpbmc6IHsKICAgIGFyaWFMYWJlbDogewogICAgICBpY29uOiAnUmF0aW5nIHswfSBvZiB7MX0nCiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/hu.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: '<PERSON><PERSON><PERSON> e<PERSON> találat',\n    loadingText: 'Betöltés...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Elem oldalanként:',\n    ariaLabel: {\n      sortDescending: 'Csökkenő sorrendbe rendezve.',\n      sortAscending: 'Növekvő sorrendbe rendezve.',\n      sortNone: 'Rendezetlen.',\n      activateNone: 'Rendezés törlése.',\n      activateDescending: 'Aktiváld a csökkenő rendezésért.',\n      activateAscending: 'Aktiváld a növekvő rendezésért.',\n    },\n    sortBy: 'Rendezés',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Elem oldalanként:',\n    itemsPerPageAll: 'Mind',\n    nextPage: 'Következ<PERSON> oldal',\n    prevPage: '<PERSON><PERSON><PERSON><PERSON> oldal',\n    firstPage: '<PERSON><PERSON><PERSON> oldal',\n    lastPage: 'U<PERSON><PERSON><PERSON> oldal',\n    pageText: '{0}-{1} / {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} kiválasztva',\n    nextMonthAriaLabel: 'Következő hónap',\n    nextYearAriaLabel: 'Következő év',\n    prevMonthAriaLabel: 'Előző hónap',\n    prevYearAriaLabel: 'Előző év',\n  },\n  noDataText: 'Nincs elérhető adat',\n  carousel: {\n    prev: 'Előző',\n    next: 'Következő',\n    ariaLabel: {\n      delimiter: 'Dia {0}/{1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} további',\n  },\n  fileInput: {\n    counter: '{0} fájl',\n    counterSize: '{0} fájl ({1} összesen)',\n  },\n  timePicker: {\n    am: 'de',\n    pm: 'du',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Oldal navigáció',\n      next: 'Következő oldal',\n      previous: 'Előző oldal',\n      page: 'Menj a(z) {0}. oldalra',\n      currentPage: 'Aktuális oldal: {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,SADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,sBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,mBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,8BADP;MAETC,aAAa,EAAE,6BAFN;MAGTC,QAAQ,EAAE,cAHD;MAITC,YAAY,EAAE,mBAJL;MAKTC,kBAAkB,EAAE,kCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,mBADR;IAEVU,eAAe,EAAE,MAFP;IAGVC,QAAQ,EAAE,iBAHA;IAIVC,QAAQ,EAAE,aAJA;IAKVC,SAAS,EAAE,YALD;IAMVC,QAAQ,EAAE,cANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,iBADL;IAEVC,kBAAkB,EAAE,iBAFV;IAGVC,iBAAiB,EAAE,cAHT;IAIVC,kBAAkB,EAAE,aAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,qBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,OADE;IAERC,IAAI,EAAE,WAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,UADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,iBADA;MAETX,IAAI,EAAE,iBAFG;MAGTY,QAAQ,EAAE,aAHD;MAITC,IAAI,EAAE,wBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}