{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VListItemAvatar.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VListItemAvatar.js", "mtime": 1757335238382}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKLy8gQ29tcG9uZW50cwppbXBvcnQgVkF2YXRhciBmcm9tICcuLi9WQXZhdGFyJzsKLyogQHZ1ZS9jb21wb25lbnQgKi8KCmV4cG9ydCBkZWZhdWx0IFZBdmF0YXIuZXh0ZW5kKHsKICBuYW1lOiAndi1saXN0LWl0ZW0tYXZhdGFyJywKICBwcm9wczogewogICAgaG9yaXpvbnRhbDogQm9vbGVhbiwKICAgIHNpemU6IHsKICAgICAgdHlwZTogW051bWJlciwgU3RyaW5nXSwKICAgICAgImRlZmF1bHQiOiA0MAogICAgfQogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNsYXNzZXM6IGZ1bmN0aW9uIGNsYXNzZXMoKSB7CiAgICAgIHJldHVybiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoewogICAgICAgICd2LWxpc3QtaXRlbV9fYXZhdGFyLS1ob3Jpem9udGFsJzogdGhpcy5ob3Jpem9udGFsCiAgICAgIH0sIFZBdmF0YXIub3B0aW9ucy5jb21wdXRlZC5jbGFzc2VzLmNhbGwodGhpcykpLCB7fSwgewogICAgICAgICd2LWF2YXRhci0tdGlsZSc6IHRoaXMudGlsZSB8fCB0aGlzLmhvcml6b250YWwKICAgICAgfSk7CiAgICB9CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICB2YXIgcmVuZGVyID0gVkF2YXRhci5vcHRpb25zLnJlbmRlci5jYWxsKHRoaXMsIGgpOwogICAgcmVuZGVyLmRhdGEgPSByZW5kZXIuZGF0YSB8fCB7fTsKICAgIHJlbmRlci5kYXRhLnN0YXRpY0NsYXNzICs9ICcgdi1saXN0LWl0ZW1fX2F2YXRhcic7CiAgICByZXR1cm4gcmVuZGVyOwogIH0KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "extend", "name", "props", "horizontal", "Boolean", "size", "type", "Number", "String", "computed", "classes", "_objectSpread", "options", "call", "tile", "render", "h", "data", "staticClass"], "sources": ["../../../src/components/VList/VListItemAvatar.ts"], "sourcesContent": ["// Components\nimport VAvatar from '../VAvatar'\n\n// Types\nimport { VNode } from 'vue'\n\n/* @vue/component */\nexport default VAvatar.extend({\n  name: 'v-list-item-avatar',\n\n  props: {\n    horizontal: Boolean,\n    size: {\n      type: [Number, String],\n      default: 40,\n    },\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        'v-list-item__avatar--horizontal': this.horizontal,\n        ...VAvatar.options.computed.classes.call(this),\n        'v-avatar--tile': this.tile || this.horizontal,\n      }\n    },\n  },\n\n  render (h): VNode {\n    const render = VAvatar.options.render.call(this, h)\n\n    render.data = render.data || {}\n    render.data.staticClass += ' v-list-item__avatar'\n\n    return render\n  },\n})\n"], "mappings": ";;AAAA;AACA,OAAOA,OAAP,MAAoB,YAApB;AAKA;;AACA,eAAeA,OAAO,CAACC,MAAR,CAAe;EAC5BC,IAAI,EAAE,oBADsB;EAG5BC,KAAK,EAAE;IACLC,UAAU,EAAEC,OADP;IAELC,IAAI,EAAE;MACJC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADF;MAEJ,WAAS;IAFL;EAFD,CAHqB;EAW5BC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA;QACE,mCAAmC,KAAKR;MADnC,GAEFJ,OAAO,CAACa,OAAR,CAAgBH,QAAhB,CAAyBC,OAAzB,CAAiCG,IAAjC,CAAsC,IAAtC,CAFE;QAGL,kBAAkB,KAAKC,IAAL,IAAa,KAAKX;MAAA;IAEvC;EAPO,CAXkB;EAqB5BY,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMD,MAAM,GAAGhB,OAAO,CAACa,OAAR,CAAgBG,MAAhB,CAAuBF,IAAvB,CAA4B,IAA5B,EAAkCG,CAAlC,CAAf;IAEAD,MAAM,CAACE,IAAP,GAAcF,MAAM,CAACE,IAAP,IAAe,EAA7B;IACAF,MAAM,CAACE,IAAP,CAAYC,WAAZ,IAA2B,sBAA3B;IAEA,OAAOH,MAAP;EACD;AA5B2B,CAAf,CAAf", "ignoreList": []}]}