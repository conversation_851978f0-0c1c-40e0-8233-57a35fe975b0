{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VStepper/VStepperContent.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VStepper/VStepperContent.js", "mtime": 1757335239115}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VTabTransition", "VTabReverseTransition", "inject", "RegistrableInject", "convertToUnit", "mixins", "baseMixins", "extend", "name", "isVerticalProvided", "from", "props", "step", "type", "Number", "String", "required", "data", "height", "isActive", "isReverse", "isVertical", "computed", "computedTransition", "reverse", "$vuetify", "rtl", "styles", "watch", "current", "previous", "enter", "leave", "mounted", "$refs", "wrapper", "addEventListener", "onTransition", "stepper", "register", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "unregister", "methods", "e", "propertyName", "_this", "scrollHeight", "requestAnimationFrame", "_setTimeout", "_this2", "clientHeight", "toggle", "toString", "render", "h", "contentData", "staticClass", "wrapperData", "style", "ref", "directives", "value", "$slots", "content", "on", "$listeners"], "sources": ["../../../src/components/VStepper/VStepperContent.ts"], "sourcesContent": ["// Components\nimport {\n  VTabTransition,\n  VTabReverseTransition,\n} from '../transitions'\n\n// Mixins\nimport { inject as RegistrableInject } from '../../mixins/registrable'\n\n// Helpers\nimport { convertToUnit } from '../../util/helpers'\n\n// Utilities\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode, FunctionalComponentOptions, VNodeData } from 'vue'\n\nconst baseMixins = mixins(\n  RegistrableInject('stepper', 'v-stepper-content', 'v-stepper')\n)\n\ninterface options extends InstanceType<typeof baseMixins> {\n  $refs: {\n    wrapper: HTMLElement\n  }\n  isVerticalProvided: boolean\n}\n\n/* @vue/component */\nexport default baseMixins.extend<options>().extend({\n  name: 'v-stepper-content',\n\n  inject: {\n    isVerticalProvided: {\n      from: 'isVertical',\n    },\n  },\n\n  props: {\n    step: {\n      type: [Number, String],\n      required: true,\n    },\n  },\n\n  data () {\n    return {\n      height: 0 as number | string,\n      // Must be null to allow\n      // previous comparison\n      isActive: null as boolean | null,\n      isReverse: false,\n      isVertical: this.isVerticalProvided,\n    }\n  },\n\n  computed: {\n    computedTransition (): FunctionalComponentOptions {\n      // Fix for #8978\n      const reverse = this.$vuetify.rtl ? !this.isReverse : this.isReverse\n\n      return reverse\n        ? VTabReverseTransition\n        : VTabTransition\n    },\n    styles (): object {\n      if (!this.isVertical) return {}\n\n      return {\n        height: convertToUnit(this.height),\n      }\n    },\n  },\n\n  watch: {\n    isActive (current, previous) {\n      // If active and the previous state\n      // was null, is just booting up\n      if (current && previous == null) {\n        this.height = 'auto'\n        return\n      }\n\n      if (!this.isVertical) return\n\n      if (this.isActive) this.enter()\n      else this.leave()\n    },\n  },\n\n  mounted () {\n    this.$refs.wrapper.addEventListener(\n      'transitionend',\n      this.onTransition,\n      false\n    )\n    this.stepper && this.stepper.register(this)\n  },\n\n  beforeDestroy () {\n    this.$refs.wrapper.removeEventListener(\n      'transitionend',\n      this.onTransition,\n      false\n    )\n    this.stepper && this.stepper.unregister(this)\n  },\n\n  methods: {\n    onTransition (e: TransitionEvent) {\n      if (!this.isActive ||\n        e.propertyName !== 'height'\n      ) return\n\n      this.height = 'auto'\n    },\n    enter () {\n      let scrollHeight = 0\n\n      // Render bug with height\n      requestAnimationFrame(() => {\n        scrollHeight = this.$refs.wrapper.scrollHeight\n      })\n\n      this.height = 0\n\n      // Give the collapsing element time to collapse\n      setTimeout(() => this.isActive && (this.height = (scrollHeight || 'auto')), 450)\n    },\n    leave () {\n      this.height = this.$refs.wrapper.clientHeight\n      setTimeout(() => (this.height = 0), 10)\n    },\n    toggle (step: string | number, reverse: boolean) {\n      this.isActive = step.toString() === this.step.toString()\n      this.isReverse = reverse\n    },\n  },\n\n  render (h): VNode {\n    const contentData = {\n      staticClass: 'v-stepper__content',\n    } as VNodeData\n    const wrapperData = {\n      staticClass: 'v-stepper__wrapper',\n      style: this.styles,\n      ref: 'wrapper',\n    }\n\n    if (!this.isVertical) {\n      contentData.directives = [{\n        name: 'show',\n        value: this.isActive,\n      }]\n    }\n\n    const wrapper = h('div', wrapperData, [this.$slots.default])\n    const content = h('div', contentData, [wrapper])\n\n    return h(this.computedTransition, {\n      on: this.$listeners,\n    }, [content])\n  },\n})\n"], "mappings": ";;;;;AAAA;AACA,SACEA,cADF,EAEEC,qBAFF,QAGO,gBAHP,C,CAKA;;AACA,SAASC,MAAM,IAAIC,iBAAnB,QAA4C,0BAA5C,C,CAEA;;AACA,SAASC,aAAT,QAA8B,oBAA9B,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAKA,IAAMC,UAAU,GAAGD,MAAM,CACvBF,iBAAiB,CAAC,SAAD,EAAY,mBAAZ,EAAiC,WAAjC,CADM,CAAzB;AAWA;;AACA,eAAeG,UAAU,CAACC,MAAX,GAA6BA,MAA7B,CAAoC;EACjDC,IAAI,EAAE,mBAD2C;EAGjDN,MAAM,EAAE;IACNO,kBAAkB,EAAE;MAClBC,IAAI,EAAE;IADY;EADd,CAHyC;EASjDC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADF;MAEJC,QAAQ,EAAE;IAFN;EADD,CAT0C;EAgBjDC,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,MAAM,EAAE,CADH;MAEL;MACA;MACAC,QAAQ,EAAE,IAJL;MAKLC,SAAS,EAAE,KALN;MAMLC,UAAU,EAAE,KAAKZ;IANZ,CAAP;EAQD,CAzBgD;EA2BjDa,QAAQ,EAAE;IACRC,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB;MACA,IAAMC,OAAO,GAAG,KAAKC,QAAL,CAAcC,GAAd,GAAoB,CAAC,KAAKN,SAA1B,GAAsC,KAAKA,SAA3D;MAEA,OAAOI,OAAO,GACVvB,qBADU,GAEVD,cAFJ;IAGD,CARO;IASR2B,MAAM,WAANA,MAAMA,CAAA;MACJ,IAAI,CAAC,KAAKN,UAAV,EAAsB,OAAO,EAAP;MAEtB,OAAO;QACLH,MAAM,EAAEd,aAAa,CAAC,KAAKc,MAAN;MADhB,CAAP;IAGD;EAfO,CA3BuC;EA6CjDU,KAAK,EAAE;IACLT,QAAQ,WAARA,QAAQA,CAAEU,OAAF,EAAWC,QAAX,EAAmB;MACzB;MACA;MACA,IAAID,OAAO,IAAIC,QAAQ,IAAI,IAA3B,EAAiC;QAC/B,KAAKZ,MAAL,GAAc,MAAd;QACA;MACD;MAED,IAAI,CAAC,KAAKG,UAAV,EAAsB;MAEtB,IAAI,KAAKF,QAAT,EAAmB,KAAKY,KAAL,GAAnB,KACK,KAAKC,KAAL;IACN;EAbI,CA7C0C;EA6DjDC,OAAO,WAAPA,OAAOA,CAAA;IACL,KAAKC,KAAL,CAAWC,OAAX,CAAmBC,gBAAnB,CACE,eADF,EAEE,KAAKC,YAFP,EAGE,KAHF;IAKA,KAAKC,OAAL,IAAgB,KAAKA,OAAL,CAAaC,QAAb,CAAsB,IAAtB,CAAhB;EACD,CApEgD;EAsEjDC,aAAa,WAAbA,aAAaA,CAAA;IACX,KAAKN,KAAL,CAAWC,OAAX,CAAmBM,mBAAnB,CACE,eADF,EAEE,KAAKJ,YAFP,EAGE,KAHF;IAKA,KAAKC,OAAL,IAAgB,KAAKA,OAAL,CAAaI,UAAb,CAAwB,IAAxB,CAAhB;EACD,CA7EgD;EA+EjDC,OAAO,EAAE;IACPN,YAAY,WAAZA,YAAYA,CAAEO,CAAF,EAAoB;MAC9B,IAAI,CAAC,KAAKzB,QAAN,IACFyB,CAAC,CAACC,YAAF,KAAmB,QADrB,EAEE;MAEF,KAAK3B,MAAL,GAAc,MAAd;IACD,CAPM;IAQPa,KAAK,WAALA,KAAKA,CAAA;MAAA,IAAAe,KAAA;MACH,IAAIC,YAAY,GAAG,CAAnB,CADG,CAGH;;MACAC,qBAAqB,CAAC,YAAK;QACzBD,YAAY,GAAGD,KAAA,CAAKZ,KAAL,CAAWC,OAAX,CAAmBY,YAAlC;MACD,CAFoB,CAArB;MAIA,KAAK7B,MAAL,GAAc,CAAd,CARG,CAUH;;MACA+B,WAAA,CAAW;QAAA,OAAMH,KAAA,CAAK3B,QAAL,KAAkB2B,KAAA,CAAK5B,MAAL,GAAe6B,YAAY,IAAI,MAAjD,CAAP;MAAA,GAAkE,GAAlE,CAAV;IACD,CApBM;IAqBPf,KAAK,WAALA,KAAKA,CAAA;MAAA,IAAAkB,MAAA;MACH,KAAKhC,MAAL,GAAc,KAAKgB,KAAL,CAAWC,OAAX,CAAmBgB,YAAjC;MACAF,WAAA,CAAW;QAAA,OAAOC,MAAA,CAAKhC,MAAL,GAAc,CAAtB;MAAA,GAA0B,EAA1B,CAAV;IACD,CAxBM;IAyBPkC,MAAM,WAANA,MAAMA,CAAExC,IAAF,EAAyBY,OAAzB,EAAyC;MAC7C,KAAKL,QAAL,GAAgBP,IAAI,CAACyC,QAAL,OAAoB,KAAKzC,IAAL,CAAUyC,QAAV,EAApC;MACA,KAAKjC,SAAL,GAAiBI,OAAjB;IACD;EA5BM,CA/EwC;EA8GjD8B,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMC,WAAW,GAAG;MAClBC,WAAW,EAAE;IADK,CAApB;IAGA,IAAMC,WAAW,GAAG;MAClBD,WAAW,EAAE,oBADK;MAElBE,KAAK,EAAE,KAAKhC,MAFM;MAGlBiC,GAAG,EAAE;IAHa,CAApB;IAMA,IAAI,CAAC,KAAKvC,UAAV,EAAsB;MACpBmC,WAAW,CAACK,UAAZ,GAAyB,CAAC;QACxBrD,IAAI,EAAE,MADkB;QAExBsD,KAAK,EAAE,KAAK3C;MAFY,CAAD,CAAzB;IAID;IAED,IAAMgB,OAAO,GAAGoB,CAAC,CAAC,KAAD,EAAQG,WAAR,EAAqB,CAAC,KAAKK,MAAL,WAAD,CAArB,CAAjB;IACA,IAAMC,OAAO,GAAGT,CAAC,CAAC,KAAD,EAAQC,WAAR,EAAqB,CAACrB,OAAD,CAArB,CAAjB;IAEA,OAAOoB,CAAC,CAAC,KAAKhC,kBAAN,EAA0B;MAChC0C,EAAE,EAAE,KAAKC;IADuB,CAA1B,EAEL,CAACF,OAAD,CAFK,CAAR;EAGD;AArIgD,CAApC,CAAf", "ignoreList": []}]}