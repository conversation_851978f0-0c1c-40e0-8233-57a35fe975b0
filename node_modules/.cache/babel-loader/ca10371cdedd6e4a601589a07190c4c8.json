{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VOverlay/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VOverlay/index.js", "mtime": 1757335236911}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZPdmVybGF5IGZyb20gJy4vVk92ZXJsYXknOwpleHBvcnQgeyBWT3ZlcmxheSB9OwpleHBvcnQgZGVmYXVsdCBWT3ZlcmxheTs="}, {"version": 3, "names": ["VOverlay"], "sources": ["../../../src/components/VOverlay/index.ts"], "sourcesContent": ["import VOverlay from './VOverlay'\n\nexport { VOverlay }\n\nexport default VOverlay\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,YAArB;AAEA,SAASA,QAAT;AAEA,eAAeA,QAAf", "ignoreList": []}]}