{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/nl.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/nl.js", "mtime": 1757335237375}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/nl.ts"], "sourcesContent": ["export default {\n  badge: 'insigne',\n  close: 'Sluiten',\n  dataIterator: {\n    noResultsText: '<PERSON><PERSON> overeenkomende resultaten gevonden',\n    loadingText: 'Items aan het laden...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Rijen per pagina:',\n    ariaLabel: {\n      sortDescending: 'Aflopend gesorteerd.',\n      sortAscending: 'Oplopend gesorteerd.',\n      sortNone: 'Niet gesorteerd.',\n      activateNone: 'Activeer om de sortering te verwijderen.',\n      activateDescending: 'Activeer om aflopend te sorteren.',\n      activateAscending: 'Activeer om oplopend te sorteren.',\n    },\n    sortBy: 'Sorteer volgens',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Aantal per pagina:',\n    itemsPerPageAll: 'Alles',\n    nextPage: 'Volgende pagina',\n    prevPage: 'Vorige pagina',\n    firstPage: 'Eerste pagina',\n    lastPage: 'Laatste pagina',\n    pageText: '{0}-{1} van {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} geselecteerd',\n    nextMonthAriaLabel: 'Volgende maand',\n    nextYearAriaLabel: 'Volgend jaar',\n    prevMonthAriaLabel: 'Vorige maand',\n    prevYearAriaLabel: 'Vorig jaar',\n  },\n  noDataText: 'Geen gegevens beschikbaar',\n  carousel: {\n    prev: 'Vorig beeld',\n    next: 'Volgend beeld',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} meer',\n  },\n  fileInput: {\n    counter: '{0} bestanden',\n    counterSize: '{0} bestanden ({1} in totaal)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Paginanavigatie',\n      next: 'Volgende pagina',\n      previous: 'Vorige pagina',\n      page: 'Ga naar pagina {0}',\n      currentPage: 'Huidige pagina, pagina {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,SADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,yCADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,mBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,sBADP;MAETC,aAAa,EAAE,sBAFN;MAGTC,QAAQ,EAAE,kBAHD;MAITC,YAAY,EAAE,0CAJL;MAKTC,kBAAkB,EAAE,mCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,oBADR;IAEVU,eAAe,EAAE,OAFP;IAGVC,QAAQ,EAAE,iBAHA;IAIVC,QAAQ,EAAE,eAJA;IAKVC,SAAS,EAAE,eALD;IAMVC,QAAQ,EAAE,gBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,kBADL;IAEVC,kBAAkB,EAAE,gBAFV;IAGVC,iBAAiB,EAAE,cAHT;IAIVC,kBAAkB,EAAE,cAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,2BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,aADE;IAERC,IAAI,EAAE,eAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,eADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,iBADA;MAETX,IAAI,EAAE,iBAFG;MAGTY,QAAQ,EAAE,eAHD;MAITC,IAAI,EAAE,oBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}