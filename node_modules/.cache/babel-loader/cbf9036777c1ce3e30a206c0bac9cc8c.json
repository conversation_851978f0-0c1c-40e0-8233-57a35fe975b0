{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/he.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/he.js", "mtime": 1757335235852}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/he.ts"], "sourcesContent": ["export default {\n  badge: 'תג',\n  close: 'סגור',\n  dataIterator: {\n    noResultsText: 'לא נמצאו תוצאות מתאימות',\n    loadingText: 'טוען פריט...',\n  },\n  dataTable: {\n    itemsPerPageText: 'שורות לעמוד:',\n    ariaLabel: {\n      sortDescending: 'ממוין לפי סדר עולה. לחץ להספקת המיון.',\n      sortAscending: 'ממוין לפי סדר יורד. לחץ למיון לפי סדר עולה.',\n      sortNone: 'לא ממוין. לחץ למיון לפי סדר עולה.',\n      activateNone: 'הפעל להסרת המיון.',\n      activateDescending: 'הפעל למיון יורד.',\n      activateAscending: 'הפעל למיון עולה.',\n    },\n    sortBy: 'סדר לפי',\n  },\n  dataFooter: {\n    itemsPerPageText: 'פריטים לדף:',\n    itemsPerPageAll: 'הכל',\n    nextPage: 'עמוד הבא',\n    prevPage: 'עמוד הקודם',\n    firstPage: 'עמוד ראשון',\n    lastPage: 'עמוד אחרון',\n    pageText: '{0}-{1} מתוך {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} נבחרו',\n    nextMonthAriaLabel: 'חודש הבא',\n    nextYearAriaLabel: 'שנה הבאה',\n    prevMonthAriaLabel: 'חודש שעבר',\n    prevYearAriaLabel: 'שנה שעברה',\n  },\n  noDataText: 'אין נתונים זמינים',\n  carousel: {\n    prev: 'מצג קודם',\n    next: 'מצג הבא',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} נוספים',\n  },\n  fileInput: {\n    counter: '{0} קבצים',\n    counterSize: '{0} קבצים ({1} בסך הכל)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'ניווט עימוד',\n      next: 'עמוד הבא',\n      previous: 'עמוד הקודם',\n      page: '{0} לך לעמוד',\n      currentPage: '{0} עמוד נוכחי, עמוד',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,IADM;EAEbC,KAAK,EAAE,MAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,yBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,cADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,uCADP;MAETC,aAAa,EAAE,6CAFN;MAGTC,QAAQ,EAAE,mCAHD;MAITC,YAAY,EAAE,mBAJL;MAKTC,kBAAkB,EAAE,kBALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,aADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,UAHA;IAIVC,QAAQ,EAAE,YAJA;IAKVC,SAAS,EAAE,YALD;IAMVC,QAAQ,EAAE,YANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,WADL;IAEVC,kBAAkB,EAAE,UAFV;IAGVC,iBAAiB,EAAE,UAHT;IAIVC,kBAAkB,EAAE,WAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,mBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,UADE;IAERC,IAAI,EAAE,SAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,aADA;MAETX,IAAI,EAAE,UAFG;MAGTY,QAAQ,EAAE,YAHD;MAITC,IAAI,EAAE,cAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}