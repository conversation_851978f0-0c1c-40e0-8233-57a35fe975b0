{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/color/transformSRGB.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/util/color/transformSRGB.js", "mtime": 1757335237575}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["clamp", "srgbForwardMatrix", "srgbForwardTransform", "C", "Math", "pow", "srgbReverseMatrix", "srgbReverseTransform", "fromXYZ", "xyz", "rgb", "Array", "transform", "matrix", "i", "round", "toXYZ", "r", "g", "b"], "sources": ["../../../src/util/color/transformSRGB.ts"], "sourcesContent": ["import { ColorInt, XYZ } from '../colorUtils'\nimport { clamp } from '../../util/helpers'\n\n// For converting XYZ to sRGB\nconst srgbForwardMatrix = [\n  [3.2406, -1.5372, -0.4986],\n  [-0.9689, 1.8758, 0.0415],\n  [0.0557, -0.2040, 1.0570],\n]\n\n// Forward gamma adjust\nconst srgbForwardTransform = (C: number): number => (\n  C <= 0.0031308\n    ? C * 12.92\n    : 1.055 * C ** (1 / 2.4) - 0.055\n)\n\n// For converting sRGB to XYZ\nconst srgbReverseMatrix = [\n  [0.4124, 0.3576, 0.1805],\n  [0.2126, 0.7152, 0.0722],\n  [0.0193, 0.1192, 0.9505],\n]\n\n// Reverse gamma adjust\nconst srgbReverseTransform = (C: number): number => (\n  C <= 0.04045\n    ? C / 12.92\n    : ((C + 0.055) / 1.055) ** 2.4\n)\n\nexport function fromXYZ (xyz: XYZ): ColorInt {\n  const rgb = Array(3)\n  const transform = srgbForwardTransform\n  const matrix = srgbForwardMatrix\n\n  // Matrix transform, then gamma adjustment\n  for (let i = 0; i < 3; ++i) {\n    rgb[i] = Math.round(clamp(transform(\n      matrix[i][0] * xyz[0] +\n      matrix[i][1] * xyz[1] +\n      matrix[i][2] * xyz[2]\n    )) * 255)\n  }\n\n  // Rescale back to [0, 255]\n  return (rgb[0] << 16) + (rgb[1] << 8) + (rgb[2] << 0)\n}\n\nexport function toXYZ (rgb: ColorInt): XYZ {\n  const xyz: XYZ = [0, 0, 0]\n  const transform = srgbReverseTransform\n  const matrix = srgbReverseMatrix\n\n  // Rescale from [0, 255] to [0, 1] then adjust sRGB gamma to linear RGB\n  const r = transform((rgb >> 16 & 0xff) / 255)\n  const g = transform((rgb >> 8 & 0xff) / 255)\n  const b = transform((rgb >> 0 & 0xff) / 255)\n\n  // Matrix color space transform\n  for (let i = 0; i < 3; ++i) {\n    xyz[i] = matrix[i][0] * r + matrix[i][1] * g + matrix[i][2] * b\n  }\n\n  return xyz\n}\n"], "mappings": "AACA,SAASA,KAAT,QAAsB,oBAAtB,C,CAEA;;AACA,IAAMC,iBAAiB,GAAG,CACxB,CAAC,MAAD,EAAS,CAAC,MAAV,EAAkB,CAAC,MAAnB,CADwB,EAExB,CAAC,CAAC,MAAF,EAAU,MAAV,EAAkB,MAAlB,CAFwB,EAGxB,CAAC,MAAD,EAAS,CAAC,MAAV,EAAkB,MAAlB,CAHwB,CAA1B,C,CAMA;;AACA,IAAMC,oBAAoB,GAAI,SAAxBA,oBAAoBA,CAAIC,CAAD;EAAA,OAC3BA,CAAC,IAAI,SAAL,GACIA,CAAC,GAAG,KADR,GAEI,QAAAC,IAAA,CAAAC,GAAA,CAAQF,CAAC,EAAK,IAAI,GAAT,CAAT,GAAyB,KAH/B;AAAA,E,CAMA;;AACA,IAAMG,iBAAiB,GAAG,CACxB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CADwB,EAExB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAFwB,EAGxB,CAAC,MAAD,EAAS,MAAT,EAAiB,MAAjB,CAHwB,CAA1B,C,CAMA;;AACA,IAAMC,oBAAoB,GAAI,SAAxBA,oBAAoBA,CAAIJ,CAAD;EAAA,OAC3BA,CAAC,IAAI,OAAL,GACIA,CAAC,GAAG,KADR,GAAAC,IAAA,CAAAC,GAAA,CAEK,CAACF,CAAC,GAAG,KAAL,IAAc,KAAf,EAAyB,GAH/B;AAAA;AAMA,OAAM,SAAUK,OAAVA,CAAmBC,GAAnB,EAA2B;EAC/B,IAAMC,GAAG,GAAGC,KAAK,CAAC,CAAD,CAAjB;EACA,IAAMC,SAAS,GAAGV,oBAAlB;EACA,IAAMW,MAAM,GAAGZ,iBAAf,CAH+B,CAK/B;;EACA,KAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuB,EAAEA,CAAzB,EAA4B;IAC1BJ,GAAG,CAACI,CAAD,CAAH,GAASV,IAAI,CAACW,KAAL,CAAWf,KAAK,CAACY,SAAS,CACjCC,MAAM,CAACC,CAAD,CAAN,CAAU,CAAV,IAAeL,GAAG,CAAC,CAAD,CAAlB,GACAI,MAAM,CAACC,CAAD,CAAN,CAAU,CAAV,IAAeL,GAAG,CAAC,CAAD,CADlB,GAEAI,MAAM,CAACC,CAAD,CAAN,CAAU,CAAV,IAAeL,GAAG,CAAC,CAAD,CAHe,CAAV,CAAL,GAIf,GAJI,CAAT;EAKD,CAZ8B,CAc/B;;EACA,OAAO,CAACC,GAAG,CAAC,CAAD,CAAH,IAAU,EAAX,KAAkBA,GAAG,CAAC,CAAD,CAAH,IAAU,CAA5B,KAAkCA,GAAG,CAAC,CAAD,CAAH,IAAU,CAA5C,CAAP;AACD;AAED,OAAM,SAAUM,KAAVA,CAAiBN,GAAjB,EAA8B;EAClC,IAAMD,GAAG,GAAQ,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAAjB;EACA,IAAMG,SAAS,GAAGL,oBAAlB;EACA,IAAMM,MAAM,GAAGP,iBAAf,CAHkC,CAKlC;;EACA,IAAMW,CAAC,GAAGL,SAAS,CAAC,CAACF,GAAG,IAAI,EAAP,GAAY,IAAb,IAAqB,GAAtB,CAAnB;EACA,IAAMQ,CAAC,GAAGN,SAAS,CAAC,CAACF,GAAG,IAAI,CAAP,GAAW,IAAZ,IAAoB,GAArB,CAAnB;EACA,IAAMS,CAAC,GAAGP,SAAS,CAAC,CAACF,GAAG,IAAI,CAAP,GAAW,IAAZ,IAAoB,GAArB,CAAnB,CARkC,CAUlC;;EACA,KAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuB,EAAEA,CAAzB,EAA4B;IAC1BL,GAAG,CAACK,CAAD,CAAH,GAASD,MAAM,CAACC,CAAD,CAAN,CAAU,CAAV,IAAeG,CAAf,GAAmBJ,MAAM,CAACC,CAAD,CAAN,CAAU,CAAV,IAAeI,CAAlC,GAAsCL,MAAM,CAACC,CAAD,CAAN,CAAU,CAAV,IAAeK,CAA9D;EACD;EAED,OAAOV,GAAP;AACD", "ignoreList": []}]}