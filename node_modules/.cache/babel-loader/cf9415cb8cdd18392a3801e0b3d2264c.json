{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/bg.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/bg.js", "mtime": 1757335234948}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/bg.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  close: 'Затвори',\n  dataIterator: {\n    noResultsText: 'Не са намерени записи',\n    loadingText: 'Зареждане на елементи...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Редове на страница:',\n    ariaLabel: {\n      sortDescending: 'Подреди в намаляващ ред.',\n      sortAscending: 'Подреди в нарастващ ред.',\n      sortNone: 'Без подредба.',\n      activateNone: 'Активирай за премахване на подредбата.',\n      activateDescending: 'Активирай за подредба в намаляващ ред.',\n      activateAscending: 'Активирай за подредба в нарастващ ред.',\n    },\n    sortBy: 'Сорти<PERSON><PERSON>й по',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Елементи на страница:',\n    itemsPerPageAll: 'Всички',\n    nextPage: 'Следваща страница',\n    prevPage: 'Предишна страница',\n    firstPage: 'Първа страница',\n    lastPage: 'Последна страница',\n    pageText: '{0}-{1} от {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} избрани',\n    nextMonthAriaLabel: 'Следващ месец',\n    nextYearAriaLabel: 'Следващата година',\n    prevMonthAriaLabel: 'Предишен месец',\n    prevYearAriaLabel: 'Предишна година',\n  },\n  noDataText: 'Няма налични данни',\n  carousel: {\n    prev: 'Предишна визуализация',\n    next: 'Следваща визуализация',\n    ariaLabel: {\n      delimiter: 'Кадър {0} от {1} на въртележката',\n    },\n  },\n  calendar: {\n    moreEvents: 'Още {0}',\n  },\n  fileInput: {\n    counter: '{0} файла',\n    counterSize: '{0} файла ({1} общо)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Странициране',\n      next: 'Следваща страница',\n      previous: 'Предишна страница',\n      page: 'Отиди на страница {0}',\n      currentPage: 'Текуща страница, Страница {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,SAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,uBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,qBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,0BADP;MAETC,aAAa,EAAE,0BAFN;MAGTC,QAAQ,EAAE,eAHD;MAITC,YAAY,EAAE,wCAJL;MAKTC,kBAAkB,EAAE,wCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,uBADR;IAEVU,eAAe,EAAE,QAFP;IAGVC,QAAQ,EAAE,mBAHA;IAIVC,QAAQ,EAAE,mBAJA;IAKVC,SAAS,EAAE,gBALD;IAMVC,QAAQ,EAAE,mBANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,aADL;IAEVC,kBAAkB,EAAE,eAFV;IAGVC,iBAAiB,EAAE,mBAHT;IAIVC,kBAAkB,EAAE,gBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,oBAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,uBADE;IAERC,IAAI,EAAE,uBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,WADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,cADA;MAETX,IAAI,EAAE,mBAFG;MAGTY,QAAQ,EAAE,mBAHD;MAITC,IAAI,EAAE,uBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}