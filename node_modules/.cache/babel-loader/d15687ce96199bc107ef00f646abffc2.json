{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VResponsive/VResponsive.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VResponsive/VResponsive.js", "mtime": 1757335238850}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0ICIuLi8uLi8uLi9zcmMvY29tcG9uZW50cy9WUmVzcG9uc2l2ZS9WUmVzcG9uc2l2ZS5zYXNzIjsgLy8gTWl4aW5zCgppbXBvcnQgTWVhc3VyYWJsZSBmcm9tICcuLi8uLi9taXhpbnMvbWVhc3VyYWJsZSc7IC8vIFV0aWxzCgppbXBvcnQgbWl4aW5zIGZyb20gJy4uLy4uL3V0aWwvbWl4aW5zJzsKaW1wb3J0IHsgZ2V0U2xvdCB9IGZyb20gJy4uLy4uL3V0aWwvaGVscGVycyc7Ci8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBtaXhpbnMoTWVhc3VyYWJsZSkuZXh0ZW5kKHsKICBuYW1lOiAndi1yZXNwb25zaXZlJywKICBwcm9wczogewogICAgYXNwZWN0UmF0aW86IFtTdHJpbmcsIE51bWJlcl0sCiAgICBjb250ZW50Q2xhc3M6IFN0cmluZwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNvbXB1dGVkQXNwZWN0UmF0aW86IGZ1bmN0aW9uIGNvbXB1dGVkQXNwZWN0UmF0aW8oKSB7CiAgICAgIHJldHVybiBOdW1iZXIodGhpcy5hc3BlY3RSYXRpbyk7CiAgICB9LAogICAgYXNwZWN0U3R5bGU6IGZ1bmN0aW9uIGFzcGVjdFN0eWxlKCkgewogICAgICByZXR1cm4gdGhpcy5jb21wdXRlZEFzcGVjdFJhdGlvID8gewogICAgICAgIHBhZGRpbmdCb3R0b206IDEgLyB0aGlzLmNvbXB1dGVkQXNwZWN0UmF0aW8gKiAxMDAgKyAnJScKICAgICAgfSA6IHVuZGVmaW5lZDsKICAgIH0sCiAgICBfX2NhY2hlZFNpemVyOiBmdW5jdGlvbiBfX2NhY2hlZFNpemVyKCkgewogICAgICBpZiAoIXRoaXMuYXNwZWN0U3R5bGUpIHJldHVybiBbXTsKICAgICAgcmV0dXJuIHRoaXMuJGNyZWF0ZUVsZW1lbnQoJ2RpdicsIHsKICAgICAgICBzdHlsZTogdGhpcy5hc3BlY3RTdHlsZSwKICAgICAgICBzdGF0aWNDbGFzczogJ3YtcmVzcG9uc2l2ZV9fc2l6ZXInCiAgICAgIH0pOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2VuQ29udGVudDogZnVuY3Rpb24gZ2VuQ29udGVudCgpIHsKICAgICAgcmV0dXJuIHRoaXMuJGNyZWF0ZUVsZW1lbnQoJ2RpdicsIHsKICAgICAgICBzdGF0aWNDbGFzczogJ3YtcmVzcG9uc2l2ZV9fY29udGVudCcsCiAgICAgICAgImNsYXNzIjogdGhpcy5jb250ZW50Q2xhc3MKICAgICAgfSwgZ2V0U2xvdCh0aGlzKSk7CiAgICB9CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICByZXR1cm4gaCgnZGl2JywgewogICAgICBzdGF0aWNDbGFzczogJ3YtcmVzcG9uc2l2ZScsCiAgICAgIHN0eWxlOiB0aGlzLm1lYXN1cmFibGVTdHlsZXMsCiAgICAgIG9uOiB0aGlzLiRsaXN0ZW5lcnMKICAgIH0sIFt0aGlzLl9fY2FjaGVkU2l6ZXIsIHRoaXMuZ2VuQ29udGVudCgpXSk7CiAgfQp9KTs="}, {"version": 3, "names": ["Measurable", "mixins", "getSlot", "extend", "name", "props", "aspectRatio", "String", "Number", "contentClass", "computed", "computedAspectRatio", "aspectStyle", "paddingBottom", "undefined", "__cachedSizer", "$createElement", "style", "staticClass", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "render", "h", "measurableStyles", "on", "$listeners"], "sources": ["../../../src/components/VResponsive/VResponsive.ts"], "sourcesContent": ["import './VResponsive.sass'\n\n// Mixins\nimport Measurable, { NumberOrNumberString } from '../../mixins/measurable'\n\n// Types\nimport { VNode } from 'vue'\n\n// Utils\nimport mixins from '../../util/mixins'\nimport { getSlot } from '../../util/helpers'\n\n/* @vue/component */\nexport default mixins(Measurable).extend({\n  name: 'v-responsive',\n\n  props: {\n    aspectRatio: [String, Number] as NumberOrNumberString,\n    contentClass: String,\n  },\n\n  computed: {\n    computedAspectRatio (): number {\n      return Number(this.aspectRatio)\n    },\n    aspectStyle (): object | undefined {\n      return this.computedAspectRatio\n        ? { paddingBottom: (1 / this.computedAspectRatio) * 100 + '%' }\n        : undefined\n    },\n    __cachedSizer (): VNode | [] {\n      if (!this.aspectStyle) return []\n\n      return this.$createElement('div', {\n        style: this.aspectStyle,\n        staticClass: 'v-responsive__sizer',\n      })\n    },\n  },\n\n  methods: {\n    genContent (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-responsive__content',\n        class: this.contentClass,\n      }, getSlot(this))\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-responsive',\n      style: this.measurableStyles,\n      on: this.$listeners,\n    }, [\n      this.__cachedSizer,\n      this.genContent(),\n    ])\n  },\n})\n"], "mappings": ";AAAA,OAAO,sDAAP,C,CAEA;;AACA,OAAOA,UAAP,MAAiD,yBAAjD,C,CAKA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,OAAT,QAAwB,oBAAxB;AAEA;;AACA,eAAeD,MAAM,CAACD,UAAD,CAAN,CAAmBG,MAAnB,CAA0B;EACvCC,IAAI,EAAE,cADiC;EAGvCC,KAAK,EAAE;IACLC,WAAW,EAAE,CAACC,MAAD,EAASC,MAAT,CADR;IAELC,YAAY,EAAEF;EAFT,CAHgC;EAQvCG,QAAQ,EAAE;IACRC,mBAAmB,WAAnBA,mBAAmBA,CAAA;MACjB,OAAOH,MAAM,CAAC,KAAKF,WAAN,CAAb;IACD,CAHO;IAIRM,WAAW,WAAXA,WAAWA,CAAA;MACT,OAAO,KAAKD,mBAAL,GACH;QAAEE,aAAa,EAAG,IAAI,KAAKF,mBAAV,GAAiC,GAAjC,GAAuC;MAAxD,CADG,GAEHG,SAFJ;IAGD,CARO;IASRC,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAI,CAAC,KAAKH,WAAV,EAAuB,OAAO,EAAP;MAEvB,OAAO,KAAKI,cAAL,CAAoB,KAApB,EAA2B;QAChCC,KAAK,EAAE,KAAKL,WADoB;QAEhCM,WAAW,EAAE;MAFmB,CAA3B,CAAP;IAID;EAhBO,CAR6B;EA2BvCC,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKJ,cAAL,CAAoB,KAApB,EAA2B;QAChCE,WAAW,EAAE,uBADmB;QAEhC,SAAO,KAAKT;MAFoB,CAA3B,EAGJP,OAAO,CAAC,IAAD,CAHH,CAAP;IAID;EANM,CA3B8B;EAoCvCmB,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ;MACdJ,WAAW,EAAE,cADC;MAEdD,KAAK,EAAE,KAAKM,gBAFE;MAGdC,EAAE,EAAE,KAAKC;IAHK,CAAR,EAIL,CACD,KAAKV,aADJ,EAED,KAAKK,UAAL,EAFC,CAJK,CAAR;EAQD;AA7CsC,CAA1B,CAAf", "ignoreList": []}]}