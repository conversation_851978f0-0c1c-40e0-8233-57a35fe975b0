{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCounter/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VCounter/index.js", "mtime": 1757335236831}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZDb3VudGVyIGZyb20gJy4vVkNvdW50ZXInOwpleHBvcnQgeyBWQ291bnRlciB9OwpleHBvcnQgZGVmYXVsdCBWQ291bnRlcjs="}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>"], "sources": ["../../../src/components/VCounter/index.ts"], "sourcesContent": ["import VCounter from './VCounter'\n\nexport { VCounter }\nexport default VCounter\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,YAArB;AAEA,SAASA,QAAT;AACA,eAAeA,QAAf", "ignoreList": []}]}