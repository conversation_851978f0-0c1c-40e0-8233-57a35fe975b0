{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithoutHoles.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithoutHoles.js", "mtime": 1757335237112}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9BcnJheSRpc0FycmF5IGZyb20gImNvcmUtanMtcHVyZS9mZWF0dXJlcy9hcnJheS9pcy1hcnJheS5qcyI7CmltcG9ydCBhcnJheUxpa2VUb0FycmF5IGZyb20gIi4vYXJyYXlMaWtlVG9BcnJheS5qcyI7CmZ1bmN0aW9uIF9hcnJheVdpdGhvdXRIb2xlcyhyKSB7CiAgaWYgKF9BcnJheSRpc0FycmF5KHIpKSByZXR1cm4gYXJyYXlMaWtlVG9BcnJheShyKTsKfQpleHBvcnQgeyBfYXJyYXlXaXRob3V0SG9sZXMgYXMgZGVmYXVsdCB9Ow=="}, {"version": 3, "names": ["_Array$isArray", "arrayLikeToArray", "_arrayWithoutHoles", "r", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithoutHoles.js"], "sourcesContent": ["import _Array$isArray from \"core-js-pure/features/array/is-array.js\";\nimport arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (_Array$isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };"], "mappings": "AAAA,OAAOA,cAAc,MAAM,yCAAyC;AACpE,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,kBAAkBA,CAACC,CAAC,EAAE;EAC7B,IAAIH,cAAc,CAACG,CAAC,CAAC,EAAE,OAAOF,gBAAgB,CAACE,CAAC,CAAC;AACnD;AACA,SAASD,kBAAkB,IAAIE,OAAO", "ignoreList": []}]}