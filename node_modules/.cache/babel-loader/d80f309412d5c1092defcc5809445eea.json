{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/VColorPickerPreview.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VColorPicker/VColorPickerPreview.js", "mtime": 1757335237748}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VSlider", "RGBtoCSS", "RGBAtoCSS", "<PERSON><PERSON>", "fromHSVA", "extend", "name", "props", "color", "Object", "disabled", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "methods", "genAl<PERSON>", "_context", "_this", "genTrack", "staticClass", "thumbColor", "hideDetails", "value", "alpha", "step", "min", "max", "style", "backgroundImage", "undefined", "_concatInstanceProperty", "concat", "$vuetify", "rtl", "call", "rgba", "on", "input", "val", "$emit", "_objectSpread", "hsva", "a", "genSliders", "$createElement", "genHue", "genDot", "background", "_this2", "hue", "h", "options", "render"], "sources": ["../../../src/components/VColorPicker/VColorPickerPreview.ts"], "sourcesContent": ["// Styles\nimport './VColorPickerPreview.sass'\n\n// Components\nimport VSlider from '../VSlider/VSlider'\n\n// Utilities\nimport { RGBtoCSS, RGBAtoCSS } from '../../util/colorUtils'\n\n// Types\nimport Vue, { VNode, VNodeData, PropType } from 'vue'\nimport { VColorPickerColor, fromHSVA } from './util'\n\nexport default Vue.extend({\n  name: 'v-color-picker-preview',\n\n  props: {\n    color: Object as PropType<VColorPickerColor>,\n    disabled: Boolean,\n    hideAlpha: Boolean,\n  },\n\n  methods: {\n    genAlpha (): VNode {\n      return this.genTrack({\n        staticClass: 'v-color-picker__alpha',\n        props: {\n          thumbColor: 'grey lighten-2',\n          hideDetails: true,\n          value: this.color.alpha,\n          step: 0,\n          min: 0,\n          max: 1,\n        },\n        style: {\n          backgroundImage: this.disabled\n            ? undefined\n            : `linear-gradient(to ${this.$vuetify.rtl ? 'left' : 'right'}, transparent, ${RGBtoCSS(this.color.rgba)})`,\n        },\n        on: {\n          input: (val: number) => this.color.alpha !== val && this.$emit('update:color', fromHSVA({ ...this.color.hsva, a: val })),\n        },\n      })\n    },\n    genSliders (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-color-picker__sliders',\n      }, [\n        this.genHue(),\n        !this.hideAlpha && this.genAlpha(),\n      ])\n    },\n    genDot (): VNode {\n      return this.$createElement('div', {\n        staticClass: 'v-color-picker__dot',\n      }, [\n        this.$createElement('div', {\n          style: {\n            background: RGBAtoCSS(this.color.rgba),\n          },\n        }),\n      ])\n    },\n    genHue (): VNode {\n      return this.genTrack({\n        staticClass: 'v-color-picker__hue',\n        props: {\n          thumbColor: 'grey lighten-2',\n          hideDetails: true,\n          value: this.color.hue,\n          step: 0,\n          min: 0,\n          max: 360,\n        },\n        on: {\n          input: (val: number) => this.color.hue !== val && this.$emit('update:color', fromHSVA({ ...this.color.hsva, h: val })),\n        },\n      })\n    },\n    genTrack (options: VNodeData): VNode {\n      return this.$createElement(VSlider, {\n        class: 'v-color-picker__track',\n        ...options,\n        props: {\n          disabled: this.disabled,\n          ...options.props,\n        },\n      })\n    },\n  },\n\n  render (h): VNode {\n    return h('div', {\n      staticClass: 'v-color-picker__preview',\n      class: {\n        'v-color-picker__preview--hide-alpha': this.hideAlpha,\n      },\n    }, [\n      this.genDot(),\n      this.genSliders(),\n    ])\n  },\n})\n"], "mappings": ";;AAAA;AACA,OAAO,+DAAP,C,CAEA;;AACA,OAAOA,OAAP,MAAoB,oBAApB,C,CAEA;;AACA,SAASC,QAAT,EAAmBC,SAAnB,QAAoC,uBAApC,C,CAEA;;AACA,OAAOC,GAAP,MAAgD,KAAhD;AACA,SAA4BC,QAA5B,QAA4C,QAA5C;AAEA,eAAeD,GAAG,CAACE,MAAJ,CAAW;EACxBC,IAAI,EAAE,wBADkB;EAGxBC,KAAK,EAAE;IACLC,KAAK,EAAEC,MADF;IAELC,QAAQ,EAAEC,OAFL;IAGLC,SAAS,EAAED;EAHN,CAHiB;EASxBE,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAA;MAAA,IAAAC,QAAA;QAAAC,KAAA;MACN,OAAO,KAAKC,QAAL,CAAc;QACnBC,WAAW,EAAE,uBADM;QAEnBX,KAAK,EAAE;UACLY,UAAU,EAAE,gBADP;UAELC,WAAW,EAAE,IAFR;UAGLC,KAAK,EAAE,KAAKb,KAAL,CAAWc,KAHb;UAILC,IAAI,EAAE,CAJD;UAKLC,GAAG,EAAE,CALA;UAMLC,GAAG,EAAE;QANA,CAFY;QAUnBC,KAAK,EAAE;UACLC,eAAe,EAAE,KAAKjB,QAAL,GACbkB,SADa,GAAAC,uBAAA,CAAAd,QAAA,yBAAAe,MAAA,CAES,KAAKC,QAAL,CAAcC,GAAd,GAAoB,MAApB,GAA6B,OAAO,sBAAAC,IAAA,CAAAlB,QAAA,EAAkBd,QAAQ,CAAC,KAAKO,KAAL,CAAW0B,IAAZ,CAAiB;QAHpG,CAVY;QAenBC,EAAE,EAAE;UACFC,KAAK,EAAG,SAARA,KAAKA,CAAGC,GAAD;YAAA,OAAiBrB,KAAA,CAAKR,KAAL,CAAWc,KAAX,KAAqBe,GAArB,IAA4BrB,KAAA,CAAKsB,KAAL,CAAW,cAAX,EAA2BlC,QAAQ,CAAAmC,aAAA,CAAAA,aAAA,KAAMvB,KAAA,CAAKR,KAAL,CAAWgC,IAAhB;cAAsBC,CAAC,EAAEJ;YAAA,EAA1B,CAAnC;UAAA;QADlD;MAfe,CAAd,CAAP;IAmBD,CArBM;IAsBPK,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QAChCzB,WAAW,EAAE;MADmB,CAA3B,EAEJ,CACD,KAAK0B,MAAL,EADC,EAED,CAAC,KAAKhC,SAAN,IAAmB,KAAKE,QAAL,EAFlB,CAFI,CAAP;IAMD,CA7BM;IA8BP+B,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO,KAAKF,cAAL,CAAoB,KAApB,EAA2B;QAChCzB,WAAW,EAAE;MADmB,CAA3B,EAEJ,CACD,KAAKyB,cAAL,CAAoB,KAApB,EAA2B;QACzBjB,KAAK,EAAE;UACLoB,UAAU,EAAE5C,SAAS,CAAC,KAAKM,KAAL,CAAW0B,IAAZ;QADhB;MADkB,CAA3B,CADC,CAFI,CAAP;IASD,CAxCM;IAyCPU,MAAM,WAANA,MAAMA,CAAA;MAAA,IAAAG,MAAA;MACJ,OAAO,KAAK9B,QAAL,CAAc;QACnBC,WAAW,EAAE,qBADM;QAEnBX,KAAK,EAAE;UACLY,UAAU,EAAE,gBADP;UAELC,WAAW,EAAE,IAFR;UAGLC,KAAK,EAAE,KAAKb,KAAL,CAAWwC,GAHb;UAILzB,IAAI,EAAE,CAJD;UAKLC,GAAG,EAAE,CALA;UAMLC,GAAG,EAAE;QANA,CAFY;QAUnBU,EAAE,EAAE;UACFC,KAAK,EAAG,SAARA,KAAKA,CAAGC,GAAD;YAAA,OAAiBU,MAAA,CAAKvC,KAAL,CAAWwC,GAAX,KAAmBX,GAAnB,IAA0BU,MAAA,CAAKT,KAAL,CAAW,cAAX,EAA2BlC,QAAQ,CAAAmC,aAAA,CAAAA,aAAA,KAAMQ,MAAA,CAAKvC,KAAL,CAAWgC,IAAhB;cAAsBS,CAAC,EAAEZ;YAAA,EAA1B,CAAnC;UAAA;QADhD;MAVe,CAAd,CAAP;IAcD,CAxDM;IAyDPpB,QAAQ,WAARA,QAAQA,CAAEiC,OAAF,EAAoB;MAC1B,OAAO,KAAKP,cAAL,CAAoB3C,OAApB,EAAAuC,aAAA,CAAAA,aAAA;QACL,SAAO;MAD2B,GAE/BW,OAF+B;QAGlC3C,KAAK,EAAAgC,aAAA;UACH7B,QAAQ,EAAE,KAAKA;QADV,GAEFwC,OAAO,CAAC3C,KAAA;MAFN,EAHF,CAAP;IAQD;EAlEM,CATe;EA8ExB4C,MAAM,WAANA,MAAMA,CAAEF,CAAF,EAAG;IACP,OAAOA,CAAC,CAAC,KAAD,EAAQ;MACd/B,WAAW,EAAE,yBADC;MAEd,SAAO;QACL,uCAAuC,KAAKN;MADvC;IAFO,CAAR,EAKL,CACD,KAAKiC,MAAL,EADC,EAED,KAAKH,UAAL,EAFC,CALK,CAAR;EASD;AAxFuB,CAAX,CAAf", "ignoreList": []}]}