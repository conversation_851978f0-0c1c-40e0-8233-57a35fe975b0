{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/binds-attrs/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/binds-attrs/index.js", "mtime": 1757335237049}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwovKioKICogVGhpcyBtaXhpbiBwcm92aWRlcyBgYXR0cnMkYCBhbmQgYGxpc3RlbmVycyRgIHRvIHdvcmsgYXJvdW5kCiAqIHZ1ZSBidWcgaHR0cHM6Ly9naXRodWIuY29tL3Z1ZWpzL3Z1ZS9pc3N1ZXMvMTAxMTUKICovCgpmdW5jdGlvbiBtYWtlV2F0Y2hlcihwcm9wZXJ0eSkgewogIHJldHVybiBmdW5jdGlvbiAodmFsLCBvbGRWYWwpIHsKICAgIGZvciAodmFyIGF0dHIgaW4gb2xkVmFsKSB7CiAgICAgIGlmICghT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHZhbCwgYXR0cikpIHsKICAgICAgICB0aGlzLiRkZWxldGUodGhpcy4kZGF0YVtwcm9wZXJ0eV0sIGF0dHIpOwogICAgICB9CiAgICB9CiAgICBmb3IgKHZhciBfYXR0ciBpbiB2YWwpIHsKICAgICAgdGhpcy4kc2V0KHRoaXMuJGRhdGFbcHJvcGVydHldLCBfYXR0ciwgdmFsW19hdHRyXSk7CiAgICB9CiAgfTsKfQpleHBvcnQgZGVmYXVsdCBWdWUuZXh0ZW5kKHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYXR0cnMkOiB7fSwKICAgICAgbGlzdGVuZXJzJDoge30KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgLy8gV29yayBhcm91bmQgdW53YW50ZWQgcmUtcmVuZGVyczogaHR0cHM6Ly9naXRodWIuY29tL3Z1ZWpzL3Z1ZS9pc3N1ZXMvMTAxMTUKICAgIC8vIE1ha2Ugc3VyZSB0byB1c2UgYGF0dHJzJGAgaW5zdGVhZCBvZiBgJGF0dHJzYCAoY29uZnVzaW5nIHJpZ2h0PykKICAgIHRoaXMuJHdhdGNoKCckYXR0cnMnLCBtYWtlV2F0Y2hlcignYXR0cnMkJyksIHsKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9KTsKICAgIHRoaXMuJHdhdGNoKCckbGlzdGVuZXJzJywgbWFrZVdhdGNoZXIoJ2xpc3RlbmVycyQnKSwgewogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0pOwogIH0KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "makeWatcher", "property", "val", "oldVal", "attr", "Object", "prototype", "hasOwnProperty", "call", "$delete", "$data", "$set", "extend", "data", "attrs$", "listeners$", "created", "$watch", "immediate"], "sources": ["../../../src/mixins/binds-attrs/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\n/**\n * This mixin provides `attrs$` and `listeners$` to work around\n * vue bug https://github.com/vuejs/vue/issues/10115\n */\n\nfunction makeWatcher (property: string): ThisType<Vue> & ((val: any, oldVal: any) => void) {\n  return function (this: Vue, val, oldVal) {\n    for (const attr in oldVal) {\n      if (!Object.prototype.hasOwnProperty.call(val, attr)) {\n        this.$delete(this.$data[property], attr)\n      }\n    }\n    for (const attr in val) {\n      this.$set(this.$data[property], attr, val[attr])\n    }\n  }\n}\n\nexport default Vue.extend({\n  data: () => ({\n    attrs$: {} as Dictionary<string>,\n    listeners$: {} as Dictionary<Function | Function[]>,\n  }),\n\n  created () {\n    // Work around unwanted re-renders: https://github.com/vuejs/vue/issues/10115\n    // Make sure to use `attrs$` instead of `$attrs` (confusing right?)\n    this.$watch('$attrs', makeWatcher('attrs$'), { immediate: true })\n    this.$watch('$listeners', makeWatcher('listeners$'), { immediate: true })\n  },\n})\n"], "mappings": "AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA;;;AAGG;;AAEH,SAASC,WAATA,CAAsBC,QAAtB,EAAsC;EACpC,OAAO,UAAqBC,GAArB,EAA0BC,MAA1B,EAAgC;IACrC,KAAK,IAAMC,IAAX,IAAmBD,MAAnB,EAA2B;MACzB,IAAI,CAACE,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCN,GAArC,EAA0CE,IAA1C,CAAL,EAAsD;QACpD,KAAKK,OAAL,CAAa,KAAKC,KAAL,CAAWT,QAAX,CAAb,EAAmCG,IAAnC;MACD;IACF;IACD,KAAK,IAAMA,KAAX,IAAmBF,GAAnB,EAAwB;MACtB,KAAKS,IAAL,CAAU,KAAKD,KAAL,CAAWT,QAAX,CAAV,EAAgCG,KAAhC,EAAsCF,GAAG,CAACE,KAAD,CAAzC;IACD;EACF,CATD;AAUD;AAED,eAAeL,GAAG,CAACa,MAAJ,CAAW;EACxBC,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,MAAM,EAAE,EADG;MAEXC,UAAU,EAAE;IAFD,CAAP;EAAA,CADkB;EAMxBC,OAAO,WAAPA,OAAOA,CAAA;IACL;IACA;IACA,KAAKC,MAAL,CAAY,QAAZ,EAAsBjB,WAAW,CAAC,QAAD,CAAjC,EAA6C;MAAEkB,SAAS,EAAE;IAAb,CAA7C;IACA,KAAKD,MAAL,CAAY,YAAZ,EAA0BjB,WAAW,CAAC,YAAD,CAArC,EAAqD;MAAEkB,SAAS,EAAE;IAAb,CAArD;EACD;AAXuB,CAAX,CAAf", "ignoreList": []}]}