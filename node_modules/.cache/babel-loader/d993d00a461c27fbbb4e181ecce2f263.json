{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/loadable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/loadable/index.js", "mtime": 1757335237127}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVlByb2dyZXNzTGluZWFyIGZyb20gJy4uLy4uL2NvbXBvbmVudHMvVlByb2dyZXNzTGluZWFyJzsKLyoqCiAqIExvYWRhYmxlCiAqCiAqIEBtaXhpbgogKgogKiBVc2VkIHRvIGFkZCBsaW5lYXIgcHJvZ3Jlc3MgYmFyIHRvIGNvbXBvbmVudHMKICogQ2FuIHVzZSBhIGRlZmF1bHQgYmFyIHdpdGggYSBzcGVjaWZpYyBjb2xvcgogKiBvciBkZXNpZ25hdGUgYSBjdXN0b20gcHJvZ3Jlc3MgbGluZWFyIGJhcgogKi8KCi8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBWdWUuZXh0ZW5kKCkuZXh0ZW5kKHsKICBuYW1lOiAnbG9hZGFibGUnLAogIHByb3BzOiB7CiAgICBsb2FkaW5nOiB7CiAgICAgIHR5cGU6IFtCb29sZWFuLCBTdHJpbmddLAogICAgICAiZGVmYXVsdCI6IGZhbHNlCiAgICB9LAogICAgbG9hZGVySGVpZ2h0OiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICAgICJkZWZhdWx0IjogMgogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2VuUHJvZ3Jlc3M6IGZ1bmN0aW9uIGdlblByb2dyZXNzKCkgewogICAgICBpZiAodGhpcy5sb2FkaW5nID09PSBmYWxzZSkgcmV0dXJuIG51bGw7CiAgICAgIHJldHVybiB0aGlzLiRzbG90cy5wcm9ncmVzcyB8fCB0aGlzLiRjcmVhdGVFbGVtZW50KFZQcm9ncmVzc0xpbmVhciwgewogICAgICAgIHByb3BzOiB7CiAgICAgICAgICBhYnNvbHV0ZTogdHJ1ZSwKICAgICAgICAgIGNvbG9yOiB0aGlzLmxvYWRpbmcgPT09IHRydWUgfHwgdGhpcy5sb2FkaW5nID09PSAnJyA/IHRoaXMuY29sb3IgfHwgJ3ByaW1hcnknIDogdGhpcy5sb2FkaW5nLAogICAgICAgICAgaGVpZ2h0OiB0aGlzLmxvYWRlckhlaWdodCwKICAgICAgICAgIGluZGV0ZXJtaW5hdGU6IHRydWUKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "VProgressLinear", "extend", "name", "props", "loading", "type", "Boolean", "String", "loaderHeight", "Number", "methods", "genProgress", "$slots", "progress", "$createElement", "absolute", "color", "height", "indeterminate"], "sources": ["../../../src/mixins/loadable/index.ts"], "sourcesContent": ["import Vue, { VNode } from 'vue'\nimport VProgressLinear from '../../components/VProgressLinear'\n\ninterface colorable extends Vue {\n  color?: string\n}\n\n/**\n * Loadable\n *\n * @mixin\n *\n * Used to add linear progress bar to components\n * Can use a default bar with a specific color\n * or designate a custom progress linear bar\n */\n/* @vue/component */\nexport default Vue.extend<colorable>().extend({\n  name: 'loadable',\n\n  props: {\n    loading: {\n      type: [Boolean, String],\n      default: false,\n    },\n    loaderHeight: {\n      type: [Number, String],\n      default: 2,\n    },\n  },\n\n  methods: {\n    genProgress (): VNode | VNode[] | null {\n      if (this.loading === false) return null\n\n      return this.$slots.progress || this.$createElement(VProgressLinear, {\n        props: {\n          absolute: true,\n          color: (this.loading === true || this.loading === '')\n            ? (this.color || 'primary')\n            : this.loading,\n          height: this.loaderHeight,\n          indeterminate: true,\n        },\n      })\n    },\n  },\n})\n"], "mappings": ";AAAA,OAAOA,GAAP,MAA2B,KAA3B;AACA,OAAOC,eAAP,MAA4B,kCAA5B;AAMA;;;;;;;;AAQG;;AACH;;AACA,eAAeD,GAAG,CAACE,MAAJ,GAAwBA,MAAxB,CAA+B;EAC5CC,IAAI,EAAE,UADsC;EAG5CC,KAAK,EAAE;IACLC,OAAO,EAAE;MACPC,IAAI,EAAE,CAACC,OAAD,EAAUC,MAAV,CADC;MAEP,WAAS;IAFF,CADJ;IAKLC,YAAY,EAAE;MACZH,IAAI,EAAE,CAACI,MAAD,EAASF,MAAT,CADM;MAEZ,WAAS;IAFG;EALT,CAHqC;EAc5CG,OAAO,EAAE;IACPC,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAI,KAAKP,OAAL,KAAiB,KAArB,EAA4B,OAAO,IAAP;MAE5B,OAAO,KAAKQ,MAAL,CAAYC,QAAZ,IAAwB,KAAKC,cAAL,CAAoBd,eAApB,EAAqC;QAClEG,KAAK,EAAE;UACLY,QAAQ,EAAE,IADL;UAELC,KAAK,EAAG,KAAKZ,OAAL,KAAiB,IAAjB,IAAyB,KAAKA,OAAL,KAAiB,EAA3C,GACF,KAAKY,KAAL,IAAc,SADZ,GAEH,KAAKZ,OAJJ;UAKLa,MAAM,EAAE,KAAKT,YALR;UAMLU,aAAa,EAAE;QANV;MAD2D,CAArC,CAA/B;IAUD;EAdM;AAdmC,CAA/B,CAAf", "ignoreList": []}]}