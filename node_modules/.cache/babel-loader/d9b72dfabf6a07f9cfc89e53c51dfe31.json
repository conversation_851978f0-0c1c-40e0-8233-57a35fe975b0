{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VWindow/VWindowItem.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VWindow/VWindowItem.js", "mtime": 1757335245285}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Bootable", "factory", "GroupableFactory", "Touch", "convertToUnit", "mixins", "baseMixins", "extend", "name", "directives", "props", "disabled", "Boolean", "reverseTransition", "type", "String", "undefined", "transition", "value", "required", "data", "isActive", "inTransition", "computed", "classes", "groupClasses", "computedTransition", "windowGroup", "internalReverse", "methods", "genDefaultSlot", "$slots", "genWindowItem", "$createElement", "staticClass", "on", "$listeners", "onAfterTransition", "transitionCount", "transitionHeight", "onBeforeTransition", "$el", "clientHeight", "onTransitionCancelled", "onEnter", "el", "_this", "$nextTick", "render", "h", "_this2", "beforeEnter", "afterEnter", "enterCancelled", "beforeLeave", "afterLeave", "leaveCancelled", "enter", "showLazyContent"], "sources": ["../../../src/components/VWindow/VWindowItem.ts"], "sourcesContent": ["// Components\nimport VWindow from './VWindow'\n\n// Mixins\nimport Bootable from '../../mixins/bootable'\nimport { factory as GroupableFactory } from '../../mixins/groupable'\n\n// Directives\nimport Touch from '../../directives/touch'\n\n// Utilities\nimport { convertToUnit } from '../../util/helpers'\nimport mixins, { ExtractVue } from '../../util/mixins'\n\n// Types\nimport { VNode } from 'vue'\n\nconst baseMixins = mixins(\n  Bootable,\n  GroupableFactory('windowGroup', 'v-window-item', 'v-window')\n)\n\ninterface options extends ExtractVue<typeof baseMixins> {\n  $el: HTMLElement\n  windowGroup: InstanceType<typeof VWindow>\n}\n\nexport default baseMixins.extend<options>().extend(\n  /* @vue/component */\n).extend({\n  name: 'v-window-item',\n\n  directives: {\n    Touch,\n  },\n\n  props: {\n    disabled: Boolean,\n    reverseTransition: {\n      type: [Boolean, String],\n      default: undefined,\n    },\n    transition: {\n      type: [Boolean, String],\n      default: undefined,\n    },\n    value: {\n      required: false,\n    },\n  },\n\n  data () {\n    return {\n      isActive: false,\n      inTransition: false,\n    }\n  },\n\n  computed: {\n    classes (): object {\n      return this.groupClasses\n    },\n    computedTransition (): string | boolean {\n      if (!this.windowGroup.internalReverse) {\n        return typeof this.transition !== 'undefined'\n          ? this.transition || ''\n          : this.windowGroup.computedTransition\n      }\n\n      return typeof this.reverseTransition !== 'undefined'\n        ? this.reverseTransition || ''\n        : this.windowGroup.computedTransition\n    },\n  },\n\n  methods: {\n    genDefaultSlot () {\n      return this.$slots.default\n    },\n    genWindowItem () {\n      return this.$createElement('div', {\n        staticClass: 'v-window-item',\n        class: this.classes,\n        directives: [{\n          name: 'show',\n          value: this.isActive,\n        }],\n        on: this.$listeners,\n      }, this.genDefaultSlot())\n    },\n    onAfterTransition () {\n      if (!this.inTransition) {\n        return\n      }\n\n      // Finalize transition state.\n      this.inTransition = false\n      if (this.windowGroup.transitionCount > 0) {\n        this.windowGroup.transitionCount--\n\n        // Remove container height if we are out of transition.\n        if (this.windowGroup.transitionCount === 0) {\n          this.windowGroup.transitionHeight = undefined\n        }\n      }\n    },\n    onBeforeTransition () {\n      if (this.inTransition) {\n        return\n      }\n\n      // Initialize transition state here.\n      this.inTransition = true\n      if (this.windowGroup.transitionCount === 0) {\n        // Set initial height for height transition.\n        this.windowGroup.transitionHeight = convertToUnit(this.windowGroup.$el.clientHeight)\n      }\n      this.windowGroup.transitionCount++\n    },\n    onTransitionCancelled () {\n      this.onAfterTransition() // This should have the same path as normal transition end.\n    },\n    onEnter (el: HTMLElement) {\n      if (!this.inTransition) {\n        return\n      }\n\n      this.$nextTick(() => {\n        // Do not set height if no transition or cancelled.\n        if (!this.computedTransition || !this.inTransition) {\n          return\n        }\n\n        // Set transition target height.\n        this.windowGroup.transitionHeight = convertToUnit(el.clientHeight)\n      })\n    },\n  },\n\n  render (h): VNode {\n    return h('transition', {\n      props: {\n        name: this.computedTransition,\n      },\n      on: {\n        // Handlers for enter windows.\n        beforeEnter: this.onBeforeTransition,\n        afterEnter: this.onAfterTransition,\n        enterCancelled: this.onTransitionCancelled,\n\n        // Handlers for leave windows.\n        beforeLeave: this.onBeforeTransition,\n        afterLeave: this.onAfterTransition,\n        leaveCancelled: this.onTransitionCancelled,\n\n        // Enter handler for height transition.\n        enter: this.onEnter,\n      },\n    }, this.showLazyContent(() => [this.genWindowItem()]))\n  },\n})\n"], "mappings": "AAGA;AACA,OAAOA,QAAP,MAAqB,uBAArB;AACA,SAASC,OAAO,IAAIC,gBAApB,QAA4C,wBAA5C,C,CAEA;;AACA,OAAOC,KAAP,MAAkB,wBAAlB,C,CAEA;;AACA,SAASC,aAAT,QAA8B,oBAA9B;AACA,OAAOC,MAAP,MAAmC,mBAAnC;AAKA,IAAMC,UAAU,GAAGD,MAAM,CACvBL,QADuB,EAEvBE,gBAAgB,CAAC,aAAD,EAAgB,eAAhB,EAAiC,UAAjC,CAFO,CAAzB;AAUA,eAAeI,UAAU,CAACC,MAAX,GAA6BA,MAA7B,GAEbA,MAFa,CAEN;EACPC,IAAI,EAAE,eADC;EAGPC,UAAU,EAAE;IACVN,KAAA,EAAAA;EADU,CAHL;EAOPO,KAAK,EAAE;IACLC,QAAQ,EAAEC,OADL;IAELC,iBAAiB,EAAE;MACjBC,IAAI,EAAE,CAACF,OAAD,EAAUG,MAAV,CADW;MAEjB,WAASC;IAFQ,CAFd;IAMLC,UAAU,EAAE;MACVH,IAAI,EAAE,CAACF,OAAD,EAAUG,MAAV,CADI;MAEV,WAASC;IAFC,CANP;IAULE,KAAK,EAAE;MACLC,QAAQ,EAAE;IADL;EAVF,CAPA;EAsBPC,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,QAAQ,EAAE,KADL;MAELC,YAAY,EAAE;IAFT,CAAP;EAID,CA3BM;EA6BPC,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAO,KAAKC,YAAZ;IACD,CAHO;IAIRC,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAI,CAAC,KAAKC,WAAL,CAAiBC,eAAtB,EAAuC;QACrC,OAAO,OAAO,KAAKX,UAAZ,KAA2B,WAA3B,GACH,KAAKA,UAAL,IAAmB,EADhB,GAEH,KAAKU,WAAL,CAAiBD,kBAFrB;MAGD;MAED,OAAO,OAAO,KAAKb,iBAAZ,KAAkC,WAAlC,GACH,KAAKA,iBAAL,IAA0B,EADvB,GAEH,KAAKc,WAAL,CAAiBD,kBAFrB;IAGD;EAdO,CA7BH;EA8CPG,OAAO,EAAE;IACPC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,KAAKC,MAAL,WAAP;IACD,CAHM;IAIPC,aAAa,WAAbA,aAAaA,CAAA;MACX,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE,eADmB;QAEhC,SAAO,KAAKV,OAFoB;QAGhCf,UAAU,EAAE,CAAC;UACXD,IAAI,EAAE,MADK;UAEXU,KAAK,EAAE,KAAKG;QAFD,CAAD,CAHoB;QAOhCc,EAAE,EAAE,KAAKC;MAPuB,CAA3B,EAQJ,KAAKN,cAAL,EARI,CAAP;IASD,CAdM;IAePO,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IAAI,CAAC,KAAKf,YAAV,EAAwB;QACtB;MACD,CAHc,CAKf;;MACA,KAAKA,YAAL,GAAoB,KAApB;MACA,IAAI,KAAKK,WAAL,CAAiBW,eAAjB,GAAmC,CAAvC,EAA0C;QACxC,KAAKX,WAAL,CAAiBW,eAAjB,GADwC,CAGxC;;QACA,IAAI,KAAKX,WAAL,CAAiBW,eAAjB,KAAqC,CAAzC,EAA4C;UAC1C,KAAKX,WAAL,CAAiBY,gBAAjB,GAAoCvB,SAApC;QACD;MACF;IACF,CA9BM;IA+BPwB,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAI,KAAKlB,YAAT,EAAuB;QACrB;MACD,CAHe,CAKhB;;MACA,KAAKA,YAAL,GAAoB,IAApB;MACA,IAAI,KAAKK,WAAL,CAAiBW,eAAjB,KAAqC,CAAzC,EAA4C;QAC1C;QACA,KAAKX,WAAL,CAAiBY,gBAAjB,GAAoCnC,aAAa,CAAC,KAAKuB,WAAL,CAAiBc,GAAjB,CAAqBC,YAAtB,CAAjD;MACD;MACD,KAAKf,WAAL,CAAiBW,eAAjB;IACD,CA3CM;IA4CPK,qBAAqB,WAArBA,qBAAqBA,CAAA;MACnB,KAAKN,iBAAL,GADmB,CACM;IAC1B,CA9CM;IA+CPO,OAAO,WAAPA,OAAOA,CAAEC,EAAF,EAAiB;MAAA,IAAAC,KAAA;MACtB,IAAI,CAAC,KAAKxB,YAAV,EAAwB;QACtB;MACD;MAED,KAAKyB,SAAL,CAAe,YAAK;QAClB;QACA,IAAI,CAACD,KAAA,CAAKpB,kBAAN,IAA4B,CAACoB,KAAA,CAAKxB,YAAtC,EAAoD;UAClD;QACD,CAJiB,CAMlB;;QACAwB,KAAA,CAAKnB,WAAL,CAAiBY,gBAAjB,GAAoCnC,aAAa,CAACyC,EAAE,CAACH,YAAJ,CAAjD;MACD,CARD;IASD;EA7DM,CA9CF;EA8GPM,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IAAA,IAAAC,MAAA;IACP,OAAOD,CAAC,CAAC,YAAD,EAAe;MACrBvC,KAAK,EAAE;QACLF,IAAI,EAAE,KAAKkB;MADN,CADc;MAIrBS,EAAE,EAAE;QACF;QACAgB,WAAW,EAAE,KAAKX,kBAFhB;QAGFY,UAAU,EAAE,KAAKf,iBAHf;QAIFgB,cAAc,EAAE,KAAKV,qBAJnB;QAMF;QACAW,WAAW,EAAE,KAAKd,kBAPhB;QAQFe,UAAU,EAAE,KAAKlB,iBARf;QASFmB,cAAc,EAAE,KAAKb,qBATnB;QAWF;QACAc,KAAK,EAAE,KAAKb;MAZV;IAJiB,CAAf,EAkBL,KAAKc,eAAL,CAAqB;MAAA,OAAM,CAACR,MAAA,CAAKlB,aAAL,EAAD,CAA3B;IAAA,EAlBK,CAAR;EAmBD;AAlIM,CAFM,CAAf", "ignoreList": []}]}