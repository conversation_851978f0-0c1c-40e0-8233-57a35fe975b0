{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VHover/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VHover/index.js", "mtime": 1757335236866}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZIb3ZlciBmcm9tICcuL1ZIb3Zlcic7CmV4cG9ydCB7IFZIb3ZlciB9OwpleHBvcnQgZGVmYXVsdCBWSG92ZXI7"}, {"version": 3, "names": ["VHover"], "sources": ["../../../src/components/VHover/index.ts"], "sourcesContent": ["import VHover from './VHover'\n\nexport { VHover }\nexport default VHover\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,UAAnB;AAEA,SAASA,MAAT;AACA,eAAeA,MAAf", "ignoreList": []}]}