{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CustomGraphDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CustomGraphDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaG93RGlhbG9nOiBmYWxzZSwKICAgICAgY3VzdG9tR3JhcGhVcmw6IG51bGwKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBzaG93OiBmdW5jdGlvbiBzaG93KCkgewogICAgICB0aGlzLnNob3dEaWFsb2cgPSB0cnVlOwogICAgfSwKICAgIGFkZFRvQ3VzdG9tR3JhcGhMaXN0OiBmdW5jdGlvbiBhZGRUb0N1c3RvbUdyYXBoTGlzdCgpIHsKICAgICAgdGhpcy4kZW1pdCgidXBkYXRlQ3VzdG9tR3JhcGgiLCB0aGlzLmN1c3RvbUdyYXBoVXJsKTsKICAgICAgdGhpcy5jdXN0b21HcmFwaFVybCA9ICIiOwogICAgICB0aGlzLnNob3dEaWFsb2cgPSBmYWxzZTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["data", "showDialog", "customGraphUrl", "methods", "show", "addToCustomGraphList", "$emit"], "sources": ["src/components/ToolbarDialog/CustomGraphDialog.vue"], "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"650\">\n    <v-card>\n      <v-card-title>添加自定义图形</v-card-title>\n      <v-card-text>\n        <v-text-field\n          v-model=\"customGraphUrl\"\n          label=\"自定义图形URL\"\n          prepend-icon=\"mdi-image\"\n        ></v-text-field>\n      </v-card-text>\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn color=\"primary\" text @click=\"addToCustomGraphList()\"\n          >添加至列表</v-btn\n        >\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      customGraphUrl: null,\n    };\n  },\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n\n    addToCustomGraphList() {\n      this.$emit(\"updateCustomGraph\", this.customGraphUrl);\n      this.customGraphUrl = \"\";\n      this.showDialog = false;\n    },\n  },\n};\n</script>"], "mappings": "AAuBA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAH,UAAA;IACA;IAEAI,oBAAA,WAAAA,qBAAA;MACA,KAAAC,KAAA,2BAAAJ,cAAA;MACA,KAAAA,cAAA;MACA,KAAAD,UAAA;IACA;EACA;AACA", "ignoreList": []}]}