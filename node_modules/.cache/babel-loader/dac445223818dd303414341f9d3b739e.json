{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sl.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/sl.js", "mtime": 1757335237514}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/sl.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON><PERSON><PERSON><PERSON>',\n  close: '<PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Ni iskanega zapisa',\n    loadingText: 'Nalaganje...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Vrst<PERSON> na stran:',\n    ariaLabel: {\n      sortDescending: '<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON> padajoče.',\n      sortAscending: '<PERSON>zv<PERSON><PERSON><PERSON><PERSON> naraščajoče.',\n      sortNone: 'Ni razvrščeno.',\n      activateNone: 'Aktivirajte za odstranitev razvrščanja.',\n      activateDescending: 'Aktivirajte za padajoče razvrščanje.',\n      activateAscending: 'Aktivirajte za naraščajoče razvrščanje.',\n    },\n    sortBy: 'Razvrsti po',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Predmetov na stran:',\n    itemsPerPageAll: 'Vse',\n    nextPage: 'Naslednja stran',\n    prevPage: 'Pre<PERSON><PERSON><PERSON> stran',\n    firstPage: 'Prva stran',\n    lastPage: 'Zadnja stran',\n    pageText: '{0}-{1} od {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} izbrano/-ih',\n    nextMonthAriaLabel: 'Naslednji mesec',\n    nextYearAriaLabel: 'Naslednje leto',\n    prevMonthAriaLabel: 'Prejšnji mesec',\n    prevYearAriaLabel: 'Prejšnje leto',\n  },\n  noDataText: 'Ni podatkov',\n  carousel: {\n    prev: 'Prejšnji prikaz',\n    next: 'Naslednji prikaz',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: 'Še {0}',\n  },\n  fileInput: {\n    counter: '{0} datotek',\n    counterSize: '{0} datotek ({1} skupno)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigacija po strani po strani',\n      next: 'Naslednja stran',\n      previous: 'Prejšnja stran',\n      page: 'Pojdi na stran {0}',\n      currentPage: 'Trenutna stran, stran {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating {0} of {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,QADM;EAEbC,KAAK,EAAE,OAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,oBADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,kBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,sBADP;MAETC,aAAa,EAAE,yBAFN;MAGTC,QAAQ,EAAE,gBAHD;MAITC,YAAY,EAAE,yCAJL;MAKTC,kBAAkB,EAAE,sCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,KAFP;IAGVC,QAAQ,EAAE,iBAHA;IAIVC,QAAQ,EAAE,gBAJA;IAKVC,SAAS,EAAE,YALD;IAMVC,QAAQ,EAAE,cANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,iBADL;IAEVC,kBAAkB,EAAE,iBAFV;IAGVC,iBAAiB,EAAE,gBAHT;IAIVC,kBAAkB,EAAE,gBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,aAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,kBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,gCADA;MAETX,IAAI,EAAE,iBAFG;MAGTY,QAAQ,EAAE,gBAHD;MAITC,IAAI,EAAE,oBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}