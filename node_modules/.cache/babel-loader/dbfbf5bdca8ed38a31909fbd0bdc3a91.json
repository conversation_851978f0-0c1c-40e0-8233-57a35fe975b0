{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ScrollbarSettingDialog.vue?vue&type=template&id=01313f3c", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ScrollbarSettingDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "showDialog", "attrs", "width", "model", "value", "callback", "$$v", "expression", "_v", "flat", "on", "handleScrollbarThumbColorChange", "thumbColor", "handleScrollbarTrackColorChange", "trackColor", "text", "click", "$event", "_e", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/Downloads/web-demo/src/components/ScrollbarSettingDialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"cursor-setting-dialog__container\" },\n    [\n      _vm.showDialog\n        ? _c(\n            \"v-dialog\",\n            {\n              attrs: { width: \"500\" },\n              model: {\n                value: _vm.showDialog,\n                callback: function ($$v) {\n                  _vm.showDialog = $$v\n                },\n                expression: \"showDialog\",\n              },\n            },\n            [\n              _c(\n                \"v-card\",\n                [\n                  _c(\"v-card-title\", { staticClass: \"headline lighten-2\" }, [\n                    _vm._v(\" 滚动条设置 \"),\n                  ]),\n                  _c(\n                    \"v-card-text\",\n                    [\n                      _c(\"span\", [_vm._v(\"滑块颜色\")]),\n                      _c(\"v-color-picker\", {\n                        attrs: { width: \"400\", \"hide-inputs\": \"\", flat: \"\" },\n                        on: {\n                          \"update:color\": _vm.handleScrollbarThumbColorChange,\n                        },\n                        model: {\n                          value: _vm.thumbColor,\n                          callback: function ($$v) {\n                            _vm.thumbColor = $$v\n                          },\n                          expression: \"thumbColor\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"v-card-text\",\n                    [\n                      _c(\"span\", [_vm._v(\"滑轨颜色\")]),\n                      _c(\"v-color-picker\", {\n                        attrs: { width: \"400\", \"hide-inputs\": \"\", flat: \"\" },\n                        on: {\n                          \"update:color\": _vm.handleScrollbarTrackColorChange,\n                        },\n                        model: {\n                          value: _vm.trackColor,\n                          callback: function ($$v) {\n                            _vm.trackColor = $$v\n                          },\n                          expression: \"trackColor\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"v-card-actions\",\n                    [\n                      _c(\"v-spacer\"),\n                      _c(\n                        \"v-btn\",\n                        {\n                          attrs: { text: \"\" },\n                          on: {\n                            click: function ($event) {\n                              _vm.showDialog = false\n                            },\n                          },\n                        },\n                        [_vm._v(\"关闭\")]\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          )\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmC,CAAC,EACnD,CACEH,GAAG,CAACI,UAAU,GACVH,EAAE,CACA,UAAU,EACV;IACEI,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM,CAAC;IACvBC,KAAK,EAAE;MACLC,KAAK,EAAER,GAAG,CAACI,UAAU;MACrBK,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAACI,UAAU,GAAGM,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEV,EAAE,CACA,QAAQ,EACR,CACEA,EAAE,CAAC,cAAc,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CACxDH,GAAG,CAACY,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACFX,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BX,EAAE,CAAC,gBAAgB,EAAE;IACnBI,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAE,aAAa,EAAE,EAAE;MAAEO,IAAI,EAAE;IAAG,CAAC;IACpDC,EAAE,EAAE;MACF,cAAc,EAAEd,GAAG,CAACe;IACtB,CAAC;IACDR,KAAK,EAAE;MACLC,KAAK,EAAER,GAAG,CAACgB,UAAU;MACrBP,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAACgB,UAAU,GAAGN,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BX,EAAE,CAAC,gBAAgB,EAAE;IACnBI,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAE,aAAa,EAAE,EAAE;MAAEO,IAAI,EAAE;IAAG,CAAC;IACpDC,EAAE,EAAE;MACF,cAAc,EAAEd,GAAG,CAACiB;IACtB,CAAC;IACDV,KAAK,EAAE;MACLC,KAAK,EAAER,GAAG,CAACkB,UAAU;MACrBT,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAACkB,UAAU,GAAGR,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,gBAAgB,EAChB,CACEA,EAAE,CAAC,UAAU,CAAC,EACdA,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MAAEc,IAAI,EAAE;IAAG,CAAC;IACnBL,EAAE,EAAE;MACFM,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBrB,GAAG,CAACI,UAAU,GAAG,KAAK;MACxB;IACF;EACF,CAAC,EACD,CAACJ,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDZ,GAAG,CAACsB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBxB,MAAM,CAACyB,aAAa,GAAG,IAAI;AAE3B,SAASzB,MAAM,EAAEwB,eAAe", "ignoreList": []}]}