{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSwitch/VSwitch.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VSwitch/VSwitch.js", "mtime": 1757335239131}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Selectable", "VInput", "Touch", "VFabTransition", "VProgressCircular", "keyCodes", "extend", "name", "directives", "props", "inset", "Boolean", "loading", "type", "String", "flat", "computed", "classes", "_objectSpread", "options", "call", "_flatInstanceProperty", "attrs", "isActive", "isDisabled", "role", "validationState", "<PERSON><PERSON><PERSON><PERSON>", "shouldValidate", "hasSuccess", "hasColor", "computedColor", "undefined", "switchData", "setTextColor", "themeClasses", "methods", "genDefaultSlot", "genSwitch", "gen<PERSON><PERSON><PERSON>", "_this$attrs$", "attrs$", "title", "switchAttrs", "_objectWithoutProperties", "_excluded", "$createElement", "staticClass", "genInput", "gen<PERSON><PERSON><PERSON>", "value", "left", "onSwipeLeft", "right", "onSwipeRight", "genProgress", "$slots", "progress", "color", "size", "width", "indeterminate", "onChange", "onKeydown", "e", "keyCode"], "sources": ["../../../src/components/VSwitch/VSwitch.ts"], "sourcesContent": ["// Styles\nimport '../../styles/components/_selection-controls.sass'\nimport './VSwitch.sass'\n\n// Mixins\nimport Selectable from '../../mixins/selectable'\nimport VInput from '../VInput'\n\n// Directives\nimport Touch from '../../directives/touch'\n\n// Components\nimport { VFabTransition } from '../transitions'\nimport VProgressCircular from '../VProgressCircular/VProgressCircular'\n\n// Helpers\nimport { keyCodes } from '../../util/helpers'\n\n// Types\nimport { VNode, VNodeData } from 'vue'\n\n/* @vue/component */\nexport default Selectable.extend({\n  name: 'v-switch',\n\n  directives: { Touch },\n\n  props: {\n    inset: Boolean,\n    loading: {\n      type: [Boolean, String],\n      default: false,\n    },\n    flat: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  computed: {\n    classes (): object {\n      return {\n        ...VInput.options.computed.classes.call(this),\n        'v-input--selection-controls v-input--switch': true,\n        'v-input--switch--flat': this.flat,\n        'v-input--switch--inset': this.inset,\n      }\n    },\n    attrs (): object {\n      return {\n        'aria-checked': String(this.isActive),\n        'aria-disabled': String(this.isDisabled),\n        role: 'switch',\n      }\n    },\n    // Do not return undefined if disabled,\n    // according to spec, should still show\n    // a color when disabled and active\n    validationState (): string | undefined {\n      if (this.hasError && this.shouldValidate) return 'error'\n      if (this.hasSuccess) return 'success'\n      if (this.hasColor !== null) return this.computedColor\n      return undefined\n    },\n    switchData (): VNodeData {\n      return this.setTextColor(this.loading ? undefined : this.validationState, {\n        class: this.themeClasses,\n      })\n    },\n  },\n\n  methods: {\n    genDefaultSlot (): (VNode | null)[] {\n      return [\n        this.genSwitch(),\n        this.genLabel(),\n      ]\n    },\n    genSwitch (): VNode {\n      const { title, ...switchAttrs } = this.attrs$\n\n      return this.$createElement('div', {\n        staticClass: 'v-input--selection-controls__input',\n      }, [\n        this.genInput('checkbox', {\n          ...this.attrs,\n          ...switchAttrs,\n        }),\n        this.genRipple(this.setTextColor(this.validationState, {\n          directives: [{\n            name: 'touch',\n            value: {\n              left: this.onSwipeLeft,\n              right: this.onSwipeRight,\n            },\n          }],\n        })),\n        this.$createElement('div', {\n          staticClass: 'v-input--switch__track',\n          ...this.switchData,\n        }),\n        this.$createElement('div', {\n          staticClass: 'v-input--switch__thumb',\n          ...this.switchData,\n        }, [this.genProgress()]),\n      ])\n    },\n    genProgress (): VNode {\n      return this.$createElement(VFabTransition, {}, [\n        this.loading === false\n          ? null\n          : this.$slots.progress || this.$createElement(VProgressCircular, {\n            props: {\n              color: (this.loading === true || this.loading === '')\n                ? (this.color || 'primary')\n                : this.loading,\n              size: 16,\n              width: 2,\n              indeterminate: true,\n            },\n          }),\n      ])\n    },\n    onSwipeLeft () {\n      if (this.isActive) this.onChange()\n    },\n    onSwipeRight () {\n      if (!this.isActive) this.onChange()\n    },\n    onKeydown (e: KeyboardEvent) {\n      if (\n        (e.keyCode === keyCodes.left && this.isActive) ||\n        (e.keyCode === keyCodes.right && !this.isActive)\n      ) this.onChange()\n    },\n  },\n})\n"], "mappings": ";;;;AAAA;AACA,OAAO,yDAAP;AACA,OAAO,8CAAP,C,CAEA;;AACA,OAAOA,UAAP,MAAuB,yBAAvB;AACA,OAAOC,MAAP,MAAmB,WAAnB,C,CAEA;;AACA,OAAOC,KAAP,MAAkB,wBAAlB,C,CAEA;;AACA,SAASC,cAAT,QAA+B,gBAA/B;AACA,OAAOC,iBAAP,MAA8B,wCAA9B,C,CAEA;;AACA,SAASC,QAAT,QAAyB,oBAAzB;AAKA;;AACA,eAAeL,UAAU,CAACM,MAAX,CAAkB;EAC/BC,IAAI,EAAE,UADyB;EAG/BC,UAAU,EAAE;IAAEN,KAAA,EAAAA;EAAF,CAHmB;EAK/BO,KAAK,EAAE;IACLC,KAAK,EAAEC,OADF;IAELC,OAAO,EAAE;MACPC,IAAI,EAAE,CAACF,OAAD,EAAUG,MAAV,CADC;MAEP,WAAS;IAFF,CAFJ;IAMLC,IAAI,EAAE;MACJF,IAAI,EAAEF,OADF;MAEJ,WAAS;IAFL;EAND,CALwB;EAiB/BK,QAAQ,EAAE;IACRC,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA,CAAAA,aAAA,KACKjB,MAAM,CAACkB,OAAP,CAAeH,QAAf,CAAwBC,OAAxB,CAAgCG,IAAhC,CAAqC,IAArC,CADE;QAEL,+CAA+C,IAF1C;QAGL,yBAAAC,qBAAA,CAAyB,KAHpB;QAIL,0BAA0B,KAAKX;MAAA;IAElC,CARO;IASRY,KAAK,WAALA,KAAKA,CAAA;MACH,OAAO;QACL,gBAAgBR,MAAM,CAAC,KAAKS,QAAN,CADjB;QAEL,iBAAiBT,MAAM,CAAC,KAAKU,UAAN,CAFlB;QAGLC,IAAI,EAAE;MAHD,CAAP;IAKD,CAfO;IAgBR;IACA;IACA;IACAC,eAAe,WAAfA,eAAeA,CAAA;MACb,IAAI,KAAKC,QAAL,IAAiB,KAAKC,cAA1B,EAA0C,OAAO,OAAP;MAC1C,IAAI,KAAKC,UAAT,EAAqB,OAAO,SAAP;MACrB,IAAI,KAAKC,QAAL,KAAkB,IAAtB,EAA4B,OAAO,KAAKC,aAAZ;MAC5B,OAAOC,SAAP;IACD,CAxBO;IAyBRC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKC,YAAL,CAAkB,KAAKtB,OAAL,GAAeoB,SAAf,GAA2B,KAAKN,eAAlD,EAAmE;QACxE,SAAO,KAAKS;MAD4D,CAAnE,CAAP;IAGD;EA7BO,CAjBqB;EAiD/BC,OAAO,EAAE;IACPC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,CACL,KAAKC,SAAL,EADK,EAEL,KAAKC,QAAL,EAFK,CAAP;IAID,CANM;IAOPD,SAAS,WAATA,SAASA,CAAA;MACP,IAAAE,YAAA,GAAkC,KAAKC,MAAvC;QAAQC,KAAF,GAAAF,YAAA,CAAEE,KAAF;QAAYC,WAAA,GAAAC,wBAAA,CAAAJ,YAAA,EAAAK,SAAA;MAElB,OAAO,KAAKC,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE;MADmB,CAA3B,EAEJ,CACD,KAAKC,QAAL,CAAc,UAAd,EAAA9B,aAAA,CAAAA,aAAA,KACK,KAAKI,KADgB,GAErBqB,WAAA,CAFL,CADC,EAKD,KAAKM,SAAL,CAAe,KAAKf,YAAL,CAAkB,KAAKR,eAAvB,EAAwC;QACrDlB,UAAU,EAAE,CAAC;UACXD,IAAI,EAAE,OADK;UAEX2C,KAAK,EAAE;YACLC,IAAI,EAAE,KAAKC,WADN;YAELC,KAAK,EAAE,KAAKC;UAFP;QAFI,CAAD;MADyC,CAAxC,CAAf,CALC,EAcD,KAAKR,cAAL,CAAoB,KAApB,EAAA5B,aAAA;QACE6B,WAAW,EAAE;MADY,GAEtB,KAAKd,UAAA,CAFV,CAdC,EAkBD,KAAKa,cAAL,CAAoB,KAApB,EAAA5B,aAAA;QACE6B,WAAW,EAAE;MADY,GAEtB,KAAKd,UAAA,GACP,CAAC,KAAKsB,WAAL,EAAD,CAHH,CAlBC,CAFI,CAAP;IAyBD,CAnCM;IAoCPA,WAAW,WAAXA,WAAWA,CAAA;MACT,OAAO,KAAKT,cAAL,CAAoB3C,cAApB,EAAoC,EAApC,EAAwC,CAC7C,KAAKS,OAAL,KAAiB,KAAjB,GACI,IADJ,GAEI,KAAK4C,MAAL,CAAYC,QAAZ,IAAwB,KAAKX,cAAL,CAAoB1C,iBAApB,EAAuC;QAC/DK,KAAK,EAAE;UACLiD,KAAK,EAAG,KAAK9C,OAAL,KAAiB,IAAjB,IAAyB,KAAKA,OAAL,KAAiB,EAA3C,GACF,KAAK8C,KAAL,IAAc,SADZ,GAEH,KAAK9C,OAHJ;UAIL+C,IAAI,EAAE,EAJD;UAKLC,KAAK,EAAE,CALF;UAMLC,aAAa,EAAE;QANV;MADwD,CAAvC,CAHiB,CAAxC,CAAP;IAcD,CAnDM;IAoDPT,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAI,KAAK7B,QAAT,EAAmB,KAAKuC,QAAL;IACpB,CAtDM;IAuDPR,YAAY,WAAZA,YAAYA,CAAA;MACV,IAAI,CAAC,KAAK/B,QAAV,EAAoB,KAAKuC,QAAL;IACrB,CAzDM;IA0DPC,SAAS,WAATA,SAASA,CAAEC,CAAF,EAAkB;MACzB,IACGA,CAAC,CAACC,OAAF,KAAc5D,QAAQ,CAAC8C,IAAvB,IAA+B,KAAK5B,QAArC,IACCyC,CAAC,CAACC,OAAF,KAAc5D,QAAQ,CAACgD,KAAvB,IAAgC,CAAC,KAAK9B,QAFzC,EAGE,KAAKuC,QAAL;IACH;EA/DM;AAjDsB,CAAlB,CAAf", "ignoreList": []}]}