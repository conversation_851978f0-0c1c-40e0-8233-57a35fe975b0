{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/md.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/services/icons/presets/md.js", "mtime": 1757335237351}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGljb25zID0gewogIGNvbXBsZXRlOiAnY2hlY2snLAogIGNhbmNlbDogJ2NhbmNlbCcsCiAgY2xvc2U6ICdjbG9zZScsCiAgImRlbGV0ZSI6ICdjYW5jZWwnLAogIGNsZWFyOiAnY2xlYXInLAogIHN1Y2Nlc3M6ICdjaGVja19jaXJjbGUnLAogIGluZm86ICdpbmZvJywKICB3YXJuaW5nOiAncHJpb3JpdHlfaGlnaCcsCiAgZXJyb3I6ICd3YXJuaW5nJywKICBwcmV2OiAnY2hldnJvbl9sZWZ0JywKICBuZXh0OiAnY2hldnJvbl9yaWdodCcsCiAgY2hlY2tib3hPbjogJ2NoZWNrX2JveCcsCiAgY2hlY2tib3hPZmY6ICdjaGVja19ib3hfb3V0bGluZV9ibGFuaycsCiAgY2hlY2tib3hJbmRldGVybWluYXRlOiAnaW5kZXRlcm1pbmF0ZV9jaGVja19ib3gnLAogIGRlbGltaXRlcjogJ2ZpYmVyX21hbnVhbF9yZWNvcmQnLAogIHNvcnQ6ICdhcnJvd191cHdhcmQnLAogIGV4cGFuZDogJ2tleWJvYXJkX2Fycm93X2Rvd24nLAogIG1lbnU6ICdtZW51JywKICBzdWJncm91cDogJ2Fycm93X2Ryb3BfZG93bicsCiAgZHJvcGRvd246ICdhcnJvd19kcm9wX2Rvd24nLAogIHJhZGlvT246ICdyYWRpb19idXR0b25fY2hlY2tlZCcsCiAgcmFkaW9PZmY6ICdyYWRpb19idXR0b25fdW5jaGVja2VkJywKICBlZGl0OiAnZWRpdCcsCiAgcmF0aW5nRW1wdHk6ICdzdGFyX2JvcmRlcicsCiAgcmF0aW5nRnVsbDogJ3N0YXInLAogIHJhdGluZ0hhbGY6ICdzdGFyX2hhbGYnLAogIGxvYWRpbmc6ICdjYWNoZWQnLAogIGZpcnN0OiAnZmlyc3RfcGFnZScsCiAgbGFzdDogJ2xhc3RfcGFnZScsCiAgdW5mb2xkOiAndW5mb2xkX21vcmUnLAogIGZpbGU6ICdhdHRhY2hfZmlsZScsCiAgcGx1czogJ2FkZCcsCiAgbWludXM6ICdyZW1vdmUnCn07CmV4cG9ydCBkZWZhdWx0IGljb25zOw=="}, {"version": 3, "names": ["icons", "complete", "cancel", "close", "clear", "success", "info", "warning", "error", "prev", "next", "checkboxOn", "checkboxOff", "checkboxIndeterminate", "delimiter", "sort", "expand", "menu", "subgroup", "dropdown", "radioOn", "radioOff", "edit", "ratingEmpty", "ratingFull", "ratingHalf", "loading", "first", "last", "unfold", "file", "plus", "minus"], "sources": ["../../../../src/services/icons/presets/md.ts"], "sourcesContent": ["import { VuetifyIcons } from 'vuetify/types/services/icons'\n\nconst icons: VuetifyIcons = {\n  complete: 'check',\n  cancel: 'cancel',\n  close: 'close',\n  delete: 'cancel', // delete (e.g. v-chip close)\n  clear: 'clear',\n  success: 'check_circle',\n  info: 'info',\n  warning: 'priority_high',\n  error: 'warning',\n  prev: 'chevron_left',\n  next: 'chevron_right',\n  checkboxOn: 'check_box',\n  checkboxOff: 'check_box_outline_blank',\n  checkboxIndeterminate: 'indeterminate_check_box',\n  delimiter: 'fiber_manual_record', // for carousel\n  sort: 'arrow_upward',\n  expand: 'keyboard_arrow_down',\n  menu: 'menu',\n  subgroup: 'arrow_drop_down',\n  dropdown: 'arrow_drop_down',\n  radioOn: 'radio_button_checked',\n  radioOff: 'radio_button_unchecked',\n  edit: 'edit',\n  ratingEmpty: 'star_border',\n  ratingFull: 'star',\n  ratingHalf: 'star_half',\n  loading: 'cached',\n  first: 'first_page',\n  last: 'last_page',\n  unfold: 'unfold_more',\n  file: 'attach_file',\n  plus: 'add',\n  minus: 'remove',\n}\n\nexport default icons\n"], "mappings": "AAEA,IAAMA,KAAK,GAAiB;EAC1BC,QAAQ,EAAE,OADgB;EAE1BC,MAAM,EAAE,QAFkB;EAG1BC,KAAK,EAAE,OAHmB;EAI1B,UAAQ,QAJkB;EAK1BC,KAAK,EAAE,OALmB;EAM1BC,OAAO,EAAE,cANiB;EAO1BC,IAAI,EAAE,MAPoB;EAQ1BC,OAAO,EAAE,eARiB;EAS1BC,KAAK,EAAE,SATmB;EAU1BC,IAAI,EAAE,cAVoB;EAW1BC,IAAI,EAAE,eAXoB;EAY1BC,UAAU,EAAE,WAZc;EAa1BC,WAAW,EAAE,yBAba;EAc1BC,qBAAqB,EAAE,yBAdG;EAe1BC,SAAS,EAAE,qBAfe;EAgB1BC,IAAI,EAAE,cAhBoB;EAiB1BC,MAAM,EAAE,qBAjBkB;EAkB1BC,IAAI,EAAE,MAlBoB;EAmB1BC,QAAQ,EAAE,iBAnBgB;EAoB1BC,QAAQ,EAAE,iBApBgB;EAqB1BC,OAAO,EAAE,sBArBiB;EAsB1BC,QAAQ,EAAE,wBAtBgB;EAuB1BC,IAAI,EAAE,MAvBoB;EAwB1BC,WAAW,EAAE,aAxBa;EAyB1BC,UAAU,EAAE,MAzBc;EA0B1BC,UAAU,EAAE,WA1Bc;EA2B1BC,OAAO,EAAE,QA3BiB;EA4B1BC,KAAK,EAAE,YA5BmB;EA6B1BC,IAAI,EAAE,WA7BoB;EA8B1BC,MAAM,EAAE,aA9BkB;EA+B1BC,IAAI,EAAE,aA/BoB;EAgC1BC,IAAI,EAAE,KAhCoB;EAiC1BC,KAAK,EAAE;AAjCmB,CAA5B;AAoCA,eAAehC,KAAf", "ignoreList": []}]}