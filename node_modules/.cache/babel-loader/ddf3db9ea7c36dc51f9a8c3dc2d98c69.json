{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/transitions/expand-transition.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/transitions/expand-transition.js", "mtime": 1757335235741}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["upperFirst", "expandedParentClass", "arguments", "length", "undefined", "x", "sizeProperty", "offsetProperty", "concat", "beforeEnter", "el", "_parent", "parentNode", "_initialStyle", "_defineProperty", "transition", "style", "overflow", "enter", "initialStyle", "setProperty", "offset", "offsetHeight", "classList", "add", "requestAnimationFrame", "afterEnter", "resetStyles", "enterCancelled", "leave", "afterLeave", "leaveCancelled", "remove", "size"], "sources": ["../../../src/components/transitions/expand-transition.ts"], "sourcesContent": ["import { upperFirst } from '../../util/helpers'\n\ninterface HTMLExpandElement extends HTMLElement {\n  _parent?: (Node & ParentNode & HTMLElement) | null\n  _initialStyle?: {\n    transition: string\n    overflow: string\n    height?: string | null\n    width?: string | null\n  }\n}\n\nexport default function (expandedParentClass = '', x = false) {\n  const sizeProperty = x ? 'width' : 'height' as 'width' | 'height'\n  const offsetProperty = `offset${upperFirst(sizeProperty)}` as 'offsetHeight' | 'offsetWidth'\n\n  return {\n    beforeEnter (el: HTMLExpandElement) {\n      el._parent = el.parentNode as (Node & ParentNode & HTMLElement) | null\n      el._initialStyle = {\n        transition: el.style.transition,\n        overflow: el.style.overflow,\n        [sizeProperty]: el.style[sizeProperty],\n      }\n    },\n\n    enter (el: HTMLExpandElement) {\n      const initialStyle = el._initialStyle!\n\n      el.style.setProperty('transition', 'none', 'important')\n      // Hide overflow to account for collapsed margins in the calculated height\n      el.style.overflow = 'hidden'\n      const offset = `${el[offsetProperty]}px`\n\n      el.style[sizeProperty] = '0'\n\n      void el.offsetHeight // force reflow\n\n      el.style.transition = initialStyle.transition\n\n      if (expandedParentClass && el._parent) {\n        el._parent.classList.add(expandedParentClass)\n      }\n\n      requestAnimationFrame(() => {\n        el.style[sizeProperty] = offset\n      })\n    },\n\n    afterEnter: resetStyles,\n    enterCancelled: resetStyles,\n\n    leave (el: HTMLExpandElement) {\n      el._initialStyle = {\n        transition: '',\n        overflow: el.style.overflow,\n        [sizeProperty]: el.style[sizeProperty],\n      }\n\n      el.style.overflow = 'hidden'\n      el.style[sizeProperty] = `${el[offsetProperty]}px`\n      void el.offsetHeight // force reflow\n\n      requestAnimationFrame(() => (el.style[sizeProperty] = '0'))\n    },\n\n    afterLeave,\n    leaveCancelled: afterLeave,\n  }\n\n  function afterLeave (el: HTMLExpandElement) {\n    if (expandedParentClass && el._parent) {\n      el._parent.classList.remove(expandedParentClass)\n    }\n    resetStyles(el)\n  }\n\n  function resetStyles (el: HTMLExpandElement) {\n    const size = el._initialStyle![sizeProperty]\n    el.style.overflow = el._initialStyle!.overflow\n    if (size != null) el.style[sizeProperty] = size\n    delete el._initialStyle\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAT,QAA2B,oBAA3B;AAYA,eAAc,YAA8C;EAAA,IAAnCC,mBAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAjC;EAAA,IAAqCG,CAAC,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAzC;EACZ,IAAMI,YAAY,GAAGD,CAAC,GAAG,OAAH,GAAa,QAAnC;EACA,IAAME,cAAc,YAAAC,MAAA,CAAYR,UAAU,CAACM,YAAD,CAAc,CAAxD;EAEA,OAAO;IACLG,WAAW,WAAXA,WAAWA,CAAEC,EAAF,EAAuB;MAChCA,EAAE,CAACC,OAAH,GAAaD,EAAE,CAACE,UAAhB;MACAF,EAAE,CAACG,aAAH,GAAAC,eAAA;QACEC,UAAU,EAAEL,EAAE,CAACM,KAAH,CAASD,UADJ;QAEjBE,QAAQ,EAAEP,EAAE,CAACM,KAAH,CAASC;MAFF,GAGhBX,YAAD,EAAgBI,EAAE,CAACM,KAAH,CAASV,YAAT,EAHlB;IAKD,CARI;IAULY,KAAK,WAALA,KAAKA,CAAER,EAAF,EAAuB;MAC1B,IAAMS,YAAY,GAAGT,EAAE,CAACG,aAAxB;MAEAH,EAAE,CAACM,KAAH,CAASI,WAAT,CAAqB,YAArB,EAAmC,MAAnC,EAA2C,WAA3C,EAH0B,CAI1B;;MACAV,EAAE,CAACM,KAAH,CAASC,QAAT,GAAoB,QAApB;MACA,IAAMI,MAAM,MAAAb,MAAA,CAAME,EAAE,CAACH,cAAD,CAAgB,OAApC;MAEAG,EAAE,CAACM,KAAH,CAASV,YAAT,IAAyB,GAAzB;MAEA,KAAKI,EAAE,CAACY,YAAR,CAV0B,CAUL;;MAErBZ,EAAE,CAACM,KAAH,CAASD,UAAT,GAAsBI,YAAY,CAACJ,UAAnC;MAEA,IAAId,mBAAmB,IAAIS,EAAE,CAACC,OAA9B,EAAuC;QACrCD,EAAE,CAACC,OAAH,CAAWY,SAAX,CAAqBC,GAArB,CAAyBvB,mBAAzB;MACD;MAEDwB,qBAAqB,CAAC,YAAK;QACzBf,EAAE,CAACM,KAAH,CAASV,YAAT,IAAyBe,MAAzB;MACD,CAFoB,CAArB;IAGD,CA/BI;IAiCLK,UAAU,EAAEC,WAjCP;IAkCLC,cAAc,EAAED,WAlCX;IAoCLE,KAAK,WAALA,KAAKA,CAAEnB,EAAF,EAAuB;MAC1BA,EAAE,CAACG,aAAH,GAAAC,eAAA;QACEC,UAAU,EAAE,EADK;QAEjBE,QAAQ,EAAEP,EAAE,CAACM,KAAH,CAASC;MAFF,GAGhBX,YAAD,EAAgBI,EAAE,CAACM,KAAH,CAASV,YAAT,EAHlB;MAMAI,EAAE,CAACM,KAAH,CAASC,QAAT,GAAoB,QAApB;MACAP,EAAE,CAACM,KAAH,CAASV,YAAT,OAAAE,MAAA,CAA4BE,EAAE,CAACH,cAAD,CAAgB,OAA9C;MACA,KAAKG,EAAE,CAACY,YAAR,CAT0B,CASL;;MAErBG,qBAAqB,CAAC;QAAA,OAAOf,EAAE,CAACM,KAAH,CAASV,YAAT,IAAyB,GAAjC;MAAA,EAArB;IACD,CAhDI;IAkDLwB,UAlDK,EAkDLA,UAlDK;IAmDLC,cAAc,EAAED;EAnDX,CAAP;EAsDA,SAASA,UAATA,CAAqBpB,EAArB,EAA0C;IACxC,IAAIT,mBAAmB,IAAIS,EAAE,CAACC,OAA9B,EAAuC;MACrCD,EAAE,CAACC,OAAH,CAAWY,SAAX,CAAqBS,MAArB,CAA4B/B,mBAA5B;IACD;IACD0B,WAAW,CAACjB,EAAD,CAAX;EACD;EAED,SAASiB,WAATA,CAAsBjB,EAAtB,EAA2C;IACzC,IAAMuB,IAAI,GAAGvB,EAAE,CAACG,aAAH,CAAkBP,YAAlB,CAAb;IACAI,EAAE,CAACM,KAAH,CAASC,QAAT,GAAoBP,EAAE,CAACG,aAAH,CAAkBI,QAAtC;IACA,IAAIgB,IAAI,IAAI,IAAZ,EAAkBvB,EAAE,CAACM,KAAH,CAASV,YAAT,IAAyB2B,IAAzB;IAClB,OAAOvB,EAAE,CAACG,aAAV;EACD;AACF", "ignoreList": []}]}