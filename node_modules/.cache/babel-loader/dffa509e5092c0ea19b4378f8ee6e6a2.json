{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VIcon/VIcon.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VIcon/VIcon.js", "mtime": 1757335238296}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["BindsAttrs", "Colorable", "Sizeable", "Themeable", "convertToUnit", "keys", "remapInternalIcon", "<PERSON><PERSON>", "mixins", "SIZE_MAP", "isFontAwesome5", "iconType", "_context", "_someInstanceProperty", "call", "val", "_includesInstanceProperty", "isSvgPath", "icon", "test", "length", "VIcon", "extend", "name", "props", "dense", "Boolean", "disabled", "left", "right", "size", "Number", "String", "tag", "type", "required", "computed", "medium", "hasClickListener", "listeners$", "click", "methods", "getIcon", "_context2", "iconName", "$slots", "_trimInstanceProperty", "text", "getSize", "_context3", "sizes", "xSmall", "small", "large", "xLarge", "explicitSize", "_findInstanceProperty", "key", "getDefaultData", "staticClass", "attrs", "_objectSpread", "undefined", "attrs$", "on", "getSvgWrapperData", "fontSize", "wrapperData", "style", "height", "width", "applyColors", "data", "themeClasses", "setTextColor", "color", "renderFontIcon", "h", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delimiterIndex", "_indexOfInstanceProperty", "isMaterialIcon", "push", "_sliceInstanceProperty", "renderSvgIcon", "svgData", "xmlns", "viewBox", "role", "d", "renderSvgIconComponent", "component", "nativeOn", "render", "$_wrapperFor", "functional", "_ref", "children", "domProps", "textContent", "innerHTML"], "sources": ["../../../src/components/VIcon/VIcon.ts"], "sourcesContent": ["import './VIcon.sass'\n\n// Mixins\nimport BindsAttrs from '../../mixins/binds-attrs'\nimport Colorable from '../../mixins/colorable'\nimport Sizeable from '../../mixins/sizeable'\nimport Themeable from '../../mixins/themeable'\n\n// Util\nimport { convertToUnit, keys, remapInternalIcon } from '../../util/helpers'\n\n// Types\nimport Vue, { CreateElement, VNode, VNodeChildren, VNodeData } from 'vue'\nimport mixins from '../../util/mixins'\nimport { VuetifyIcon, VuetifyIconComponent } from 'vuetify/types/services/icons'\n\nenum SIZE_MAP {\n  xSmall = '12px',\n  small = '16px',\n  default = '24px',\n  medium = '28px',\n  large = '36px',\n  xLarge = '40px'\n}\n\nfunction isFontAwesome5 (iconType: string): boolean {\n  return ['fas', 'far', 'fal', 'fab', 'fad', 'fak'].some(val => iconType.includes(val))\n}\n\nfunction isSvgPath (icon: string): boolean {\n  return (/^[mzlhvcsqta]\\s*[-+.0-9][^mlhvzcsqta]+/i.test(icon) && /[\\dz]$/i.test(icon) && icon.length > 4)\n}\n\nconst VIcon = mixins(\n  BindsAttrs,\n  Colorable,\n  Sizeable,\n  Themeable\n  /* @vue/component */\n).extend({\n  name: 'v-icon',\n\n  props: {\n    dense: Boolean,\n    disabled: Boolean,\n    left: Boolean,\n    right: Boolean,\n    size: [Number, String],\n    tag: {\n      type: String,\n      required: false,\n      default: 'i',\n    },\n  },\n\n  computed: {\n    medium () {\n      return false\n    },\n    hasClickListener (): boolean {\n      return Boolean(\n        this.listeners$.click || this.listeners$['!click']\n      )\n    },\n  },\n\n  methods: {\n    getIcon (): VuetifyIcon {\n      let iconName = ''\n      if (this.$slots.default) iconName = this.$slots.default[0].text!.trim()\n\n      return remapInternalIcon(this, iconName)\n    },\n    getSize (): string | undefined {\n      const sizes = {\n        xSmall: this.xSmall,\n        small: this.small,\n        medium: this.medium,\n        large: this.large,\n        xLarge: this.xLarge,\n      }\n\n      const explicitSize = keys(sizes).find(key => sizes[key])\n\n      return (\n        (explicitSize && SIZE_MAP[explicitSize]) || convertToUnit(this.size)\n      )\n    },\n    // Component data for both font icon and SVG wrapper span\n    getDefaultData (): VNodeData {\n      return {\n        staticClass: 'v-icon notranslate',\n        class: {\n          'v-icon--disabled': this.disabled,\n          'v-icon--left': this.left,\n          'v-icon--link': this.hasClickListener,\n          'v-icon--right': this.right,\n          'v-icon--dense': this.dense,\n        },\n        attrs: {\n          'aria-hidden': !this.hasClickListener,\n          disabled: this.hasClickListener && this.disabled,\n          type: this.hasClickListener ? 'button' : undefined,\n          ...this.attrs$,\n        },\n        on: this.listeners$,\n      }\n    },\n    getSvgWrapperData () {\n      const fontSize = this.getSize()\n      const wrapperData = {\n        ...this.getDefaultData(),\n        style: fontSize ? {\n          fontSize,\n          height: fontSize,\n          width: fontSize,\n        } : undefined,\n      }\n      this.applyColors(wrapperData)\n\n      return wrapperData\n    },\n    applyColors (data: VNodeData): void {\n      data.class = { ...data.class, ...this.themeClasses }\n      this.setTextColor(this.color, data)\n    },\n    renderFontIcon (icon: string, h: CreateElement): VNode {\n      const newChildren: VNodeChildren = []\n      const data = this.getDefaultData()\n\n      let iconType = 'material-icons'\n      // Material Icon delimiter is _\n      // https://material.io/icons/\n      const delimiterIndex = icon.indexOf('-')\n      const isMaterialIcon = delimiterIndex <= -1\n\n      if (isMaterialIcon) {\n        // Material icon uses ligatures.\n        newChildren.push(icon)\n      } else {\n        iconType = icon.slice(0, delimiterIndex)\n        if (isFontAwesome5(iconType)) iconType = ''\n      }\n\n      data.class[iconType] = true\n      data.class[icon] = !isMaterialIcon\n\n      const fontSize = this.getSize()\n      if (fontSize) data.style = { fontSize }\n\n      this.applyColors(data)\n\n      return h(this.hasClickListener ? 'button' : this.tag, data, newChildren)\n    },\n    renderSvgIcon (icon: string, h: CreateElement): VNode {\n      const svgData: VNodeData = {\n        class: 'v-icon__svg',\n        attrs: {\n          xmlns: 'http://www.w3.org/2000/svg',\n          viewBox: '0 0 24 24',\n          role: 'img',\n          'aria-hidden': true,\n        },\n      }\n\n      const size = this.getSize()\n      if (size) {\n        svgData.style = {\n          fontSize: size,\n          height: size,\n          width: size,\n        }\n      }\n\n      return h(this.hasClickListener ? 'button' : 'span', this.getSvgWrapperData(), [\n        h('svg', svgData, [\n          h('path', {\n            attrs: {\n              d: icon,\n            },\n          }),\n        ]),\n      ])\n    },\n    renderSvgIconComponent (\n      icon: VuetifyIconComponent,\n      h: CreateElement\n    ): VNode {\n      const data: VNodeData = {\n        class: {\n          'v-icon__component': true,\n        },\n      }\n\n      const size = this.getSize()\n      if (size) {\n        data.style = {\n          fontSize: size,\n          height: size,\n          width: size,\n        }\n      }\n\n      this.applyColors(data)\n\n      const component = icon.component\n      data.props = icon.props\n      data.nativeOn = data.on\n\n      return h(this.hasClickListener ? 'button' : 'span', this.getSvgWrapperData(), [\n        h(component, data),\n      ])\n    },\n  },\n\n  render (h: CreateElement): VNode {\n    const icon = this.getIcon()\n\n    if (typeof icon === 'string') {\n      if (isSvgPath(icon)) {\n        return this.renderSvgIcon(icon, h)\n      }\n      return this.renderFontIcon(icon, h)\n    }\n\n    return this.renderSvgIconComponent(icon, h)\n  },\n})\n\nexport default Vue.extend({\n  name: 'v-icon',\n\n  $_wrapperFor: VIcon,\n\n  functional: true,\n\n  render (h, { data, children }): VNode {\n    let iconName = ''\n\n    // Support usage of v-text and v-html\n    if (data.domProps) {\n      iconName = data.domProps.textContent ||\n        data.domProps.innerHTML ||\n        iconName\n\n      // Remove nodes so it doesn't\n      // overwrite our changes\n      delete data.domProps.textContent\n      delete data.domProps.innerHTML\n    }\n\n    return h(VIcon, data, iconName ? [iconName] : children)\n  },\n})\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,0CAAP,C,CAEA;;AACA,OAAOA,UAAP,MAAuB,0BAAvB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,QAAP,MAAqB,uBAArB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,SAASC,aAAT,EAAwBC,IAAxB,EAA8BC,iBAA9B,QAAuD,oBAAvD,C,CAEA;;AACA,OAAOC,GAAP,MAAoE,KAApE;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAGA,IAAKC,QAAL;AAAA,WAAKA,QAAL,EAAa;EACXA,QAAA;EACAA,QAAA;EACAA,QAAA;EACAA,QAAA;EACAA,QAAA;EACAA,QAAA;AACD,CAPD,EAAKA,QAAQ,KAARA,QAAQ,MAAb;AASA,SAASC,cAATA,CAAyBC,QAAzB,EAAyC;EAAA,IAAAC,QAAA;EACvC,OAAOC,qBAAA,CAAAD,QAAA,IAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,GAAAE,IAAA,CAAAF,QAAA,EAAgD,UAAAG,GAAG;IAAA,OAAIC,yBAAA,CAAAL,QAAQ,EAAAG,IAAA,CAARH,QAAQ,EAAUI,GAAlB,CAAvD;EAAA,EAAP;AACD;AAED,SAASE,SAATA,CAAoBC,IAApB,EAAgC;EAC9B,OAAQ,0CAA0CC,IAA1C,CAA+CD,IAA/C,KAAwD,UAAUC,IAAV,CAAeD,IAAf,CAAxD,IAAgFA,IAAI,CAACE,MAAL,GAAc,CAAtG;AACD;AAED,IAAMC,KAAK,GAAGb,MAAM,CAClBR,UADkB,EAElBC,SAFkB,EAGlBC,QAHkB,EAIlBC;AACA,oBALkB,CAAN,CAMZmB,MANY,CAML;EACPC,IAAI,EAAE,QADC;EAGPC,KAAK,EAAE;IACLC,KAAK,EAAEC,OADF;IAELC,QAAQ,EAAED,OAFL;IAGLE,IAAI,EAAEF,OAHD;IAILG,KAAK,EAAEH,OAJF;IAKLI,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CALD;IAMLC,GAAG,EAAE;MACHC,IAAI,EAAEF,MADH;MAEHG,QAAQ,EAAE,KAFP;MAGH,WAAS;IAHN;EANA,CAHA;EAgBPC,QAAQ,EAAE;IACRC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO,KAAP;IACD,CAHO;IAIRC,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,OAAOZ,OAAO,CACZ,KAAKa,UAAL,CAAgBC,KAAhB,IAAyB,KAAKD,UAAL,CAAgB,QAAhB,CADb,CAAd;IAGD;EARO,CAhBH;EA2BPE,OAAO,EAAE;IACPC,OAAO,WAAPA,OAAOA,CAAA;MAAA,IAAAC,SAAA;MACL,IAAIC,QAAQ,GAAG,EAAf;MACA,IAAI,KAAKC,MAAL,WAAJ,EAAyBD,QAAQ,GAAGE,qBAAA,CAAAH,SAAA,QAAKE,MAAL,YAAoB,CAApB,EAAuBE,IAAvB,EAAAjC,IAAA,CAAA6B,SAAA,CAAX;MAEzB,OAAOrC,iBAAiB,CAAC,IAAD,EAAOsC,QAAP,CAAxB;IACD,CANM;IAOPI,OAAO,WAAPA,OAAOA,CAAA;MAAA,IAAAC,SAAA;MACL,IAAMC,KAAK,GAAG;QACZC,MAAM,EAAE,KAAKA,MADD;QAEZC,KAAK,EAAE,KAAKA,KAFA;QAGZf,MAAM,EAAE,KAAKA,MAHD;QAIZgB,KAAK,EAAE,KAAKA,KAJA;QAKZC,MAAM,EAAE,KAAKA;MALD,CAAd;MAQA,IAAMC,YAAY,GAAGC,qBAAA,CAAAP,SAAA,GAAA5C,IAAI,CAAC6C,KAAD,CAAJ,EAAApC,IAAA,CAAAmC,SAAA,EAAiB,UAAAQ,GAAG;QAAA,OAAIP,KAAK,CAACO,GAAD,CAA7B;MAAA,EAArB;MAEA,OACGF,YAAY,IAAI9C,QAAQ,CAAC8C,YAAD,CAAzB,IAA4CnD,aAAa,CAAC,KAAK0B,IAAN,CAD3D;IAGD,CArBM;IAsBP;IACA4B,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO;QACLC,WAAW,EAAE,oBADR;QAEL,SAAO;UACL,oBAAoB,KAAKhC,QADpB;UAEL,gBAAgB,KAAKC,IAFhB;UAGL,gBAAgB,KAAKU,gBAHhB;UAIL,iBAAiB,KAAKT,KAJjB;UAKL,iBAAiB,KAAKJ;QALjB,CAFF;QASLmC,KAAK,EAAAC,aAAA;UACH,eAAe,CAAC,KAAKvB,gBADhB;UAELX,QAAQ,EAAE,KAAKW,gBAAL,IAAyB,KAAKX,QAFnC;UAGLO,IAAI,EAAE,KAAKI,gBAAL,GAAwB,QAAxB,GAAmCwB;QAHpC,GAIF,KAAKC,MAAA,CAbL;QAeLC,EAAE,EAAE,KAAKzB;MAfJ,CAAP;IAiBD,CAzCM;IA0CP0B,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IAAMC,QAAQ,GAAG,KAAKlB,OAAL,EAAjB;MACA,IAAMmB,WAAW,GAAAN,aAAA,CAAAA,aAAA,KACZ,KAAKH,cAAL,EADe;QAElBU,KAAK,EAAEF,QAAQ,GAAG;UAChBA,QADgB,EAChBA,QADgB;UAEhBG,MAAM,EAAEH,QAFQ;UAGhBI,KAAK,EAAEJ;QAHS,CAAH,GAIXJ;MAAA,EANN;MAQA,KAAKS,WAAL,CAAiBJ,WAAjB;MAEA,OAAOA,WAAP;IACD,CAvDM;IAwDPI,WAAW,WAAXA,WAAWA,CAAEC,IAAF,EAAiB;MAC1BA,IAAI,SAAJ,GAAAX,aAAA,CAAAA,aAAA,KAAkBW,IAAI,SAAT,GAAoB,KAAKC,YAAA,CAAtC;MACA,KAAKC,YAAL,CAAkB,KAAKC,KAAvB,EAA8BH,IAA9B;IACD,CA3DM;IA4DPI,cAAc,WAAdA,cAAcA,CAAE1D,IAAF,EAAgB2D,CAAhB,EAAgC;MAC5C,IAAMC,WAAW,GAAkB,EAAnC;MACA,IAAMN,IAAI,GAAG,KAAKd,cAAL,EAAb;MAEA,IAAI/C,QAAQ,GAAG,gBAAf,CAJ4C,CAK5C;MACA;;MACA,IAAMoE,cAAc,GAAGC,wBAAA,CAAA9D,IAAI,EAAAJ,IAAA,CAAJI,IAAI,EAAS,GAAb,CAAvB;MACA,IAAM+D,cAAc,GAAGF,cAAc,IAAI,CAAC,CAA1C;MAEA,IAAIE,cAAJ,EAAoB;QAClB;QACAH,WAAW,CAACI,IAAZ,CAAiBhE,IAAjB;MACD,CAHD,MAGO;QACLP,QAAQ,GAAGwE,sBAAA,CAAAjE,IAAI,EAAAJ,IAAA,CAAJI,IAAI,EAAO,CAAX,EAAc6D,cAAd,CAAX;QACA,IAAIrE,cAAc,CAACC,QAAD,CAAlB,EAA8BA,QAAQ,GAAG,EAAX;MAC/B;MAED6D,IAAI,SAAJ,CAAW7D,QAAX,IAAuB,IAAvB;MACA6D,IAAI,SAAJ,CAAWtD,IAAX,IAAmB,CAAC+D,cAApB;MAEA,IAAMf,QAAQ,GAAG,KAAKlB,OAAL,EAAjB;MACA,IAAIkB,QAAJ,EAAcM,IAAI,CAACJ,KAAL,GAAa;QAAEF,QAAA,EAAAA;MAAF,CAAb;MAEd,KAAKK,WAAL,CAAiBC,IAAjB;MAEA,OAAOK,CAAC,CAAC,KAAKvC,gBAAL,GAAwB,QAAxB,GAAmC,KAAKL,GAAzC,EAA8CuC,IAA9C,EAAoDM,WAApD,CAAR;IACD,CAvFM;IAwFPM,aAAa,WAAbA,aAAaA,CAAElE,IAAF,EAAgB2D,CAAhB,EAAgC;MAC3C,IAAMQ,OAAO,GAAc;QACzB,SAAO,aADkB;QAEzBzB,KAAK,EAAE;UACL0B,KAAK,EAAE,4BADF;UAELC,OAAO,EAAE,WAFJ;UAGLC,IAAI,EAAE,KAHD;UAIL,eAAe;QAJV;MAFkB,CAA3B;MAUA,IAAM1D,IAAI,GAAG,KAAKkB,OAAL,EAAb;MACA,IAAIlB,IAAJ,EAAU;QACRuD,OAAO,CAACjB,KAAR,GAAgB;UACdF,QAAQ,EAAEpC,IADI;UAEduC,MAAM,EAAEvC,IAFM;UAGdwC,KAAK,EAAExC;QAHO,CAAhB;MAKD;MAED,OAAO+C,CAAC,CAAC,KAAKvC,gBAAL,GAAwB,QAAxB,GAAmC,MAApC,EAA4C,KAAK2B,iBAAL,EAA5C,EAAsE,CAC5EY,CAAC,CAAC,KAAD,EAAQQ,OAAR,EAAiB,CAChBR,CAAC,CAAC,MAAD,EAAS;QACRjB,KAAK,EAAE;UACL6B,CAAC,EAAEvE;QADE;MADC,CAAT,CADe,CAAjB,CAD2E,CAAtE,CAAR;IASD,CArHM;IAsHPwE,sBAAsB,WAAtBA,sBAAsBA,CACpBxE,IADoB,EAEpB2D,CAFoB,EAEJ;MAEhB,IAAML,IAAI,GAAc;QACtB,SAAO;UACL,qBAAqB;QADhB;MADe,CAAxB;MAMA,IAAM1C,IAAI,GAAG,KAAKkB,OAAL,EAAb;MACA,IAAIlB,IAAJ,EAAU;QACR0C,IAAI,CAACJ,KAAL,GAAa;UACXF,QAAQ,EAAEpC,IADC;UAEXuC,MAAM,EAAEvC,IAFG;UAGXwC,KAAK,EAAExC;QAHI,CAAb;MAKD;MAED,KAAKyC,WAAL,CAAiBC,IAAjB;MAEA,IAAMmB,SAAS,GAAGzE,IAAI,CAACyE,SAAvB;MACAnB,IAAI,CAAChD,KAAL,GAAaN,IAAI,CAACM,KAAlB;MACAgD,IAAI,CAACoB,QAAL,GAAgBpB,IAAI,CAACR,EAArB;MAEA,OAAOa,CAAC,CAAC,KAAKvC,gBAAL,GAAwB,QAAxB,GAAmC,MAApC,EAA4C,KAAK2B,iBAAL,EAA5C,EAAsE,CAC5EY,CAAC,CAACc,SAAD,EAAYnB,IAAZ,CAD2E,CAAtE,CAAR;IAGD;EAlJM,CA3BF;EAgLPqB,MAAM,WAANA,MAAMA,CAAEhB,CAAF,EAAkB;IACtB,IAAM3D,IAAI,GAAG,KAAKwB,OAAL,EAAb;IAEA,IAAI,OAAOxB,IAAP,KAAgB,QAApB,EAA8B;MAC5B,IAAID,SAAS,CAACC,IAAD,CAAb,EAAqB;QACnB,OAAO,KAAKkE,aAAL,CAAmBlE,IAAnB,EAAyB2D,CAAzB,CAAP;MACD;MACD,OAAO,KAAKD,cAAL,CAAoB1D,IAApB,EAA0B2D,CAA1B,CAAP;IACD;IAED,OAAO,KAAKa,sBAAL,CAA4BxE,IAA5B,EAAkC2D,CAAlC,CAAP;EACD;AA3LM,CANK,CAAd;AAoMA,eAAetE,GAAG,CAACe,MAAJ,CAAW;EACxBC,IAAI,EAAE,QADkB;EAGxBuE,YAAY,EAAEzE,KAHU;EAKxB0E,UAAU,EAAE,IALY;EAOxBF,MAAM,WAANA,MAAMA,CAAEhB,CAAF,EAAAmB,IAAA,EAAuB;IAAA,IAAhBxB,IAAF,GAAAwB,IAAA,CAAExB,IAAF;MAAQyB,QAAA,GAAAD,IAAA,CAAAC,QAAA;IACjB,IAAIrD,QAAQ,GAAG,EAAf,CAD2B,CAG3B;;IACA,IAAI4B,IAAI,CAAC0B,QAAT,EAAmB;MACjBtD,QAAQ,GAAG4B,IAAI,CAAC0B,QAAL,CAAcC,WAAd,IACT3B,IAAI,CAAC0B,QAAL,CAAcE,SADL,IAETxD,QAFF,CADiB,CAKjB;MACA;;MACA,OAAO4B,IAAI,CAAC0B,QAAL,CAAcC,WAArB;MACA,OAAO3B,IAAI,CAAC0B,QAAL,CAAcE,SAArB;IACD;IAED,OAAOvB,CAAC,CAACxD,KAAD,EAAQmD,IAAR,EAAc5B,QAAQ,GAAG,CAACA,QAAD,CAAH,GAAgBqD,QAAtC,CAAR;EACD;AAvBuB,CAAX,CAAf", "ignoreList": []}]}