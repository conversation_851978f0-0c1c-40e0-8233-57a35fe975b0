{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/es.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/es.js", "mtime": 1757335235651}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/es.ts"], "sourcesContent": ["export default {\n  badge: '<PERSON>la<PERSON>',\n  close: 'Cerrar',\n  dataIterator: {\n    noResultsText: 'Ningún elemento coincide con la búsqueda',\n    loadingText: 'Cargando...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Filas por página:',\n    ariaLabel: {\n      sortDescending: 'Orden descendente.',\n      sortAscending: 'Orden ascendente.',\n      sortNone: 'Sin ordenar.',\n      activateNone: 'Pulse para quitar orden.',\n      activateDescending: 'Pulse para ordenar de forma descendente.',\n      activateAscending: 'Pulse para ordenar de forma ascendente.',\n    },\n    sortBy: 'Ordenado por',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Elementos por página:',\n    itemsPerPageAll: 'Todos',\n    nextPage: 'Página siguiente',\n    prevPage: 'Página anterior',\n    firstPage: 'Primera página',\n    lastPage: 'Última página',\n    pageText: '{0}-{1} de {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} seleccionados',\n    nextMonthAriaLabel: 'Próximo mes',\n    nextYearAriaLabel: 'Próximo año',\n    prevMonthAriaLabel: 'Mes anterior',\n    prevYearAriaLabel: 'Año anterior',\n  },\n  noDataText: 'No hay datos disponibles',\n  carousel: {\n    prev: 'Visual anterior',\n    next: 'Visual siguiente',\n    ariaLabel: {\n      delimiter: 'Visual {0} de {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} más',\n  },\n  fileInput: {\n    counter: '{0} archivos',\n    counterSize: '{0} archivos ({1} en total)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navegación de paginación',\n      next: 'Página siguiente',\n      previous: 'Página anterior',\n      page: 'Ir a la página {0}',\n      currentPage: 'Página actual, página {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Puntuación {0} de {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,OADM;EAEbC,KAAK,EAAE,QAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,0CADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,mBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,oBADP;MAETC,aAAa,EAAE,mBAFN;MAGTC,QAAQ,EAAE,cAHD;MAITC,YAAY,EAAE,0BAJL;MAKTC,kBAAkB,EAAE,0CALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,uBADR;IAEVU,eAAe,EAAE,OAFP;IAGVC,QAAQ,EAAE,kBAHA;IAIVC,QAAQ,EAAE,iBAJA;IAKVC,SAAS,EAAE,gBALD;IAMVC,QAAQ,EAAE,eANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,mBADL;IAEVC,kBAAkB,EAAE,aAFV;IAGVC,iBAAiB,EAAE,aAHT;IAIVC,kBAAkB,EAAE,cAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,0BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,iBADE;IAERC,IAAI,EAAE,kBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,cADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,0BADA;MAETX,IAAI,EAAE,kBAFG;MAGTY,QAAQ,EAAE,iBAHD;MAITC,IAAI,EAAE,oBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}