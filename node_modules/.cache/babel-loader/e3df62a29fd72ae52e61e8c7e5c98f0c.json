{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/core-js-stable/instance/splice.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/core-js-stable/instance/splice.js", "mtime": 1757335235282}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:bW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCJjb3JlLWpzLXB1cmUvc3RhYmxlL2luc3RhbmNlL3NwbGljZSIpOw=="}, {"version": 3, "names": ["module", "exports", "require"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/core-js-stable/instance/splice.js"], "sourcesContent": ["module.exports = require(\"core-js-pure/stable/instance/splice\");"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,qCAAqC,CAAC", "ignoreList": []}]}