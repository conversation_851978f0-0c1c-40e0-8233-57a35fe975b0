{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/mobile/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/mobile/index.js", "mtime": 1757335237149}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["deprecate", "<PERSON><PERSON>", "extend", "name", "props", "mobileBreakpoint", "type", "Number", "String", "default", "$vuetify", "breakpoint", "undefined", "validator", "v", "_context", "isNaN", "_includesInstanceProperty", "call", "computed", "isMobile", "_this$$vuetify$breakp", "mobile", "width", "mobileWidth", "_parseInt", "isNumber", "created", "$attrs", "hasOwnProperty"], "sources": ["../../../src/mixins/mobile/index.ts"], "sourcesContent": ["// Types\nimport { BreakpointName } from 'vuetify/types/services/breakpoint'\nimport { deprecate } from '../../util/console'\nimport Vue, { PropType } from 'vue'\n\n/* @vue/component */\nexport default Vue.extend({\n  name: 'mobile',\n\n  props: {\n    mobileBreakpoint: {\n      type: [Number, String] as PropType<number | BreakpointName>,\n      default (): number | BreakpointName | undefined {\n        // Avoid destroying unit\n        // tests for users\n        return this.$vuetify\n          ? this.$vuetify.breakpoint.mobileBreakpoint\n          : undefined\n      },\n      validator: v => (\n        !isNaN(Number(v)) ||\n        ['xs', 'sm', 'md', 'lg', 'xl'].includes(String(v))\n      ),\n    },\n  },\n\n  computed: {\n    isMobile (): boolean {\n      const {\n        mobile,\n        width,\n        name,\n        mobileBreakpoint,\n      } = this.$vuetify.breakpoint\n\n      // Check if local mobileBreakpoint matches\n      // the application's mobileBreakpoint\n      if (mobileBreakpoint === this.mobileBreakpoint) return mobile\n\n      const mobileWidth = parseInt(this.mobileBreakpoint, 10)\n      const isNumber = !isNaN(mobileWidth)\n\n      return isNumber\n        ? width < mobileWidth\n        : name === this.mobileBreakpoint\n    },\n  },\n\n  created () {\n    /* istanbul ignore next */\n    if (this.$attrs.hasOwnProperty('mobile-break-point')) {\n      deprecate('mobile-break-point', 'mobile-breakpoint', this)\n    }\n  },\n})\n"], "mappings": ";;;;AAEA,SAASA,SAAT,QAA0B,oBAA1B;AACA,OAAOC,GAAP,MAA8B,KAA9B;AAEA;;AACA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,QADkB;EAGxBC,KAAK,EAAE;IACLC,gBAAgB,EAAE;MAChBC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADU;MAAA,oBAEhBC,QAAOA,CAAA;QACL;QACA;QACA,OAAO,KAAKC,QAAL,GACH,KAAKA,QAAL,CAAcC,UAAd,CAAyBN,gBADtB,GAEHO,SAFJ;MAGD,CARe;MAShBC,SAAS,EAAE,SAAXA,SAASA,CAAEC,CAAC;QAAA,IAAAC,QAAA;QAAA,OACV,CAACC,KAAK,CAACT,MAAM,CAACO,CAAD,CAAP,CAAN,IACAG,yBAAA,CAAAF,QAAA,IAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,IAAzB,GAAAG,IAAA,CAAAH,QAAA,EAAwCP,MAAM,CAACM,CAAD,CAA9C;MAAA;IAXc;EADb,CAHiB;EAoBxBK,QAAQ,EAAE;IACRC,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAAC,qBAAA,GAKI,KAAKX,QAAL,CAAcC,UALlB;QACEW,MADI,GAAAD,qBAAA,CACJC,MADI;QAEJC,KAFI,GAAAF,qBAAA,CAEJE,KAFI;QAGJpB,IAHI,GAAAkB,qBAAA,CAGJlB,IAHI;QAIJE,gBAAA,GAAAgB,qBAAA,CAAAhB,gBAAA,CALI,CAQN;MACA;;MACA,IAAIA,gBAAgB,KAAK,KAAKA,gBAA9B,EAAgD,OAAOiB,MAAP;MAEhD,IAAME,WAAW,GAAGC,SAAA,CAAS,KAAKpB,gBAAN,EAAwB,EAAxB,CAA5B;MACA,IAAMqB,QAAQ,GAAG,CAACV,KAAK,CAACQ,WAAD,CAAvB;MAEA,OAAOE,QAAQ,GACXH,KAAK,GAAGC,WADG,GAEXrB,IAAI,KAAK,KAAKE,gBAFlB;IAGD;EAnBO,CApBc;EA0CxBsB,OAAO,WAAPA,OAAOA,CAAA;IACL;IACA,IAAI,KAAKC,MAAL,CAAYC,cAAZ,CAA2B,oBAA3B,CAAJ,EAAsD;MACpD7B,SAAS,CAAC,oBAAD,EAAuB,mBAAvB,EAA4C,IAA5C,CAAT;IACD;EACF;AA/CuB,CAAX,CAAf", "ignoreList": []}]}