{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VLabel/VLabel.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VLabel/VLabel.js", "mtime": 1757335238368}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Colorable", "Themeable", "functionalThemeClasses", "mixins", "convertToUnit", "extend", "name", "functional", "props", "absolute", "Boolean", "color", "type", "String", "disabled", "focused", "left", "Number", "right", "value", "render", "h", "ctx", "children", "listeners", "data", "staticClass", "_objectSpread", "attrs", "on", "style", "position", "ref", "options", "methods", "setTextColor"], "sources": ["../../../src/components/VLabel/VLabel.ts"], "sourcesContent": ["// Styles\nimport './VLabel.sass'\n\n// Mixins\nimport Colorable from '../../mixins/colorable'\nimport Themeable, { functionalThemeClasses } from '../../mixins/themeable'\n\n// Types\nimport { VNode } from 'vue'\nimport mixins from '../../util/mixins'\n\n// Helpers\nimport { convertToUnit } from '../../util/helpers'\n\n/* @vue/component */\nexport default mixins(Themeable).extend({\n  name: 'v-label',\n\n  functional: true,\n\n  props: {\n    absolute: Boolean,\n    color: {\n      type: String,\n      default: 'primary',\n    },\n    disabled: Boolean,\n    focused: Boolean,\n    for: String,\n    left: {\n      type: [Number, String],\n      default: 0,\n    },\n    right: {\n      type: [Number, String],\n      default: 'auto',\n    },\n    value: Boolean,\n  },\n\n  render (h, ctx): VNode {\n    const { children, listeners, props } = ctx\n    const data = {\n      staticClass: 'v-label',\n      class: {\n        'v-label--active': props.value,\n        'v-label--is-disabled': props.disabled,\n        ...functionalThemeClasses(ctx),\n      },\n      attrs: {\n        for: props.for,\n        'aria-hidden': !props.for,\n      },\n      on: listeners,\n      style: {\n        left: convertToUnit(props.left),\n        right: convertToUnit(props.right),\n        position: props.absolute ? 'absolute' : 'relative',\n      },\n      ref: 'label',\n    }\n\n    return h('label', Colorable.options.methods.setTextColor(props.focused && props.color, data), children)\n  },\n})\n"], "mappings": ";;AAAA;AACA,OAAO,4CAAP,C,CAEA;;AACA,OAAOA,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,IAAoBC,sBAApB,QAAkD,wBAAlD;AAIA,OAAOC,MAAP,MAAmB,mBAAnB,C,CAEA;;AACA,SAASC,aAAT,QAA8B,oBAA9B;AAEA;;AACA,eAAeD,MAAM,CAACF,SAAD,CAAN,CAAkBI,MAAlB,CAAyB;EACtCC,IAAI,EAAE,SADgC;EAGtCC,UAAU,EAAE,IAH0B;EAKtCC,KAAK,EAAE;IACLC,QAAQ,EAAEC,OADL;IAELC,KAAK,EAAE;MACLC,IAAI,EAAEC,MADD;MAEL,WAAS;IAFJ,CAFF;IAMLC,QAAQ,EAAEJ,OANL;IAOLK,OAAO,EAAEL,OAPJ;IAQL,OAAKG,MARA;IASLG,IAAI,EAAE;MACJJ,IAAI,EAAE,CAACK,MAAD,EAASJ,MAAT,CADF;MAEJ,WAAS;IAFL,CATD;IAaLK,KAAK,EAAE;MACLN,IAAI,EAAE,CAACK,MAAD,EAASJ,MAAT,CADD;MAEL,WAAS;IAFJ,CAbF;IAiBLM,KAAK,EAAET;EAjBF,CAL+B;EAyBtCU,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAKC,GAAL,EAAQ;IACZ,IAAQC,QAAF,GAAiCD,GAAvC,CAAQC,QAAF;MAAYC,SAAZ,GAAiCF,GAAvC,CAAkBE,SAAZ;MAAuBhB,KAAA,GAAUc,GAAvC,CAA6Bd,KAAA;IAC7B,IAAMiB,IAAI,GAAG;MACXC,WAAW,EAAE,SADF;MAEX,SAAAC,aAAA;QACE,mBAAmBnB,KAAK,CAACW,KADpB;QAEL,wBAAwBX,KAAK,CAACM;MAFzB,GAGFZ,sBAAsB,CAACoB,GAAD,EALhB;MAOXM,KAAK,EAAE;QACL,OAAKpB,KAAK,OADL;QAEL,eAAe,CAACA,KAAK;MAFhB,CAPI;MAWXqB,EAAE,EAAEL,SAXO;MAYXM,KAAK,EAAE;QACLd,IAAI,EAAEZ,aAAa,CAACI,KAAK,CAACQ,IAAP,CADd;QAELE,KAAK,EAAEd,aAAa,CAACI,KAAK,CAACU,KAAP,CAFf;QAGLa,QAAQ,EAAEvB,KAAK,CAACC,QAAN,GAAiB,UAAjB,GAA8B;MAHnC,CAZI;MAiBXuB,GAAG,EAAE;IAjBM,CAAb;IAoBA,OAAOX,CAAC,CAAC,OAAD,EAAUrB,SAAS,CAACiC,OAAV,CAAkBC,OAAlB,CAA0BC,YAA1B,CAAuC3B,KAAK,CAACO,OAAN,IAAiBP,KAAK,CAACG,KAA9D,EAAqEc,IAArE,CAAV,EAAsFF,QAAtF,CAAR;EACD;AAhDqC,CAAzB,CAAf", "ignoreList": []}]}