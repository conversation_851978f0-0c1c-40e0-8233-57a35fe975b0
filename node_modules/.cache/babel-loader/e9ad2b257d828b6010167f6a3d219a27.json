{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/iterableToArray.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/iterableToArray.js", "mtime": 1757335237644}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9TeW1ib2wgZnJvbSAiY29yZS1qcy1wdXJlL2ZlYXR1cmVzL3N5bWJvbC9pbmRleC5qcyI7CmltcG9ydCBfZ2V0SXRlcmF0b3JNZXRob2QgZnJvbSAiY29yZS1qcy1wdXJlL2ZlYXR1cmVzL2dldC1pdGVyYXRvci1tZXRob2QuanMiOwppbXBvcnQgX0FycmF5JGZyb20gZnJvbSAiY29yZS1qcy1wdXJlL2ZlYXR1cmVzL2FycmF5L2Zyb20uanMiOwpmdW5jdGlvbiBfaXRlcmFibGVUb0FycmF5KHIpIHsKICBpZiAoInVuZGVmaW5lZCIgIT0gdHlwZW9mIF9TeW1ib2wgJiYgbnVsbCAhPSBfZ2V0SXRlcmF0b3JNZXRob2QocikgfHwgbnVsbCAhPSByWyJAQGl0ZXJhdG9yIl0pIHJldHVybiBfQXJyYXkkZnJvbShyKTsKfQpleHBvcnQgeyBfaXRlcmFibGVUb0FycmF5IGFzIGRlZmF1bHQgfTs="}, {"version": 3, "names": ["_Symbol", "_getIteratorMethod", "_Array$from", "_iterableToArray", "r", "default"], "sources": ["/Users/<USER>/Downloads/web-demo/node_modules/@babel/runtime-corejs3/helpers/esm/iterableToArray.js"], "sourcesContent": ["import _Symbol from \"core-js-pure/features/symbol/index.js\";\nimport _getIteratorMethod from \"core-js-pure/features/get-iterator-method.js\";\nimport _Array$from from \"core-js-pure/features/array/from.js\";\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof _Symbol && null != _getIteratorMethod(r) || null != r[\"@@iterator\"]) return _Array$from(r);\n}\nexport { _iterableToArray as default };"], "mappings": "AAAA,OAAOA,OAAO,MAAM,uCAAuC;AAC3D,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,SAASC,gBAAgBA,CAACC,CAAC,EAAE;EAC3B,IAAI,WAAW,IAAI,OAAOJ,OAAO,IAAI,IAAI,IAAIC,kBAAkB,CAACG,CAAC,CAAC,IAAI,IAAI,IAAIA,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOF,WAAW,CAACE,CAAC,CAAC;AACtH;AACA,SAASD,gBAAgB,IAAIE,OAAO", "ignoreList": []}]}