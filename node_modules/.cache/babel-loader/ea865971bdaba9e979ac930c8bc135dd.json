{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/sizeable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/sizeable/index.js", "mtime": 1757335237248}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnNtYWxsLmpzIjsKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwpleHBvcnQgZGVmYXVsdCBWdWUuZXh0ZW5kKHsKICBuYW1lOiAnc2l6ZWFibGUnLAogIHByb3BzOiB7CiAgICBsYXJnZTogQm9vbGVhbiwKICAgIHNtYWxsOiBCb29sZWFuLAogICAgeExhcmdlOiBCb29sZWFuLAogICAgeFNtYWxsOiBCb29sZWFuCiAgfSwKICBjb21wdXRlZDogewogICAgbWVkaXVtOiBmdW5jdGlvbiBtZWRpdW0oKSB7CiAgICAgIHJldHVybiBCb29sZWFuKCF0aGlzLnhTbWFsbCAmJiAhdGhpcy5zbWFsbCAmJiAhdGhpcy5sYXJnZSAmJiAhdGhpcy54TGFyZ2UpOwogICAgfSwKICAgIHNpemVhYmxlQ2xhc3NlczogZnVuY3Rpb24gc2l6ZWFibGVDbGFzc2VzKCkgewogICAgICByZXR1cm4gewogICAgICAgICd2LXNpemUtLXgtc21hbGwnOiB0aGlzLnhTbWFsbCwKICAgICAgICAndi1zaXplLS1zbWFsbCc6IHRoaXMuc21hbGwsCiAgICAgICAgJ3Ytc2l6ZS0tZGVmYXVsdCc6IHRoaXMubWVkaXVtLAogICAgICAgICd2LXNpemUtLWxhcmdlJzogdGhpcy5sYXJnZSwKICAgICAgICAndi1zaXplLS14LWxhcmdlJzogdGhpcy54TGFyZ2UKICAgICAgfTsKICAgIH0KICB9Cn0pOw=="}, {"version": 3, "names": ["<PERSON><PERSON>", "extend", "name", "props", "large", "Boolean", "small", "xLarge", "xSmall", "computed", "medium", "sizeableClasses"], "sources": ["../../../src/mixins/sizeable/index.ts"], "sourcesContent": ["import Vue from 'vue'\n\nexport default Vue.extend({\n  name: 'sizeable',\n\n  props: {\n    large: Boolean,\n    small: Boolean,\n    xLarge: Boolean,\n    xSmall: Boolean,\n  },\n\n  computed: {\n    medium (): boolean {\n      return Boolean(\n        !this.xSmall &&\n        !this.small &&\n        !this.large &&\n        !this.xLarge\n      )\n    },\n    sizeableClasses (): object {\n      return {\n        'v-size--x-small': this.xSmall,\n        'v-size--small': this.small,\n        'v-size--default': this.medium,\n        'v-size--large': this.large,\n        'v-size--x-large': this.xLarge,\n      }\n    },\n  },\n})\n"], "mappings": ";AAAA,OAAOA,GAAP,MAAgB,KAAhB;AAEA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,UADkB;EAGxBC,KAAK,EAAE;IACLC,KAAK,EAAEC,OADF;IAELC,KAAK,EAAED,OAFF;IAGLE,MAAM,EAAEF,OAHH;IAILG,MAAM,EAAEH;EAJH,CAHiB;EAUxBI,QAAQ,EAAE;IACRC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAOL,OAAO,CACZ,CAAC,KAAKG,MAAN,IACA,CAAC,KAAKF,KADN,IAEA,CAAC,KAAKF,KAFN,IAGA,CAAC,KAAKG,MAJM,CAAd;IAMD,CARO;IASRI,eAAe,WAAfA,eAAeA,CAAA;MACb,OAAO;QACL,mBAAmB,KAAKH,MADnB;QAEL,iBAAiB,KAAKF,KAFjB;QAGL,mBAAmB,KAAKI,MAHnB;QAIL,iBAAiB,KAAKN,KAJjB;QAKL,mBAAmB,KAAKG;MALnB,CAAP;IAOD;EAjBO;AAVc,CAAX,CAAf", "ignoreList": []}]}