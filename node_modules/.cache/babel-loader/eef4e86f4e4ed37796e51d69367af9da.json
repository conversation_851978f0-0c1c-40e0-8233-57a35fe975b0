{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VListItemAction.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VList/VListItemAction.js", "mtime": 1757335238381}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9maWx0ZXJJbnN0YW5jZVByb3BlcnR5IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvaW5zdGFuY2UvZmlsdGVyIjsKLy8gVHlwZXMKaW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwovKiBAdnVlL2NvbXBvbmVudCAqLwoKZXhwb3J0IGRlZmF1bHQgVnVlLmV4dGVuZCh7CiAgbmFtZTogJ3YtbGlzdC1pdGVtLWFjdGlvbicsCiAgZnVuY3Rpb25hbDogdHJ1ZSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoLCBfcmVmKSB7CiAgICB2YXIgZGF0YSA9IF9yZWYuZGF0YSwKICAgICAgX3JlZiRjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sCiAgICAgIGNoaWxkcmVuID0gX3JlZiRjaGlsZHJlbiA9PT0gdm9pZCAwID8gW10gOiBfcmVmJGNoaWxkcmVuOwogICAgZGF0YS5zdGF0aWNDbGFzcyA9IGRhdGEuc3RhdGljQ2xhc3MgPyAidi1saXN0LWl0ZW1fX2FjdGlvbiAiLmNvbmNhdChkYXRhLnN0YXRpY0NsYXNzKSA6ICd2LWxpc3QtaXRlbV9fYWN0aW9uJzsKICAgIHZhciBmaWx0ZXJlZENoaWxkID0gX2ZpbHRlckluc3RhbmNlUHJvcGVydHkoY2hpbGRyZW4pLmNhbGwoY2hpbGRyZW4sIGZ1bmN0aW9uIChWTm9kZSkgewogICAgICByZXR1cm4gVk5vZGUuaXNDb21tZW50ID09PSBmYWxzZSAmJiBWTm9kZS50ZXh0ICE9PSAnICc7CiAgICB9KTsKICAgIGlmIChmaWx0ZXJlZENoaWxkLmxlbmd0aCA+IDEpIGRhdGEuc3RhdGljQ2xhc3MgKz0gJyB2LWxpc3QtaXRlbV9fYWN0aW9uLS1zdGFjayc7CiAgICByZXR1cm4gaCgnZGl2JywgZGF0YSwgY2hpbGRyZW4pOwogIH0KfSk7"}, {"version": 3, "names": ["<PERSON><PERSON>", "extend", "name", "functional", "render", "h", "_ref", "data", "_ref$children", "children", "staticClass", "concat", "<PERSON><PERSON><PERSON><PERSON>", "_filterInstanceProperty", "call", "VNode", "isComment", "text", "length"], "sources": ["../../../src/components/VList/VListItemAction.ts"], "sourcesContent": ["// Types\nimport Vue, { VNode } from 'vue'\n\n/* @vue/component */\nexport default Vue.extend({\n  name: 'v-list-item-action',\n\n  functional: true,\n\n  render (h, { data, children = [] }): VNode {\n    data.staticClass = data.staticClass ? `v-list-item__action ${data.staticClass}` : 'v-list-item__action'\n    const filteredChild = children.filter(VNode => {\n      return VNode.isComment === false && VNode.text !== ' '\n    })\n    if (filteredChild.length > 1) data.staticClass += ' v-list-item__action--stack'\n\n    return h('div', data, children)\n  },\n})\n"], "mappings": ";AAAA;AACA,OAAOA,GAAP,MAA2B,KAA3B;AAEA;;AACA,eAAeA,GAAG,CAACC,MAAJ,CAAW;EACxBC,IAAI,EAAE,oBADkB;EAGxBC,UAAU,EAAE,IAHY;EAKxBC,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAAC,IAAA,EAA4B;IAAA,IAArBC,IAAF,GAAAD,IAAA,CAAEC,IAAF;MAAAC,aAAA,GAAAF,IAAA,CAAQG,QAAQ;MAARA,QAAQ,GAAAD,aAAA,cAAG,KAAAA,aAAA;IAC5BD,IAAI,CAACG,WAAL,GAAmBH,IAAI,CAACG,WAAL,0BAAAC,MAAA,CAA0CJ,IAAI,CAACG,WAAW,IAAK,qBAAlF;IACA,IAAME,aAAa,GAAGC,uBAAA,CAAAJ,QAAQ,EAAAK,IAAA,CAARL,QAAQ,EAAQ,UAAAM,KAAK,EAAG;MAC5C,OAAOA,KAAK,CAACC,SAAN,KAAoB,KAApB,IAA6BD,KAAK,CAACE,IAAN,KAAe,GAAnD;IACD,CAFqB,CAAtB;IAGA,IAAIL,aAAa,CAACM,MAAd,GAAuB,CAA3B,EAA8BX,IAAI,CAACG,WAAL,IAAoB,6BAApB;IAE9B,OAAOL,CAAC,CAAC,KAAD,EAAQE,IAAR,EAAcE,QAAd,CAAR;EACD;AAbuB,CAAX,CAAf", "ignoreList": []}]}