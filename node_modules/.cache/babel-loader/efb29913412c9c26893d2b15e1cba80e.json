{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMenu/VMenu.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VMenu/VMenu.js", "mtime": 1757335238544}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9kZWZpbmVQcm9wZXJ0eSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL2RlZmluZVByb3BlcnR5LmpzIjsKaW1wb3J0IF90b0NvbnN1bWFibGVBcnJheSBmcm9tICIvVXNlcnMvaHVhbmdjb25ncWlhbmcvRG93bmxvYWRzL3dlYi1kZW1vL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS1jb3JlanMzL2hlbHBlcnMvZXNtL3RvQ29uc3VtYWJsZUFycmF5LmpzIjsKaW1wb3J0IF9vYmplY3RTcHJlYWQgZnJvbSAiL1VzZXJzL2h1YW5nY29uZ3FpYW5nL0Rvd25sb2Fkcy93ZWItZGVtby9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9oZWxwZXJzL2VzbS9vYmplY3RTcHJlYWQyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBfcGFyc2VGbG9hdCBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL3BhcnNlLWZsb2F0IjsKaW1wb3J0IF9wYXJzZUludCBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL3BhcnNlLWludCI7CmltcG9ydCBfZmluZEluc3RhbmNlUHJvcGVydHkgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS9maW5kIjsKaW1wb3J0IF9pbmRleE9mSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL2luZGV4LW9mIjsKaW1wb3J0IF9BcnJheSRmcm9tIGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvYXJyYXkvZnJvbSI7CmltcG9ydCBfY29uY2F0SW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL2NvbmNhdCI7CmltcG9ydCBfdHJpbUluc3RhbmNlUHJvcGVydHkgZnJvbSAiQGJhYmVsL3J1bnRpbWUtY29yZWpzMy9jb3JlLWpzLXN0YWJsZS9pbnN0YW5jZS90cmltIjsKaW1wb3J0IF9zZXRUaW1lb3V0IGZyb20gIkBiYWJlbC9ydW50aW1lLWNvcmVqczMvY29yZS1qcy1zdGFibGUvc2V0LXRpbWVvdXQiOwppbXBvcnQgX2luY2x1ZGVzSW5zdGFuY2VQcm9wZXJ0eSBmcm9tICJAYmFiZWwvcnVudGltZS1jb3JlanMzL2NvcmUtanMtc3RhYmxlL2luc3RhbmNlL2luY2x1ZGVzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yLmpzIjsKLy8gU3R5bGVzCmltcG9ydCAiLi4vLi4vLi4vc3JjL2NvbXBvbmVudHMvVk1lbnUvVk1lbnUuc2FzcyI7IC8vIENvbXBvbmVudHMKCmltcG9ydCB7IFZUaGVtZVByb3ZpZGVyIH0gZnJvbSAnLi4vVlRoZW1lUHJvdmlkZXInOyAvLyBNaXhpbnMKCmltcG9ydCBBY3RpdmF0YWJsZSBmcm9tICcuLi8uLi9taXhpbnMvYWN0aXZhdGFibGUnOwppbXBvcnQgRGVsYXlhYmxlIGZyb20gJy4uLy4uL21peGlucy9kZWxheWFibGUnOwppbXBvcnQgRGVwZW5kZW50IGZyb20gJy4uLy4uL21peGlucy9kZXBlbmRlbnQnOwppbXBvcnQgTWVudWFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL21lbnVhYmxlJzsKaW1wb3J0IFJldHVybmFibGUgZnJvbSAnLi4vLi4vbWl4aW5zL3JldHVybmFibGUnOwppbXBvcnQgUm91bmRhYmxlIGZyb20gJy4uLy4uL21peGlucy9yb3VuZGFibGUnOwppbXBvcnQgVGhlbWVhYmxlIGZyb20gJy4uLy4uL21peGlucy90aGVtZWFibGUnOyAvLyBEaXJlY3RpdmVzCgppbXBvcnQgQ2xpY2tPdXRzaWRlIGZyb20gJy4uLy4uL2RpcmVjdGl2ZXMvY2xpY2stb3V0c2lkZSc7CmltcG9ydCBSZXNpemUgZnJvbSAnLi4vLi4vZGlyZWN0aXZlcy9yZXNpemUnOyAvLyBVdGlsaXRpZXMKCmltcG9ydCBtaXhpbnMgZnJvbSAnLi4vLi4vdXRpbC9taXhpbnMnOwppbXBvcnQgeyByZW1vdmVkIH0gZnJvbSAnLi4vLi4vdXRpbC9jb25zb2xlJzsKaW1wb3J0IHsgY29udmVydFRvVW5pdCwga2V5Q29kZXMgfSBmcm9tICcuLi8uLi91dGlsL2hlbHBlcnMnOwppbXBvcnQgZ29UbyBmcm9tICcuLi8uLi9zZXJ2aWNlcy9nb3RvJzsKdmFyIGJhc2VNaXhpbnMgPSBtaXhpbnMoRGVwZW5kZW50LCBEZWxheWFibGUsIFJldHVybmFibGUsIFJvdW5kYWJsZSwgVGhlbWVhYmxlLCBNZW51YWJsZSk7Ci8qIEB2dWUvY29tcG9uZW50ICovCgpleHBvcnQgZGVmYXVsdCBiYXNlTWl4aW5zLmV4dGVuZCh7CiAgbmFtZTogJ3YtbWVudScsCiAgZGlyZWN0aXZlczogewogICAgQ2xpY2tPdXRzaWRlOiBDbGlja091dHNpZGUsCiAgICBSZXNpemU6IFJlc2l6ZQogIH0sCiAgcHJvdmlkZTogZnVuY3Rpb24gcHJvdmlkZSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzSW5NZW51OiB0cnVlLAogICAgICAvLyBQYXNzIHRoZW1lIHRocm91Z2ggdG8gZGVmYXVsdCBzbG90CiAgICAgIHRoZW1lOiB0aGlzLnRoZW1lCiAgICB9OwogIH0sCiAgcHJvcHM6IHsKICAgIGF1dG86IEJvb2xlYW4sCiAgICBjbG9zZU9uQ2xpY2s6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgImRlZmF1bHQiOiB0cnVlCiAgICB9LAogICAgY2xvc2VPbkNvbnRlbnRDbGljazogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICAiZGVmYXVsdCI6IHRydWUKICAgIH0sCiAgICBkaXNhYmxlZDogQm9vbGVhbiwKICAgIGRpc2FibGVLZXlzOiBCb29sZWFuLAogICAgbWF4SGVpZ2h0OiB7CiAgICAgIHR5cGU6IFtOdW1iZXIsIFN0cmluZ10sCiAgICAgICJkZWZhdWx0IjogJ2F1dG8nCiAgICB9LAogICAgb2Zmc2V0WDogQm9vbGVhbiwKICAgIG9mZnNldFk6IEJvb2xlYW4sCiAgICBvcGVuT25Ib3ZlcjogQm9vbGVhbiwKICAgIG9yaWdpbjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgICJkZWZhdWx0IjogJ3RvcCBsZWZ0JwogICAgfSwKICAgIHRyYW5zaXRpb246IHsKICAgICAgdHlwZTogW0Jvb2xlYW4sIFN0cmluZ10sCiAgICAgICJkZWZhdWx0IjogJ3YtbWVudS10cmFuc2l0aW9uJwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGNhbGN1bGF0ZWRUb3BBdXRvOiAwLAogICAgICBkZWZhdWx0T2Zmc2V0OiA4LAogICAgICBoYXNKdXN0Rm9jdXNlZDogZmFsc2UsCiAgICAgIGxpc3RJbmRleDogLTEsCiAgICAgIHJlc2l6ZVRpbWVvdXQ6IDAsCiAgICAgIHNlbGVjdGVkSW5kZXg6IG51bGwsCiAgICAgIHRpbGVzOiBbXQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBhY3RpdmVUaWxlOiBmdW5jdGlvbiBhY3RpdmVUaWxlKCkgewogICAgICByZXR1cm4gdGhpcy50aWxlc1t0aGlzLmxpc3RJbmRleF07CiAgICB9LAogICAgY2FsY3VsYXRlZExlZnQ6IGZ1bmN0aW9uIGNhbGN1bGF0ZWRMZWZ0KCkgewogICAgICB2YXIgbWVudVdpZHRoID0gTWF0aC5tYXgodGhpcy5kaW1lbnNpb25zLmNvbnRlbnQud2lkdGgsIF9wYXJzZUZsb2F0KHRoaXMuY2FsY3VsYXRlZE1pbldpZHRoKSk7CiAgICAgIGlmICghdGhpcy5hdXRvKSByZXR1cm4gdGhpcy5jYWxjTGVmdChtZW51V2lkdGgpIHx8ICcwJzsKICAgICAgcmV0dXJuIGNvbnZlcnRUb1VuaXQodGhpcy5jYWxjWE92ZXJmbG93KHRoaXMuY2FsY0xlZnRBdXRvKCksIG1lbnVXaWR0aCkpIHx8ICcwJzsKICAgIH0sCiAgICBjYWxjdWxhdGVkTWF4SGVpZ2h0OiBmdW5jdGlvbiBjYWxjdWxhdGVkTWF4SGVpZ2h0KCkgewogICAgICB2YXIgaGVpZ2h0ID0gdGhpcy5hdXRvID8gJzIwMHB4JyA6IGNvbnZlcnRUb1VuaXQodGhpcy5tYXhIZWlnaHQpOwogICAgICByZXR1cm4gaGVpZ2h0IHx8ICcwJzsKICAgIH0sCiAgICBjYWxjdWxhdGVkTWF4V2lkdGg6IGZ1bmN0aW9uIGNhbGN1bGF0ZWRNYXhXaWR0aCgpIHsKICAgICAgcmV0dXJuIGNvbnZlcnRUb1VuaXQodGhpcy5tYXhXaWR0aCkgfHwgJzAnOwogICAgfSwKICAgIGNhbGN1bGF0ZWRNaW5XaWR0aDogZnVuY3Rpb24gY2FsY3VsYXRlZE1pbldpZHRoKCkgewogICAgICBpZiAodGhpcy5taW5XaWR0aCkgewogICAgICAgIHJldHVybiBjb252ZXJ0VG9Vbml0KHRoaXMubWluV2lkdGgpIHx8ICcwJzsKICAgICAgfQogICAgICB2YXIgbWluV2lkdGggPSBNYXRoLm1pbih0aGlzLmRpbWVuc2lvbnMuYWN0aXZhdG9yLndpZHRoICsgTnVtYmVyKHRoaXMubnVkZ2VXaWR0aCkgKyAodGhpcy5hdXRvID8gMTYgOiAwKSwgTWF0aC5tYXgodGhpcy5wYWdlV2lkdGggLSAyNCwgMCkpOwogICAgICB2YXIgY2FsY3VsYXRlZE1heFdpZHRoID0gaXNOYU4oX3BhcnNlSW50KHRoaXMuY2FsY3VsYXRlZE1heFdpZHRoKSkgPyBtaW5XaWR0aCA6IF9wYXJzZUludCh0aGlzLmNhbGN1bGF0ZWRNYXhXaWR0aCk7CiAgICAgIHJldHVybiBjb252ZXJ0VG9Vbml0KE1hdGgubWluKGNhbGN1bGF0ZWRNYXhXaWR0aCwgbWluV2lkdGgpKSB8fCAnMCc7CiAgICB9LAogICAgY2FsY3VsYXRlZFRvcDogZnVuY3Rpb24gY2FsY3VsYXRlZFRvcCgpIHsKICAgICAgdmFyIHRvcCA9ICF0aGlzLmF1dG8gPyB0aGlzLmNhbGNUb3AoKSA6IGNvbnZlcnRUb1VuaXQodGhpcy5jYWxjWU92ZXJmbG93KHRoaXMuY2FsY3VsYXRlZFRvcEF1dG8pKTsKICAgICAgcmV0dXJuIHRvcCB8fCAnMCc7CiAgICB9LAogICAgaGFzQ2xpY2thYmxlVGlsZXM6IGZ1bmN0aW9uIGhhc0NsaWNrYWJsZVRpbGVzKCkgewogICAgICB2YXIgX2NvbnRleHQ7CiAgICAgIHJldHVybiBCb29sZWFuKF9maW5kSW5zdGFuY2VQcm9wZXJ0eShfY29udGV4dCA9IHRoaXMudGlsZXMpLmNhbGwoX2NvbnRleHQsIGZ1bmN0aW9uICh0aWxlKSB7CiAgICAgICAgcmV0dXJuIHRpbGUudGFiSW5kZXggPiAtMTsKICAgICAgfSkpOwogICAgfSwKICAgIHN0eWxlczogZnVuY3Rpb24gc3R5bGVzKCkgewogICAgICByZXR1cm4gewogICAgICAgIG1heEhlaWdodDogdGhpcy5jYWxjdWxhdGVkTWF4SGVpZ2h0LAogICAgICAgIG1pbldpZHRoOiB0aGlzLmNhbGN1bGF0ZWRNaW5XaWR0aCwKICAgICAgICBtYXhXaWR0aDogdGhpcy5jYWxjdWxhdGVkTWF4V2lkdGgsCiAgICAgICAgdG9wOiB0aGlzLmNhbGN1bGF0ZWRUb3AsCiAgICAgICAgbGVmdDogdGhpcy5jYWxjdWxhdGVkTGVmdCwKICAgICAgICB0cmFuc2Zvcm1PcmlnaW46IHRoaXMub3JpZ2luLAogICAgICAgIHpJbmRleDogdGhpcy56SW5kZXggfHwgdGhpcy5hY3RpdmVaSW5kZXgKICAgICAgfTsKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBpc0FjdGl2ZTogZnVuY3Rpb24gaXNBY3RpdmUodmFsKSB7CiAgICAgIGlmICghdmFsKSB0aGlzLmxpc3RJbmRleCA9IC0xOwogICAgfSwKICAgIGlzQ29udGVudEFjdGl2ZTogZnVuY3Rpb24gaXNDb250ZW50QWN0aXZlKHZhbCkgewogICAgICB0aGlzLmhhc0p1c3RGb2N1c2VkID0gdmFsOwogICAgfSwKICAgIGxpc3RJbmRleDogZnVuY3Rpb24gbGlzdEluZGV4KG5leHQsIHByZXYpIHsKICAgICAgaWYgKG5leHQgaW4gdGhpcy50aWxlcykgewogICAgICAgIHZhciB0aWxlID0gdGhpcy50aWxlc1tuZXh0XTsKICAgICAgICB0aWxlLmNsYXNzTGlzdC5hZGQoJ3YtbGlzdC1pdGVtLS1oaWdobGlnaHRlZCcpOwogICAgICAgIHZhciBzY3JvbGxUb3AgPSB0aGlzLiRyZWZzLmNvbnRlbnQuc2Nyb2xsVG9wOwogICAgICAgIHZhciBjb250ZW50SGVpZ2h0ID0gdGhpcy4kcmVmcy5jb250ZW50LmNsaWVudEhlaWdodDsKICAgICAgICBpZiAoc2Nyb2xsVG9wID4gdGlsZS5vZmZzZXRUb3AgLSA4KSB7CiAgICAgICAgICBnb1RvKHRpbGUub2Zmc2V0VG9wIC0gdGlsZS5jbGllbnRIZWlnaHQsIHsKICAgICAgICAgICAgYXBwT2Zmc2V0OiBmYWxzZSwKICAgICAgICAgICAgZHVyYXRpb246IDMwMCwKICAgICAgICAgICAgY29udGFpbmVyOiB0aGlzLiRyZWZzLmNvbnRlbnQKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSBpZiAoc2Nyb2xsVG9wICsgY29udGVudEhlaWdodCA8IHRpbGUub2Zmc2V0VG9wICsgdGlsZS5jbGllbnRIZWlnaHQgKyA4KSB7CiAgICAgICAgICBnb1RvKHRpbGUub2Zmc2V0VG9wIC0gY29udGVudEhlaWdodCArIHRpbGUuY2xpZW50SGVpZ2h0ICogMiwgewogICAgICAgICAgICBhcHBPZmZzZXQ6IGZhbHNlLAogICAgICAgICAgICBkdXJhdGlvbjogMzAwLAogICAgICAgICAgICBjb250YWluZXI6IHRoaXMuJHJlZnMuY29udGVudAogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICAgIHByZXYgaW4gdGhpcy50aWxlcyAmJiB0aGlzLnRpbGVzW3ByZXZdLmNsYXNzTGlzdC5yZW1vdmUoJ3YtbGlzdC1pdGVtLS1oaWdobGlnaHRlZCcpOwogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovCiAgICBpZiAodGhpcy4kYXR0cnMuaGFzT3duUHJvcGVydHkoJ2Z1bGwtd2lkdGgnKSkgewogICAgICByZW1vdmVkKCdmdWxsLXdpZHRoJywgdGhpcyk7CiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pc0FjdGl2ZSAmJiB0aGlzLmNhbGxBY3RpdmF0ZSgpOwogIH0sCiAgbWV0aG9kczogewogICAgYWN0aXZhdGU6IGZ1bmN0aW9uIGFjdGl2YXRlKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICAvLyBVcGRhdGUgY29vcmRpbmF0ZXMgYW5kIGRpbWVuc2lvbnMgb2YgbWVudQogICAgICAvLyBhbmQgaXRzIGFjdGl2YXRvcgogICAgICB0aGlzLnVwZGF0ZURpbWVuc2lvbnMoKTsgLy8gU3RhcnQgdGhlIHRyYW5zaXRpb24KCiAgICAgIHJlcXVlc3RBbmltYXRpb25GcmFtZShmdW5jdGlvbiAoKSB7CiAgICAgICAgLy8gT25jZSB0cmFuc2l0aW9uaW5nLCBjYWxjdWxhdGUgc2Nyb2xsIGFuZCB0b3AgcG9zaXRpb24KICAgICAgICBfdGhpcy5zdGFydFRyYW5zaXRpb24oKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIGlmIChfdGhpcy4kcmVmcy5jb250ZW50KSB7CiAgICAgICAgICAgIF90aGlzLmNhbGN1bGF0ZWRUb3BBdXRvID0gX3RoaXMuY2FsY1RvcEF1dG8oKTsKICAgICAgICAgICAgX3RoaXMuYXV0byAmJiAoX3RoaXMuJHJlZnMuY29udGVudC5zY3JvbGxUb3AgPSBfdGhpcy5jYWxjU2Nyb2xsUG9zaXRpb24oKSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGNhbGNTY3JvbGxQb3NpdGlvbjogZnVuY3Rpb24gY2FsY1Njcm9sbFBvc2l0aW9uKCkgewogICAgICB2YXIgJGVsID0gdGhpcy4kcmVmcy5jb250ZW50OwogICAgICB2YXIgYWN0aXZlVGlsZSA9ICRlbC5xdWVyeVNlbGVjdG9yKCcudi1saXN0LWl0ZW0tLWFjdGl2ZScpOwogICAgICB2YXIgbWF4U2Nyb2xsVG9wID0gJGVsLnNjcm9sbEhlaWdodCAtICRlbC5vZmZzZXRIZWlnaHQ7CiAgICAgIHJldHVybiBhY3RpdmVUaWxlID8gTWF0aC5taW4obWF4U2Nyb2xsVG9wLCBNYXRoLm1heCgwLCBhY3RpdmVUaWxlLm9mZnNldFRvcCAtICRlbC5vZmZzZXRIZWlnaHQgLyAyICsgYWN0aXZlVGlsZS5vZmZzZXRIZWlnaHQgLyAyKSkgOiAkZWwuc2Nyb2xsVG9wOwogICAgfSwKICAgIGNhbGNMZWZ0QXV0bzogZnVuY3Rpb24gY2FsY0xlZnRBdXRvKCkgewogICAgICByZXR1cm4gX3BhcnNlSW50KHRoaXMuZGltZW5zaW9ucy5hY3RpdmF0b3IubGVmdCAtIHRoaXMuZGVmYXVsdE9mZnNldCAqIDIpOwogICAgfSwKICAgIGNhbGNUb3BBdXRvOiBmdW5jdGlvbiBjYWxjVG9wQXV0bygpIHsKICAgICAgdmFyIF9jb250ZXh0MjsKICAgICAgdmFyICRlbCA9IHRoaXMuJHJlZnMuY29udGVudDsKICAgICAgdmFyIGFjdGl2ZVRpbGUgPSAkZWwucXVlcnlTZWxlY3RvcignLnYtbGlzdC1pdGVtLS1hY3RpdmUnKTsKICAgICAgaWYgKCFhY3RpdmVUaWxlKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gbnVsbDsKICAgICAgfQogICAgICBpZiAodGhpcy5vZmZzZXRZIHx8ICFhY3RpdmVUaWxlKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuY29tcHV0ZWRUb3A7CiAgICAgIH0KICAgICAgdGhpcy5zZWxlY3RlZEluZGV4ID0gX2luZGV4T2ZJbnN0YW5jZVByb3BlcnR5KF9jb250ZXh0MiA9IF9BcnJheSRmcm9tKHRoaXMudGlsZXMpKS5jYWxsKF9jb250ZXh0MiwgYWN0aXZlVGlsZSk7CiAgICAgIHZhciB0aWxlRGlzdGFuY2VGcm9tTWVudVRvcCA9IGFjdGl2ZVRpbGUub2Zmc2V0VG9wIC0gdGhpcy5jYWxjU2Nyb2xsUG9zaXRpb24oKTsKICAgICAgdmFyIGZpcnN0VGlsZU9mZnNldFRvcCA9ICRlbC5xdWVyeVNlbGVjdG9yKCcudi1saXN0LWl0ZW0nKS5vZmZzZXRUb3A7CiAgICAgIHJldHVybiB0aGlzLmNvbXB1dGVkVG9wIC0gdGlsZURpc3RhbmNlRnJvbU1lbnVUb3AgLSBmaXJzdFRpbGVPZmZzZXRUb3AgLSAxOwogICAgfSwKICAgIGNoYW5nZUxpc3RJbmRleDogZnVuY3Rpb24gY2hhbmdlTGlzdEluZGV4KGUpIHsKICAgICAgLy8gRm9yIGluZmluaXRlIHNjcm9sbCBhbmQgYXV0b2NvbXBsZXRlLCByZS1ldmFsdWF0ZSBjaGlsZHJlbgogICAgICB0aGlzLmdldFRpbGVzKCk7CiAgICAgIGlmICghdGhpcy5pc0FjdGl2ZSB8fCAhdGhpcy5oYXNDbGlja2FibGVUaWxlcykgewogICAgICAgIHJldHVybjsKICAgICAgfSBlbHNlIGlmIChlLmtleUNvZGUgPT09IGtleUNvZGVzLnRhYikgewogICAgICAgIHRoaXMuaXNBY3RpdmUgPSBmYWxzZTsKICAgICAgICByZXR1cm47CiAgICAgIH0gZWxzZSBpZiAoZS5rZXlDb2RlID09PSBrZXlDb2Rlcy5kb3duKSB7CiAgICAgICAgdGhpcy5uZXh0VGlsZSgpOwogICAgICB9IGVsc2UgaWYgKGUua2V5Q29kZSA9PT0ga2V5Q29kZXMudXApIHsKICAgICAgICB0aGlzLnByZXZUaWxlKCk7CiAgICAgIH0gZWxzZSBpZiAoZS5rZXlDb2RlID09PSBrZXlDb2Rlcy5lbmQpIHsKICAgICAgICB0aGlzLmxhc3RUaWxlKCk7CiAgICAgIH0gZWxzZSBpZiAoZS5rZXlDb2RlID09PSBrZXlDb2Rlcy5ob21lKSB7CiAgICAgICAgdGhpcy5maXJzdFRpbGUoKTsKICAgICAgfSBlbHNlIGlmIChlLmtleUNvZGUgPT09IGtleUNvZGVzLmVudGVyICYmIHRoaXMubGlzdEluZGV4ICE9PSAtMSkgewogICAgICAgIHRoaXMudGlsZXNbdGhpcy5saXN0SW5kZXhdLmNsaWNrKCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9IC8vIE9uZSBvZiB0aGUgY29uZGl0aW9ucyB3YXMgbWV0LCBwcmV2ZW50IGRlZmF1bHQgYWN0aW9uICgjMjk4OCkKCiAgICAgIGUucHJldmVudERlZmF1bHQoKTsKICAgIH0sCiAgICBjbG9zZUNvbmRpdGlvbmFsOiBmdW5jdGlvbiBjbG9zZUNvbmRpdGlvbmFsKGUpIHsKICAgICAgdmFyIHRhcmdldCA9IGUudGFyZ2V0OwogICAgICByZXR1cm4gdGhpcy5pc0FjdGl2ZSAmJiAhdGhpcy5faXNEZXN0cm95ZWQgJiYgdGhpcy5jbG9zZU9uQ2xpY2sgJiYgIXRoaXMuJHJlZnMuY29udGVudC5jb250YWlucyh0YXJnZXQpOwogICAgfSwKICAgIGdlbkFjdGl2YXRvckF0dHJpYnV0ZXM6IGZ1bmN0aW9uIGdlbkFjdGl2YXRvckF0dHJpYnV0ZXMoKSB7CiAgICAgIHZhciBhdHRyaWJ1dGVzID0gQWN0aXZhdGFibGUub3B0aW9ucy5tZXRob2RzLmdlbkFjdGl2YXRvckF0dHJpYnV0ZXMuY2FsbCh0aGlzKTsKICAgICAgaWYgKHRoaXMuYWN0aXZlVGlsZSAmJiB0aGlzLmFjdGl2ZVRpbGUuaWQpIHsKICAgICAgICByZXR1cm4gX29iamVjdFNwcmVhZChfb2JqZWN0U3ByZWFkKHt9LCBhdHRyaWJ1dGVzKSwge30sIHsKICAgICAgICAgICdhcmlhLWFjdGl2ZWRlc2NlbmRhbnQnOiB0aGlzLmFjdGl2ZVRpbGUuaWQKICAgICAgICB9KTsKICAgICAgfQogICAgICByZXR1cm4gYXR0cmlidXRlczsKICAgIH0sCiAgICBnZW5BY3RpdmF0b3JMaXN0ZW5lcnM6IGZ1bmN0aW9uIGdlbkFjdGl2YXRvckxpc3RlbmVycygpIHsKICAgICAgdmFyIGxpc3RlbmVycyA9IE1lbnVhYmxlLm9wdGlvbnMubWV0aG9kcy5nZW5BY3RpdmF0b3JMaXN0ZW5lcnMuY2FsbCh0aGlzKTsKICAgICAgaWYgKCF0aGlzLmRpc2FibGVLZXlzKSB7CiAgICAgICAgbGlzdGVuZXJzLmtleWRvd24gPSB0aGlzLm9uS2V5RG93bjsKICAgICAgfQogICAgICByZXR1cm4gbGlzdGVuZXJzOwogICAgfSwKICAgIGdlblRyYW5zaXRpb246IGZ1bmN0aW9uIGdlblRyYW5zaXRpb24oKSB7CiAgICAgIHZhciBjb250ZW50ID0gdGhpcy5nZW5Db250ZW50KCk7CiAgICAgIGlmICghdGhpcy50cmFuc2l0aW9uKSByZXR1cm4gY29udGVudDsKICAgICAgcmV0dXJuIHRoaXMuJGNyZWF0ZUVsZW1lbnQoJ3RyYW5zaXRpb24nLCB7CiAgICAgICAgcHJvcHM6IHsKICAgICAgICAgIG5hbWU6IHRoaXMudHJhbnNpdGlvbgogICAgICAgIH0KICAgICAgfSwgW2NvbnRlbnRdKTsKICAgIH0sCiAgICBnZW5EaXJlY3RpdmVzOiBmdW5jdGlvbiBnZW5EaXJlY3RpdmVzKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdmFyIGRpcmVjdGl2ZXMgPSBbewogICAgICAgIG5hbWU6ICdzaG93JywKICAgICAgICB2YWx1ZTogdGhpcy5pc0NvbnRlbnRBY3RpdmUKICAgICAgfV07IC8vIERvIG5vdCBhZGQgY2xpY2sgb3V0c2lkZSBmb3IgaG92ZXIgbWVudQoKICAgICAgaWYgKCF0aGlzLm9wZW5PbkhvdmVyICYmIHRoaXMuY2xvc2VPbkNsaWNrKSB7CiAgICAgICAgZGlyZWN0aXZlcy5wdXNoKHsKICAgICAgICAgIG5hbWU6ICdjbGljay1vdXRzaWRlJywKICAgICAgICAgIHZhbHVlOiB7CiAgICAgICAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIoKSB7CiAgICAgICAgICAgICAgX3RoaXMyLmlzQWN0aXZlID0gZmFsc2U7CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGNsb3NlQ29uZGl0aW9uYWw6IHRoaXMuY2xvc2VDb25kaXRpb25hbCwKICAgICAgICAgICAgaW5jbHVkZTogZnVuY3Rpb24gaW5jbHVkZSgpIHsKICAgICAgICAgICAgICB2YXIgX2NvbnRleHQzOwogICAgICAgICAgICAgIHJldHVybiBfY29uY2F0SW5zdGFuY2VQcm9wZXJ0eShfY29udGV4dDMgPSBbX3RoaXMyLiRlbF0pLmNhbGwoX2NvbnRleHQzLCBfdG9Db25zdW1hYmxlQXJyYXkoX3RoaXMyLmdldE9wZW5EZXBlbmRlbnRFbGVtZW50cygpKSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgICByZXR1cm4gZGlyZWN0aXZlczsKICAgIH0sCiAgICBnZW5Db250ZW50OiBmdW5jdGlvbiBnZW5Db250ZW50KCkgewogICAgICB2YXIgX2NvbnRleHQ0LAogICAgICAgIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciBvcHRpb25zID0gewogICAgICAgIGF0dHJzOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoe30sIHRoaXMuZ2V0U2NvcGVJZEF0dHJzKCkpLCB7fSwgewogICAgICAgICAgcm9sZTogJ3JvbGUnIGluIHRoaXMuJGF0dHJzID8gdGhpcy4kYXR0cnMucm9sZSA6ICdtZW51JwogICAgICAgIH0pLAogICAgICAgIHN0YXRpY0NsYXNzOiAndi1tZW51X19jb250ZW50JywKICAgICAgICAiY2xhc3MiOiBfb2JqZWN0U3ByZWFkKF9vYmplY3RTcHJlYWQoX29iamVjdFNwcmVhZCh7fSwgdGhpcy5yb290VGhlbWVDbGFzc2VzKSwgdGhpcy5yb3VuZGVkQ2xhc3NlcyksIHt9LCBfZGVmaW5lUHJvcGVydHkoewogICAgICAgICAgJ3YtbWVudV9fY29udGVudC0tYXV0byc6IHRoaXMuYXV0bywKICAgICAgICAgICd2LW1lbnVfX2NvbnRlbnQtLWZpeGVkJzogdGhpcy5hY3RpdmF0b3JGaXhlZCwKICAgICAgICAgIG1lbnVhYmxlX19jb250ZW50X19hY3RpdmU6IHRoaXMuaXNBY3RpdmUKICAgICAgICB9LCBfdHJpbUluc3RhbmNlUHJvcGVydHkoX2NvbnRleHQ0ID0gdGhpcy5jb250ZW50Q2xhc3MpLmNhbGwoX2NvbnRleHQ0KSwgdHJ1ZSkpLAogICAgICAgIHN0eWxlOiB0aGlzLnN0eWxlcywKICAgICAgICBkaXJlY3RpdmVzOiB0aGlzLmdlbkRpcmVjdGl2ZXMoKSwKICAgICAgICByZWY6ICdjb250ZW50JywKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKGUpIHsKICAgICAgICAgICAgdmFyIHRhcmdldCA9IGUudGFyZ2V0OwogICAgICAgICAgICBpZiAodGFyZ2V0LmdldEF0dHJpYnV0ZSgnZGlzYWJsZWQnKSkgcmV0dXJuOwogICAgICAgICAgICBpZiAoX3RoaXMzLmNsb3NlT25Db250ZW50Q2xpY2spIF90aGlzMy5pc0FjdGl2ZSA9IGZhbHNlOwogICAgICAgICAgfSwKICAgICAgICAgIGtleWRvd246IHRoaXMub25LZXlEb3duCiAgICAgICAgfQogICAgICB9OwogICAgICBpZiAodGhpcy4kbGlzdGVuZXJzLnNjcm9sbCkgewogICAgICAgIG9wdGlvbnMub24gPSBvcHRpb25zLm9uIHx8IHt9OwogICAgICAgIG9wdGlvbnMub24uc2Nyb2xsID0gdGhpcy4kbGlzdGVuZXJzLnNjcm9sbDsKICAgICAgfQogICAgICBpZiAoIXRoaXMuZGlzYWJsZWQgJiYgdGhpcy5vcGVuT25Ib3ZlcikgewogICAgICAgIG9wdGlvbnMub24gPSBvcHRpb25zLm9uIHx8IHt9OwogICAgICAgIG9wdGlvbnMub24ubW91c2VlbnRlciA9IHRoaXMubW91c2VFbnRlckhhbmRsZXI7CiAgICAgIH0KICAgICAgaWYgKHRoaXMub3Blbk9uSG92ZXIpIHsKICAgICAgICBvcHRpb25zLm9uID0gb3B0aW9ucy5vbiB8fCB7fTsKICAgICAgICBvcHRpb25zLm9uLm1vdXNlbGVhdmUgPSB0aGlzLm1vdXNlTGVhdmVIYW5kbGVyOwogICAgICB9CiAgICAgIHJldHVybiB0aGlzLiRjcmVhdGVFbGVtZW50KCdkaXYnLCBvcHRpb25zLCB0aGlzLmdldENvbnRlbnRTbG90KCkpOwogICAgfSwKICAgIGdldFRpbGVzOiBmdW5jdGlvbiBnZXRUaWxlcygpIHsKICAgICAgaWYgKCF0aGlzLiRyZWZzLmNvbnRlbnQpIHJldHVybjsKICAgICAgdGhpcy50aWxlcyA9IF9BcnJheSRmcm9tKHRoaXMuJHJlZnMuY29udGVudC5xdWVyeVNlbGVjdG9yQWxsKCcudi1saXN0LWl0ZW0sIC52LWRpdmlkZXIsIC52LXN1YmhlYWRlcicpKTsKICAgIH0sCiAgICBtb3VzZUVudGVySGFuZGxlcjogZnVuY3Rpb24gbW91c2VFbnRlckhhbmRsZXIoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLnJ1bkRlbGF5KCdvcGVuJywgZnVuY3Rpb24gKCkgewogICAgICAgIGlmIChfdGhpczQuaGFzSnVzdEZvY3VzZWQpIHJldHVybjsKICAgICAgICBfdGhpczQuaGFzSnVzdEZvY3VzZWQgPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICBtb3VzZUxlYXZlSGFuZGxlcjogZnVuY3Rpb24gbW91c2VMZWF2ZUhhbmRsZXIoZSkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgLy8gUHJldmVudCBhY2NpZGVudGFsIHJlLWFjdGl2YXRpb24KICAgICAgdGhpcy5ydW5EZWxheSgnY2xvc2UnLCBmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIF9hOwogICAgICAgIGlmICgoX2EgPSBfdGhpczUuJHJlZnMuY29udGVudCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNvbnRhaW5zKGUucmVsYXRlZFRhcmdldCkpIHJldHVybjsKICAgICAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXM1LmlzQWN0aXZlID0gZmFsc2U7CiAgICAgICAgICBfdGhpczUuY2FsbERlYWN0aXZhdGUoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgbmV4dFRpbGU6IGZ1bmN0aW9uIG5leHRUaWxlKCkgewogICAgICB2YXIgdGlsZSA9IHRoaXMudGlsZXNbdGhpcy5saXN0SW5kZXggKyAxXTsKICAgICAgaWYgKCF0aWxlKSB7CiAgICAgICAgaWYgKCF0aGlzLnRpbGVzLmxlbmd0aCkgcmV0dXJuOwogICAgICAgIHRoaXMubGlzdEluZGV4ID0gLTE7CiAgICAgICAgdGhpcy5uZXh0VGlsZSgpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmxpc3RJbmRleCsrOwogICAgICBpZiAodGlsZS50YWJJbmRleCA9PT0gLTEpIHRoaXMubmV4dFRpbGUoKTsKICAgIH0sCiAgICBwcmV2VGlsZTogZnVuY3Rpb24gcHJldlRpbGUoKSB7CiAgICAgIHZhciB0aWxlID0gdGhpcy50aWxlc1t0aGlzLmxpc3RJbmRleCAtIDFdOwogICAgICBpZiAoIXRpbGUpIHsKICAgICAgICBpZiAoIXRoaXMudGlsZXMubGVuZ3RoKSByZXR1cm47CiAgICAgICAgdGhpcy5saXN0SW5kZXggPSB0aGlzLnRpbGVzLmxlbmd0aDsKICAgICAgICB0aGlzLnByZXZUaWxlKCk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMubGlzdEluZGV4LS07CiAgICAgIGlmICh0aWxlLnRhYkluZGV4ID09PSAtMSkgdGhpcy5wcmV2VGlsZSgpOwogICAgfSwKICAgIGxhc3RUaWxlOiBmdW5jdGlvbiBsYXN0VGlsZSgpIHsKICAgICAgdmFyIHRpbGUgPSB0aGlzLnRpbGVzW3RoaXMudGlsZXMubGVuZ3RoIC0gMV07CiAgICAgIGlmICghdGlsZSkgcmV0dXJuOwogICAgICB0aGlzLmxpc3RJbmRleCA9IHRoaXMudGlsZXMubGVuZ3RoIC0gMTsKICAgICAgaWYgKHRpbGUudGFiSW5kZXggPT09IC0xKSB0aGlzLnByZXZUaWxlKCk7CiAgICB9LAogICAgZmlyc3RUaWxlOiBmdW5jdGlvbiBmaXJzdFRpbGUoKSB7CiAgICAgIHZhciB0aWxlID0gdGhpcy50aWxlc1swXTsKICAgICAgaWYgKCF0aWxlKSByZXR1cm47CiAgICAgIHRoaXMubGlzdEluZGV4ID0gMDsKICAgICAgaWYgKHRpbGUudGFiSW5kZXggPT09IC0xKSB0aGlzLm5leHRUaWxlKCk7CiAgICB9LAogICAgb25LZXlEb3duOiBmdW5jdGlvbiBvbktleURvd24oZSkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpcywKICAgICAgICBfY29udGV4dDU7CiAgICAgIGlmIChlLmtleUNvZGUgPT09IGtleUNvZGVzLmVzYykgewogICAgICAgIC8vIFdhaXQgZm9yIGRlcGVuZGVudCBlbGVtZW50cyB0byBjbG9zZSBmaXJzdAogICAgICAgIF9zZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzNi5pc0FjdGl2ZSA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICAgIHZhciBhY3RpdmF0b3IgPSB0aGlzLmdldEFjdGl2YXRvcigpOwogICAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHJldHVybiBhY3RpdmF0b3IgJiYgYWN0aXZhdG9yLmZvY3VzKCk7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSBpZiAoIXRoaXMuaXNBY3RpdmUgJiYgX2luY2x1ZGVzSW5zdGFuY2VQcm9wZXJ0eShfY29udGV4dDUgPSBba2V5Q29kZXMudXAsIGtleUNvZGVzLmRvd25dKS5jYWxsKF9jb250ZXh0NSwgZS5rZXlDb2RlKSkgewogICAgICAgIHRoaXMuaXNBY3RpdmUgPSB0cnVlOwogICAgICB9IC8vIEFsbG93IGZvciBpc0FjdGl2ZSB3YXRjaGVyIHRvIGdlbmVyYXRlIHRpbGUgbGlzdAoKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBfdGhpczYuY2hhbmdlTGlzdEluZGV4KGUpOwogICAgICB9KTsKICAgIH0sCiAgICBvblJlc2l6ZTogZnVuY3Rpb24gb25SZXNpemUoKSB7CiAgICAgIGlmICghdGhpcy5pc0FjdGl2ZSkgcmV0dXJuOyAvLyBBY2NvdW50IGZvciBzY3JlZW4gcmVzaXplCiAgICAgIC8vIGFuZCBvcmllbnRhdGlvbiBjaGFuZ2UKICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC1leHByZXNzaW9ucwoKICAgICAgdGhpcy4kcmVmcy5jb250ZW50Lm9mZnNldFdpZHRoOwogICAgICB0aGlzLnVwZGF0ZURpbWVuc2lvbnMoKTsgLy8gV2hlbiByZXNpemluZyB0byBhIHNtYWxsZXIgd2lkdGgKICAgICAgLy8gY29udGVudCB3aWR0aCBpcyBldmFsdWF0ZWQgYmVmb3JlCiAgICAgIC8vIHRoZSBuZXcgYWN0aXZhdG9yIHdpZHRoIGhhcyBiZWVuCiAgICAgIC8vIHNldCwgY2F1c2luZyBpdCB0byBub3Qgc2l6ZSBwcm9wZXJseQogICAgICAvLyBoYWNreSBidXQgd2lsbCByZXZpc2l0IGluIHRoZSBmdXR1cmUKCiAgICAgIGNsZWFyVGltZW91dCh0aGlzLnJlc2l6ZVRpbWVvdXQpOwogICAgICB0aGlzLnJlc2l6ZVRpbWVvdXQgPSBfc2V0VGltZW91dCh0aGlzLnVwZGF0ZURpbWVuc2lvbnMsIDEwMCk7CiAgICB9CiAgfSwKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgIHZhciBkYXRhID0gewogICAgICBzdGF0aWNDbGFzczogJ3YtbWVudScsCiAgICAgICJjbGFzcyI6IHsKICAgICAgICAndi1tZW51LS1hdHRhY2hlZCc6IHRoaXMuYXR0YWNoID09PSAnJyB8fCB0aGlzLmF0dGFjaCA9PT0gdHJ1ZSB8fCB0aGlzLmF0dGFjaCA9PT0gJ2F0dGFjaCcKICAgICAgfSwKICAgICAgZGlyZWN0aXZlczogW3sKICAgICAgICBhcmc6ICc1MDAnLAogICAgICAgIG5hbWU6ICdyZXNpemUnLAogICAgICAgIHZhbHVlOiB0aGlzLm9uUmVzaXplCiAgICAgIH1dCiAgICB9OwogICAgcmV0dXJuIGgoJ2RpdicsIGRhdGEsIFshdGhpcy5hY3RpdmF0b3IgJiYgdGhpcy5nZW5BY3RpdmF0b3IoKSwgdGhpcy5zaG93TGF6eUNvbnRlbnQoZnVuY3Rpb24gKCkgewogICAgICByZXR1cm4gW190aGlzNy4kY3JlYXRlRWxlbWVudChWVGhlbWVQcm92aWRlciwgewogICAgICAgIHByb3BzOiB7CiAgICAgICAgICByb290OiB0cnVlLAogICAgICAgICAgbGlnaHQ6IF90aGlzNy5saWdodCwKICAgICAgICAgIGRhcms6IF90aGlzNy5kYXJrCiAgICAgICAgfQogICAgICB9LCBbX3RoaXM3LmdlblRyYW5zaXRpb24oKV0pXTsKICAgIH0pXSk7CiAgfQp9KTs="}, {"version": 3, "names": ["VThemeProvider", "Activatable", "Delayable", "Dependent", "Menuable", "Returnable", "Roundable", "Themeable", "ClickOutside", "Resize", "mixins", "removed", "convertToUnit", "keyCodes", "goTo", "baseMixins", "extend", "name", "directives", "provide", "isInMenu", "theme", "props", "auto", "Boolean", "closeOnClick", "type", "closeOnContentClick", "disabled", "disable<PERSON><PERSON>s", "maxHeight", "Number", "String", "offsetX", "offsetY", "openOnHover", "origin", "transition", "data", "calculatedTopAuto", "defaultOffset", "hasJustFocused", "listIndex", "resizeTimeout", "selectedIndex", "tiles", "computed", "activeTile", "calculatedLeft", "menuWidth", "Math", "max", "dimensions", "content", "width", "_parseFloat", "calculated<PERSON><PERSON><PERSON><PERSON><PERSON>", "calcLeft", "calcXOverflow", "calcLeftAuto", "calculatedMaxHeight", "height", "calculatedMaxWidth", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "min", "activator", "nudgeWidth", "pageWidth", "isNaN", "_parseInt", "calculatedTop", "top", "calcTop", "calcYOverflow", "hasClickableTiles", "_context", "_findInstanceProperty", "call", "tile", "tabIndex", "styles", "left", "transform<PERSON><PERSON>in", "zIndex", "activeZIndex", "watch", "isActive", "val", "isContentActive", "next", "prev", "classList", "add", "scrollTop", "$refs", "contentHeight", "clientHeight", "offsetTop", "appOffset", "duration", "container", "remove", "created", "$attrs", "hasOwnProperty", "mounted", "callActivate", "methods", "activate", "_this", "updateDimensions", "requestAnimationFrame", "startTransition", "then", "calcTopAuto", "calcScrollPosition", "$el", "querySelector", "maxScrollTop", "scrollHeight", "offsetHeight", "_context2", "computedTop", "_indexOfInstanceProperty", "_Array$from", "tileDistanceFromMenuTop", "firstTileOffsetTop", "changeListIndex", "e", "getTiles", "keyCode", "tab", "down", "nextTile", "up", "prevTile", "end", "lastTile", "home", "firstTile", "enter", "click", "preventDefault", "closeConditional", "target", "_isDestroyed", "contains", "genActivatorAttributes", "attributes", "options", "id", "_objectSpread", "genActivatorListeners", "listeners", "keydown", "onKeyDown", "genTransition", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$createElement", "genDirectives", "_this2", "value", "push", "handler", "include", "_context3", "_concatInstanceProperty", "_toConsumableArray", "getOpenDependentElements", "_context4", "_this3", "attrs", "getScopeIdAttrs", "role", "staticClass", "rootThemeClasses", "roundedClasses", "_defineProperty", "activatorFixed", "menuable__content__active", "_trimInstanceProperty", "contentClass", "style", "ref", "on", "getAttribute", "$listeners", "scroll", "mouseenter", "mouseEnterHandler", "mouseleave", "mouseLeaveHandler", "getContentSlot", "querySelectorAll", "_this4", "runDelay", "_this5", "_a", "relatedTarget", "callDeactivate", "length", "_this6", "_context5", "esc", "_setTimeout", "getActivator", "$nextTick", "focus", "_includesInstanceProperty", "onResize", "offsetWidth", "clearTimeout", "render", "h", "_this7", "attach", "arg", "genActivator", "showLazyContent", "root", "light", "dark"], "sources": ["../../../src/components/VMenu/VMenu.ts"], "sourcesContent": ["// Styles\nimport './VMenu.sass'\n\n// Components\nimport { VThemeProvider } from '../VThemeProvider'\n\n// Mixins\nimport Activatable from '../../mixins/activatable'\nimport Delayable from '../../mixins/delayable'\nimport Dependent from '../../mixins/dependent'\nimport Menuable from '../../mixins/menuable'\nimport Returnable from '../../mixins/returnable'\nimport Roundable from '../../mixins/roundable'\nimport Themeable from '../../mixins/themeable'\n\n// Directives\nimport ClickOutside from '../../directives/click-outside'\nimport Resize from '../../directives/resize'\n\n// Utilities\nimport mixins from '../../util/mixins'\nimport { removed } from '../../util/console'\nimport {\n  convertToUnit,\n  keyCodes,\n} from '../../util/helpers'\nimport goTo from '../../services/goto'\n\n// Types\nimport { VNode, VNodeDirective, VNodeData } from 'vue'\n\nconst baseMixins = mixins(\n  Dependent,\n  Delayable,\n  Returnable,\n  Roundable,\n  Themeable,\n  Menuable,\n)\n\n/* @vue/component */\nexport default baseMixins.extend({\n  name: 'v-menu',\n\n  directives: {\n    ClickOutside,\n    Resize,\n  },\n\n  provide (): object {\n    return {\n      isInMenu: true,\n      // Pass theme through to default slot\n      theme: this.theme,\n    }\n  },\n\n  props: {\n    auto: Boolean,\n    closeOnClick: {\n      type: Boolean,\n      default: true,\n    },\n    closeOnContentClick: {\n      type: Boolean,\n      default: true,\n    },\n    disabled: Boolean,\n    disableKeys: Boolean,\n    maxHeight: {\n      type: [Number, String],\n      default: 'auto',\n    },\n    offsetX: Boolean,\n    offsetY: Boolean,\n    openOnHover: Boolean,\n    origin: {\n      type: String,\n      default: 'top left',\n    },\n    transition: {\n      type: [Boolean, String],\n      default: 'v-menu-transition',\n    },\n  },\n\n  data () {\n    return {\n      calculatedTopAuto: 0,\n      defaultOffset: 8,\n      hasJustFocused: false,\n      listIndex: -1,\n      resizeTimeout: 0,\n      selectedIndex: null as null | number,\n      tiles: [] as HTMLElement[],\n    }\n  },\n\n  computed: {\n    activeTile (): HTMLElement | undefined {\n      return this.tiles[this.listIndex]\n    },\n    calculatedLeft (): string {\n      const menuWidth = Math.max(this.dimensions.content.width, parseFloat(this.calculatedMinWidth))\n\n      if (!this.auto) return this.calcLeft(menuWidth) || '0'\n\n      return convertToUnit(this.calcXOverflow(this.calcLeftAuto(), menuWidth)) || '0'\n    },\n    calculatedMaxHeight (): string {\n      const height = this.auto\n        ? '200px'\n        : convertToUnit(this.maxHeight)\n\n      return height || '0'\n    },\n    calculatedMaxWidth (): string {\n      return convertToUnit(this.maxWidth) || '0'\n    },\n    calculatedMinWidth (): string {\n      if (this.minWidth) {\n        return convertToUnit(this.minWidth) || '0'\n      }\n\n      const minWidth = Math.min(\n        this.dimensions.activator.width +\n        Number(this.nudgeWidth) +\n        (this.auto ? 16 : 0),\n        Math.max(this.pageWidth - 24, 0)\n      )\n\n      const calculatedMaxWidth = isNaN(parseInt(this.calculatedMaxWidth))\n        ? minWidth\n        : parseInt(this.calculatedMaxWidth)\n\n      return convertToUnit(Math.min(\n        calculatedMaxWidth,\n        minWidth\n      )) || '0'\n    },\n    calculatedTop (): string {\n      const top = !this.auto\n        ? this.calcTop()\n        : convertToUnit(this.calcYOverflow(this.calculatedTopAuto))\n\n      return top || '0'\n    },\n    hasClickableTiles (): boolean {\n      return Boolean(this.tiles.find(tile => tile.tabIndex > -1))\n    },\n    styles (): object {\n      return {\n        maxHeight: this.calculatedMaxHeight,\n        minWidth: this.calculatedMinWidth,\n        maxWidth: this.calculatedMaxWidth,\n        top: this.calculatedTop,\n        left: this.calculatedLeft,\n        transformOrigin: this.origin,\n        zIndex: this.zIndex || this.activeZIndex,\n      }\n    },\n  },\n\n  watch: {\n    isActive (val) {\n      if (!val) this.listIndex = -1\n    },\n    isContentActive (val) {\n      this.hasJustFocused = val\n    },\n    listIndex (next, prev) {\n      if (next in this.tiles) {\n        const tile = this.tiles[next]\n        tile.classList.add('v-list-item--highlighted')\n        const scrollTop = this.$refs.content.scrollTop\n        const contentHeight = this.$refs.content.clientHeight\n\n        if (scrollTop > tile.offsetTop - 8) {\n          goTo(tile.offsetTop - tile.clientHeight, {\n            appOffset: false,\n            duration: 300,\n            container: this.$refs.content,\n          })\n        } else if (scrollTop + contentHeight < tile.offsetTop + tile.clientHeight + 8) {\n          goTo(tile.offsetTop - contentHeight + tile.clientHeight * 2, {\n            appOffset: false,\n            duration: 300,\n            container: this.$refs.content,\n          })\n        }\n      }\n\n      prev in this.tiles &&\n        this.tiles[prev].classList.remove('v-list-item--highlighted')\n    },\n  },\n\n  created () {\n    /* istanbul ignore next */\n    if (this.$attrs.hasOwnProperty('full-width')) {\n      removed('full-width', this)\n    }\n  },\n\n  mounted () {\n    this.isActive && this.callActivate()\n  },\n\n  methods: {\n    activate () {\n      // Update coordinates and dimensions of menu\n      // and its activator\n      this.updateDimensions()\n      // Start the transition\n      requestAnimationFrame(() => {\n        // Once transitioning, calculate scroll and top position\n        this.startTransition().then(() => {\n          if (this.$refs.content) {\n            this.calculatedTopAuto = this.calcTopAuto()\n            this.auto && (this.$refs.content.scrollTop = this.calcScrollPosition())\n          }\n        })\n      })\n    },\n    calcScrollPosition () {\n      const $el = this.$refs.content\n      const activeTile = $el.querySelector('.v-list-item--active') as HTMLElement\n      const maxScrollTop = $el.scrollHeight - $el.offsetHeight\n\n      return activeTile\n        ? Math.min(maxScrollTop, Math.max(0, activeTile.offsetTop - $el.offsetHeight / 2 + activeTile.offsetHeight / 2))\n        : $el.scrollTop\n    },\n    calcLeftAuto () {\n      return parseInt(this.dimensions.activator.left - this.defaultOffset * 2)\n    },\n    calcTopAuto () {\n      const $el = this.$refs.content\n      const activeTile = $el.querySelector('.v-list-item--active') as HTMLElement | null\n\n      if (!activeTile) {\n        this.selectedIndex = null\n      }\n\n      if (this.offsetY || !activeTile) {\n        return this.computedTop\n      }\n\n      this.selectedIndex = Array.from(this.tiles).indexOf(activeTile)\n\n      const tileDistanceFromMenuTop = activeTile.offsetTop - this.calcScrollPosition()\n      const firstTileOffsetTop = ($el.querySelector('.v-list-item') as HTMLElement).offsetTop\n\n      return this.computedTop - tileDistanceFromMenuTop - firstTileOffsetTop - 1\n    },\n    changeListIndex (e: KeyboardEvent) {\n      // For infinite scroll and autocomplete, re-evaluate children\n      this.getTiles()\n\n      if (!this.isActive || !this.hasClickableTiles) {\n        return\n      } else if (e.keyCode === keyCodes.tab) {\n        this.isActive = false\n        return\n      } else if (e.keyCode === keyCodes.down) {\n        this.nextTile()\n      } else if (e.keyCode === keyCodes.up) {\n        this.prevTile()\n      } else if (e.keyCode === keyCodes.end) {\n        this.lastTile()\n      } else if (e.keyCode === keyCodes.home) {\n        this.firstTile()\n      } else if (e.keyCode === keyCodes.enter && this.listIndex !== -1) {\n        this.tiles[this.listIndex].click()\n      } else { return }\n      // One of the conditions was met, prevent default action (#2988)\n      e.preventDefault()\n    },\n    closeConditional (e: Event) {\n      const target = e.target as HTMLElement\n\n      return this.isActive &&\n        !this._isDestroyed &&\n        this.closeOnClick &&\n        !this.$refs.content.contains(target)\n    },\n    genActivatorAttributes () {\n      const attributes = Activatable.options.methods.genActivatorAttributes.call(this)\n\n      if (this.activeTile && this.activeTile.id) {\n        return {\n          ...attributes,\n          'aria-activedescendant': this.activeTile.id,\n        }\n      }\n\n      return attributes\n    },\n    genActivatorListeners () {\n      const listeners = Menuable.options.methods.genActivatorListeners.call(this)\n\n      if (!this.disableKeys) {\n        listeners.keydown = this.onKeyDown\n      }\n\n      return listeners\n    },\n    genTransition (): VNode {\n      const content = this.genContent()\n\n      if (!this.transition) return content\n\n      return this.$createElement('transition', {\n        props: {\n          name: this.transition,\n        },\n      }, [content])\n    },\n    genDirectives (): VNodeDirective[] {\n      const directives: VNodeDirective[] = [{\n        name: 'show',\n        value: this.isContentActive,\n      }]\n\n      // Do not add click outside for hover menu\n      if (!this.openOnHover && this.closeOnClick) {\n        directives.push({\n          name: 'click-outside',\n          value: {\n            handler: () => { this.isActive = false },\n            closeConditional: this.closeConditional,\n            include: () => [this.$el, ...this.getOpenDependentElements()],\n          },\n        })\n      }\n\n      return directives\n    },\n    genContent (): VNode {\n      const options = {\n        attrs: {\n          ...this.getScopeIdAttrs(),\n          role: 'role' in this.$attrs ? this.$attrs.role : 'menu',\n        },\n        staticClass: 'v-menu__content',\n        class: {\n          ...this.rootThemeClasses,\n          ...this.roundedClasses,\n          'v-menu__content--auto': this.auto,\n          'v-menu__content--fixed': this.activatorFixed,\n          menuable__content__active: this.isActive,\n          [this.contentClass.trim()]: true,\n        },\n        style: this.styles,\n        directives: this.genDirectives(),\n        ref: 'content',\n        on: {\n          click: (e: Event) => {\n            const target = e.target as HTMLElement\n\n            if (target.getAttribute('disabled')) return\n            if (this.closeOnContentClick) this.isActive = false\n          },\n          keydown: this.onKeyDown,\n        },\n      } as VNodeData\n\n      if (this.$listeners.scroll) {\n        options.on = options.on || {}\n        options.on.scroll = this.$listeners.scroll\n      }\n\n      if (!this.disabled && this.openOnHover) {\n        options.on = options.on || {}\n        options.on.mouseenter = this.mouseEnterHandler\n      }\n\n      if (this.openOnHover) {\n        options.on = options.on || {}\n        options.on.mouseleave = this.mouseLeaveHandler\n      }\n\n      return this.$createElement('div', options, this.getContentSlot())\n    },\n    getTiles () {\n      if (!this.$refs.content) return\n\n      this.tiles = Array.from(this.$refs.content.querySelectorAll('.v-list-item, .v-divider, .v-subheader'))\n    },\n    mouseEnterHandler () {\n      this.runDelay('open', () => {\n        if (this.hasJustFocused) return\n\n        this.hasJustFocused = true\n      })\n    },\n    mouseLeaveHandler (e: MouseEvent) {\n      // Prevent accidental re-activation\n      this.runDelay('close', () => {\n        if (this.$refs.content?.contains(e.relatedTarget as HTMLElement)) return\n\n        requestAnimationFrame(() => {\n          this.isActive = false\n          this.callDeactivate()\n        })\n      })\n    },\n    nextTile () {\n      const tile = this.tiles[this.listIndex + 1]\n\n      if (!tile) {\n        if (!this.tiles.length) return\n\n        this.listIndex = -1\n        this.nextTile()\n\n        return\n      }\n\n      this.listIndex++\n      if (tile.tabIndex === -1) this.nextTile()\n    },\n    prevTile () {\n      const tile = this.tiles[this.listIndex - 1]\n\n      if (!tile) {\n        if (!this.tiles.length) return\n\n        this.listIndex = this.tiles.length\n        this.prevTile()\n\n        return\n      }\n\n      this.listIndex--\n      if (tile.tabIndex === -1) this.prevTile()\n    },\n    lastTile () {\n      const tile = this.tiles[this.tiles.length - 1]\n\n      if (!tile) return\n\n      this.listIndex = this.tiles.length - 1\n\n      if (tile.tabIndex === -1) this.prevTile()\n    },\n    firstTile () {\n      const tile = this.tiles[0]\n\n      if (!tile) return\n\n      this.listIndex = 0\n\n      if (tile.tabIndex === -1) this.nextTile()\n    },\n    onKeyDown (e: KeyboardEvent) {\n      if (e.keyCode === keyCodes.esc) {\n        // Wait for dependent elements to close first\n        setTimeout(() => { this.isActive = false })\n        const activator = this.getActivator()\n        this.$nextTick(() => activator && activator.focus())\n      } else if (\n        !this.isActive &&\n        [keyCodes.up, keyCodes.down].includes(e.keyCode)\n      ) {\n        this.isActive = true\n      }\n\n      // Allow for isActive watcher to generate tile list\n      this.$nextTick(() => this.changeListIndex(e))\n    },\n    onResize () {\n      if (!this.isActive) return\n\n      // Account for screen resize\n      // and orientation change\n      // eslint-disable-next-line no-unused-expressions\n      this.$refs.content.offsetWidth\n      this.updateDimensions()\n\n      // When resizing to a smaller width\n      // content width is evaluated before\n      // the new activator width has been\n      // set, causing it to not size properly\n      // hacky but will revisit in the future\n      clearTimeout(this.resizeTimeout)\n      this.resizeTimeout = window.setTimeout(this.updateDimensions, 100)\n    },\n  },\n\n  render (h): VNode {\n    const data = {\n      staticClass: 'v-menu',\n      class: {\n        'v-menu--attached':\n          this.attach === '' ||\n          this.attach === true ||\n          this.attach === 'attach',\n      },\n      directives: [{\n        arg: '500',\n        name: 'resize',\n        value: this.onResize,\n      }],\n    }\n\n    return h('div', data, [\n      !this.activator && this.genActivator(),\n      this.showLazyContent(() => [\n        this.$createElement(VThemeProvider, {\n          props: {\n            root: true,\n            light: this.light,\n            dark: this.dark,\n          },\n        }, [this.genTransition()]),\n      ]),\n    ])\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AACA,OAAO,0CAAP,C,CAEA;;AACA,SAASA,cAAT,QAA+B,mBAA/B,C,CAEA;;AACA,OAAOC,WAAP,MAAwB,0BAAxB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,QAAP,MAAqB,uBAArB;AACA,OAAOC,UAAP,MAAuB,yBAAvB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,YAAP,MAAyB,gCAAzB;AACA,OAAOC,MAAP,MAAmB,yBAAnB,C,CAEA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,OAAT,QAAwB,oBAAxB;AACA,SACEC,aADF,EAEEC,QAFF,QAGO,oBAHP;AAIA,OAAOC,IAAP,MAAiB,qBAAjB;AAKA,IAAMC,UAAU,GAAGL,MAAM,CACvBP,SADuB,EAEvBD,SAFuB,EAGvBG,UAHuB,EAIvBC,SAJuB,EAKvBC,SALuB,EAMvBH,QANuB,CAAzB;AASA;;AACA,eAAeW,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,QADyB;EAG/BC,UAAU,EAAE;IACVV,YADU,EACVA,YADU;IAEVC,MAAA,EAAAA;EAFU,CAHmB;EAQ/BU,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,QAAQ,EAAE,IADL;MAEL;MACAC,KAAK,EAAE,KAAKA;IAHP,CAAP;EAKD,CAd8B;EAgB/BC,KAAK,EAAE;IACLC,IAAI,EAAEC,OADD;IAELC,YAAY,EAAE;MACZC,IAAI,EAAEF,OADM;MAEZ,WAAS;IAFG,CAFT;IAMLG,mBAAmB,EAAE;MACnBD,IAAI,EAAEF,OADa;MAEnB,WAAS;IAFU,CANhB;IAULI,QAAQ,EAAEJ,OAVL;IAWLK,WAAW,EAAEL,OAXR;IAYLM,SAAS,EAAE;MACTJ,IAAI,EAAE,CAACK,MAAD,EAASC,MAAT,CADG;MAET,WAAS;IAFA,CAZN;IAgBLC,OAAO,EAAET,OAhBJ;IAiBLU,OAAO,EAAEV,OAjBJ;IAkBLW,WAAW,EAAEX,OAlBR;IAmBLY,MAAM,EAAE;MACNV,IAAI,EAAEM,MADA;MAEN,WAAS;IAFH,CAnBH;IAuBLK,UAAU,EAAE;MACVX,IAAI,EAAE,CAACF,OAAD,EAAUQ,MAAV,CADI;MAEV,WAAS;IAFC;EAvBP,CAhBwB;EA6C/BM,IAAI,WAAJA,IAAIA,CAAA;IACF,OAAO;MACLC,iBAAiB,EAAE,CADd;MAELC,aAAa,EAAE,CAFV;MAGLC,cAAc,EAAE,KAHX;MAILC,SAAS,EAAE,CAAC,CAJP;MAKLC,aAAa,EAAE,CALV;MAMLC,aAAa,EAAE,IANV;MAOLC,KAAK,EAAE;IAPF,CAAP;EASD,CAvD8B;EAyD/BC,QAAQ,EAAE;IACRC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKF,KAAL,CAAW,KAAKH,SAAhB,CAAP;IACD,CAHO;IAIRM,cAAc,WAAdA,cAAcA,CAAA;MACZ,IAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKC,UAAL,CAAgBC,OAAhB,CAAwBC,KAAjC,EAAwCC,WAAA,CAAW,KAAKC,kBAAN,CAAlD,CAAlB;MAEA,IAAI,CAAC,KAAKjC,IAAV,EAAgB,OAAO,KAAKkC,QAAL,CAAcR,SAAd,KAA4B,GAAnC;MAEhB,OAAOrC,aAAa,CAAC,KAAK8C,aAAL,CAAmB,KAAKC,YAAL,EAAnB,EAAwCV,SAAxC,CAAD,CAAb,IAAqE,GAA5E;IACD,CAVO;IAWRW,mBAAmB,WAAnBA,mBAAmBA,CAAA;MACjB,IAAMC,MAAM,GAAG,KAAKtC,IAAL,GACX,OADW,GAEXX,aAAa,CAAC,KAAKkB,SAAN,CAFjB;MAIA,OAAO+B,MAAM,IAAI,GAAjB;IACD,CAjBO;IAkBRC,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,OAAOlD,aAAa,CAAC,KAAKmD,QAAN,CAAb,IAAgC,GAAvC;IACD,CApBO;IAqBRP,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAI,KAAKQ,QAAT,EAAmB;QACjB,OAAOpD,aAAa,CAAC,KAAKoD,QAAN,CAAb,IAAgC,GAAvC;MACD;MAED,IAAMA,QAAQ,GAAGd,IAAI,CAACe,GAAL,CACf,KAAKb,UAAL,CAAgBc,SAAhB,CAA0BZ,KAA1B,GACAvB,MAAM,CAAC,KAAKoC,UAAN,CADN,IAEC,KAAK5C,IAAL,GAAY,EAAZ,GAAiB,CAFlB,CADe,EAIf2B,IAAI,CAACC,GAAL,CAAS,KAAKiB,SAAL,GAAiB,EAA1B,EAA8B,CAA9B,CAJe,CAAjB;MAOA,IAAMN,kBAAkB,GAAGO,KAAK,CAACC,SAAA,CAAS,KAAKR,kBAAN,CAAT,CAAL,GACvBE,QADuB,GAEvBM,SAAA,CAAS,KAAKR,kBAAN,CAFZ;MAIA,OAAOlD,aAAa,CAACsC,IAAI,CAACe,GAAL,CACnBH,kBADmB,EAEnBE,QAFmB,CAAD,CAAb,IAGD,GAHN;IAID,CAzCO;IA0CRO,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAMC,GAAG,GAAG,CAAC,KAAKjD,IAAN,GACR,KAAKkD,OAAL,EADQ,GAER7D,aAAa,CAAC,KAAK8D,aAAL,CAAmB,KAAKnC,iBAAxB,CAAD,CAFjB;MAIA,OAAOiC,GAAG,IAAI,GAAd;IACD,CAhDO;IAiDRG,iBAAiB,WAAjBA,iBAAiBA,CAAA;MAAA,IAAAC,QAAA;MACf,OAAOpD,OAAO,CAACqD,qBAAA,CAAAD,QAAA,QAAK/B,KAAL,EAAAiC,IAAA,CAAAF,QAAA,EAAgB,UAAAG,IAAI;QAAA,OAAIA,IAAI,CAACC,QAAL,GAAgB,CAAC,CAAzC;MAAA,EAAD,CAAd;IACD,CAnDO;IAoDRC,MAAM,WAANA,MAAMA,CAAA;MACJ,OAAO;QACLnD,SAAS,EAAE,KAAK8B,mBADX;QAELI,QAAQ,EAAE,KAAKR,kBAFV;QAGLO,QAAQ,EAAE,KAAKD,kBAHV;QAILU,GAAG,EAAE,KAAKD,aAJL;QAKLW,IAAI,EAAE,KAAKlC,cALN;QAMLmC,eAAe,EAAE,KAAK/C,MANjB;QAOLgD,MAAM,EAAE,KAAKA,MAAL,IAAe,KAAKC;MAPvB,CAAP;IASD;EA9DO,CAzDqB;EA0H/BC,KAAK,EAAE;IACLC,QAAQ,WAARA,QAAQA,CAAEC,GAAF,EAAK;MACX,IAAI,CAACA,GAAL,EAAU,KAAK9C,SAAL,GAAiB,CAAC,CAAlB;IACX,CAHI;IAIL+C,eAAe,WAAfA,eAAeA,CAAED,GAAF,EAAK;MAClB,KAAK/C,cAAL,GAAsB+C,GAAtB;IACD,CANI;IAOL9C,SAAS,WAATA,SAASA,CAAEgD,IAAF,EAAQC,IAAR,EAAY;MACnB,IAAID,IAAI,IAAI,KAAK7C,KAAjB,EAAwB;QACtB,IAAMkC,IAAI,GAAG,KAAKlC,KAAL,CAAW6C,IAAX,CAAb;QACAX,IAAI,CAACa,SAAL,CAAeC,GAAf,CAAmB,0BAAnB;QACA,IAAMC,SAAS,GAAG,KAAKC,KAAL,CAAW1C,OAAX,CAAmByC,SAArC;QACA,IAAME,aAAa,GAAG,KAAKD,KAAL,CAAW1C,OAAX,CAAmB4C,YAAzC;QAEA,IAAIH,SAAS,GAAGf,IAAI,CAACmB,SAAL,GAAiB,CAAjC,EAAoC;UAClCpF,IAAI,CAACiE,IAAI,CAACmB,SAAL,GAAiBnB,IAAI,CAACkB,YAAvB,EAAqC;YACvCE,SAAS,EAAE,KAD4B;YAEvCC,QAAQ,EAAE,GAF6B;YAGvCC,SAAS,EAAE,KAAKN,KAAL,CAAW1C;UAHiB,CAArC,CAAJ;QAKD,CAND,MAMO,IAAIyC,SAAS,GAAGE,aAAZ,GAA4BjB,IAAI,CAACmB,SAAL,GAAiBnB,IAAI,CAACkB,YAAtB,GAAqC,CAArE,EAAwE;UAC7EnF,IAAI,CAACiE,IAAI,CAACmB,SAAL,GAAiBF,aAAjB,GAAiCjB,IAAI,CAACkB,YAAL,GAAoB,CAAtD,EAAyD;YAC3DE,SAAS,EAAE,KADgD;YAE3DC,QAAQ,EAAE,GAFiD;YAG3DC,SAAS,EAAE,KAAKN,KAAL,CAAW1C;UAHqC,CAAzD,CAAJ;QAKD;MACF;MAEDsC,IAAI,IAAI,KAAK9C,KAAb,IACE,KAAKA,KAAL,CAAW8C,IAAX,EAAiBC,SAAjB,CAA2BU,MAA3B,CAAkC,0BAAlC,CADF;IAED;EA/BI,CA1HwB;EA4J/BC,OAAO,WAAPA,OAAOA,CAAA;IACL;IACA,IAAI,KAAKC,MAAL,CAAYC,cAAZ,CAA2B,YAA3B,CAAJ,EAA8C;MAC5C9F,OAAO,CAAC,YAAD,EAAe,IAAf,CAAP;IACD;EACF,CAjK8B;EAmK/B+F,OAAO,WAAPA,OAAOA,CAAA;IACL,KAAKnB,QAAL,IAAiB,KAAKoB,YAAL,EAAjB;EACD,CArK8B;EAuK/BC,OAAO,EAAE;IACPC,QAAQ,WAARA,QAAQA,CAAA;MAAA,IAAAC,KAAA;MACN;MACA;MACA,KAAKC,gBAAL,GAHM,CAIN;;MACAC,qBAAqB,CAAC,YAAK;QACzB;QACAF,KAAA,CAAKG,eAAL,GAAuBC,IAAvB,CAA4B,YAAK;UAC/B,IAAIJ,KAAA,CAAKf,KAAL,CAAW1C,OAAf,EAAwB;YACtByD,KAAA,CAAKvE,iBAAL,GAAyBuE,KAAA,CAAKK,WAAL,EAAzB;YACAL,KAAA,CAAKvF,IAAL,KAAcuF,KAAA,CAAKf,KAAL,CAAW1C,OAAX,CAAmByC,SAAnB,GAA+BgB,KAAA,CAAKM,kBAAL,EAA7C;UACD;QACF,CALD;MAMD,CARoB,CAArB;IASD,CAfM;IAgBPA,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAMC,GAAG,GAAG,KAAKtB,KAAL,CAAW1C,OAAvB;MACA,IAAMN,UAAU,GAAGsE,GAAG,CAACC,aAAJ,CAAkB,sBAAlB,CAAnB;MACA,IAAMC,YAAY,GAAGF,GAAG,CAACG,YAAJ,GAAmBH,GAAG,CAACI,YAA5C;MAEA,OAAO1E,UAAU,GACbG,IAAI,CAACe,GAAL,CAASsD,YAAT,EAAuBrE,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYJ,UAAU,CAACmD,SAAX,GAAuBmB,GAAG,CAACI,YAAJ,GAAmB,CAA1C,GAA8C1E,UAAU,CAAC0E,YAAX,GAA0B,CAApF,CAAvB,CADa,GAEbJ,GAAG,CAACvB,SAFR;IAGD,CAxBM;IAyBPnC,YAAY,WAAZA,YAAYA,CAAA;MACV,OAAOW,SAAA,CAAS,KAAKlB,UAAL,CAAgBc,SAAhB,CAA0BgB,IAA1B,GAAiC,KAAK1C,aAAL,GAAqB,CAAvD,CAAf;IACD,CA3BM;IA4BP2E,WAAW,WAAXA,WAAWA,CAAA;MAAA,IAAAO,SAAA;MACT,IAAML,GAAG,GAAG,KAAKtB,KAAL,CAAW1C,OAAvB;MACA,IAAMN,UAAU,GAAGsE,GAAG,CAACC,aAAJ,CAAkB,sBAAlB,CAAnB;MAEA,IAAI,CAACvE,UAAL,EAAiB;QACf,KAAKH,aAAL,GAAqB,IAArB;MACD;MAED,IAAI,KAAKV,OAAL,IAAgB,CAACa,UAArB,EAAiC;QAC/B,OAAO,KAAK4E,WAAZ;MACD;MAED,KAAK/E,aAAL,GAAqBgF,wBAAA,CAAAF,SAAA,GAAAG,WAAA,CAAW,KAAKhF,KAAhB,GAAAiC,IAAA,CAAA4C,SAAA,EAA+B3E,UAA/B,CAArB;MAEA,IAAM+E,uBAAuB,GAAG/E,UAAU,CAACmD,SAAX,GAAuB,KAAKkB,kBAAL,EAAvD;MACA,IAAMW,kBAAkB,GAAIV,GAAG,CAACC,aAAJ,CAAkB,cAAlB,EAAkDpB,SAA9E;MAEA,OAAO,KAAKyB,WAAL,GAAmBG,uBAAnB,GAA6CC,kBAA7C,GAAkE,CAAzE;IACD,CA9CM;IA+CPC,eAAe,WAAfA,eAAeA,CAAEC,CAAF,EAAkB;MAC/B;MACA,KAAKC,QAAL;MAEA,IAAI,CAAC,KAAK3C,QAAN,IAAkB,CAAC,KAAKZ,iBAA5B,EAA+C;QAC7C;MACD,CAFD,MAEO,IAAIsD,CAAC,CAACE,OAAF,KAActH,QAAQ,CAACuH,GAA3B,EAAgC;QACrC,KAAK7C,QAAL,GAAgB,KAAhB;QACA;MACD,CAHM,MAGA,IAAI0C,CAAC,CAACE,OAAF,KAActH,QAAQ,CAACwH,IAA3B,EAAiC;QACtC,KAAKC,QAAL;MACD,CAFM,MAEA,IAAIL,CAAC,CAACE,OAAF,KAActH,QAAQ,CAAC0H,EAA3B,EAA+B;QACpC,KAAKC,QAAL;MACD,CAFM,MAEA,IAAIP,CAAC,CAACE,OAAF,KAActH,QAAQ,CAAC4H,GAA3B,EAAgC;QACrC,KAAKC,QAAL;MACD,CAFM,MAEA,IAAIT,CAAC,CAACE,OAAF,KAActH,QAAQ,CAAC8H,IAA3B,EAAiC;QACtC,KAAKC,SAAL;MACD,CAFM,MAEA,IAAIX,CAAC,CAACE,OAAF,KAActH,QAAQ,CAACgI,KAAvB,IAAgC,KAAKnG,SAAL,KAAmB,CAAC,CAAxD,EAA2D;QAChE,KAAKG,KAAL,CAAW,KAAKH,SAAhB,EAA2BoG,KAA3B;MACD,CAFM,MAEA;QAAE;MAAQ,CAnBc,CAoB/B;;MACAb,CAAC,CAACc,cAAF;IACD,CArEM;IAsEPC,gBAAgB,WAAhBA,gBAAgBA,CAAEf,CAAF,EAAU;MACxB,IAAMgB,MAAM,GAAGhB,CAAC,CAACgB,MAAjB;MAEA,OAAO,KAAK1D,QAAL,IACL,CAAC,KAAK2D,YADD,IAEL,KAAKzH,YAFA,IAGL,CAAC,KAAKsE,KAAL,CAAW1C,OAAX,CAAmB8F,QAAnB,CAA4BF,MAA5B,CAHH;IAID,CA7EM;IA8EPG,sBAAsB,WAAtBA,sBAAsBA,CAAA;MACpB,IAAMC,UAAU,GAAGpJ,WAAW,CAACqJ,OAAZ,CAAoB1C,OAApB,CAA4BwC,sBAA5B,CAAmDtE,IAAnD,CAAwD,IAAxD,CAAnB;MAEA,IAAI,KAAK/B,UAAL,IAAmB,KAAKA,UAAL,CAAgBwG,EAAvC,EAA2C;QACzC,OAAAC,aAAA,CAAAA,aAAA,KACKH,UADE;UAEL,yBAAyB,KAAKtG,UAAL,CAAgBwG;QAAA;MAE5C;MAED,OAAOF,UAAP;IACD,CAzFM;IA0FPI,qBAAqB,WAArBA,qBAAqBA,CAAA;MACnB,IAAMC,SAAS,GAAGtJ,QAAQ,CAACkJ,OAAT,CAAiB1C,OAAjB,CAAyB6C,qBAAzB,CAA+C3E,IAA/C,CAAoD,IAApD,CAAlB;MAEA,IAAI,CAAC,KAAKjD,WAAV,EAAuB;QACrB6H,SAAS,CAACC,OAAV,GAAoB,KAAKC,SAAzB;MACD;MAED,OAAOF,SAAP;IACD,CAlGM;IAmGPG,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAMxG,OAAO,GAAG,KAAKyG,UAAL,EAAhB;MAEA,IAAI,CAAC,KAAKzH,UAAV,EAAsB,OAAOgB,OAAP;MAEtB,OAAO,KAAK0G,cAAL,CAAoB,YAApB,EAAkC;QACvCzI,KAAK,EAAE;UACLL,IAAI,EAAE,KAAKoB;QADN;MADgC,CAAlC,EAIJ,CAACgB,OAAD,CAJI,CAAP;IAKD,CA7GM;IA8GP2G,aAAa,WAAbA,aAAaA,CAAA;MAAA,IAAAC,MAAA;MACX,IAAM/I,UAAU,GAAqB,CAAC;QACpCD,IAAI,EAAE,MAD8B;QAEpCiJ,KAAK,EAAE,KAAKzE;MAFwB,CAAD,CAArC,CADW,CAMX;;MACA,IAAI,CAAC,KAAKtD,WAAN,IAAqB,KAAKV,YAA9B,EAA4C;QAC1CP,UAAU,CAACiJ,IAAX,CAAgB;UACdlJ,IAAI,EAAE,eADQ;UAEdiJ,KAAK,EAAE;YACLE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAO;cAAGH,MAAA,CAAK1E,QAAL,GAAgB,KAAhB;YAAuB,CADnC;YAELyD,gBAAgB,EAAE,KAAKA,gBAFlB;YAGLqB,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,IAAAC,SAAA;cAAA,OAAAC,uBAAA,CAAAD,SAAA,IAASL,MAAA,CAAK5C,GAAN,GAAAvC,IAAA,CAAAwF,SAAA,EAAAE,kBAAA,CAAcP,MAAA,CAAKQ,wBAAL,EAAd;YAAA;UAHV;QAFO,CAAhB;MAQD;MAED,OAAOvJ,UAAP;IACD,CAjIM;IAkIP4I,UAAU,WAAVA,UAAUA,CAAA;MAAA,IAAAY,SAAA;QAAAC,MAAA;MACR,IAAMrB,OAAO,GAAG;QACdsB,KAAK,EAAApB,aAAA,CAAAA,aAAA,KACA,KAAKqB,eAAL,EADE;UAELC,IAAI,EAAE,UAAU,KAAKtE,MAAf,GAAwB,KAAKA,MAAL,CAAYsE,IAApC,GAA2C;QAAA,EAHrC;QAKdC,WAAW,EAAE,iBALC;QAMd,SAAAvB,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACK,KAAKwB,gBADH,GAEF,KAAKC,cAFH,OAAAC,eAAA;UAGL,yBAAyB,KAAK3J,IAHzB;UAIL,0BAA0B,KAAK4J,cAJ1B;UAKLC,yBAAyB,EAAE,KAAK7F;QAL3B,GAMJ8F,qBAAA,CAAAX,SAAA,QAAKY,YAAL,EAAAxG,IAAA,CAAA4F,SAAA,CAAD,EAA4B,MAZhB;QAcda,KAAK,EAAE,KAAKtG,MAdE;QAed/D,UAAU,EAAE,KAAK8I,aAAL,EAfE;QAgBdwB,GAAG,EAAE,SAhBS;QAiBdC,EAAE,EAAE;UACF3C,KAAK,EAAG,SAARA,KAAKA,CAAGb,CAAD,EAAa;YAClB,IAAMgB,MAAM,GAAGhB,CAAC,CAACgB,MAAjB;YAEA,IAAIA,MAAM,CAACyC,YAAP,CAAoB,UAApB,CAAJ,EAAqC;YACrC,IAAIf,MAAA,CAAKhJ,mBAAT,EAA8BgJ,MAAA,CAAKpF,QAAL,GAAgB,KAAhB;UAC/B,CANC;UAOFoE,OAAO,EAAE,KAAKC;QAPZ;MAjBU,CAAhB;MA4BA,IAAI,KAAK+B,UAAL,CAAgBC,MAApB,EAA4B;QAC1BtC,OAAO,CAACmC,EAAR,GAAanC,OAAO,CAACmC,EAAR,IAAc,EAA3B;QACAnC,OAAO,CAACmC,EAAR,CAAWG,MAAX,GAAoB,KAAKD,UAAL,CAAgBC,MAApC;MACD;MAED,IAAI,CAAC,KAAKhK,QAAN,IAAkB,KAAKO,WAA3B,EAAwC;QACtCmH,OAAO,CAACmC,EAAR,GAAanC,OAAO,CAACmC,EAAR,IAAc,EAA3B;QACAnC,OAAO,CAACmC,EAAR,CAAWI,UAAX,GAAwB,KAAKC,iBAA7B;MACD;MAED,IAAI,KAAK3J,WAAT,EAAsB;QACpBmH,OAAO,CAACmC,EAAR,GAAanC,OAAO,CAACmC,EAAR,IAAc,EAA3B;QACAnC,OAAO,CAACmC,EAAR,CAAWM,UAAX,GAAwB,KAAKC,iBAA7B;MACD;MAED,OAAO,KAAKjC,cAAL,CAAoB,KAApB,EAA2BT,OAA3B,EAAoC,KAAK2C,cAAL,EAApC,CAAP;IACD,CA/KM;IAgLP/D,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAI,CAAC,KAAKnC,KAAL,CAAW1C,OAAhB,EAAyB;MAEzB,KAAKR,KAAL,GAAagF,WAAA,CAAW,KAAK9B,KAAL,CAAW1C,OAAX,CAAmB6I,gBAAnB,CAAoC,wCAApC,CAAX,CAAb;IACD,CApLM;IAqLPJ,iBAAiB,WAAjBA,iBAAiBA,CAAA;MAAA,IAAAK,MAAA;MACf,KAAKC,QAAL,CAAc,MAAd,EAAsB,YAAK;QACzB,IAAID,MAAA,CAAK1J,cAAT,EAAyB;QAEzB0J,MAAA,CAAK1J,cAAL,GAAsB,IAAtB;MACD,CAJD;IAKD,CA3LM;IA4LPuJ,iBAAiB,WAAjBA,iBAAiBA,CAAE/D,CAAF,EAAe;MAAA,IAAAoE,MAAA;MAC9B;MACA,KAAKD,QAAL,CAAc,OAAd,EAAuB,YAAK;;QAC1B,IAAI,CAAAE,EAAA,GAAAD,MAAA,CAAKtG,KAAL,CAAW1C,OAAX,MAAkB,IAAlB,IAAkBiJ,EAAA,WAAlB,GAAkB,MAAlB,GAAkBA,EAAA,CAAEnD,QAAF,CAAWlB,CAAC,CAACsE,aAAb,CAAtB,EAAkE;QAElEvF,qBAAqB,CAAC,YAAK;UACzBqF,MAAA,CAAK9G,QAAL,GAAgB,KAAhB;UACA8G,MAAA,CAAKG,cAAL;QACD,CAHoB,CAArB;MAID,CAPD;IAQD,CAtMM;IAuMPlE,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAMvD,IAAI,GAAG,KAAKlC,KAAL,CAAW,KAAKH,SAAL,GAAiB,CAA5B,CAAb;MAEA,IAAI,CAACqC,IAAL,EAAW;QACT,IAAI,CAAC,KAAKlC,KAAL,CAAW4J,MAAhB,EAAwB;QAExB,KAAK/J,SAAL,GAAiB,CAAC,CAAlB;QACA,KAAK4F,QAAL;QAEA;MACD;MAED,KAAK5F,SAAL;MACA,IAAIqC,IAAI,CAACC,QAAL,KAAkB,CAAC,CAAvB,EAA0B,KAAKsD,QAAL;IAC3B,CArNM;IAsNPE,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAMzD,IAAI,GAAG,KAAKlC,KAAL,CAAW,KAAKH,SAAL,GAAiB,CAA5B,CAAb;MAEA,IAAI,CAACqC,IAAL,EAAW;QACT,IAAI,CAAC,KAAKlC,KAAL,CAAW4J,MAAhB,EAAwB;QAExB,KAAK/J,SAAL,GAAiB,KAAKG,KAAL,CAAW4J,MAA5B;QACA,KAAKjE,QAAL;QAEA;MACD;MAED,KAAK9F,SAAL;MACA,IAAIqC,IAAI,CAACC,QAAL,KAAkB,CAAC,CAAvB,EAA0B,KAAKwD,QAAL;IAC3B,CApOM;IAqOPE,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAM3D,IAAI,GAAG,KAAKlC,KAAL,CAAW,KAAKA,KAAL,CAAW4J,MAAX,GAAoB,CAA/B,CAAb;MAEA,IAAI,CAAC1H,IAAL,EAAW;MAEX,KAAKrC,SAAL,GAAiB,KAAKG,KAAL,CAAW4J,MAAX,GAAoB,CAArC;MAEA,IAAI1H,IAAI,CAACC,QAAL,KAAkB,CAAC,CAAvB,EAA0B,KAAKwD,QAAL;IAC3B,CA7OM;IA8OPI,SAAS,WAATA,SAASA,CAAA;MACP,IAAM7D,IAAI,GAAG,KAAKlC,KAAL,CAAW,CAAX,CAAb;MAEA,IAAI,CAACkC,IAAL,EAAW;MAEX,KAAKrC,SAAL,GAAiB,CAAjB;MAEA,IAAIqC,IAAI,CAACC,QAAL,KAAkB,CAAC,CAAvB,EAA0B,KAAKsD,QAAL;IAC3B,CAtPM;IAuPPsB,SAAS,WAATA,SAASA,CAAE3B,CAAF,EAAkB;MAAA,IAAAyE,MAAA;QAAAC,SAAA;MACzB,IAAI1E,CAAC,CAACE,OAAF,KAActH,QAAQ,CAAC+L,GAA3B,EAAgC;QAC9B;QACAC,WAAA,CAAW,YAAK;UAAGH,MAAA,CAAKnH,QAAL,GAAgB,KAAhB;QAAuB,CAAhC,CAAV;QACA,IAAMrB,SAAS,GAAG,KAAK4I,YAAL,EAAlB;QACA,KAAKC,SAAL,CAAe;UAAA,OAAM7I,SAAS,IAAIA,SAAS,CAAC8I,KAAV,EAAlC;QAAA;MACD,CALD,MAKO,IACL,CAAC,KAAKzH,QAAN,IACA0H,yBAAA,CAAAN,SAAA,IAAC9L,QAAQ,CAAC0H,EAAV,EAAc1H,QAAQ,CAACwH,IAAvB,GAAAvD,IAAA,CAAA6H,SAAA,EAAsC1E,CAAC,CAACE,OAAxC,CAFK,EAGL;QACA,KAAK5C,QAAL,GAAgB,IAAhB;MACD,CAXwB,CAazB;;MACA,KAAKwH,SAAL,CAAe;QAAA,OAAML,MAAA,CAAK1E,eAAL,CAAqBC,CAArB,CAArB;MAAA;IACD,CAtQM;IAuQPiF,QAAQ,WAARA,QAAQA,CAAA;MACN,IAAI,CAAC,KAAK3H,QAAV,EAAoB,OADd,CAGN;MACA;MACA;;MACA,KAAKQ,KAAL,CAAW1C,OAAX,CAAmB8J,WAAnB;MACA,KAAKpG,gBAAL,GAPM,CASN;MACA;MACA;MACA;MACA;;MACAqG,YAAY,CAAC,KAAKzK,aAAN,CAAZ;MACA,KAAKA,aAAL,GAAqBkK,WAAA,CAAkB,KAAK9F,gBAAvB,EAAyC,GAAzC,CAArB;IACD;EAvRM,CAvKsB;EAic/BsG,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IAAA,IAAAC,MAAA;IACP,IAAMjL,IAAI,GAAG;MACXyI,WAAW,EAAE,QADF;MAEX,SAAO;QACL,oBACE,KAAKyC,MAAL,KAAgB,EAAhB,IACA,KAAKA,MAAL,KAAgB,IADhB,IAEA,KAAKA,MAAL,KAAgB;MAJb,CAFI;MAQXtM,UAAU,EAAE,CAAC;QACXuM,GAAG,EAAE,KADM;QAEXxM,IAAI,EAAE,QAFK;QAGXiJ,KAAK,EAAE,KAAKgD;MAHD,CAAD;IARD,CAAb;IAeA,OAAOI,CAAC,CAAC,KAAD,EAAQhL,IAAR,EAAc,CACpB,CAAC,KAAK4B,SAAN,IAAmB,KAAKwJ,YAAL,EADC,EAEpB,KAAKC,eAAL,CAAqB;MAAA,OAAM,CACzBJ,MAAA,CAAKxD,cAAL,CAAoB/J,cAApB,EAAoC;QAClCsB,KAAK,EAAE;UACLsM,IAAI,EAAE,IADD;UAELC,KAAK,EAAEN,MAAA,CAAKM,KAFP;UAGLC,IAAI,EAAEP,MAAA,CAAKO;QAHN;MAD2B,CAApC,EAMG,CAACP,MAAA,CAAK1D,aAAL,EAAD,CANH,CADyB,CAA3B;IAAA,EAFoB,CAAd,CAAR;EAYD;AA7d8B,CAAlB,CAAf", "ignoreList": []}]}