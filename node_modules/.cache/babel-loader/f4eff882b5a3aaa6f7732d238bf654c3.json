{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAlert/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VAlert/index.js", "mtime": 1757335236778}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZBbGVydCBmcm9tICcuL1ZBbGVydCc7CmV4cG9ydCB7IFZBbGVydCB9OwpleHBvcnQgZGVmYXVsdCBWQWxlcnQ7"}, {"version": 3, "names": ["<PERSON><PERSON><PERSON>"], "sources": ["../../../src/components/VAlert/index.ts"], "sourcesContent": ["import VAlert from './VAlert'\n\nexport { VAlert }\nexport default VAlert\n"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,UAAnB;AAEA,SAASA,MAAT;AACA,eAAeA,MAAf", "ignoreList": []}]}