{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/resize/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/resize/index.js", "mtime": 1757335237025}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZnVuY3Rpb24gaW5zZXJ0ZWQoZWwsIGJpbmRpbmcsIHZub2RlKSB7CiAgdmFyIGNhbGxiYWNrID0gYmluZGluZy52YWx1ZTsKICB2YXIgb3B0aW9ucyA9IGJpbmRpbmcub3B0aW9ucyB8fCB7CiAgICBwYXNzaXZlOiB0cnVlCiAgfTsKICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2FsbGJhY2ssIG9wdGlvbnMpOwogIGVsLl9vblJlc2l6ZSA9IE9iamVjdChlbC5fb25SZXNpemUpOwogIGVsLl9vblJlc2l6ZVt2bm9kZS5jb250ZXh0Ll91aWRdID0gewogICAgY2FsbGJhY2s6IGNhbGxiYWNrLAogICAgb3B0aW9uczogb3B0aW9ucwogIH07CiAgaWYgKCFiaW5kaW5nLm1vZGlmaWVycyB8fCAhYmluZGluZy5tb2RpZmllcnMucXVpZXQpIHsKICAgIGNhbGxiYWNrKCk7CiAgfQp9CmZ1bmN0aW9uIHVuYmluZChlbCwgYmluZGluZywgdm5vZGUpIHsKICB2YXIgX2E7CiAgaWYgKCEoKF9hID0gZWwuX29uUmVzaXplKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Fbdm5vZGUuY29udGV4dC5fdWlkXSkpIHJldHVybjsKICB2YXIgX2VsJF9vblJlc2l6ZSR2bm9kZSRjID0gZWwuX29uUmVzaXplW3Zub2RlLmNvbnRleHQuX3VpZF0sCiAgICBjYWxsYmFjayA9IF9lbCRfb25SZXNpemUkdm5vZGUkYy5jYWxsYmFjaywKICAgIG9wdGlvbnMgPSBfZWwkX29uUmVzaXplJHZub2RlJGMub3B0aW9uczsKICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2FsbGJhY2ssIG9wdGlvbnMpOwogIGRlbGV0ZSBlbC5fb25SZXNpemVbdm5vZGUuY29udGV4dC5fdWlkXTsKfQpleHBvcnQgdmFyIFJlc2l6ZSA9IHsKICBpbnNlcnRlZDogaW5zZXJ0ZWQsCiAgdW5iaW5kOiB1bmJpbmQKfTsKZXhwb3J0IGRlZmF1bHQgUmVzaXplOw=="}, {"version": 3, "names": ["inserted", "el", "binding", "vnode", "callback", "value", "options", "passive", "window", "addEventListener", "_onResize", "Object", "context", "_uid", "modifiers", "quiet", "unbind", "_a", "_el$_onResize$vnode$c", "removeEventListener", "Resize"], "sources": ["../../../src/directives/resize/index.ts"], "sourcesContent": ["import { VNodeDirective } from 'vue/types/vnode'\nimport { VNode } from 'vue'\n\ninterface ResizeVNodeDirective extends VNodeDirective {\n  value?: () => void\n  options?: boolean | AddEventListenerOptions\n}\n\nfunction inserted (el: HTMLElement, binding: ResizeVNodeDirective, vnode: VNode) {\n  const callback = binding.value!\n  const options = binding.options || { passive: true }\n\n  window.addEventListener('resize', callback, options)\n\n  el._onResize = Object(el._onResize)\n  el._onResize![vnode.context!._uid] = {\n    callback,\n    options,\n  }\n\n  if (!binding.modifiers || !binding.modifiers.quiet) {\n    callback()\n  }\n}\n\nfunction unbind (el: HTMLElement, binding: ResizeVNodeDirective, vnode: VNode) {\n  if (!el._onResize?.[vnode.context!._uid]) return\n\n  const { callback, options } = el._onResize[vnode.context!._uid]!\n\n  window.removeEventListener('resize', callback, options)\n\n  delete el._onResize[vnode.context!._uid]\n}\n\nexport const Resize = {\n  inserted,\n  unbind,\n}\n\nexport default Resize\n"], "mappings": "AAQA,SAASA,QAATA,CAAmBC,EAAnB,EAAoCC,OAApC,EAAmEC,KAAnE,EAA+E;EAC7E,IAAMC,QAAQ,GAAGF,OAAO,CAACG,KAAzB;EACA,IAAMC,OAAO,GAAGJ,OAAO,CAACI,OAAR,IAAmB;IAAEC,OAAO,EAAE;EAAX,CAAnC;EAEAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkCL,QAAlC,EAA4CE,OAA5C;EAEAL,EAAE,CAACS,SAAH,GAAeC,MAAM,CAACV,EAAE,CAACS,SAAJ,CAArB;EACAT,EAAE,CAACS,SAAH,CAAcP,KAAK,CAACS,OAAN,CAAeC,IAA7B,IAAqC;IACnCT,QADmC,EACnCA,QADmC;IAEnCE,OAAA,EAAAA;EAFmC,CAArC;EAKA,IAAI,CAACJ,OAAO,CAACY,SAAT,IAAsB,CAACZ,OAAO,CAACY,SAAR,CAAkBC,KAA7C,EAAoD;IAClDX,QAAQ;EACT;AACF;AAED,SAASY,MAATA,CAAiBf,EAAjB,EAAkCC,OAAlC,EAAiEC,KAAjE,EAA6E;;EAC3E,IAAI,EAAC,CAAAc,EAAA,GAAAhB,EAAE,CAACS,SAAH,MAAY,IAAZ,IAAYO,EAAA,WAAZ,GAAY,MAAZ,GAAYA,EAAA,CAAGd,KAAK,CAACS,OAAN,CAAeC,IAAlB,CAAb,CAAJ,EAA0C;EAE1C,IAAAK,qBAAA,GAA8BjB,EAAE,CAACS,SAAH,CAAaP,KAAK,CAACS,OAAN,CAAeC,IAA5B,CAA9B;IAAQT,QAAF,GAAAc,qBAAA,CAAEd,QAAF;IAAYE,OAAA,GAAAY,qBAAA,CAAAZ,OAAA;EAElBE,MAAM,CAACW,mBAAP,CAA2B,QAA3B,EAAqCf,QAArC,EAA+CE,OAA/C;EAEA,OAAOL,EAAE,CAACS,SAAH,CAAaP,KAAK,CAACS,OAAN,CAAeC,IAA5B,CAAP;AACD;AAED,OAAO,IAAMO,MAAM,GAAG;EACpBpB,QADoB,EACpBA,QADoB;EAEpBgB,MAAA,EAAAA;AAFoB,CAAf;AAKP,eAAeI,MAAf", "ignoreList": []}]}