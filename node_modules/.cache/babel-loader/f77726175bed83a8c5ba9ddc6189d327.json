{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/touch/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/directives/touch/index.js", "mtime": 1757335237033}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["keys", "handleGesture", "wrapper", "touchstartX", "touchendX", "touchstartY", "touchendY", "dirRatio", "minDistance", "offsetX", "offsetY", "Math", "abs", "left", "right", "up", "down", "touchstart", "event", "touch", "changedTouches", "clientX", "clientY", "start", "_Object$assign", "touchend", "end", "touchmove", "touchmoveX", "touchmoveY", "move", "createHandlers", "value", "e", "inserted", "el", "binding", "vnode", "_context", "target", "parent", "parentElement", "options", "passive", "handlers", "_touchHandlers", "Object", "context", "_uid", "_forEachInstanceProperty", "call", "eventName", "addEventListener", "unbind", "_context2", "removeEventListener", "Touch"], "sources": ["../../../src/directives/touch/index.ts"], "sourcesContent": ["import { VNodeDirective, VNode } from 'vue/types/vnode'\nimport { keys } from '../../util/helpers'\nimport { TouchHandlers, TouchValue, TouchWrapper } from 'vuetify/types'\n\nexport interface TouchStoredHandlers {\n  touchstart: (e: TouchEvent) => void\n  touchend: (e: TouchEvent) => void\n  touchmove: (e: TouchEvent) => void\n}\n\ninterface TouchVNodeDirective extends VNodeDirective {\n  value?: TouchValue\n}\n\nconst handleGesture = (wrapper: TouchWrapper) => {\n  const { touchstartX, touchendX, touchstartY, touchendY } = wrapper\n  const dirRatio = 0.5\n  const minDistance = 16\n  wrapper.offsetX = touchendX - touchstartX\n  wrapper.offsetY = touchendY - touchstartY\n\n  if (Math.abs(wrapper.offsetY) < dirRatio * Math.abs(wrapper.offsetX)) {\n    wrapper.left && (touchendX < touchstartX - minDistance) && wrapper.left(wrapper)\n    wrapper.right && (touchendX > touchstartX + minDistance) && wrapper.right(wrapper)\n  }\n\n  if (Math.abs(wrapper.offsetX) < dirRatio * Math.abs(wrapper.offsetY)) {\n    wrapper.up && (touchendY < touchstartY - minDistance) && wrapper.up(wrapper)\n    wrapper.down && (touchendY > touchstartY + minDistance) && wrapper.down(wrapper)\n  }\n}\n\nfunction touchstart (event: TouchEvent, wrapper: TouchWrapper) {\n  const touch = event.changedTouches[0]\n  wrapper.touchstartX = touch.clientX\n  wrapper.touchstartY = touch.clientY\n\n  wrapper.start &&\n    wrapper.start(Object.assign(event, wrapper))\n}\n\nfunction touchend (event: TouchEvent, wrapper: TouchWrapper) {\n  const touch = event.changedTouches[0]\n  wrapper.touchendX = touch.clientX\n  wrapper.touchendY = touch.clientY\n\n  wrapper.end &&\n    wrapper.end(Object.assign(event, wrapper))\n\n  handleGesture(wrapper)\n}\n\nfunction touchmove (event: TouchEvent, wrapper: TouchWrapper) {\n  const touch = event.changedTouches[0]\n  wrapper.touchmoveX = touch.clientX\n  wrapper.touchmoveY = touch.clientY\n\n  wrapper.move && wrapper.move(Object.assign(event, wrapper))\n}\n\nfunction createHandlers (value: TouchHandlers): TouchStoredHandlers {\n  const wrapper = {\n    touchstartX: 0,\n    touchstartY: 0,\n    touchendX: 0,\n    touchendY: 0,\n    touchmoveX: 0,\n    touchmoveY: 0,\n    offsetX: 0,\n    offsetY: 0,\n    left: value.left,\n    right: value.right,\n    up: value.up,\n    down: value.down,\n    start: value.start,\n    move: value.move,\n    end: value.end,\n  }\n\n  return {\n    touchstart: (e: TouchEvent) => touchstart(e, wrapper),\n    touchend: (e: TouchEvent) => touchend(e, wrapper),\n    touchmove: (e: TouchEvent) => touchmove(e, wrapper),\n  }\n}\n\nfunction inserted (el: HTMLElement, binding: TouchVNodeDirective, vnode: VNode) {\n  const value = binding.value!\n  const target = value.parent ? el.parentElement : el\n  const options = value.options || { passive: true }\n\n  // Needed to pass unit tests\n  if (!target) return\n\n  const handlers = createHandlers(binding.value!)\n  target._touchHandlers = Object(target._touchHandlers)\n  target._touchHandlers![vnode.context!._uid] = handlers\n\n  keys(handlers).forEach(eventName => {\n    target.addEventListener(eventName, handlers[eventName] as EventListener, options)\n  })\n}\n\nfunction unbind (el: HTMLElement, binding: TouchVNodeDirective, vnode: VNode) {\n  const target = binding.value!.parent ? el.parentElement : el\n  if (!target || !target._touchHandlers) return\n\n  const handlers = target._touchHandlers[vnode.context!._uid]\n  keys(handlers).forEach(eventName => {\n    target.removeEventListener(eventName, handlers[eventName])\n  })\n  delete target._touchHandlers[vnode.context!._uid]\n}\n\nexport const Touch = {\n  inserted,\n  unbind,\n}\n\nexport default Touch\n"], "mappings": ";;AACA,SAASA,IAAT,QAAqB,oBAArB;AAaA,IAAMC,aAAa,GAAI,SAAjBA,aAAaA,CAAIC,OAAD,EAA0B;EAC9C,IAAQC,WAAF,GAAqDD,OAA3D,CAAQC,WAAF;IAAeC,SAAf,GAAqDF,OAA3D,CAAqBE,SAAf;IAA0BC,WAA1B,GAAqDH,OAA3D,CAAgCG,WAA1B;IAAuCC,SAAA,GAAcJ,OAA3D,CAA6CI,SAAA;EAC7C,IAAMC,QAAQ,GAAG,GAAjB;EACA,IAAMC,WAAW,GAAG,EAApB;EACAN,OAAO,CAACO,OAAR,GAAkBL,SAAS,GAAGD,WAA9B;EACAD,OAAO,CAACQ,OAAR,GAAkBJ,SAAS,GAAGD,WAA9B;EAEA,IAAIM,IAAI,CAACC,GAAL,CAASV,OAAO,CAACQ,OAAjB,IAA4BH,QAAQ,GAAGI,IAAI,CAACC,GAAL,CAASV,OAAO,CAACO,OAAjB,CAA3C,EAAsE;IACpEP,OAAO,CAACW,IAAR,IAAiBT,SAAS,GAAGD,WAAW,GAAGK,WAA3C,IAA2DN,OAAO,CAACW,IAAR,CAAaX,OAAb,CAA3D;IACAA,OAAO,CAACY,KAAR,IAAkBV,SAAS,GAAGD,WAAW,GAAGK,WAA5C,IAA4DN,OAAO,CAACY,KAAR,CAAcZ,OAAd,CAA5D;EACD;EAED,IAAIS,IAAI,CAACC,GAAL,CAASV,OAAO,CAACO,OAAjB,IAA4BF,QAAQ,GAAGI,IAAI,CAACC,GAAL,CAASV,OAAO,CAACQ,OAAjB,CAA3C,EAAsE;IACpER,OAAO,CAACa,EAAR,IAAeT,SAAS,GAAGD,WAAW,GAAGG,WAAzC,IAAyDN,OAAO,CAACa,EAAR,CAAWb,OAAX,CAAzD;IACAA,OAAO,CAACc,IAAR,IAAiBV,SAAS,GAAGD,WAAW,GAAGG,WAA3C,IAA2DN,OAAO,CAACc,IAAR,CAAad,OAAb,CAA3D;EACD;AACF,CAhBD;AAkBA,SAASe,WAATA,CAAqBC,KAArB,EAAwChB,OAAxC,EAA6D;EAC3D,IAAMiB,KAAK,GAAGD,KAAK,CAACE,cAAN,CAAqB,CAArB,CAAd;EACAlB,OAAO,CAACC,WAAR,GAAsBgB,KAAK,CAACE,OAA5B;EACAnB,OAAO,CAACG,WAAR,GAAsBc,KAAK,CAACG,OAA5B;EAEApB,OAAO,CAACqB,KAAR,IACErB,OAAO,CAACqB,KAAR,CAAcC,cAAA,CAAcN,KAAd,EAAqBhB,OAArB,CAAd,CADF;AAED;AAED,SAASuB,SAATA,CAAmBP,KAAnB,EAAsChB,OAAtC,EAA2D;EACzD,IAAMiB,KAAK,GAAGD,KAAK,CAACE,cAAN,CAAqB,CAArB,CAAd;EACAlB,OAAO,CAACE,SAAR,GAAoBe,KAAK,CAACE,OAA1B;EACAnB,OAAO,CAACI,SAAR,GAAoBa,KAAK,CAACG,OAA1B;EAEApB,OAAO,CAACwB,GAAR,IACExB,OAAO,CAACwB,GAAR,CAAYF,cAAA,CAAcN,KAAd,EAAqBhB,OAArB,CAAZ,CADF;EAGAD,aAAa,CAACC,OAAD,CAAb;AACD;AAED,SAASyB,UAATA,CAAoBT,KAApB,EAAuChB,OAAvC,EAA4D;EAC1D,IAAMiB,KAAK,GAAGD,KAAK,CAACE,cAAN,CAAqB,CAArB,CAAd;EACAlB,OAAO,CAAC0B,UAAR,GAAqBT,KAAK,CAACE,OAA3B;EACAnB,OAAO,CAAC2B,UAAR,GAAqBV,KAAK,CAACG,OAA3B;EAEApB,OAAO,CAAC4B,IAAR,IAAgB5B,OAAO,CAAC4B,IAAR,CAAaN,cAAA,CAAcN,KAAd,EAAqBhB,OAArB,CAAb,CAAhB;AACD;AAED,SAAS6B,cAATA,CAAyBC,KAAzB,EAA6C;EAC3C,IAAM9B,OAAO,GAAG;IACdC,WAAW,EAAE,CADC;IAEdE,WAAW,EAAE,CAFC;IAGdD,SAAS,EAAE,CAHG;IAIdE,SAAS,EAAE,CAJG;IAKdsB,UAAU,EAAE,CALE;IAMdC,UAAU,EAAE,CANE;IAOdpB,OAAO,EAAE,CAPK;IAQdC,OAAO,EAAE,CARK;IASdG,IAAI,EAAEmB,KAAK,CAACnB,IATE;IAUdC,KAAK,EAAEkB,KAAK,CAAClB,KAVC;IAWdC,EAAE,EAAEiB,KAAK,CAACjB,EAXI;IAYdC,IAAI,EAAEgB,KAAK,CAAChB,IAZE;IAadO,KAAK,EAAES,KAAK,CAACT,KAbC;IAcdO,IAAI,EAAEE,KAAK,CAACF,IAdE;IAedJ,GAAG,EAAEM,KAAK,CAACN;EAfG,CAAhB;EAkBA,OAAO;IACLT,UAAU,EAAG,SAAbA,UAAUA,CAAGgB,CAAD;MAAA,OAAmBhB,WAAU,CAACgB,CAAD,EAAI/B,OAAJ,CADpC;IAAA;IAELuB,QAAQ,EAAG,SAAXA,QAAQA,CAAGQ,CAAD;MAAA,OAAmBR,SAAQ,CAACQ,CAAD,EAAI/B,OAAJ,CAFhC;IAAA;IAGLyB,SAAS,EAAG,SAAZA,SAASA,CAAGM,CAAD;MAAA,OAAmBN,UAAS,CAACM,CAAD,EAAI/B,OAAJ;IAAA;EAHlC,CAAP;AAKD;AAED,SAASgC,QAATA,CAAmBC,EAAnB,EAAoCC,OAApC,EAAkEC,KAAlE,EAA8E;EAAA,IAAAC,QAAA;EAC5E,IAAMN,KAAK,GAAGI,OAAO,CAACJ,KAAtB;EACA,IAAMO,MAAM,GAAGP,KAAK,CAACQ,MAAN,GAAeL,EAAE,CAACM,aAAlB,GAAkCN,EAAjD;EACA,IAAMO,OAAO,GAAGV,KAAK,CAACU,OAAN,IAAiB;IAAEC,OAAO,EAAE;EAAX,CAAjC,CAH4E,CAK5E;;EACA,IAAI,CAACJ,MAAL,EAAa;EAEb,IAAMK,QAAQ,GAAGb,cAAc,CAACK,OAAO,CAACJ,KAAT,CAA/B;EACAO,MAAM,CAACM,cAAP,GAAwBC,MAAM,CAACP,MAAM,CAACM,cAAR,CAA9B;EACAN,MAAM,CAACM,cAAP,CAAuBR,KAAK,CAACU,OAAN,CAAeC,IAAtC,IAA8CJ,QAA9C;EAEAK,wBAAA,CAAAX,QAAA,GAAAtC,IAAI,CAAC4C,QAAD,CAAJ,EAAAM,IAAA,CAAAZ,QAAA,EAAuB,UAAAa,SAAS,EAAG;IACjCZ,MAAM,CAACa,gBAAP,CAAwBD,SAAxB,EAAmCP,QAAQ,CAACO,SAAD,CAA3C,EAAyET,OAAzE;EACD,CAFD;AAGD;AAED,SAASW,MAATA,CAAiBlB,EAAjB,EAAkCC,OAAlC,EAAgEC,KAAhE,EAA4E;EAAA,IAAAiB,SAAA;EAC1E,IAAMf,MAAM,GAAGH,OAAO,CAACJ,KAAR,CAAeQ,MAAf,GAAwBL,EAAE,CAACM,aAA3B,GAA2CN,EAA1D;EACA,IAAI,CAACI,MAAD,IAAW,CAACA,MAAM,CAACM,cAAvB,EAAuC;EAEvC,IAAMD,QAAQ,GAAGL,MAAM,CAACM,cAAP,CAAsBR,KAAK,CAACU,OAAN,CAAeC,IAArC,CAAjB;EACAC,wBAAA,CAAAK,SAAA,GAAAtD,IAAI,CAAC4C,QAAD,CAAJ,EAAAM,IAAA,CAAAI,SAAA,EAAuB,UAAAH,SAAS,EAAG;IACjCZ,MAAM,CAACgB,mBAAP,CAA2BJ,SAA3B,EAAsCP,QAAQ,CAACO,SAAD,CAA9C;EACD,CAFD;EAGA,OAAOZ,MAAM,CAACM,cAAP,CAAsBR,KAAK,CAACU,OAAN,CAAeC,IAArC,CAAP;AACD;AAED,OAAO,IAAMQ,KAAK,GAAG;EACnBtB,QADmB,EACnBA,QADmB;EAEnBmB,MAAA,EAAAA;AAFmB,CAAd;AAKP,eAAeG,KAAf", "ignoreList": []}]}