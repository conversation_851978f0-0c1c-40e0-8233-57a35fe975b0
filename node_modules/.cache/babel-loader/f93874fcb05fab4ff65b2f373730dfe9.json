{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/applicationable/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/mixins/applicationable/index.js", "mtime": 1757335237044}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["factory", "PositionableFactory", "mixins", "applicationable", "value", "events", "arguments", "length", "undefined", "extend", "name", "props", "app", "Boolean", "computed", "applicationProperty", "watch", "x", "prev", "removeApplication", "callUpdate", "newVal", "oldVal", "$vuetify", "application", "unregister", "_uid", "activated", "created", "i", "$watch", "mounted", "deactivated", "destroyed", "methods", "register", "updateApplication", "force"], "sources": ["../../../src/mixins/applicationable/index.ts"], "sourcesContent": ["import { factory as PositionableFactory } from '../positionable'\nimport { TargetProp } from 'vuetify/types/services/application'\n\n// Util\nimport mixins from '../../util/mixins'\n\nexport default function applicationable (value: TargetProp, events: string[] = []) {\n  /* @vue/component */\n  return mixins(PositionableFactory(['absolute', 'fixed'])).extend({\n    name: 'applicationable',\n\n    props: {\n      app: Boolean,\n    },\n\n    computed: {\n      applicationProperty (): TargetProp {\n        return value\n      },\n    },\n\n    watch: {\n      // If previous value was app\n      // reset the provided prop\n      app (x: boolean, prev: boolean) {\n        prev\n          ? this.removeApplication(true)\n          : this.callUpdate()\n      },\n      applicationProperty (newVal, oldVal) {\n        this.$vuetify.application.unregister(this._uid, oldVal)\n      },\n    },\n\n    activated () {\n      this.callUpdate()\n    },\n\n    created () {\n      for (let i = 0, length = events.length; i < length; i++) {\n        this.$watch(events[i], this.callUpdate)\n      }\n      this.callUpdate()\n    },\n\n    mounted () {\n      this.callUpdate()\n    },\n\n    deactivated () {\n      this.removeApplication()\n    },\n\n    destroyed () {\n      this.removeApplication()\n    },\n\n    methods: {\n      callUpdate () {\n        if (!this.app) return\n\n        this.$vuetify.application.register(\n          this._uid,\n          this.applicationProperty,\n          this.updateApplication()\n        )\n      },\n      removeApplication (force = false) {\n        if (!force && !this.app) return\n\n        this.$vuetify.application.unregister(\n          this._uid,\n          this.applicationProperty\n        )\n      },\n      updateApplication: () => 0,\n    },\n  })\n}\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,mBAApB,QAA+C,iBAA/C,C,CAGA;;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAEA,eAAc,SAAUC,eAAVA,CAA2BC,KAA3B,EAAmE;EAAA,IAArBC,MAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAmB,EAAjE;EACZ;EACA,OAAOJ,MAAM,CAACD,mBAAmB,CAAC,CAAC,UAAD,EAAa,OAAb,CAAD,CAApB,CAAN,CAAmDQ,MAAnD,CAA0D;IAC/DC,IAAI,EAAE,iBADyD;IAG/DC,KAAK,EAAE;MACLC,GAAG,EAAEC;IADA,CAHwD;IAO/DC,QAAQ,EAAE;MACRC,mBAAmB,WAAnBA,mBAAmBA,CAAA;QACjB,OAAOX,KAAP;MACD;IAHO,CAPqD;IAa/DY,KAAK,EAAE;MACL;MACA;MACAJ,GAAG,WAAHA,GAAGA,CAAEK,CAAF,EAAcC,IAAd,EAA2B;QAC5BA,IAAI,GACA,KAAKC,iBAAL,CAAuB,IAAvB,CADA,GAEA,KAAKC,UAAL,EAFJ;MAGD,CAPI;MAQLL,mBAAmB,WAAnBA,mBAAmBA,CAAEM,MAAF,EAAUC,MAAV,EAAgB;QACjC,KAAKC,QAAL,CAAcC,WAAd,CAA0BC,UAA1B,CAAqC,KAAKC,IAA1C,EAAgDJ,MAAhD;MACD;IAVI,CAbwD;IA0B/DK,SAAS,WAATA,SAASA,CAAA;MACP,KAAKP,UAAL;IACD,CA5B8D;IA8B/DQ,OAAO,WAAPA,OAAOA,CAAA;MACL,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWtB,MAAM,GAAGF,MAAM,CAACE,MAAhC,EAAwCsB,CAAC,GAAGtB,MAA5C,EAAoDsB,CAAC,EAArD,EAAyD;QACvD,KAAKC,MAAL,CAAYzB,MAAM,CAACwB,CAAD,CAAlB,EAAuB,KAAKT,UAA5B;MACD;MACD,KAAKA,UAAL;IACD,CAnC8D;IAqC/DW,OAAO,WAAPA,OAAOA,CAAA;MACL,KAAKX,UAAL;IACD,CAvC8D;IAyC/DY,WAAW,WAAXA,WAAWA,CAAA;MACT,KAAKb,iBAAL;IACD,CA3C8D;IA6C/Dc,SAAS,WAATA,SAASA,CAAA;MACP,KAAKd,iBAAL;IACD,CA/C8D;IAiD/De,OAAO,EAAE;MACPd,UAAU,WAAVA,UAAUA,CAAA;QACR,IAAI,CAAC,KAAKR,GAAV,EAAe;QAEf,KAAKW,QAAL,CAAcC,WAAd,CAA0BW,QAA1B,CACE,KAAKT,IADP,EAEE,KAAKX,mBAFP,EAGE,KAAKqB,iBAAL,EAHF;MAKD,CATM;MAUPjB,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAe;QAAA,IAAbkB,KAAK,GAAA/B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAV;QACf,IAAI,CAAC+B,KAAD,IAAU,CAAC,KAAKzB,GAApB,EAAyB;QAEzB,KAAKW,QAAL,CAAcC,WAAd,CAA0BC,UAA1B,CACE,KAAKC,IADP,EAEE,KAAKX,mBAFP;MAID,CAjBM;MAkBPqB,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAA;QAAA,OAAQ;MAAA;IAlBlB;EAjDsD,CAA1D,CAAP;AAsED", "ignoreList": []}]}