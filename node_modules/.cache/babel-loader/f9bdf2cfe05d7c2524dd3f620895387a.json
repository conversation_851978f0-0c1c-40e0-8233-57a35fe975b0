{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/index.js", "mtime": 1757335237034}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["default", "af", "ar", "bg", "ca", "ckb", "cs", "da", "de", "el", "en", "es", "et", "fa", "fi", "fr", "hr", "hu", "he", "id", "it", "ja", "ko", "lv", "lt", "nl", "no", "pl", "pt", "ro", "ru", "sk", "sl", "srCyrl", "srLatn", "sv", "th", "tr", "az", "uk", "vi", "zhHans", "zhHant"], "sources": ["../../src/locale/index.ts"], "sourcesContent": ["export { default as af } from './af'\nexport { default as ar } from './ar'\nexport { default as bg } from './bg'\nexport { default as ca } from './ca'\nexport { default as ckb } from './ckb'\nexport { default as cs } from './cs'\nexport { default as da } from './da'\nexport { default as de } from './de'\nexport { default as el } from './el'\nexport { default as en } from './en'\nexport { default as es } from './es'\nexport { default as et } from './et'\nexport { default as fa } from './fa'\nexport { default as fi } from './fi'\nexport { default as fr } from './fr'\nexport { default as hr } from './hr'\nexport { default as hu } from './hu'\nexport { default as he } from './he'\nexport { default as id } from './id'\nexport { default as it } from './it'\nexport { default as ja } from './ja'\nexport { default as ko } from './ko'\nexport { default as lv } from './lv'\nexport { default as lt } from './lt'\nexport { default as nl } from './nl'\nexport { default as no } from './no'\nexport { default as pl } from './pl'\nexport { default as pt } from './pt'\nexport { default as ro } from './ro'\nexport { default as ru } from './ru'\nexport { default as sk } from './sk'\nexport { default as sl } from './sl'\nexport { default as srCyrl } from './sr-Cyrl'\nexport { default as srLatn } from './sr-Latn'\nexport { default as sv } from './sv'\nexport { default as th } from './th'\nexport { default as tr } from './tr'\nexport { default as az } from './az'\nexport { default as uk } from './uk'\nexport { default as vi } from './vi'\nexport { default as zhHans } from './zh-Hans'\nexport { default as zhHant } from './zh-Hant'\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,EAApB,QAA8B,MAA9B;AACA,SAASD,OAAO,IAAIE,EAApB,QAA8B,MAA9B;AACA,SAASF,OAAO,IAAIG,EAApB,QAA8B,MAA9B;AACA,SAASH,OAAO,IAAII,EAApB,QAA8B,MAA9B;AACA,SAASJ,OAAO,IAAIK,GAApB,QAA+B,OAA/B;AACA,SAASL,OAAO,IAAIM,EAApB,QAA8B,MAA9B;AACA,SAASN,OAAO,IAAIO,EAApB,QAA8B,MAA9B;AACA,SAASP,OAAO,IAAIQ,EAApB,QAA8B,MAA9B;AACA,SAASR,OAAO,IAAIS,EAApB,QAA8B,MAA9B;AACA,SAAST,OAAO,IAAIU,EAApB,QAA8B,MAA9B;AACA,SAASV,OAAO,IAAIW,EAApB,QAA8B,MAA9B;AACA,SAASX,OAAO,IAAIY,EAApB,QAA8B,MAA9B;AACA,SAASZ,OAAO,IAAIa,EAApB,QAA8B,MAA9B;AACA,SAASb,OAAO,IAAIc,EAApB,QAA8B,MAA9B;AACA,SAASd,OAAO,IAAIe,EAApB,QAA8B,MAA9B;AACA,SAASf,OAAO,IAAIgB,EAApB,QAA8B,MAA9B;AACA,SAAShB,OAAO,IAAIiB,EAApB,QAA8B,MAA9B;AACA,SAASjB,OAAO,IAAIkB,EAApB,QAA8B,MAA9B;AACA,SAASlB,OAAO,IAAImB,EAApB,QAA8B,MAA9B;AACA,SAASnB,OAAO,IAAIoB,EAApB,QAA8B,MAA9B;AACA,SAASpB,OAAO,IAAIqB,EAApB,QAA8B,MAA9B;AACA,SAASrB,OAAO,IAAIsB,EAApB,QAA8B,MAA9B;AACA,SAAStB,OAAO,IAAIuB,EAApB,QAA8B,MAA9B;AACA,SAASvB,OAAO,IAAIwB,EAApB,QAA8B,MAA9B;AACA,SAASxB,OAAO,IAAIyB,EAApB,QAA8B,MAA9B;AACA,SAASzB,OAAO,IAAI0B,EAApB,QAA8B,MAA9B;AACA,SAAS1B,OAAO,IAAI2B,EAApB,QAA8B,MAA9B;AACA,SAAS3B,OAAO,IAAI4B,EAApB,QAA8B,MAA9B;AACA,SAAS5B,OAAO,IAAI6B,EAApB,QAA8B,MAA9B;AACA,SAAS7B,OAAO,IAAI8B,EAApB,QAA8B,MAA9B;AACA,SAAS9B,OAAO,IAAI+B,EAApB,QAA8B,MAA9B;AACA,SAAS/B,OAAO,IAAIgC,EAApB,QAA8B,MAA9B;AACA,SAAShC,OAAO,IAAIiC,MAApB,QAAkC,WAAlC;AACA,SAASjC,OAAO,IAAIkC,MAApB,QAAkC,WAAlC;AACA,SAASlC,OAAO,IAAImC,EAApB,QAA8B,MAA9B;AACA,SAASnC,OAAO,IAAIoC,EAApB,QAA8B,MAA9B;AACA,SAASpC,OAAO,IAAIqC,EAApB,QAA8B,MAA9B;AACA,SAASrC,OAAO,IAAIsC,EAApB,QAA8B,MAA9B;AACA,SAAStC,OAAO,IAAIuC,EAApB,QAA8B,MAA9B;AACA,SAASvC,OAAO,IAAIwC,EAApB,QAA8B,MAA9B;AACA,SAASxC,OAAO,IAAIyC,MAApB,QAAkC,WAAlC;AACA,SAASzC,OAAO,IAAI0C,MAApB,QAAkC,WAAlC", "ignoreList": []}]}