{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ro.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/locale/ro.js", "mtime": 1757335237494}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["badge", "close", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "datePicker", "itemsSelected", "nextMonthAriaLabel", "nextYearAriaLabel", "prevMonthAriaLabel", "prevYearAriaLabel", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "fileInput", "counter", "counterSize", "timePicker", "am", "pm", "pagination", "wrapper", "previous", "page", "currentPage", "rating", "icon"], "sources": ["../../src/locale/ro.ts"], "sourcesContent": ["export default {\n  badge: 'Insignă',\n  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n  dataIterator: {\n    noResultsText: 'Nu s-au găsit înregistrări corespunzătoare',\n    loadingText: 'Se încarcă articolele...',\n  },\n  dataTable: {\n    itemsPerPageText: '<PERSON><PERSON><PERSON><PERSON> pe pagină:',\n    ariaLabel: {\n      sortDescending: 'Sortate descendent.',\n      sortAscending: 'Sortate ascendent.',\n      sortNone: 'Nesortate.',\n      activateNone: 'Activați pentru a elimina sortarea.',\n      activateDescending: 'Activați pentru a sorta descendent.',\n      activateAscending: 'Activați pentru a sorta ascendent.',\n    },\n    sortBy: 'Sortați după',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Articole pe pagină:',\n    itemsPerPageAll: 'Toate',\n    nextPage: 'Pagina următoare',\n    prevPage: 'Pagina anterioară',\n    firstPage: 'Prima pagină',\n    lastPage: 'Ultima pagină',\n    pageText: '{0}-{1} din {2}',\n  },\n  datePicker: {\n    itemsSelected: '{0} selectate',\n    nextMonthAriaLabel: 'Luna următoare',\n    nextYearAriaLabel: 'Anul următor',\n    prevMonthAriaLabel: 'Luna anterioară',\n    prevYearAriaLabel: 'Anul anterior',\n  },\n  noDataText: 'Nu există date disponibile',\n  carousel: {\n    prev: 'Grafica anterioară',\n    next: 'Grafica următoare',\n    ariaLabel: {\n      delimiter: 'Slide carusel {0} din {1}',\n    },\n  },\n  calendar: {\n    moreEvents: 'încă {0}',\n  },\n  fileInput: {\n    counter: '{0} fișiere',\n    counterSize: '{0} fișiere ({1} în total)',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n  },\n  pagination: {\n    ariaLabel: {\n      wrapper: 'Navigare prin paginare',\n      next: 'Pagina următoare',\n      previous: 'Pagina anterioară',\n      page: 'Mergeți la pagina {0}',\n      currentPage: 'Pagina curentă, pagina {0}',\n    },\n  },\n  rating: {\n    ariaLabel: {\n      icon: 'Rating de {0} din {1}',\n    },\n  },\n}\n"], "mappings": "AAAA,eAAe;EACbA,KAAK,EAAE,SADM;EAEbC,KAAK,EAAE,WAFM;EAGbC,YAAY,EAAE;IACZC,aAAa,EAAE,4CADH;IAEZC,WAAW,EAAE;EAFD,CAHD;EAObC,SAAS,EAAE;IACTC,gBAAgB,EAAE,oBADT;IAETC,SAAS,EAAE;MACTC,cAAc,EAAE,qBADP;MAETC,aAAa,EAAE,oBAFN;MAGTC,QAAQ,EAAE,YAHD;MAITC,YAAY,EAAE,qCAJL;MAKTC,kBAAkB,EAAE,qCALX;MAMTC,iBAAiB,EAAE;IANV,CAFF;IAUTC,MAAM,EAAE;EAVC,CAPE;EAmBbC,UAAU,EAAE;IACVT,gBAAgB,EAAE,qBADR;IAEVU,eAAe,EAAE,OAFP;IAGVC,QAAQ,EAAE,kBAHA;IAIVC,QAAQ,EAAE,mBAJA;IAKVC,SAAS,EAAE,cALD;IAMVC,QAAQ,EAAE,eANA;IAOVC,QAAQ,EAAE;EAPA,CAnBC;EA4BbC,UAAU,EAAE;IACVC,aAAa,EAAE,eADL;IAEVC,kBAAkB,EAAE,gBAFV;IAGVC,iBAAiB,EAAE,cAHT;IAIVC,kBAAkB,EAAE,iBAJV;IAKVC,iBAAiB,EAAE;EALT,CA5BC;EAmCbC,UAAU,EAAE,4BAnCC;EAoCbC,QAAQ,EAAE;IACRC,IAAI,EAAE,oBADE;IAERC,IAAI,EAAE,mBAFE;IAGRxB,SAAS,EAAE;MACTyB,SAAS,EAAE;IADF;EAHH,CApCG;EA2CbC,QAAQ,EAAE;IACRC,UAAU,EAAE;EADJ,CA3CG;EA8CbC,SAAS,EAAE;IACTC,OAAO,EAAE,aADA;IAETC,WAAW,EAAE;EAFJ,CA9CE;EAkDbC,UAAU,EAAE;IACVC,EAAE,EAAE,IADM;IAEVC,EAAE,EAAE;EAFM,CAlDC;EAsDbC,UAAU,EAAE;IACVlC,SAAS,EAAE;MACTmC,OAAO,EAAE,wBADA;MAETX,IAAI,EAAE,kBAFG;MAGTY,QAAQ,EAAE,mBAHD;MAITC,IAAI,EAAE,uBAJG;MAKTC,WAAW,EAAE;IALJ;EADD,CAtDC;EA+DbC,MAAM,EAAE;IACNvC,SAAS,EAAE;MACTwC,IAAI,EAAE;IADG;EADL;AA/DK,CAAf", "ignoreList": []}]}