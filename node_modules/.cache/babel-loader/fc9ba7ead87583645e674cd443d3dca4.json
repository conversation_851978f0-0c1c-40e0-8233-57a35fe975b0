{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/Document.vue?vue&type=template&id=5ffe286a", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/Document.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "height", "_l", "examples", "example", "i", "key", "tile", "align", "justify", "staticClass", "staticStyle", "background", "_v", "_s", "title", "src", "url", "contain", "scopedSlots", "_u", "fn", "indeterminate", "color", "proxy", "doc<PERSON>rls", "item", "outlined", "dark", "block", "on", "click", "$event", "window", "open", "left", "icon", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/Downloads/web-demo/src/components/Document.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"v-carousel\",\n        {\n          attrs: {\n            height: \"500\",\n            \"hide-delimiters\": \"\",\n            \"show-arrows-on-hover\": \"\",\n          },\n        },\n        _vm._l(_vm.examples, function (example, i) {\n          return _c(\n            \"v-carousel-item\",\n            { key: i },\n            [\n              _c(\n                \"v-sheet\",\n                { attrs: { tile: \"\" } },\n                [\n                  _c(\n                    \"v-row\",\n                    { attrs: { align: \"center\", justify: \"center\" } },\n                    [\n                      _c(\n                        \"v-col\",\n                        {\n                          staticClass: \"pt-4 pb-1\",\n                          staticStyle: {\n                            \"font-size\": \"20px\",\n                            background: \"#616161\",\n                          },\n                          attrs: { align: \"center\" },\n                        },\n                        [_vm._v(\" \" + _vm._s(example.title) + \" \")]\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"v-row\",\n                    {\n                      staticClass: \"fill-width\",\n                      attrs: { align: \"center\", justify: \"center\" },\n                    },\n                    [\n                      _c(\"v-img\", {\n                        attrs: { src: example.url, contain: \"\" },\n                        scopedSlots: _vm._u(\n                          [\n                            {\n                              key: \"placeholder\",\n                              fn: function () {\n                                return [\n                                  _c(\n                                    \"v-row\",\n                                    {\n                                      staticClass: \"fill-height\",\n                                      attrs: {\n                                        align: \"center\",\n                                        justify: \"center\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"v-progress-circular\", {\n                                        attrs: {\n                                          indeterminate: \"\",\n                                          color: \"grey lighten-5\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ]\n                              },\n                              proxy: true,\n                            },\n                          ],\n                          null,\n                          true\n                        ),\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          )\n        }),\n        1\n      ),\n      _c(\n        \"v-row\",\n        _vm._l(_vm.docUrls, function (item) {\n          return _c(\n            \"v-col\",\n            { key: item.url },\n            [\n              _c(\n                \"v-btn\",\n                {\n                  attrs: {\n                    outlined: item.outlined,\n                    color: item.color,\n                    dark: \"\",\n                    block: \"\",\n                  },\n                  on: {\n                    click: function ($event) {\n                      return _vm.window.open(item.url)\n                    },\n                  },\n                },\n                [\n                  _c(\"v-icon\", { attrs: { left: \"\", dark: \"\" } }, [\n                    _vm._v(\" \" + _vm._s(item.icon) + \" \"),\n                  ]),\n                  _vm._v(\" \" + _vm._s(item.title) + \" \"),\n                ],\n                1\n              ),\n            ],\n            1\n          )\n        }),\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CACA,YAAY,EACZ;IACEE,KAAK,EAAE;MACLC,MAAM,EAAE,KAAK;MACb,iBAAiB,EAAE,EAAE;MACrB,sBAAsB,EAAE;IAC1B;EACF,CAAC,EACDJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,QAAQ,EAAE,UAAUC,OAAO,EAAEC,CAAC,EAAE;IACzC,OAAOP,EAAE,CACP,iBAAiB,EACjB;MAAEQ,GAAG,EAAED;IAAE,CAAC,EACV,CACEP,EAAE,CACA,SAAS,EACT;MAAEE,KAAK,EAAE;QAAEO,IAAI,EAAE;MAAG;IAAE,CAAC,EACvB,CACET,EAAE,CACA,OAAO,EACP;MAAEE,KAAK,EAAE;QAAEQ,KAAK,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAS;IAAE,CAAC,EACjD,CACEX,EAAE,CACA,OAAO,EACP;MACEY,WAAW,EAAE,WAAW;MACxBC,WAAW,EAAE;QACX,WAAW,EAAE,MAAM;QACnBC,UAAU,EAAE;MACd,CAAC;MACDZ,KAAK,EAAE;QAAEQ,KAAK,EAAE;MAAS;IAC3B,CAAC,EACD,CAACX,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACV,OAAO,CAACW,KAAK,CAAC,GAAG,GAAG,CAAC,CAC5C,CAAC,CACF,EACD,CACF,CAAC,EACDjB,EAAE,CACA,OAAO,EACP;MACEY,WAAW,EAAE,YAAY;MACzBV,KAAK,EAAE;QAAEQ,KAAK,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAS;IAC9C,CAAC,EACD,CACEX,EAAE,CAAC,OAAO,EAAE;MACVE,KAAK,EAAE;QAAEgB,GAAG,EAAEZ,OAAO,CAACa,GAAG;QAAEC,OAAO,EAAE;MAAG,CAAC;MACxCC,WAAW,EAAEtB,GAAG,CAACuB,EAAE,CACjB,CACE;QACEd,GAAG,EAAE,aAAa;QAClBe,EAAE,EAAE,SAAJA,EAAEA,CAAA,EAAc;UACd,OAAO,CACLvB,EAAE,CACA,OAAO,EACP;YACEY,WAAW,EAAE,aAAa;YAC1BV,KAAK,EAAE;cACLQ,KAAK,EAAE,QAAQ;cACfC,OAAO,EAAE;YACX;UACF,CAAC,EACD,CACEX,EAAE,CAAC,qBAAqB,EAAE;YACxBE,KAAK,EAAE;cACLsB,aAAa,EAAE,EAAE;cACjBC,KAAK,EAAE;YACT;UACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF;QACH,CAAC;QACDC,KAAK,EAAE;MACT,CAAC,CACF,EACD,IAAI,EACJ,IACF;IACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,EACD1B,EAAE,CACA,OAAO,EACPD,GAAG,CAACK,EAAE,CAACL,GAAG,CAAC4B,OAAO,EAAE,UAAUC,IAAI,EAAE;IAClC,OAAO5B,EAAE,CACP,OAAO,EACP;MAAEQ,GAAG,EAAEoB,IAAI,CAACT;IAAI,CAAC,EACjB,CACEnB,EAAE,CACA,OAAO,EACP;MACEE,KAAK,EAAE;QACL2B,QAAQ,EAAED,IAAI,CAACC,QAAQ;QACvBJ,KAAK,EAAEG,IAAI,CAACH,KAAK;QACjBK,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACT,CAAC;MACDC,EAAE,EAAE;QACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;UACvB,OAAOnC,GAAG,CAACoC,MAAM,CAACC,IAAI,CAACR,IAAI,CAACT,GAAG,CAAC;QAClC;MACF;IACF,CAAC,EACD,CACEnB,EAAE,CAAC,QAAQ,EAAE;MAAEE,KAAK,EAAE;QAAEmC,IAAI,EAAE,EAAE;QAAEP,IAAI,EAAE;MAAG;IAAE,CAAC,EAAE,CAC9C/B,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACY,IAAI,CAACU,IAAI,CAAC,GAAG,GAAG,CAAC,CACtC,CAAC,EACFvC,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACY,IAAI,CAACX,KAAK,CAAC,GAAG,GAAG,CAAC,CACvC,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIsB,eAAe,GAAG,EAAE;AACxBzC,MAAM,CAAC0C,aAAa,GAAG,IAAI;AAE3B,SAAS1C,MAAM,EAAEyC,eAAe", "ignoreList": []}]}