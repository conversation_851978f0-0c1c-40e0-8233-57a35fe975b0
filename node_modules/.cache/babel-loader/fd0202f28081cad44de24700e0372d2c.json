{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VNavigationDrawer/VNavigationDrawer.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VNavigationDrawer/VNavigationDrawer.js", "mtime": 1757335238551}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["VImg", "Applicationable", "Colorable", "Dependent", "Mobile", "Overlayable", "SSRBootable", "Themeable", "ClickOutside", "Resize", "Touch", "convertToUnit", "getSlot", "mixins", "baseMixins", "extend", "name", "directives", "provide", "isInNav", "tag", "props", "bottom", "Boolean", "clipped", "disableResizeWatcher", "disableRouteWatcher", "expandOnHover", "floating", "height", "type", "Number", "String", "default", "app", "miniVariant", "miniVariantWidth", "permanent", "right", "src", "Object", "stateless", "temporary", "touchless", "width", "value", "data", "isMouseover", "touchArea", "left", "stackMinZIndex", "computed", "applicationProperty", "classes", "_objectSpread", "absolute", "isActive", "fixed", "isMobile", "isMiniVariant", "themeClasses", "computedMaxHeight", "hasApp", "$vuetify", "application", "footer", "bar", "top", "computedTop", "computedTransform", "isBottom", "computedWidth", "options", "call", "reactsToClick", "reactsToMobile", "reactsToResize", "reactsToRoute", "showOverlay", "hideOverlay", "styles", "_context", "translate", "maxHeight", "concat", "undefined", "transform", "_concatInstanceProperty", "watch", "$route", "val", "$emit", "prev", "removeOverlay", "genOverlay", "init", "updateMiniVariant", "beforeMount", "methods", "calculateTouchArea", "parent", "$el", "parentNode", "parentRect", "getBoundingClientRect", "closeConditional", "_isDestroyed", "genAppend", "genPosition", "genBackground", "image", "$scopedSlots", "img", "$createElement", "staticClass", "genDirectives", "_this", "handler", "include", "getOpenDependentElements", "push", "swipeLeft", "swipeRight", "genListeners", "_this2", "on", "mouseenter", "mouseleave", "transitionend", "e", "target", "currentTarget", "resizeEvent", "document", "createEvent", "initUIEvent", "window", "dispatchEvent", "click", "slot", "genPrepend", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$slots", "genBorder", "onRouteChange", "Math", "abs", "touchendX", "touchstartX", "updateApplication", "isNaN", "clientWidth", "render", "h", "children", "unshift", "setBackgroundColor", "color", "style"], "sources": ["../../../src/components/VNavigationDrawer/VNavigationDrawer.ts"], "sourcesContent": ["// Styles\nimport './VNavigationDrawer.sass'\n\n// Components\nimport VImg, { srcObject } from '../VImg/VImg'\n\n// Mixins\nimport Applicationable from '../../mixins/applicationable'\nimport Colorable from '../../mixins/colorable'\nimport Dependent from '../../mixins/dependent'\nimport Mobile from '../../mixins/mobile'\nimport Overlayable from '../../mixins/overlayable'\nimport SSRBootable from '../../mixins/ssr-bootable'\nimport Themeable from '../../mixins/themeable'\n\n// Directives\nimport ClickOutside from '../../directives/click-outside'\nimport Resize from '../../directives/resize'\nimport Touch from '../../directives/touch'\n\n// Utilities\nimport { convertToUnit, getSlot } from '../../util/helpers'\nimport mixins from '../../util/mixins'\n\n// Types\nimport { VNode, VNodeDirective, PropType } from 'vue'\nimport { TouchWrapper } from 'vuetify/types'\n\nconst baseMixins = mixins(\n  Applicationable('left', [\n    'isActive',\n    'isMobile',\n    'miniVariant',\n    'expandOnHover',\n    'permanent',\n    'right',\n    'temporary',\n    'width',\n  ]),\n  Colorable,\n  Dependent,\n  Mobile,\n  Overlayable,\n  SSRBootable,\n  Themeable\n)\n\n/* @vue/component */\nexport default baseMixins.extend({\n  name: 'v-navigation-drawer',\n\n  directives: {\n    ClickOutside,\n    Resize,\n    Touch,\n  },\n\n  provide (): object {\n    return {\n      isInNav: this.tag === 'nav',\n    }\n  },\n\n  props: {\n    bottom: Boolean,\n    clipped: Boolean,\n    disableResizeWatcher: Boolean,\n    disableRouteWatcher: Boolean,\n    expandOnHover: Boolean,\n    floating: Boolean,\n    height: {\n      type: [Number, String],\n      default (): string {\n        return this.app ? '100vh' : '100%'\n      },\n    },\n    miniVariant: Boolean,\n    miniVariantWidth: {\n      type: [Number, String],\n      default: 56,\n    },\n    permanent: Boolean,\n    right: Boolean,\n    src: {\n      type: [String, Object] as PropType<string | srcObject>,\n      default: '',\n    },\n    stateless: Boolean,\n    tag: {\n      type: String,\n      default (): string {\n        return this.app ? 'nav' : 'aside'\n      },\n    },\n    temporary: Boolean,\n    touchless: Boolean,\n    width: {\n      type: [Number, String],\n      default: 256,\n    },\n    value: null as unknown as PropType<any>,\n  },\n\n  data: () => ({\n    isMouseover: false,\n    touchArea: {\n      left: 0,\n      right: 0,\n    },\n    stackMinZIndex: 6,\n  }),\n\n  computed: {\n    /**\n     * Used for setting an app value from a dynamic\n     * property. Called from applicationable.js\n     */\n    applicationProperty (): string {\n      return this.right ? 'right' : 'left'\n    },\n    classes (): object {\n      return {\n        'v-navigation-drawer': true,\n        'v-navigation-drawer--absolute': this.absolute,\n        'v-navigation-drawer--bottom': this.bottom,\n        'v-navigation-drawer--clipped': this.clipped,\n        'v-navigation-drawer--close': !this.isActive,\n        'v-navigation-drawer--fixed': !this.absolute && (this.app || this.fixed),\n        'v-navigation-drawer--floating': this.floating,\n        'v-navigation-drawer--is-mobile': this.isMobile,\n        'v-navigation-drawer--is-mouseover': this.isMouseover,\n        'v-navigation-drawer--mini-variant': this.isMiniVariant,\n        'v-navigation-drawer--custom-mini-variant': Number(this.miniVariantWidth) !== 56,\n        'v-navigation-drawer--open': this.isActive,\n        'v-navigation-drawer--open-on-hover': this.expandOnHover,\n        'v-navigation-drawer--right': this.right,\n        'v-navigation-drawer--temporary': this.temporary,\n        ...this.themeClasses,\n      }\n    },\n    computedMaxHeight (): number | null {\n      if (!this.hasApp) return null\n\n      const computedMaxHeight = (\n        this.$vuetify.application.bottom +\n        this.$vuetify.application.footer +\n        this.$vuetify.application.bar\n      )\n\n      if (!this.clipped) return computedMaxHeight\n\n      return computedMaxHeight + this.$vuetify.application.top\n    },\n    computedTop (): number {\n      if (!this.hasApp) return 0\n\n      let computedTop = this.$vuetify.application.bar\n\n      computedTop += this.clipped\n        ? this.$vuetify.application.top\n        : 0\n\n      return computedTop\n    },\n    computedTransform (): number {\n      if (this.isActive) return 0\n      if (this.isBottom) return 100\n      return this.right ? 100 : -100\n    },\n    computedWidth (): string | number {\n      return this.isMiniVariant ? this.miniVariantWidth : this.width\n    },\n    hasApp (): boolean {\n      return (\n        this.app &&\n        (!this.isMobile && !this.temporary)\n      )\n    },\n    isBottom (): boolean {\n      return this.bottom && this.isMobile\n    },\n    isMiniVariant (): boolean {\n      return (\n        !this.expandOnHover &&\n        this.miniVariant\n      ) || (\n        this.expandOnHover &&\n        !this.isMouseover\n      )\n    },\n    isMobile (): boolean {\n      return (\n        !this.stateless &&\n        !this.permanent &&\n        Mobile.options.computed.isMobile.call(this)\n      )\n    },\n    reactsToClick (): boolean {\n      return (\n        !this.stateless &&\n        !this.permanent &&\n        (this.isMobile || this.temporary)\n      )\n    },\n    reactsToMobile (): boolean {\n      return (\n        this.app &&\n        !this.disableResizeWatcher &&\n        !this.permanent &&\n        !this.stateless &&\n        !this.temporary\n      )\n    },\n    reactsToResize (): boolean {\n      return !this.disableResizeWatcher && !this.stateless\n    },\n    reactsToRoute (): boolean {\n      return (\n        !this.disableRouteWatcher &&\n        !this.stateless &&\n        (this.temporary || this.isMobile)\n      )\n    },\n    showOverlay (): boolean {\n      return (\n        !this.hideOverlay &&\n        this.isActive &&\n        (this.isMobile || this.temporary)\n      )\n    },\n    styles (): object {\n      const translate = this.isBottom ? 'translateY' : 'translateX'\n      return {\n        height: convertToUnit(this.height),\n        top: !this.isBottom ? convertToUnit(this.computedTop) : 'auto',\n        maxHeight: this.computedMaxHeight != null\n          ? `calc(100% - ${convertToUnit(this.computedMaxHeight)})`\n          : undefined,\n        transform: `${translate}(${convertToUnit(this.computedTransform, '%')})`,\n        width: convertToUnit(this.computedWidth),\n      }\n    },\n  },\n\n  watch: {\n    $route: 'onRouteChange',\n    isActive (val) {\n      this.$emit('input', val)\n    },\n    /**\n     * When mobile changes, adjust the active state\n     * only when there has been a previous value\n     */\n    isMobile (val, prev) {\n      !val &&\n        this.isActive &&\n        !this.temporary &&\n        this.removeOverlay()\n\n      if (prev == null ||\n        !this.reactsToResize ||\n        !this.reactsToMobile\n      ) return\n\n      this.isActive = !val\n    },\n    permanent (val) {\n      // If enabling prop enable the drawer\n      if (val) this.isActive = true\n    },\n    showOverlay (val) {\n      if (val) this.genOverlay()\n      else this.removeOverlay()\n    },\n    value (val) {\n      if (this.permanent) return\n\n      if (val == null) {\n        this.init()\n        return\n      }\n\n      if (val !== this.isActive) this.isActive = val\n    },\n    expandOnHover: 'updateMiniVariant',\n    isMouseover (val) {\n      this.updateMiniVariant(!val)\n    },\n  },\n\n  beforeMount () {\n    this.init()\n  },\n\n  methods: {\n    calculateTouchArea () {\n      const parent = this.$el.parentNode as Element\n\n      if (!parent) return\n\n      const parentRect = parent.getBoundingClientRect()\n\n      this.touchArea = {\n        left: parentRect.left + 50,\n        right: parentRect.right - 50,\n      }\n    },\n    closeConditional () {\n      return this.isActive && !this._isDestroyed && this.reactsToClick\n    },\n    genAppend () {\n      return this.genPosition('append')\n    },\n    genBackground () {\n      const props = {\n        height: '100%',\n        width: '100%',\n        src: this.src,\n      }\n\n      const image = this.$scopedSlots.img\n        ? this.$scopedSlots.img(props)\n        : this.$createElement(VImg, { props })\n\n      return this.$createElement('div', {\n        staticClass: 'v-navigation-drawer__image',\n      }, [image])\n    },\n    genDirectives (): VNodeDirective[] {\n      const directives = [{\n        name: 'click-outside',\n        value: {\n          handler: () => { this.isActive = false },\n          closeConditional: this.closeConditional,\n          include: this.getOpenDependentElements,\n        },\n      }]\n\n      if (!this.touchless && !this.stateless) {\n        directives.push({\n          name: 'touch',\n          value: {\n            parent: true,\n            left: this.swipeLeft,\n            right: this.swipeRight,\n          },\n        } as any)\n      }\n\n      return directives\n    },\n    genListeners () {\n      const on: Record<string, (e: Event) => void> = {\n        mouseenter: () => (this.isMouseover = true),\n        mouseleave: () => (this.isMouseover = false),\n        transitionend: (e: Event) => {\n          if (e.target !== e.currentTarget) return\n          this.$emit('transitionend', e)\n\n          // IE11 does not support new Event('resize')\n          const resizeEvent = document.createEvent('UIEvents')\n          resizeEvent.initUIEvent('resize', true, false, window, 0)\n          window.dispatchEvent(resizeEvent)\n        },\n      }\n\n      if (this.miniVariant) {\n        on.click = () => this.$emit('update:mini-variant', false)\n      }\n\n      return on\n    },\n    genPosition (name: 'prepend' | 'append') {\n      const slot = getSlot(this, name)\n\n      if (!slot) return slot\n\n      return this.$createElement('div', {\n        staticClass: `v-navigation-drawer__${name}`,\n      }, slot)\n    },\n    genPrepend () {\n      return this.genPosition('prepend')\n    },\n    genContent () {\n      return this.$createElement('div', {\n        staticClass: 'v-navigation-drawer__content',\n      }, this.$slots.default)\n    },\n    genBorder () {\n      return this.$createElement('div', {\n        staticClass: 'v-navigation-drawer__border',\n      })\n    },\n    init () {\n      if (this.permanent) {\n        this.isActive = true\n      } else if (this.stateless ||\n        this.value != null\n      ) {\n        this.isActive = this.value\n      } else if (!this.temporary) {\n        this.isActive = !this.isMobile\n      }\n    },\n    onRouteChange () {\n      if (this.reactsToRoute && this.closeConditional()) {\n        this.isActive = false\n      }\n    },\n    swipeLeft (e: TouchWrapper) {\n      if (this.isActive && this.right) return\n      this.calculateTouchArea()\n\n      if (Math.abs(e.touchendX - e.touchstartX) < 100) return\n      if (this.right &&\n        e.touchstartX >= this.touchArea.right\n      ) this.isActive = true\n      else if (!this.right && this.isActive) this.isActive = false\n    },\n    swipeRight (e: TouchWrapper) {\n      if (this.isActive && !this.right) return\n      this.calculateTouchArea()\n\n      if (Math.abs(e.touchendX - e.touchstartX) < 100) return\n      if (!this.right &&\n        e.touchstartX <= this.touchArea.left\n      ) this.isActive = true\n      else if (this.right && this.isActive) this.isActive = false\n    },\n    /**\n     * Update the application layout\n     */\n    updateApplication () {\n      if (\n        !this.isActive ||\n        this.isMobile ||\n        this.temporary ||\n        !this.$el\n      ) return 0\n\n      const width = Number(this.miniVariant ? this.miniVariantWidth : this.width)\n\n      return isNaN(width) ? this.$el.clientWidth : width\n    },\n    updateMiniVariant (val: boolean) {\n      if (this.expandOnHover && this.miniVariant !== val) this.$emit('update:mini-variant', val)\n    },\n  },\n\n  render (h): VNode {\n    const children = [\n      this.genPrepend(),\n      this.genContent(),\n      this.genAppend(),\n      this.genBorder(),\n    ]\n\n    if (this.src || getSlot(this, 'img')) children.unshift(this.genBackground())\n\n    return h(this.tag, this.setBackgroundColor(this.color, {\n      class: this.classes,\n      style: this.styles,\n      directives: this.genDirectives(),\n      on: this.genListeners(),\n    }), children)\n  },\n})\n"], "mappings": ";;;;;AAAA;AACA,OAAO,kEAAP,C,CAEA;;AACA,OAAOA,IAAP,MAAgC,cAAhC,C,CAEA;;AACA,OAAOC,eAAP,MAA4B,8BAA5B;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,SAAP,MAAsB,wBAAtB;AACA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,WAAP,MAAwB,0BAAxB;AACA,OAAOC,WAAP,MAAwB,2BAAxB;AACA,OAAOC,SAAP,MAAsB,wBAAtB,C,CAEA;;AACA,OAAOC,YAAP,MAAyB,gCAAzB;AACA,OAAOC,MAAP,MAAmB,yBAAnB;AACA,OAAOC,KAAP,MAAkB,wBAAlB,C,CAEA;;AACA,SAASC,aAAT,EAAwBC,OAAxB,QAAuC,oBAAvC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAMA,IAAMC,UAAU,GAAGD,MAAM,CACvBZ,eAAe,CAAC,MAAD,EAAS,CACtB,UADsB,EAEtB,UAFsB,EAGtB,aAHsB,EAItB,eAJsB,EAKtB,WALsB,EAMtB,OANsB,EAOtB,WAPsB,EAQtB,OARsB,CAAT,CADQ,EAWvBC,SAXuB,EAYvBC,SAZuB,EAavBC,MAbuB,EAcvBC,WAduB,EAevBC,WAfuB,EAgBvBC,SAhBuB,CAAzB;AAmBA;;AACA,eAAeO,UAAU,CAACC,MAAX,CAAkB;EAC/BC,IAAI,EAAE,qBADyB;EAG/BC,UAAU,EAAE;IACVT,YADU,EACVA,YADU;IAEVC,MAFU,EAEVA,MAFU;IAGVC,KAAA,EAAAA;EAHU,CAHmB;EAS/BQ,OAAO,WAAPA,OAAOA,CAAA;IACL,OAAO;MACLC,OAAO,EAAE,KAAKC,GAAL,KAAa;IADjB,CAAP;EAGD,CAb8B;EAe/BC,KAAK,EAAE;IACLC,MAAM,EAAEC,OADH;IAELC,OAAO,EAAED,OAFJ;IAGLE,oBAAoB,EAAEF,OAHjB;IAILG,mBAAmB,EAAEH,OAJhB;IAKLI,aAAa,EAAEJ,OALV;IAMLK,QAAQ,EAAEL,OANL;IAOLM,MAAM,EAAE;MACNC,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADA;MAAA,oBAENC,QAAOA,CAAA;QACL,OAAO,KAAKC,GAAL,GAAW,OAAX,GAAqB,MAA5B;MACD;IAJK,CAPH;IAaLC,WAAW,EAAEZ,OAbR;IAcLa,gBAAgB,EAAE;MAChBN,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADU;MAEhB,WAAS;IAFO,CAdb;IAkBLK,SAAS,EAAEd,OAlBN;IAmBLe,KAAK,EAAEf,OAnBF;IAoBLgB,GAAG,EAAE;MACHT,IAAI,EAAE,CAACE,MAAD,EAASQ,MAAT,CADH;MAEH,WAAS;IAFN,CApBA;IAwBLC,SAAS,EAAElB,OAxBN;IAyBLH,GAAG,EAAE;MACHU,IAAI,EAAEE,MADH;MAAA,oBAEHC,QAAOA,CAAA;QACL,OAAO,KAAKC,GAAL,GAAW,KAAX,GAAmB,OAA1B;MACD;IAJE,CAzBA;IA+BLQ,SAAS,EAAEnB,OA/BN;IAgCLoB,SAAS,EAAEpB,OAhCN;IAiCLqB,KAAK,EAAE;MACLd,IAAI,EAAE,CAACC,MAAD,EAASC,MAAT,CADD;MAEL,WAAS;IAFJ,CAjCF;IAqCLa,KAAK,EAAE;EArCF,CAfwB;EAuD/BC,IAAI,EAAE,SAANA,IAAIA,CAAA;IAAA,OAAS;MACXC,WAAW,EAAE,KADF;MAEXC,SAAS,EAAE;QACTC,IAAI,EAAE,CADG;QAETX,KAAK,EAAE;MAFE,CAFA;MAMXY,cAAc,EAAE;IANL,CAAP;EAAA,CAvDyB;EAgE/BC,QAAQ,EAAE;IACR;;;AAGG;IACHC,mBAAmB,WAAnBA,mBAAmBA,CAAA;MACjB,OAAO,KAAKd,KAAL,GAAa,OAAb,GAAuB,MAA9B;IACD,CAPO;IAQRe,OAAO,WAAPA,OAAOA,CAAA;MACL,OAAAC,aAAA;QACE,uBAAuB,IADlB;QAEL,iCAAiC,KAAKC,QAFjC;QAGL,+BAA+B,KAAKjC,MAH/B;QAIL,gCAAgC,KAAKE,OAJhC;QAKL,8BAA8B,CAAC,KAAKgC,QAL/B;QAML,8BAA8B,CAAC,KAAKD,QAAN,KAAmB,KAAKrB,GAAL,IAAY,KAAKuB,KAApC,CANzB;QAOL,iCAAiC,KAAK7B,QAPjC;QAQL,kCAAkC,KAAK8B,QARlC;QASL,qCAAqC,KAAKX,WATrC;QAUL,qCAAqC,KAAKY,aAVrC;QAWL,4CAA4C5B,MAAM,CAAC,KAAKK,gBAAN,CAAN,KAAkC,EAXzE;QAYL,6BAA6B,KAAKoB,QAZ7B;QAaL,sCAAsC,KAAK7B,aAbtC;QAcL,8BAA8B,KAAKW,KAd9B;QAeL,kCAAkC,KAAKI;MAflC,GAgBF,KAAKkB,YAAA;IAEX,CA3BO;IA4BRC,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IAAI,CAAC,KAAKC,MAAV,EAAkB,OAAO,IAAP;MAElB,IAAMD,iBAAiB,GACrB,KAAKE,QAAL,CAAcC,WAAd,CAA0B1C,MAA1B,GACA,KAAKyC,QAAL,CAAcC,WAAd,CAA0BC,MAD1B,GAEA,KAAKF,QAAL,CAAcC,WAAd,CAA0BE,GAH5B;MAMA,IAAI,CAAC,KAAK1C,OAAV,EAAmB,OAAOqC,iBAAP;MAEnB,OAAOA,iBAAiB,GAAG,KAAKE,QAAL,CAAcC,WAAd,CAA0BG,GAArD;IACD,CAxCO;IAyCRC,WAAW,WAAXA,WAAWA,CAAA;MACT,IAAI,CAAC,KAAKN,MAAV,EAAkB,OAAO,CAAP;MAElB,IAAIM,WAAW,GAAG,KAAKL,QAAL,CAAcC,WAAd,CAA0BE,GAA5C;MAEAE,WAAW,IAAI,KAAK5C,OAAL,GACX,KAAKuC,QAAL,CAAcC,WAAd,CAA0BG,GADf,GAEX,CAFJ;MAIA,OAAOC,WAAP;IACD,CAnDO;IAoDRC,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IAAI,KAAKb,QAAT,EAAmB,OAAO,CAAP;MACnB,IAAI,KAAKc,QAAT,EAAmB,OAAO,GAAP;MACnB,OAAO,KAAKhC,KAAL,GAAa,GAAb,GAAmB,CAAC,GAA3B;IACD,CAxDO;IAyDRiC,aAAa,WAAbA,aAAaA,CAAA;MACX,OAAO,KAAKZ,aAAL,GAAqB,KAAKvB,gBAA1B,GAA6C,KAAKQ,KAAzD;IACD,CA3DO;IA4DRkB,MAAM,WAANA,MAAMA,CAAA;MACJ,OACE,KAAK5B,GAAL,IACC,CAAC,KAAKwB,QAAN,IAAkB,CAAC,KAAKhB,SAF3B;IAID,CAjEO;IAkER4B,QAAQ,WAARA,QAAQA,CAAA;MACN,OAAO,KAAKhD,MAAL,IAAe,KAAKoC,QAA3B;IACD,CApEO;IAqERC,aAAa,WAAbA,aAAaA,CAAA;MACX,OACE,CAAC,KAAKhC,aAAN,IACA,KAAKQ,WAFA,IAIL,KAAKR,aAAL,IACA,CAAC,KAAKoB,WALR;IAOD,CA7EO;IA8ERW,QAAQ,WAARA,QAAQA,CAAA;MACN,OACE,CAAC,KAAKjB,SAAN,IACA,CAAC,KAAKJ,SADN,IAEAjC,MAAM,CAACoE,OAAP,CAAerB,QAAf,CAAwBO,QAAxB,CAAiCe,IAAjC,CAAsC,IAAtC,CAHF;IAKD,CApFO;IAqFRC,aAAa,WAAbA,aAAaA,CAAA;MACX,OACE,CAAC,KAAKjC,SAAN,IACA,CAAC,KAAKJ,SADN,KAEC,KAAKqB,QAAL,IAAiB,KAAKhB,SAFvB,CADF;IAKD,CA3FO;IA4FRiC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OACE,KAAKzC,GAAL,IACA,CAAC,KAAKT,oBADN,IAEA,CAAC,KAAKY,SAFN,IAGA,CAAC,KAAKI,SAHN,IAIA,CAAC,KAAKC,SALR;IAOD,CApGO;IAqGRkC,cAAc,WAAdA,cAAcA,CAAA;MACZ,OAAO,CAAC,KAAKnD,oBAAN,IAA8B,CAAC,KAAKgB,SAA3C;IACD,CAvGO;IAwGRoC,aAAa,WAAbA,aAAaA,CAAA;MACX,OACE,CAAC,KAAKnD,mBAAN,IACA,CAAC,KAAKe,SADN,KAEC,KAAKC,SAAL,IAAkB,KAAKgB,QAFxB,CADF;IAKD,CA9GO;IA+GRoB,WAAW,WAAXA,WAAWA,CAAA;MACT,OACE,CAAC,KAAKC,WAAN,IACA,KAAKvB,QADL,KAEC,KAAKE,QAAL,IAAiB,KAAKhB,SAFvB,CADF;IAKD,CArHO;IAsHRsC,MAAM,WAANA,MAAMA,CAAA;MAAA,IAAAC,QAAA;MACJ,IAAMC,SAAS,GAAG,KAAKZ,QAAL,GAAgB,YAAhB,GAA+B,YAAjD;MACA,OAAO;QACLzC,MAAM,EAAElB,aAAa,CAAC,KAAKkB,MAAN,CADhB;QAELsC,GAAG,EAAE,CAAC,KAAKG,QAAN,GAAiB3D,aAAa,CAAC,KAAKyD,WAAN,CAA9B,GAAmD,MAFnD;QAGLe,SAAS,EAAE,KAAKtB,iBAAL,IAA0B,IAA1B,kBAAAuB,MAAA,CACQzE,aAAa,CAAC,KAAKkD,iBAAN,CAAwB,SACpDwB,SALC;QAMLC,SAAS,EAAAC,uBAAA,CAAAN,QAAA,MAAAG,MAAA,CAAKF,SAAS,QAAAT,IAAA,CAAAQ,QAAA,EAAItE,aAAa,CAAC,KAAK0D,iBAAN,EAAyB,GAAzB,CAA6B,MANhE;QAOLzB,KAAK,EAAEjC,aAAa,CAAC,KAAK4D,aAAN;MAPf,CAAP;IASD;EAjIO,CAhEqB;EAoM/BiB,KAAK,EAAE;IACLC,MAAM,EAAE,eADH;IAELjC,QAAQ,WAARA,QAAQA,CAAEkC,GAAF,EAAK;MACX,KAAKC,KAAL,CAAW,OAAX,EAAoBD,GAApB;IACD,CAJI;IAKL;;;AAGG;IACHhC,QAAQ,WAARA,QAAQA,CAAEgC,GAAF,EAAOE,IAAP,EAAW;MACjB,CAACF,GAAD,IACE,KAAKlC,QADP,IAEE,CAAC,KAAKd,SAFR,IAGE,KAAKmD,aAAL,EAHF;MAKA,IAAID,IAAI,IAAI,IAAR,IACF,CAAC,KAAKhB,cADJ,IAEF,CAAC,KAAKD,cAFR,EAGE;MAEF,KAAKnB,QAAL,GAAgB,CAACkC,GAAjB;IACD,CArBI;IAsBLrD,SAAS,WAATA,SAASA,CAAEqD,GAAF,EAAK;MACZ;MACA,IAAIA,GAAJ,EAAS,KAAKlC,QAAL,GAAgB,IAAhB;IACV,CAzBI;IA0BLsB,WAAW,WAAXA,WAAWA,CAAEY,GAAF,EAAK;MACd,IAAIA,GAAJ,EAAS,KAAKI,UAAL,GAAT,KACK,KAAKD,aAAL;IACN,CA7BI;IA8BLhD,KAAK,WAALA,KAAKA,CAAE6C,GAAF,EAAK;MACR,IAAI,KAAKrD,SAAT,EAAoB;MAEpB,IAAIqD,GAAG,IAAI,IAAX,EAAiB;QACf,KAAKK,IAAL;QACA;MACD;MAED,IAAIL,GAAG,KAAK,KAAKlC,QAAjB,EAA2B,KAAKA,QAAL,GAAgBkC,GAAhB;IAC5B,CAvCI;IAwCL/D,aAAa,EAAE,mBAxCV;IAyCLoB,WAAW,WAAXA,WAAWA,CAAE2C,GAAF,EAAK;MACd,KAAKM,iBAAL,CAAuB,CAACN,GAAxB;IACD;EA3CI,CApMwB;EAkP/BO,WAAW,WAAXA,WAAWA,CAAA;IACT,KAAKF,IAAL;EACD,CApP8B;EAsP/BG,OAAO,EAAE;IACPC,kBAAkB,WAAlBA,kBAAkBA,CAAA;MAChB,IAAMC,MAAM,GAAG,KAAKC,GAAL,CAASC,UAAxB;MAEA,IAAI,CAACF,MAAL,EAAa;MAEb,IAAMG,UAAU,GAAGH,MAAM,CAACI,qBAAP,EAAnB;MAEA,KAAKxD,SAAL,GAAiB;QACfC,IAAI,EAAEsD,UAAU,CAACtD,IAAX,GAAkB,EADT;QAEfX,KAAK,EAAEiE,UAAU,CAACjE,KAAX,GAAmB;MAFX,CAAjB;IAID,CAZM;IAaPmE,gBAAgB,WAAhBA,gBAAgBA,CAAA;MACd,OAAO,KAAKjD,QAAL,IAAiB,CAAC,KAAKkD,YAAvB,IAAuC,KAAKhC,aAAnD;IACD,CAfM;IAgBPiC,SAAS,WAATA,SAASA,CAAA;MACP,OAAO,KAAKC,WAAL,CAAiB,QAAjB,CAAP;IACD,CAlBM;IAmBPC,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAMxF,KAAK,GAAG;QACZQ,MAAM,EAAE,MADI;QAEZe,KAAK,EAAE,MAFK;QAGZL,GAAG,EAAE,KAAKA;MAHE,CAAd;MAMA,IAAMuE,KAAK,GAAG,KAAKC,YAAL,CAAkBC,GAAlB,GACV,KAAKD,YAAL,CAAkBC,GAAlB,CAAsB3F,KAAtB,CADU,GAEV,KAAK4F,cAAL,CAAoBjH,IAApB,EAA0B;QAAEqB,KAAA,EAAAA;MAAF,CAA1B,CAFJ;MAIA,OAAO,KAAK4F,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE;MADmB,CAA3B,EAEJ,CAACJ,KAAD,CAFI,CAAP;IAGD,CAjCM;IAkCPK,aAAa,WAAbA,aAAaA,CAAA;MAAA,IAAAC,KAAA;MACX,IAAMnG,UAAU,GAAG,CAAC;QAClBD,IAAI,EAAE,eADY;QAElB6B,KAAK,EAAE;UACLwE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAO;YAAGD,KAAA,CAAK5D,QAAL,GAAgB,KAAhB;UAAuB,CADnC;UAELiD,gBAAgB,EAAE,KAAKA,gBAFlB;UAGLa,OAAO,EAAE,KAAKC;QAHT;MAFW,CAAD,CAAnB;MASA,IAAI,CAAC,KAAK5E,SAAN,IAAmB,CAAC,KAAKF,SAA7B,EAAwC;QACtCxB,UAAU,CAACuG,IAAX,CAAgB;UACdxG,IAAI,EAAE,OADQ;UAEd6B,KAAK,EAAE;YACLuD,MAAM,EAAE,IADH;YAELnD,IAAI,EAAE,KAAKwE,SAFN;YAGLnF,KAAK,EAAE,KAAKoF;UAHP;QAFO,CAAhB;MAQD;MAED,OAAOzG,UAAP;IACD,CAxDM;IAyDP0G,YAAY,WAAZA,YAAYA,CAAA;MAAA,IAAAC,MAAA;MACV,IAAMC,EAAE,GAAuC;QAC7CC,UAAU,EAAE,SAAZA,UAAUA,CAAA;UAAA,OAASF,MAAA,CAAK7E,WAAL,GAAmB,IADO;QAAA;QAE7CgF,UAAU,EAAE,SAAZA,UAAUA,CAAA;UAAA,OAASH,MAAA,CAAK7E,WAAL,GAAmB,KAFO;QAAA;QAG7CiF,aAAa,EAAG,SAAhBA,aAAaA,CAAGC,CAAD,EAAa;UAC1B,IAAIA,CAAC,CAACC,MAAF,KAAaD,CAAC,CAACE,aAAnB,EAAkC;UAClCP,MAAA,CAAKjC,KAAL,CAAW,eAAX,EAA4BsC,CAA5B,EAF0B,CAI1B;;UACA,IAAMG,WAAW,GAAGC,QAAQ,CAACC,WAAT,CAAqB,UAArB,CAApB;UACAF,WAAW,CAACG,WAAZ,CAAwB,QAAxB,EAAkC,IAAlC,EAAwC,KAAxC,EAA+CC,MAA/C,EAAuD,CAAvD;UACAA,MAAM,CAACC,aAAP,CAAqBL,WAArB;QACD;MAX4C,CAA/C;MAcA,IAAI,KAAKjG,WAAT,EAAsB;QACpB0F,EAAE,CAACa,KAAH,GAAW;UAAA,OAAMd,MAAA,CAAKjC,KAAL,CAAW,qBAAX,EAAkC,KAAlC,CAAjB;QAAA;MACD;MAED,OAAOkC,EAAP;IACD,CA7EM;IA8EPjB,WAAW,WAAXA,WAAWA,CAAE5F,IAAF,EAA4B;MACrC,IAAM2H,IAAI,GAAG/H,OAAO,CAAC,IAAD,EAAOI,IAAP,CAApB;MAEA,IAAI,CAAC2H,IAAL,EAAW,OAAOA,IAAP;MAEX,OAAO,KAAK1B,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,0BAAA9B,MAAA,CAA0BpE,IAAI;MADT,CAA3B,EAEJ2H,IAFI,CAAP;IAGD,CAtFM;IAuFPC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAKhC,WAAL,CAAiB,SAAjB,CAAP;IACD,CAzFM;IA0FPiC,UAAU,WAAVA,UAAUA,CAAA;MACR,OAAO,KAAK5B,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE;MADmB,CAA3B,EAEJ,KAAK4B,MAAL,WAFI,CAAP;IAGD,CA9FM;IA+FPC,SAAS,WAATA,SAASA,CAAA;MACP,OAAO,KAAK9B,cAAL,CAAoB,KAApB,EAA2B;QAChCC,WAAW,EAAE;MADmB,CAA3B,CAAP;IAGD,CAnGM;IAoGPnB,IAAI,WAAJA,IAAIA,CAAA;MACF,IAAI,KAAK1D,SAAT,EAAoB;QAClB,KAAKmB,QAAL,GAAgB,IAAhB;MACD,CAFD,MAEO,IAAI,KAAKf,SAAL,IACT,KAAKI,KAAL,IAAc,IADT,EAEL;QACA,KAAKW,QAAL,GAAgB,KAAKX,KAArB;MACD,CAJM,MAIA,IAAI,CAAC,KAAKH,SAAV,EAAqB;QAC1B,KAAKc,QAAL,GAAgB,CAAC,KAAKE,QAAtB;MACD;IACF,CA9GM;IA+GPsF,aAAa,WAAbA,aAAaA,CAAA;MACX,IAAI,KAAKnE,aAAL,IAAsB,KAAK4B,gBAAL,EAA1B,EAAmD;QACjD,KAAKjD,QAAL,GAAgB,KAAhB;MACD;IACF,CAnHM;IAoHPiE,SAAS,WAATA,SAASA,CAAEQ,CAAF,EAAiB;MACxB,IAAI,KAAKzE,QAAL,IAAiB,KAAKlB,KAA1B,EAAiC;MACjC,KAAK6D,kBAAL;MAEA,IAAI8C,IAAI,CAACC,GAAL,CAASjB,CAAC,CAACkB,SAAF,GAAclB,CAAC,CAACmB,WAAzB,IAAwC,GAA5C,EAAiD;MACjD,IAAI,KAAK9G,KAAL,IACF2F,CAAC,CAACmB,WAAF,IAAiB,KAAKpG,SAAL,CAAeV,KADlC,EAEE,KAAKkB,QAAL,GAAgB,IAAhB,CAFF,KAGK,IAAI,CAAC,KAAKlB,KAAN,IAAe,KAAKkB,QAAxB,EAAkC,KAAKA,QAAL,GAAgB,KAAhB;IACxC,CA7HM;IA8HPkE,UAAU,WAAVA,UAAUA,CAAEO,CAAF,EAAiB;MACzB,IAAI,KAAKzE,QAAL,IAAiB,CAAC,KAAKlB,KAA3B,EAAkC;MAClC,KAAK6D,kBAAL;MAEA,IAAI8C,IAAI,CAACC,GAAL,CAASjB,CAAC,CAACkB,SAAF,GAAclB,CAAC,CAACmB,WAAzB,IAAwC,GAA5C,EAAiD;MACjD,IAAI,CAAC,KAAK9G,KAAN,IACF2F,CAAC,CAACmB,WAAF,IAAiB,KAAKpG,SAAL,CAAeC,IADlC,EAEE,KAAKO,QAAL,GAAgB,IAAhB,CAFF,KAGK,IAAI,KAAKlB,KAAL,IAAc,KAAKkB,QAAvB,EAAiC,KAAKA,QAAL,GAAgB,KAAhB;IACvC,CAvIM;IAwIP;;AAEG;IACH6F,iBAAiB,WAAjBA,iBAAiBA,CAAA;MACf,IACE,CAAC,KAAK7F,QAAN,IACA,KAAKE,QADL,IAEA,KAAKhB,SAFL,IAGA,CAAC,KAAK2D,GAJR,EAKE,OAAO,CAAP;MAEF,IAAMzD,KAAK,GAAGb,MAAM,CAAC,KAAKI,WAAL,GAAmB,KAAKC,gBAAxB,GAA2C,KAAKQ,KAAjD,CAApB;MAEA,OAAO0G,KAAK,CAAC1G,KAAD,CAAL,GAAe,KAAKyD,GAAL,CAASkD,WAAxB,GAAsC3G,KAA7C;IACD,CAtJM;IAuJPoD,iBAAiB,WAAjBA,iBAAiBA,CAAEN,GAAF,EAAc;MAC7B,IAAI,KAAK/D,aAAL,IAAsB,KAAKQ,WAAL,KAAqBuD,GAA/C,EAAoD,KAAKC,KAAL,CAAW,qBAAX,EAAkCD,GAAlC;IACrD;EAzJM,CAtPsB;EAkZ/B8D,MAAM,WAANA,MAAMA,CAAEC,CAAF,EAAG;IACP,IAAMC,QAAQ,GAAG,CACf,KAAKd,UAAL,EADe,EAEf,KAAKC,UAAL,EAFe,EAGf,KAAKlC,SAAL,EAHe,EAIf,KAAKoC,SAAL,EAJe,CAAjB;IAOA,IAAI,KAAKxG,GAAL,IAAY3B,OAAO,CAAC,IAAD,EAAO,KAAP,CAAvB,EAAsC8I,QAAQ,CAACC,OAAT,CAAiB,KAAK9C,aAAL,EAAjB;IAEtC,OAAO4C,CAAC,CAAC,KAAKrI,GAAN,EAAW,KAAKwI,kBAAL,CAAwB,KAAKC,KAA7B,EAAoC;MACrD,SAAO,KAAKxG,OADyC;MAErDyG,KAAK,EAAE,KAAK9E,MAFyC;MAGrD/D,UAAU,EAAE,KAAKkG,aAAL,EAHyC;MAIrDU,EAAE,EAAE,KAAKF,YAAL;IAJiD,CAApC,CAAX,EAKJ+B,QALI,CAAR;EAMD;AAla8B,CAAlB,CAAf", "ignoreList": []}]}