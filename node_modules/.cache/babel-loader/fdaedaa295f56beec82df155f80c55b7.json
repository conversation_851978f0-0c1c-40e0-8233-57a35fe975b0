{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/BackgroundDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/BackgroundDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "showDialog", "backgroundTab", "elementParams", "backgroundH5", "url", "backgroundImg", "angle", "methods", "show", "backgroundImage", "window", "teduBoard", "getBackgroundImage", "type", "addBackground", "_context", "_context2", "_startsWithInstanceProperty", "call", "$toasted", "error", "setBackgroundImage", "TEduBoard", "TEduBoardImageFitMode", "TEDU_BOARD_IMAGE_FIT_MODE_CENTER", "setBackgroundH5", "setBackgroundImageAngle"], "sources": ["src/components/ToolbarDialog/BackgroundDialog.vue"], "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n    <v-card>\n      <v-card-title class=\"headline lighten-2\"> 背景 </v-card-title>\n      <v-card-text>\n        <v-tabs v-model=\"backgroundTab\">\n          <v-tab>H5背景</v-tab>\n          <v-tab>图片背景</v-tab>\n          <v-tab>修改图片背景角度</v-tab>\n        </v-tabs>\n        <v-row>\n          <v-col cols=\"12\" md=\"12\">\n            <v-tabs-items v-model=\"backgroundTab\">\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"elementParams['backgroundH5'].url\"\n                  label=\"H5背景URL\"\n                  prepend-icon=\"mdi-language-html5\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"elementParams['backgroundImg'].url\"\n                  label=\"背景图片URL\"\n                  prepend-icon=\"mdi-image\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model.number=\"elementParams['backgroundImg'].angle\"\n                  label=\"背景图片角度\"\n                ></v-text-field>\n              </v-tab-item>\n            </v-tabs-items>\n          </v-col>\n        </v-row>\n      </v-card-text>\n\n      <v-divider></v-divider>\n\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn\n          color=\"primary\"\n          v-if=\"backgroundTab < 2\"\n          text\n          @click=\"addBackground\"\n          >添加</v-btn\n        >\n        <v-btn\n          v-if=\"backgroundTab === 2\"\n          class=\"ma-2\"\n          outlined\n          color=\"indigo\"\n          @click=\"setBackgroundImageAngle\"\n        >\n          设置背景图片角度\n        </v-btn>\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      backgroundTab: null,\n      elementParams: {\n        backgroundH5: {\n          url: \"\",\n        },\n\n        backgroundImg: {\n          url: \"\",\n          angle: 0,\n        },\n      },\n    };\n  },\n\n  methods: {\n    show() {\n      this.elementParams.backgroundImg.url = \"\";\n      this.elementParams.backgroundImg.angle = 0;\n      this.elementParams.backgroundH5.url = \"\";\n\n      const backgroundImage = window.teduBoard.getBackgroundImage();\n      if (backgroundImage.type === 0) {\n        // 图片背景\n        this.elementParams.backgroundImg.url = backgroundImage.url;\n        this.elementParams.backgroundImg.angle = backgroundImage.angle;\n      } else {\n        // h5背景\n        this.elementParams.backgroundH5.url = backgroundImage.url;\n      }\n      this.showDialog = true;\n    },\n    addBackground() {\n      switch (this.backgroundTab) {\n        case 1:\n          if (!this.elementParams.backgroundImg.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.setBackgroundImage(\n            this.elementParams.backgroundImg.url,\n            TEduBoard.TEduBoardImageFitMode\n              .TEDU_BOARD_IMAGE_FIT_MODE_CENTER\n          );\n          this.elementParams.backgroundImg.url = null;\n          break;\n        case 0:\n          if (!this.elementParams.backgroundH5.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.setBackgroundH5(this.elementParams.backgroundH5.url);\n          this.elementParams.backgroundH5.url = null;\n          break;\n      }\n      this.showDialog = false;\n    },\n\n    setBackgroundImageAngle() {\n      window.teduBoard.setBackgroundImageAngle(\n        this.elementParams[\"backgroundImg\"].angle\n      );\n      this.showDialog = false;\n    },\n  },\n};\n</script>"], "mappings": ";AAiEA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,aAAA;MACAC,aAAA;QACAC,YAAA;UACAC,GAAA;QACA;QAEAC,aAAA;UACAD,GAAA;UACAE,KAAA;QACA;MACA;IACA;EACA;EAEAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,KAAAN,aAAA,CAAAG,aAAA,CAAAD,GAAA;MACA,KAAAF,aAAA,CAAAG,aAAA,CAAAC,KAAA;MACA,KAAAJ,aAAA,CAAAC,YAAA,CAAAC,GAAA;MAEA,IAAAK,eAAA,GAAAC,MAAA,CAAAC,SAAA,CAAAC,kBAAA;MACA,IAAAH,eAAA,CAAAI,IAAA;QACA;QACA,KAAAX,aAAA,CAAAG,aAAA,CAAAD,GAAA,GAAAK,eAAA,CAAAL,GAAA;QACA,KAAAF,aAAA,CAAAG,aAAA,CAAAC,KAAA,GAAAG,eAAA,CAAAH,KAAA;MACA;QACA;QACA,KAAAJ,aAAA,CAAAC,YAAA,CAAAC,GAAA,GAAAK,eAAA,CAAAL,GAAA;MACA;MACA,KAAAJ,UAAA;IACA;IACAc,aAAA,WAAAA,cAAA;MAAA,IAAAC,QAAA,EAAAC,SAAA;MACA,aAAAf,aAAA;QACA;UACA,KAAAgB,2BAAA,CAAAF,QAAA,QAAAb,aAAA,CAAAG,aAAA,CAAAD,GAAA,EAAAc,IAAA,CAAAH,QAAA;YACA,KAAAI,QAAA,CAAAC,KAAA;YACA;UACA;UACAV,MAAA,CAAAC,SAAA,CAAAU,kBAAA,CACA,KAAAnB,aAAA,CAAAG,aAAA,CAAAD,GAAA,EACAkB,SAAA,CAAAC,qBAAA,CACAC,gCACA;UACA,KAAAtB,aAAA,CAAAG,aAAA,CAAAD,GAAA;UACA;QACA;UACA,KAAAa,2BAAA,CAAAD,SAAA,QAAAd,aAAA,CAAAC,YAAA,CAAAC,GAAA,EAAAc,IAAA,CAAAF,SAAA;YACA,KAAAG,QAAA,CAAAC,KAAA;YACA;UACA;UACAV,MAAA,CAAAC,SAAA,CAAAc,eAAA,MAAAvB,aAAA,CAAAC,YAAA,CAAAC,GAAA;UACA,KAAAF,aAAA,CAAAC,YAAA,CAAAC,GAAA;UACA;MACA;MACA,KAAAJ,UAAA;IACA;IAEA0B,uBAAA,WAAAA,wBAAA;MACAhB,MAAA,CAAAC,SAAA,CAAAe,uBAAA,CACA,KAAAxB,aAAA,kBAAAI,KACA;MACA,KAAAN,UAAA;IACA;EACA;AACA", "ignoreList": []}]}