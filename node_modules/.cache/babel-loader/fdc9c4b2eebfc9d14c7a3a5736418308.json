{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VDivider/index.js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify/lib/components/VDivider/index.js", "mtime": 1757335236852}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZEaXZpZGVyIGZyb20gJy4vVkRpdmlkZXInOwpleHBvcnQgeyBWRGl2aWRlciB9OwpleHBvcnQgZGVmYXVsdCBWRGl2aWRlcjs="}, {"version": 3, "names": ["VDivider"], "sources": ["../../../src/components/VDivider/index.ts"], "sourcesContent": ["import VDivider from './VDivider'\n\nexport { VDivider }\nexport default VDivider\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,YAArB;AAEA,SAASA,QAAT;AACA,eAAeA,QAAf", "ignoreList": []}]}