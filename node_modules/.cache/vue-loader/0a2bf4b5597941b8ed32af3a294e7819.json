{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/Document.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/Document.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnRG9jdW1lbnQnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB3aW5kb3csCiAgICAgIGV4YW1wbGVzOiBbCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICflrabnp5HlhazlvI8nLAogICAgICAgICAgdXJsOiAnaHR0cHM6Ly9kZW1vLnFjbG91ZHRpdy5jb20vd2ViL2xhdGVzdC9kb2N1bWVudC/lrabnp5HlhazlvI8uZ2lmJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAn5pWw5a2m5Ye95pWwJywKICAgICAgICAgIHVybDogJ2h0dHBzOi8vZGVtby5xY2xvdWR0aXcuY29tL3dlYi9sYXRlc3QvZG9jdW1lbnQv5pWw5a2m5Ye95pWwLmdpZicsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogJ+mtlOazleeslCcsCiAgICAgICAgICB1cmw6ICdodHRwczovL2RlbW8ucWNsb3VkdGl3LmNvbS93ZWIvbGF0ZXN0L2RvY3VtZW50L+mtlOazleeslC5naWYnLAogICAgICAgIH0sCiAgICAgIF0sCiAgICAgIGRvY1VybHM6IFsKICAgICAgICB7CiAgICAgICAgICBpY29uOiAnbWRpLWNsb3VkLWRvd25sb2FkJywKICAgICAgICAgIGNvbG9yOiAncHJpbWFyeScsCiAgICAgICAgICB0aXRsZTogJ2RlbW/kuIvovb0nLAogICAgICAgICAgdXJsOiAnaHR0cHM6Ly9kZW1vLnFjbG91ZHRpdy5jb20vd2ViL2xhdGVzdC93ZWItZGVtby56aXAnLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgaWNvbjogJ21kaS1maWxlLWRvY3VtZW50JywKICAgICAgICAgIGNvbG9yOiAnYmx1ZSBsaWdodGVuLTEnLAogICAgICAgICAgdGl0bGU6ICdTREvmlofmoaMnLAogICAgICAgICAgdXJsOiAnaHR0cHM6Ly9kb2MucWNsb3VkdGl3LmNvbS93ZWIvaW5kZXguaHRtbCcsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBpY29uOiAnbWRpLWNsb3VkJywKICAgICAgICAgIGNvbG9yOiAnZ3JleSBkYXJrZW4tMycsCiAgICAgICAgICB0aXRsZTogJ+WumOe9keWcsOWdgCcsCiAgICAgICAgICB1cmw6ICdodHRwczovL2Nsb3VkLnRlbmNlbnQuY29tL2RvY3VtZW50L3Byb2R1Y3QvMTEzNycsCiAgICAgICAgfSwKICAgICAgXSwKICAgIH07CiAgfSwKfTsK"}, {"version": 3, "sources": ["Document.vue"], "names": [], "mappings": ";AAuEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Document.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div>\n    <v-carousel\n      height=\"500\"\n      hide-delimiters\n      show-arrows-on-hover\n    >\n      <v-carousel-item\n        v-for=\"(example, i) in examples\"\n        :key=\"i\"\n      >\n        <v-sheet\n          tile\n        >\n          <v-row\n            align=\"center\"\n            justify=\"center\"\n          >\n            <v-col align=\"center\" class=\"pt-4 pb-1\" style=\"font-size: 20px; background: #616161;\">\n              {{ example.title }}\n            </v-col>\n          </v-row>\n          <v-row\n            class=\"fill-width\"\n            align=\"center\"\n            justify=\"center\"\n          >\n            <v-img\n              :src=\"example.url\"\n              contain\n            >\n              <template v-slot:placeholder>\n                <v-row\n                  class=\"fill-height\"\n                  align=\"center\"\n                  justify=\"center\"\n                >\n                  <v-progress-circular\n                    indeterminate\n                    color=\"grey lighten-5\"\n                  ></v-progress-circular>\n                </v-row>\n              </template>\n            </v-img>\n          </v-row>\n        </v-sheet>\n      </v-carousel-item>\n    </v-carousel>\n    <v-row>\n      <v-col v-for=\"item in docUrls\" :key=\"item.url\">\n        <v-btn\n          :outlined=\"item.outlined\"\n          :color=\"item.color\"\n          dark\n          @click=\"window.open(item.url)\"\n          block\n        >\n          <v-icon\n            left\n            dark\n          >\n            {{ item.icon }}\n          </v-icon>\n          {{ item.title }}\n        </v-btn>\n      </v-col>\n    </v-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Document',\n  data() {\n    return {\n      window,\n      examples: [\n        {\n          title: '学科公式',\n          url: 'https://demo.qcloudtiw.com/web/latest/document/学科公式.gif',\n        },\n        {\n          title: '数学函数',\n          url: 'https://demo.qcloudtiw.com/web/latest/document/数学函数.gif',\n        },\n        {\n          title: '魔法笔',\n          url: 'https://demo.qcloudtiw.com/web/latest/document/魔法笔.gif',\n        },\n      ],\n      docUrls: [\n        {\n          icon: 'mdi-cloud-download',\n          color: 'primary',\n          title: 'demo下载',\n          url: 'https://demo.qcloudtiw.com/web/latest/web-demo.zip',\n        },\n        {\n          icon: 'mdi-file-document',\n          color: 'blue lighten-1',\n          title: 'SDK文档',\n          url: 'https://doc.qcloudtiw.com/web/index.html',\n        },\n        {\n          icon: 'mdi-cloud',\n          color: 'grey darken-3',\n          title: '官网地址',\n          url: 'https://cloud.tencent.com/document/product/1137',\n        },\n      ],\n    };\n  },\n};\n</script>\n"]}]}