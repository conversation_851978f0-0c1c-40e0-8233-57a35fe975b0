{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SettingDialog.vue"], "names": [], "mappings": ";AAoKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SettingDialog.vue", "sourceRoot": "src/components/ToolbarDialog", "sourcesContent": ["<template>\n  <div class=\"setting-dialog__container\">\n    <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"600\">\n      <v-card>\n        <v-card-title class=\"headline lighten-2\">\n          <v-tabs v-model=\"settingType\">\n            <v-tab v-for=\"item in settingTabs\" :key=\"item\">\n              {{ item }}\n            </v-tab>\n          </v-tabs>\n        </v-card-title>\n        <v-card-text>\n          <v-tabs-items v-model=\"settingType\">\n            <v-tab-item>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].drawEnable\"\n                    label=\"允许涂鸦\"\n                    hide-details\n                    @change=\"setDrawEnable\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].dataSyncEnable\"\n                    label=\"开启数据同步\"\n                    hide-details\n                    @change=\"setDataSyncEnable\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].handwritingEnable\"\n                    label=\"开启笔锋\"\n                    hide-details\n                    @change=\"setHandwritingEnable\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].showRemoteCursor\"\n                    label=\"显示远端画笔\"\n                    hide-details\n                    @click=\"setRemoteCursorVisible\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <!-- TODO 2.9.2的测试 -->\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].disablePointerEventResponding\"\n                    label=\"禁用鼠标响应\"\n                    hide-details\n                    @change=\"setDisablePointerEventResponding\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].showCursorWhenDisabled\"\n                    label=\"禁用响应也显示光标\"\n                    hide-details\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].enableMultiTouch\"\n                    label=\"开启多点触控(只有触摸屏才有效)\"\n                    hide-details\n                    @click=\"setEnableMultiTouch\"\n                  ></v-switch>\n                </v-col>\n                 <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[0].graphicAutoSelect\"\n                    label=\"几何图形自动选中\"\n                    hide-details\n                    @click=\"setEnableMultiTouch\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n            </v-tab-item>\n            <v-tab-item>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.whiteBoard\"\n                    label=\"允许鼠标点击普通白板翻页\"\n                    hide-details\n                    @change=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.h5PPT\"\n                    label=\"允许鼠标点击H5PPT翻页\"\n                    hide-details\n                    @change=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n              <v-row align=\"center\">\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.imgPPT\"\n                    label=\"允许鼠标点击静态PPT翻页\"\n                    hide-details\n                    @change=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n                <v-col align-self=\"center\">\n                  <v-switch\n                    v-model=\"settingItems[1].turnPage.imgFile\"\n                    label=\"允许鼠标点击图片组文件翻页\"\n                    hide-details\n                    @click=\"setMouseToolBehavior\"\n                  ></v-switch>\n                </v-col>\n              </v-row>\n            </v-tab-item>\n            <v-tab-item>\n              <v-sheet\n                v-for=\"(stat, index) in authorityStatus\"\n                :key=\"stat.text\"\n              >\n                <v-switch\n                  v-model=\"stat.value\"\n                  :label=\"stat.text\"\n                  @change=\"(e) => onAuthorityChange(index, e)\"\n                ></v-switch>\n                <v-sheet>\n                  <v-row>\n                    <v-col\n                      cols=\"3\"\n                      v-for=\"cmd in authorityCmd[index]\"\n                      :key=\"cmd.value\"\n                    >\n                      <v-checkbox\n                        v-model=\"allowCmdList[index]\"\n                        :label=\"cmd.text\"\n                        :value=\"cmd.value\"\n                        :disabled=\"!stat.value\"\n                        dense\n                        @change=\"(e) => onAuthorityCmdChange(index, e)\"\n                      ></v-checkbox>\n                    </v-col>\n                  </v-row>\n                </v-sheet>\n                <v-divider />\n              </v-sheet>\n            </v-tab-item>\n          </v-tabs-items>\n        </v-card-text>\n      </v-card>\n    </v-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      settingType: null,\n      settingTabs: ['基础设置', '鼠标行为设置', '权限校验设置'],\n      settingItems: [\n        {\n          drawEnable: true,\n          handwritingEnable: true,\n          showRemoteCursor: true,\n          dataSyncEnable: true,\n          enableMultiTouch: false,\n          disablePointerEventResponding: false,\n          showCursorWhenDisabled: false,\n          graphicAutoSelect: false,\n        },\n        {\n          turnPage: {\n            whiteBoard: true,\n            h5PPT: true,\n            imgPPT: true,\n            imgFile: true,\n          },\n        },\n      ],\n      authorityCmd: [\n        [\n          {\n            text: '设置背景颜色',\n            value: 'Background::Update::Color',\n          },\n          {\n            text: '设置背景Frame',\n            value: 'Background::Update::Frame',\n          },\n          {\n            text: '设置背景图片',\n            value: 'Background::Update::Image',\n          },\n        ],\n        [\n          {\n            text: '添加文件',\n            value: 'File::Add::*',\n          },\n          {\n            text: '删除文件',\n            value: 'File::Delete::*',\n          },\n          {\n            text: '清空文件',\n            value: 'File::Clear::*',\n          },\n          {\n            text: '切换文件',\n            value: 'File::Switch::*',\n          },\n          {\n            text: '更新文件',\n            value: 'File::Update::*',\n          },\n        ],\n        [\n          {\n            text: '添加白板',\n            value: 'Board::Add::*',\n          },\n          {\n            text: '清空白板',\n            value: 'Board::Clear::*',\n          },\n          {\n            text: '删除白板',\n            value: 'Board::Delete::*',\n          },\n          {\n            text: '缩放白板',\n            value: 'Board::Scale::*',\n          },\n          {\n            text: '切换白板',\n            value: 'Board::Switch::Page',\n          },\n          {\n            text: '切换动画步数',\n            value: 'Board::Switch::Step',\n          },\n          {\n            text: '更新白板内容填充模式',\n            value: 'Board::Update::ContentFitMode',\n          },\n          {\n            text: '更新白板比例',\n            value: 'Board::Update::Ratio',\n          },\n          {\n            text: '撤销',\n            value: 'Board::Undo::*',\n          },\n          {\n            text: '重做',\n            value: 'Board::Redo::*',\n          },\n        ],\n        [\n          {\n            text: '添加元素',\n            value: 'Element::Add::*',\n          },\n          {\n            text: '删除元素',\n            value: 'Element::Delete::*',\n          },\n          {\n            text: '更新元素',\n            value: 'Element::Update::*',\n          },\n          {\n            text: '选择元素',\n            value: 'Element::Select::*',\n          },\n          {\n            text: '移动元素',\n            value: 'Element::Move::*',\n          },\n          {\n            text: '旋转元素',\n            value: 'Element::Rotate::*',\n          },\n          {\n            text: '缩放元素',\n            value: 'Element::Scale::*',\n          },\n        ],\n        [\n          {\n            text: '重置白板',\n            value: 'Instance::Reset::*',\n          },\n        ],\n      ],\n\n      allowCmdList: [\n        [\n          'Background::Update::Color',\n          'Background::Update::Frame',\n          'Background::Update::Image',\n        ],\n        [\n          'File::Add::*',\n          'File::Delete::*',\n          'File::Clear::*',\n          'File::Switch::*',\n          'File::Update::*',\n        ],\n        [\n          'Board::Add::*',\n          'Board::Clear::*',\n          'Board::Delete::*',\n          'Board::Scale::*',\n          'Board::Switch::Page',\n          'Board::Switch::Step',\n          'Board::Update::ContentFitMode',\n          'Board::Update::Ratio',\n          'Board::Undo::*',\n          'Board::Redo::*',\n        ],\n        [\n          'Element::Add::*',\n          'Element::Delete::*',\n          'Element::Update::*',\n          'Element::Select::*',\n          'Element::Move::*',\n          'Element::Rotate::*',\n          'Element::Scale::*',\n        ],\n        ['Instance::Reset::*'],\n      ],\n\n      authorityStatus: [\n        {\n          text: '启用背景类权限校验',\n          value: false,\n        },\n        {\n          text: '启用文件类权限校验',\n          value: false,\n        },\n        {\n          text: '启用白板类权限校验',\n          value: false,\n        },\n        {\n          text: '启用元素类权限校验',\n          value: false,\n        },\n        {\n          text: '启用实例权限校验',\n          value: false,\n        },\n      ],\n    };\n  },\n  watch: {\n    settingItems: {\n      handler() {\n        this.updateSetting();\n      },\n      deep: true,\n      immediate: true,\n    },\n  },\n  mounted() {\n    // 2.9.2 测试设置鼠标位置\n    window.addEventListener('mousemove', (e) => {\n      const canvasWrap = document.querySelector('.tx_board_canvas_wrap');\n      if (this.settingItems[0].showCursorWhenDisabled) {\n        const pageY =\n          e.pageY - canvasWrap.offsetTop + canvasWrap.clientHeight / 2;\n        window.teduBoard.setCursorPosition({\n          x: e.pageX,\n          y: pageY,\n        });\n      }\n    });\n  },\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n    setDrawEnable() {\n      window.teduBoard.setDrawEnable(this.settingItems[0].drawEnable);\n    },\n    setHandwritingEnable() {\n      window.teduBoard.setHandwritingEnable(\n        this.settingItems[0].handwritingEnable\n      );\n    },\n\n    setDataSyncEnable() {\n      window.teduBoard.setDataSyncEnable(this.settingItems[0].dataSyncEnable);\n    },\n\n    setRemoteCursorVisible() {\n      window.teduBoard.setRemoteCursorVisible(\n        this.settingItems[0].showRemoteCursor\n      );\n    },\n\n    setEnableMultiTouch() {\n      const enable = this.settingItems[0].enableMultiTouch;\n      window.teduBoard.enableMultiTouch(enable);\n      window.teduBoard.setSyncFps(enable ? 10 : 5);\n    },\n\n    // 设置鼠标操作行为\n    setMouseToolBehavior() {\n      window.teduBoard.setMouseToolBehavior({\n        turnPage: this.settingItems[1].turnPage,\n      });\n    },\n\n    onAuthorityChange(index, enable) {\n      if (enable) {\n        const allowCmds = this.allowCmdList[index];\n        const denyCmds = [];\n        const totalCmds = this.authorityCmd[index];\n        totalCmds.forEach((c) => {\n          if (!allowCmds.includes(c.value)) {\n            denyCmds.push(c.value);\n          }\n        });\n        window.teduBoard.enablePermissionChecker(allowCmds, [\n          'operator/' + window.teduBoard.identifier,\n        ]);\n        window.teduBoard.enablePermissionChecker(denyCmds, ['operator/']);\n      } else {\n        const totalCmds = this.authorityCmd[index].map((c) => c.value);\n        window.teduBoard.disablePermissionChecker(totalCmds);\n      }\n    },\n\n    onAuthorityCmdChange(index, cmdList) {\n      const enable = this.authorityStatus[index].value;\n      if (enable) {\n        const allowCmds = cmdList;\n        const denyCmds = [];\n        const totalCmds = this.authorityCmd[index];\n        totalCmds.forEach((c) => {\n          if (!allowCmds.includes(c.value)) {\n            denyCmds.push(c.value);\n          }\n        });\n        window.teduBoard.enablePermissionChecker(allowCmds, [\n          'operator/' + window.teduBoard.identifier,\n        ]);\n        window.teduBoard.enablePermissionChecker(denyCmds, ['operator/']);\n      }\n    },\n\n    updateSetting() {\n      this.$emit('updateSetting', this.settingItems);\n    },\n\n    setDisablePointerEventResponding() {\n      window.teduBoard.disablePointerEventResponding(\n        this.settingItems[0].disablePointerEventResponding\n      );\n    },\n  },\n};\n</script>\n"]}]}