{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/BackgroundDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/BackgroundDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BackgroundDialog.vue"], "names": [], "mappings": ";AAiEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BackgroundDialog.vue", "sourceRoot": "src/components/ToolbarDialog", "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n    <v-card>\n      <v-card-title class=\"headline lighten-2\"> 背景 </v-card-title>\n      <v-card-text>\n        <v-tabs v-model=\"backgroundTab\">\n          <v-tab>H5背景</v-tab>\n          <v-tab>图片背景</v-tab>\n          <v-tab>修改图片背景角度</v-tab>\n        </v-tabs>\n        <v-row>\n          <v-col cols=\"12\" md=\"12\">\n            <v-tabs-items v-model=\"backgroundTab\">\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"elementParams['backgroundH5'].url\"\n                  label=\"H5背景URL\"\n                  prepend-icon=\"mdi-language-html5\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"elementParams['backgroundImg'].url\"\n                  label=\"背景图片URL\"\n                  prepend-icon=\"mdi-image\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model.number=\"elementParams['backgroundImg'].angle\"\n                  label=\"背景图片角度\"\n                ></v-text-field>\n              </v-tab-item>\n            </v-tabs-items>\n          </v-col>\n        </v-row>\n      </v-card-text>\n\n      <v-divider></v-divider>\n\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn\n          color=\"primary\"\n          v-if=\"backgroundTab < 2\"\n          text\n          @click=\"addBackground\"\n          >添加</v-btn\n        >\n        <v-btn\n          v-if=\"backgroundTab === 2\"\n          class=\"ma-2\"\n          outlined\n          color=\"indigo\"\n          @click=\"setBackgroundImageAngle\"\n        >\n          设置背景图片角度\n        </v-btn>\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      backgroundTab: null,\n      elementParams: {\n        backgroundH5: {\n          url: \"\",\n        },\n\n        backgroundImg: {\n          url: \"\",\n          angle: 0,\n        },\n      },\n    };\n  },\n\n  methods: {\n    show() {\n      this.elementParams.backgroundImg.url = \"\";\n      this.elementParams.backgroundImg.angle = 0;\n      this.elementParams.backgroundH5.url = \"\";\n\n      const backgroundImage = window.teduBoard.getBackgroundImage();\n      if (backgroundImage.type === 0) {\n        // 图片背景\n        this.elementParams.backgroundImg.url = backgroundImage.url;\n        this.elementParams.backgroundImg.angle = backgroundImage.angle;\n      } else {\n        // h5背景\n        this.elementParams.backgroundH5.url = backgroundImage.url;\n      }\n      this.showDialog = true;\n    },\n    addBackground() {\n      switch (this.backgroundTab) {\n        case 1:\n          if (!this.elementParams.backgroundImg.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.setBackgroundImage(\n            this.elementParams.backgroundImg.url,\n            TEduBoard.TEduBoardImageFitMode\n              .TEDU_BOARD_IMAGE_FIT_MODE_CENTER\n          );\n          this.elementParams.backgroundImg.url = null;\n          break;\n        case 0:\n          if (!this.elementParams.backgroundH5.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.setBackgroundH5(this.elementParams.backgroundH5.url);\n          this.elementParams.backgroundH5.url = null;\n          break;\n      }\n      this.showDialog = false;\n    },\n\n    setBackgroundImageAngle() {\n      window.teduBoard.setBackgroundImageAngle(\n        this.elementParams[\"backgroundImg\"].angle\n      );\n      this.showDialog = false;\n    },\n  },\n};\n</script>"]}]}