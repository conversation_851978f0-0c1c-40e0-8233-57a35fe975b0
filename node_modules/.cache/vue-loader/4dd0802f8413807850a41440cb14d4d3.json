{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js??ref--4!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--13-0!/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/SelectBoxSettingDialog.vue?vue&type=template&id=6b36cb0e", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/SelectBoxSettingDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/src/components/SelectBoxSettingDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "showDialog", "attrs", "model", "value", "callback", "$$v", "expression", "staticClass", "_v", "_l", "elementOperationAuthority", "elementItem", "key", "_s", "text", "operationAuthorityList", "auth<PERSON><PERSON>", "cols", "label", "dense", "$set", "color", "on", "click", "setOperationAuthority", "$event", "_e", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/Downloads/web-demo/src/components/SelectBoxSettingDialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _vm.showDialog\n    ? _c(\n        \"v-dialog\",\n        {\n          attrs: { \"max-width\": \"1000\" },\n          model: {\n            value: _vm.showDialog,\n            callback: function ($$v) {\n              _vm.showDialog = $$v\n            },\n            expression: \"showDialog\",\n          },\n        },\n        [\n          _c(\n            \"v-card\",\n            [\n              _c(\"v-card-title\", { staticClass: \"headline lighten-2\" }, [\n                _vm._v(\" 选框操作权限 \"),\n              ]),\n              _c(\n                \"v-card-text\",\n                _vm._l(\n                  _vm.elementOperationAuthority,\n                  function (elementItem, key) {\n                    return _c(\n                      \"v-sheet\",\n                      { key: key },\n                      [\n                        _c(\"span\", [_vm._v(_vm._s(elementItem.text))]),\n                        _c(\"v-divider\"),\n                        _c(\n                          \"v-sheet\",\n                          [\n                            _c(\n                              \"v-row\",\n                              _vm._l(\n                                _vm.operationAuthorityList,\n                                function (text, authKey) {\n                                  return _c(\n                                    \"v-col\",\n                                    { key: authKey, attrs: { cols: \"3\" } },\n                                    [\n                                      _c(\"v-checkbox\", {\n                                        attrs: {\n                                          label: text,\n                                          value: elementItem[authKey],\n                                          dense: \"\",\n                                        },\n                                        model: {\n                                          value: elementItem[authKey],\n                                          callback: function ($$v) {\n                                            _vm.$set(elementItem, authKey, $$v)\n                                          },\n                                          expression: \"elementItem[authKey]\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  )\n                                }\n                              ),\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                        _c(\"v-divider\"),\n                      ],\n                      1\n                    )\n                  }\n                ),\n                1\n              ),\n              _c(\n                \"v-card-actions\",\n                [\n                  _c(\"v-spacer\"),\n                  _c(\n                    \"v-btn\",\n                    {\n                      attrs: { color: \"primary\", text: \"\" },\n                      on: { click: _vm.setOperationAuthority },\n                    },\n                    [_vm._v(\"设置\")]\n                  ),\n                  _c(\n                    \"v-btn\",\n                    {\n                      attrs: { text: \"\" },\n                      on: {\n                        click: function ($event) {\n                          _vm.showDialog = false\n                        },\n                      },\n                    },\n                    [_vm._v(\"关闭\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOD,GAAG,CAACG,UAAU,GACjBF,EAAE,CACA,UAAU,EACV;IACEG,KAAK,EAAE;MAAE,WAAW,EAAE;IAAO,CAAC;IAC9BC,KAAK,EAAE;MACLC,KAAK,EAAEN,GAAG,CAACG,UAAU;MACrBI,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBR,GAAG,CAACG,UAAU,GAAGK,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACER,EAAE,CACA,QAAQ,EACR,CACEA,EAAE,CAAC,cAAc,EAAE;IAAES,WAAW,EAAE;EAAqB,CAAC,EAAE,CACxDV,GAAG,CAACW,EAAE,CAAC,UAAU,CAAC,CACnB,CAAC,EACFV,EAAE,CACA,aAAa,EACbD,GAAG,CAACY,EAAE,CACJZ,GAAG,CAACa,yBAAyB,EAC7B,UAAUC,WAAW,EAAEC,GAAG,EAAE;IAC1B,OAAOd,EAAE,CACP,SAAS,EACT;MAAEc,GAAG,EAAEA;IAAI,CAAC,EACZ,CACEd,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACW,EAAE,CAACX,GAAG,CAACgB,EAAE,CAACF,WAAW,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,EAC9ChB,EAAE,CAAC,WAAW,CAAC,EACfA,EAAE,CACA,SAAS,EACT,CACEA,EAAE,CACA,OAAO,EACPD,GAAG,CAACY,EAAE,CACJZ,GAAG,CAACkB,sBAAsB,EAC1B,UAAUD,IAAI,EAAEE,OAAO,EAAE;MACvB,OAAOlB,EAAE,CACP,OAAO,EACP;QAAEc,GAAG,EAAEI,OAAO;QAAEf,KAAK,EAAE;UAAEgB,IAAI,EAAE;QAAI;MAAE,CAAC,EACtC,CACEnB,EAAE,CAAC,YAAY,EAAE;QACfG,KAAK,EAAE;UACLiB,KAAK,EAAEJ,IAAI;UACXX,KAAK,EAAEQ,WAAW,CAACK,OAAO,CAAC;UAC3BG,KAAK,EAAE;QACT,CAAC;QACDjB,KAAK,EAAE;UACLC,KAAK,EAAEQ,WAAW,CAACK,OAAO,CAAC;UAC3BZ,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;YACvBR,GAAG,CAACuB,IAAI,CAACT,WAAW,EAAEK,OAAO,EAAEX,GAAG,CAAC;UACrC,CAAC;UACDC,UAAU,EAAE;QACd;MACF,CAAC,CAAC,CACH,EACD,CACF,CAAC;IACH,CACF,CAAC,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDR,EAAE,CAAC,WAAW,CAAC,CAChB,EACD,CACF,CAAC;EACH,CACF,CAAC,EACD,CACF,CAAC,EACDA,EAAE,CACA,gBAAgB,EAChB,CACEA,EAAE,CAAC,UAAU,CAAC,EACdA,EAAE,CACA,OAAO,EACP;IACEG,KAAK,EAAE;MAAEoB,KAAK,EAAE,SAAS;MAAEP,IAAI,EAAE;IAAG,CAAC;IACrCQ,EAAE,EAAE;MAAEC,KAAK,EAAE1B,GAAG,CAAC2B;IAAsB;EACzC,CAAC,EACD,CAAC3B,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,EACDV,EAAE,CACA,OAAO,EACP;IACEG,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAG,CAAC;IACnBQ,EAAE,EAAE;MACFC,KAAK,EAAE,SAAPA,KAAKA,CAAYE,MAAM,EAAE;QACvB5B,GAAG,CAACG,UAAU,GAAG,KAAK;MACxB;IACF;EACF,CAAC,EACD,CAACH,GAAG,CAACW,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDX,GAAG,CAAC6B,EAAE,CAAC,CAAC;AACd,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB/B,MAAM,CAACgC,aAAa,GAAG,IAAI;AAE3B,SAAShC,MAAM,EAAE+B,eAAe", "ignoreList": []}]}