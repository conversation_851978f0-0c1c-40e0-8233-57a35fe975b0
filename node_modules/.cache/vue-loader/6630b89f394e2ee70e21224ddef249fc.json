{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/MediaDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/MediaDialog.vue", "mtime": 1703055368000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["MediaDialog.vue"], "names": [], "mappings": ";AAmEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "MediaDialog.vue", "sourceRoot": "src/components/ToolbarDialog", "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n    <v-card>\n      <v-card-title class=\"headline lighten-2\"> 多媒体资源 </v-card-title>\n      <v-card-text>\n        <v-tabs v-model=\"mediaTab\">\n          <v-tab value=\"video\">MP4视频</v-tab>\n          <v-tab value=\"audio\">MP3音频</v-tab>\n          <v-tab value=\"h5File\">H5网页</v-tab>\n        </v-tabs>\n        <v-row>\n          <v-col cols=\"12\" md=\"12\">\n            <v-tabs-items v-model=\"mediaTab\">\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"media['video'].url\"\n                  label=\"mp4视频URL\"\n                  prepend-icon=\"mdi-video\"\n                ></v-text-field>\n                <v-text-field\n                  v-model=\"media['video'].title\"\n                  label=\"mp4标题\"\n                  prepend-icon=\"mdi-format-title\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"media['audio'].url\"\n                  label=\"MP3音频URL\"\n                  prepend-icon=\"mdi-music\"\n                ></v-text-field>\n                <v-text-field\n                  v-model=\"media['audio'].title\"\n                  label=\"mp3标题\"\n                  prepend-icon=\"mdi-format-title\"\n                ></v-text-field>\n              </v-tab-item>\n              <v-tab-item>\n                <v-text-field\n                  v-model=\"media['h5File'].url\"\n                  label=\"H5网页URL\"\n                  prepend-icon=\"mdi-language-html5\"\n                ></v-text-field>\n                <v-text-field\n                  v-model=\"media['audio'].title\"\n                  label=\"H5网页标题\"\n                  prepend-icon=\"mdi-format-title\"\n                ></v-text-field>\n              </v-tab-item>\n            </v-tabs-items>\n          </v-col>\n        </v-row>\n      </v-card-text>\n\n      <v-divider></v-divider>\n\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn color=\"primary\" text @click=\"addMedia\">添加</v-btn>\n\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      rules: {\n        urlRule: [\n          (v) => !!v || \"url is required\",\n          (v) => /^https:\\/\\//.test(v) || \"url必须是https协议\",\n        ],\n      },\n      showDialog: false,\n      mediaTab: \"video\",\n      media: {\n        video: {\n          url: \"\",\n          title: \"\",\n        },\n        audio: {\n          url: \"\",\n          title: \"\",\n        },\n        h5File: {\n          url: \"\",\n          title: \"\",\n        },\n      },\n    };\n  },\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n    addMedia() {\n      switch (this.mediaTab) {\n        case 0:\n          if (!this.media.video.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.addVideoFile(\n            this.media.video.url,\n            this.media.video.title\n          );\n          this.media['video'].url = \"\";\n          this.media['video'].title = \"\";\n          break;\n        case 1:\n          if (!this.media.audio.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_AUDIO,\n            this.media.audio.url,\n            {\n              title: this.media.audio.title,\n            }\n          );\n          this.media['audio'].url = \"\";\n          this.media['audio'].title = \"\";\n          break;\n        case 2:\n          if (!this.media.h5File.url.startsWith(\"https://\")) {\n            this.$toasted.error(\"请输入合法的https协议的url\");\n            return;\n          }\n          window.teduBoard.addH5File(\n            this.media.h5File.url,\n            this.media.h5File.title\n          );\n          this.media['h5File'].url = \"\";\n          this.media['h5File'].title = \"\";\n          break;\n      }\n\n      this.showDialog = false;\n    },\n  },\n};\n</script>"]}]}