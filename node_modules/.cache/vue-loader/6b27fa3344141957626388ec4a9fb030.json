{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CoursewareDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CoursewareDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["CoursewareDialog.vue"], "names": [], "mappings": ";AAmKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CoursewareDialog.vue", "sourceRoot": "src/components/ToolbarDialog", "sourcesContent": ["<template>\n  <div class=\"courseware-dialog_container\">\n    <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n      <v-card>\n        <v-card-title class=\"headline lighten-2\"> 课件转码 </v-card-title>\n        <v-card-text>\n          <v-alert text type=\"info\">\n            1. ppt默认转码为动态转码，即保留ppt中的动画效果。<br />\n            2. word，pdf只能转换为静态图片，ppt也可以设置转换为静态图片。<br />\n          </v-alert>\n\n          <v-row>\n            <v-col cols=\"12\" md=\"8\">\n              <v-radio-group v-model=\"transcodeMode\" row>\n                <v-radio label=\"主转码\" :value=\"0\"></v-radio>\n                <v-radio label=\"备份转码\" :value=\"1\"></v-radio>\n              </v-radio-group>\n            </v-col>\n          </v-row>\n          <v-row>\n            <v-col cols=\"12\" md=\"8\">\n              <v-btn\n                prepend-icon=\"mdi-cloud-upload\"\n                @click=\"showTranscodeDialog\"\n                >点我选择文件</v-btn\n              >\n            </v-col>\n          </v-row>\n        </v-card-text>\n        <v-divider></v-divider>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <v-dialog\n      v-if=\"showTranscodeFileDialog\"\n      v-model=\"showTranscodeFileDialog\"\n      max-width=\"800\"\n    >\n      <v-stepper v-model=\"uploadSteps\" vertical>\n        <v-stepper-step :complete=\"uploadSteps > 1\" step=\"1\">\n          上传文件到COS桶{{ transcodeMode === 1 ? '(需开通文档预览)' : '' }}\n        </v-stepper-step>\n\n        <v-stepper-content step=\"1\">\n          <v-file-input\n            v-model=\"transcodeFileObj\"\n            label=\"先点我选择上传课件\"\n            prepend-icon=\"mdi-file\"\n            accept=\".doc,.docx,.ppt,.pptx,.pdf\"\n          ></v-file-input>\n          <v-btn\n            @click=\"uploadBackupTranscodeFile\"\n            :loading=\"backupTranscodeFileUploading\"\n            color=\"primary\"\n            >上传</v-btn\n          >\n        </v-stepper-content>\n\n        <v-stepper-step :complete=\"uploadSteps > 2\" step=\"2\">\n          {{\n            transcodeMode === 1\n              ? '获取文件COS地址并拼接白板参数'\n              : '发起转码任务'\n          }}\n        </v-stepper-step>\n\n        <v-stepper-content step=\"2\" v-if=\"transcodeMode === 1\">\n          <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n            >备份转码课件url:</span\n          >\n          <p style=\"word-break: break-all\">\n            {{ this.courseware.url }}\n          </p>\n          <v-text-field\n            v-model=\"courseware.title\"\n            label=\"课件名称\"\n          ></v-text-field>\n\n          <v-btn color=\"primary\" @click=\"addTranscodeFile\"> 确认添加 </v-btn>\n        </v-stepper-content>\n\n        <v-stepper-content step=\"2\" v-else>\n          <div v-if=\"!transcodeStart\">\n            <v-radio-group v-model=\"transcodeStaticMode\" row>\n              <v-radio\n                label=\"动态转码(暂时只支持ppt,pptx)\"\n                :value=\"false\"\n              ></v-radio>\n              <v-radio label=\"静态转码\" :value=\"true\"></v-radio>\n            </v-radio-group>\n\n            <v-btn color=\"primary\" @click=\"createTranscodeTask()\">\n              开始转码\n            </v-btn>\n          </div>\n\n          <div v-else>\n            <div class=\"mb-4\">\n              <v-alert type=\"error\" v-if=\"transcodeErrMsg.status === 'ERROR'\">\n                <h4>{{ transcodeErrMsg.code }}</h4>\n                {{ transcodeErrMsg.message }}\n              </v-alert>\n\n              <v-progress-linear\n                v-else\n                class=\"mb-2\"\n                stream\n                buffer-value=\"0\"\n                v-model=\"transcodePercent\"\n                height=\"25\"\n                :color=\"transcodePercent === 100 ? 'success' : 'primary'\"\n              >\n                <strong>{{ transcodePercent }}%</strong>\n              </v-progress-linear>\n\n              <div v-if=\"transcodePercent === 100\">\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >转码文件名</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.title }}\n                </p>\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >转码文件地址</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.url }}\n                </p>\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >文件页数</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.pages }}\n                </p>\n                <span style=\"color: rgba(0, 0, 0, 0.6); font-size: 12px\"\n                  >分辨率</span\n                >\n                <p style=\"word-break: break-all\">\n                  {{ courseware.resolution }}\n                </p>\n              </div>\n            </div>\n\n            <v-btn\n              color=\"primary\"\n              :disabled=\"transcodePercent !== 100\"\n              @click=\"addTranscodeFile\"\n            >\n              确认添加\n            </v-btn>\n          </div>\n        </v-stepper-content>\n      </v-stepper>\n    </v-dialog>\n  </div>\n</template>\n\n<script>\nimport TiwTranscode from '../../util/TiwTranscode';\n\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      courseware: {\n        title: '课件',\n        url: '',\n        resolution: '',\n        pages: 0,\n      },\n      transcodeMode: 0, // 0 主转码，1 备份转码\n      uploadSteps: 1, // 转码步骤\n      showTranscodeFileDialog: false,\n      backupTranscodeFileUploading: false,\n      transcodeFileObj: {},\n      transcodeStart: false,\n      transcodePercent: 0,\n      transcodeStaticMode: false,\n      transcodeErrMsg: {\n        staus: 'SUCCESS',\n        code: '',\n        error: '',\n      },\n    };\n  },\n\n  mounted() {\n    this.cosInstance = new window.COS({\n      async getAuthorization(options, callback) {\n        // 异步获取临时密钥\n        const res = await window.axios({\n          method: 'post',\n          url:\n            'https://tiw-3gflb4tn0d7f550f-1259648581.ap-shanghai.app.tcloudbase.com/get_ci_cos_token',\n          responseType: 'json',\n          data: {\n            cmd: 'getToken',\n          },\n        });\n        const { data } = res;\n        const credentials = data && data.credentials;\n        if (!data || !credentials) return console.error('credentials invalid');\n        callback({\n          TmpSecretId: credentials.tmpSecretId,\n          TmpSecretKey: credentials.tmpSecretKey,\n          XCosSecurityToken: credentials.sessionToken,\n          StartTime: data.startTime,\n          ExpiredTime: data.expiredTime,\n        });\n      },\n    });\n  },\n\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n\n    showTranscodeDialog() {\n      this.showTranscodeFileDialog = true;\n      this.uploadSteps = 1;\n    },\n\n    addTranscodeFile() {\n      if (this.transcodeMode === 1) {\n        // 备份转码\n        window.teduBoard.addTranscodeFile({\n          url: this.courseware.url,\n          title: this.courseware.title || `测试文件-${Date.now()}`,\n        });\n      } else {\n        // 主转码\n        window.teduBoard.addTranscodeFile({\n          url: this.courseware.url,\n          title: this.courseware.title || `测试文件-${Date.now()}`,\n          pages: this.courseware.pages,\n          resolution: this.courseware.resolution,\n        });\n      }\n\n      // 重置数据\n      this.transcodeFileObj = null;\n\n      this.courseware.url = null;\n      this.courseware.title = null;\n      this.courseware.pages = 0;\n      this.courseware.resolution = '';\n\n      this.showDialog = false;\n      this.uploadSteps = 1;\n      this.showTranscodeFileDialog = false;\n    },\n\n    viewDoc() {\n      window.open(\n        'https://cloud.tencent.com/document/product/1137/55888',\n        '_blank'\n      );\n    },\n\n    /**\n     * 备份转码方案\n     */\n    uploadBackupTranscodeFile() {\n      if (!this.transcodeFileObj) return;\n\n      this.backupTranscodeFileUploading = true;\n      this.cosInstance.putObject(\n        {\n          Bucket: 'tiw-ci-1259648581' /* 必须 */,\n          Region: 'ap-nanjing' /* 存储桶所在地域，必须字段 */,\n          Key: this.transcodeFileObj.name /* 必须 */,\n          StorageClass: 'STANDARD',\n          Body: this.transcodeFileObj, // 上传文件对象\n          onProgress: (progressData) => {\n            if (progressData.percent === 1) {\n              this.backupTranscodeFileUploading = false;\n              this.uploadSteps = 2;\n              this.transcodeStart = false;\n              this.transcodeErrMsg.status = 'SUCCESS';\n              this.transcodePercent = 0;\n            }\n          },\n        },\n        async (err, data) => {\n          if (!err) {\n            const cosUrl = `https://${data.Location}`;\n            if (this.transcodeMode === 1) {\n              // 备份转码\n              this.courseware.url = `${cosUrl}?for_tiw=1`;\n              this.courseware.title = this.transcodeFileObj.name;\n            } else {\n              // 主转码\n              this.courseware.url = cosUrl;\n            }\n          }\n        }\n      );\n    },\n\n    createTranscodeTask() {\n      this.transcodeStart = true;\n      TiwTranscode.createTranscodeTask(\n        {\n          Url: this.courseware.url,\n          IsStaticPPT: this.transcodeStaticMode,\n        },\n        (data) => {\n          if (data.status === 'SUCCESS') {\n            this.transcodePercent = data.fileData.Progress;\n            this.transcodeErrMsg.status = 'SUCCESS';\n            if (this.transcodePercent === 100) {\n              this.courseware.url = `${data.fileData.ResultUrl}`;\n              this.courseware.urlName = `${data.fileData.Title}`;\n              this.courseware.pages = `${data.fileData.Pages}`;\n              this.courseware.resolution = `${data.fileData.Resolution}`;\n            }\n          } else {\n            this.transcodeErrMsg = {\n              status: 'ERROR',\n              code: data.code,\n              message: data.message,\n            };\n          }\n        }\n      );\n    },\n  },\n};\n</script>\n"]}]}