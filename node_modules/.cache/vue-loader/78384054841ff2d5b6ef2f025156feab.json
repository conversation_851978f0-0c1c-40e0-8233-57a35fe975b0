{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js??ref--4!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--13-0!/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ScrollbarSettingDialog.vue?vue&type=template&id=01313f3c", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ScrollbarSettingDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/src/components/ScrollbarSettingDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "showDialog", "attrs", "width", "model", "value", "callback", "$$v", "expression", "_v", "flat", "on", "handleScrollbarThumbColorChange", "thumbColor", "handleScrollbarTrackColorChange", "trackColor", "text", "click", "$event", "_e", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/Downloads/web-demo/src/components/ScrollbarSettingDialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"cursor-setting-dialog__container\" },\n    [\n      _vm.showDialog\n        ? _c(\n            \"v-dialog\",\n            {\n              attrs: { width: \"500\" },\n              model: {\n                value: _vm.showDialog,\n                callback: function ($$v) {\n                  _vm.showDialog = $$v\n                },\n                expression: \"showDialog\",\n              },\n            },\n            [\n              _c(\n                \"v-card\",\n                [\n                  _c(\"v-card-title\", { staticClass: \"headline lighten-2\" }, [\n                    _vm._v(\" 滚动条设置 \"),\n                  ]),\n                  _c(\n                    \"v-card-text\",\n                    [\n                      _c(\"span\", [_vm._v(\"滑块颜色\")]),\n                      _c(\"v-color-picker\", {\n                        attrs: { width: \"400\", \"hide-inputs\": \"\", flat: \"\" },\n                        on: {\n                          \"update:color\": _vm.handleScrollbarThumbColorChange,\n                        },\n                        model: {\n                          value: _vm.thumbColor,\n                          callback: function ($$v) {\n                            _vm.thumbColor = $$v\n                          },\n                          expression: \"thumbColor\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"v-card-text\",\n                    [\n                      _c(\"span\", [_vm._v(\"滑轨颜色\")]),\n                      _c(\"v-color-picker\", {\n                        attrs: { width: \"400\", \"hide-inputs\": \"\", flat: \"\" },\n                        on: {\n                          \"update:color\": _vm.handleScrollbarTrackColorChange,\n                        },\n                        model: {\n                          value: _vm.trackColor,\n                          callback: function ($$v) {\n                            _vm.trackColor = $$v\n                          },\n                          expression: \"trackColor\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"v-card-actions\",\n                    [\n                      _c(\"v-spacer\"),\n                      _c(\n                        \"v-btn\",\n                        {\n                          attrs: { text: \"\" },\n                          on: {\n                            click: function ($event) {\n                              _vm.showDialog = false\n                            },\n                          },\n                        },\n                        [_vm._v(\"关闭\")]\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          )\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmC,CAAC,EACnD,CACEH,GAAG,CAACI,UAAU,GACVH,EAAE,CACA,UAAU,EACV;IACEI,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM,CAAC;IACvBC,KAAK,EAAE;MACLC,KAAK,EAAER,GAAG,CAACI,UAAU;MACrBK,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAACI,UAAU,GAAGM,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACEV,EAAE,CACA,QAAQ,EACR,CACEA,EAAE,CAAC,cAAc,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,EAAE,CACxDH,GAAG,CAACY,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACFX,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BX,EAAE,CAAC,gBAAgB,EAAE;IACnBI,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAE,aAAa,EAAE,EAAE;MAAEO,IAAI,EAAE;IAAG,CAAC;IACpDC,EAAE,EAAE;MACF,cAAc,EAAEd,GAAG,CAACe;IACtB,CAAC;IACDR,KAAK,EAAE;MACLC,KAAK,EAAER,GAAG,CAACgB,UAAU;MACrBP,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAACgB,UAAU,GAAGN,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACY,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5BX,EAAE,CAAC,gBAAgB,EAAE;IACnBI,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAE,aAAa,EAAE,EAAE;MAAEO,IAAI,EAAE;IAAG,CAAC;IACpDC,EAAE,EAAE;MACF,cAAc,EAAEd,GAAG,CAACiB;IACtB,CAAC;IACDV,KAAK,EAAE;MACLC,KAAK,EAAER,GAAG,CAACkB,UAAU;MACrBT,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBV,GAAG,CAACkB,UAAU,GAAGR,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDV,EAAE,CACA,gBAAgB,EAChB,CACEA,EAAE,CAAC,UAAU,CAAC,EACdA,EAAE,CACA,OAAO,EACP;IACEI,KAAK,EAAE;MAAEc,IAAI,EAAE;IAAG,CAAC;IACnBL,EAAE,EAAE;MACFM,KAAK,EAAE,SAAPA,KAAKA,CAAYC,MAAM,EAAE;QACvBrB,GAAG,CAACI,UAAU,GAAG,KAAK;MACxB;IACF;EACF,CAAC,EACD,CAACJ,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDZ,GAAG,CAACsB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBxB,MAAM,CAACyB,aAAa,GAAG,IAAI;AAE3B,SAASzB,MAAM,EAAEwB,eAAe", "ignoreList": []}]}