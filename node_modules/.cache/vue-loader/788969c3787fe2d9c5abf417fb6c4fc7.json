{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CoursewareDialog.vue?vue&type=template&id=0f49b0bc", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CoursewareDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImNvdXJzZXdhcmUtZGlhbG9nX2NvbnRhaW5lciI+CiAgPHYtZGlhbG9nIHYtaWY9InNob3dEaWFsb2ciIHYtbW9kZWw9InNob3dEaWFsb2ciIG1heC13aWR0aD0iMTAwMCI+CiAgICA8di1jYXJkPgogICAgICA8di1jYXJkLXRpdGxlIGNsYXNzPSJoZWFkbGluZSBsaWdodGVuLTIiPiDor77ku7bovaznoIEgPC92LWNhcmQtdGl0bGU+CiAgICAgIDx2LWNhcmQtdGV4dD4KICAgICAgICA8di1hbGVydCB0ZXh0IHR5cGU9ImluZm8iPgogICAgICAgICAgMS4gcHB06buY6K6k6L2s56CB5Li65Yqo5oCB6L2s56CB77yM5Y2z5L+d55WZcHB05Lit55qE5Yqo55S75pWI5p6c44CCPGJyIC8+CiAgICAgICAgICAyLiB3b3Jk77yMcGRm5Y+q6IO96L2s5o2i5Li66Z2Z5oCB5Zu+54mH77yMcHB05Lmf5Y+v5Lul6K6+572u6L2s5o2i5Li66Z2Z5oCB5Zu+54mH44CCPGJyIC8+CiAgICAgICAgPC92LWFsZXJ0PgoKICAgICAgICA8di1yb3c+CiAgICAgICAgICA8di1jb2wgY29scz0iMTIiIG1kPSI4Ij4KICAgICAgICAgICAgPHYtcmFkaW8tZ3JvdXAgdi1tb2RlbD0idHJhbnNjb2RlTW9kZSIgcm93PgogICAgICAgICAgICAgIDx2LXJhZGlvIGxhYmVsPSLkuLvovaznoIEiIDp2YWx1ZT0iMCI+PC92LXJhZGlvPgogICAgICAgICAgICAgIDx2LXJhZGlvIGxhYmVsPSLlpIfku73ovaznoIEiIDp2YWx1ZT0iMSI+PC92LXJhZGlvPgogICAgICAgICAgICA8L3YtcmFkaW8tZ3JvdXA+CiAgICAgICAgICA8L3YtY29sPgogICAgICAgIDwvdi1yb3c+CiAgICAgICAgPHYtcm93PgogICAgICAgICAgPHYtY29sIGNvbHM9IjEyIiBtZD0iOCI+CiAgICAgICAgICAgIDx2LWJ0bgogICAgICAgICAgICAgIHByZXBlbmQtaWNvbj0ibWRpLWNsb3VkLXVwbG9hZCIKICAgICAgICAgICAgICBAY2xpY2s9InNob3dUcmFuc2NvZGVEaWFsb2ciCiAgICAgICAgICAgICAgPueCueaIkemAieaLqeaWh+S7tjwvdi1idG4KICAgICAgICAgICAgPgogICAgICAgICAgPC92LWNvbD4KICAgICAgICA8L3Ytcm93PgogICAgICA8L3YtY2FyZC10ZXh0PgogICAgICA8di1kaXZpZGVyPjwvdi1kaXZpZGVyPgoKICAgICAgPHYtY2FyZC1hY3Rpb25zPgogICAgICAgIDx2LXNwYWNlcj48L3Ytc3BhY2VyPgogICAgICAgIDx2LWJ0biB0ZXh0IEBjbGljaz0ic2hvd0RpYWxvZyA9IGZhbHNlIj7lhbPpl608L3YtYnRuPgogICAgICA8L3YtY2FyZC1hY3Rpb25zPgogICAgPC92LWNhcmQ+CiAgPC92LWRpYWxvZz4KCiAgPHYtZGlhbG9nCiAgICB2LWlmPSJzaG93VHJhbnNjb2RlRmlsZURpYWxvZyIKICAgIHYtbW9kZWw9InNob3dUcmFuc2NvZGVGaWxlRGlhbG9nIgogICAgbWF4LXdpZHRoPSI4MDAiCiAgPgogICAgPHYtc3RlcHBlciB2LW1vZGVsPSJ1cGxvYWRTdGVwcyIgdmVydGljYWw+CiAgICAgIDx2LXN0ZXBwZXItc3RlcCA6Y29tcGxldGU9InVwbG9hZFN0ZXBzID4gMSIgc3RlcD0iMSI+CiAgICAgICAg5LiK5Lyg5paH5Lu25YiwQ09T5qG2e3sgdHJhbnNjb2RlTW9kZSA9PT0gMSA/ICco6ZyA5byA6YCa5paH5qGj6aKE6KeIKScgOiAnJyB9fQogICAgICA8L3Ytc3RlcHBlci1zdGVwPgoKICAgICAgPHYtc3RlcHBlci1jb250ZW50IHN0ZXA9IjEiPgogICAgICAgIDx2LWZpbGUtaW5wdXQKICAgICAgICAgIHYtbW9kZWw9InRyYW5zY29kZUZpbGVPYmoiCiAgICAgICAgICBsYWJlbD0i5YWI54K55oiR6YCJ5oup5LiK5Lyg6K++5Lu2IgogICAgICAgICAgcHJlcGVuZC1pY29uPSJtZGktZmlsZSIKICAgICAgICAgIGFjY2VwdD0iLmRvYywuZG9jeCwucHB0LC5wcHR4LC5wZGYiCiAgICAgICAgPjwvdi1maWxlLWlucHV0PgogICAgICAgIDx2LWJ0bgogICAgICAgICAgQGNsaWNrPSJ1cGxvYWRCYWNrdXBUcmFuc2NvZGVGaWxlIgogICAgICAgICAgOmxvYWRpbmc9ImJhY2t1cFRyYW5zY29kZUZpbGVVcGxvYWRpbmciCiAgICAgICAgICBjb2xvcj0icHJpbWFyeSIKICAgICAgICAgID7kuIrkvKA8L3YtYnRuCiAgICAgICAgPgogICAgICA8L3Ytc3RlcHBlci1jb250ZW50PgoKICAgICAgPHYtc3RlcHBlci1zdGVwIDpjb21wbGV0ZT0idXBsb2FkU3RlcHMgPiAyIiBzdGVwPSIyIj4KICAgICAgICB7ewogICAgICAgICAgdHJhbnNjb2RlTW9kZSA9PT0gMQogICAgICAgICAgICA/ICfojrflj5bmlofku7ZDT1PlnLDlnYDlubbmi7zmjqXnmb3mnb/lj4LmlbAnCiAgICAgICAgICAgIDogJ+WPkei1t+i9rOeggeS7u+WKoScKICAgICAgICB9fQogICAgICA8L3Ytc3RlcHBlci1zdGVwPgoKICAgICAgPHYtc3RlcHBlci1jb250ZW50IHN0ZXA9IjIiIHYtaWY9InRyYW5zY29kZU1vZGUgPT09IDEiPgogICAgICAgIDxzcGFuIHN0eWxlPSJjb2xvcjogcmdiYSgwLCAwLCAwLCAwLjYpOyBmb250LXNpemU6IDEycHgiCiAgICAgICAgICA+5aSH5Lu96L2s56CB6K++5Lu2dXJsOjwvc3BhbgogICAgICAgID4KICAgICAgICA8cCBzdHlsZT0id29yZC1icmVhazogYnJlYWstYWxsIj4KICAgICAgICAgIHt7IHRoaXMuY291cnNld2FyZS51cmwgfX0KICAgICAgICA8L3A+CiAgICAgICAgPHYtdGV4dC1maWVsZAogICAgICAgICAgdi1tb2RlbD0iY291cnNld2FyZS50aXRsZSIKICAgICAgICAgIGxhYmVsPSLor77ku7blkI3np7AiCiAgICAgICAgPjwvdi10ZXh0LWZpZWxkPgoKICAgICAgICA8di1idG4gY29sb3I9InByaW1hcnkiIEBjbGljaz0iYWRkVHJhbnNjb2RlRmlsZSI+IOehruiupOa3u+WKoCA8L3YtYnRuPgogICAgICA8L3Ytc3RlcHBlci1jb250ZW50PgoKICAgICAgPHYtc3RlcHBlci1jb250ZW50IHN0ZXA9IjIiIHYtZWxzZT4KICAgICAgICA8ZGl2IHYtaWY9IiF0cmFuc2NvZGVTdGFydCI+CiAgICAgICAgICA8di1yYWRpby1ncm91cCB2LW1vZGVsPSJ0cmFuc2NvZGVTdGF0aWNNb2RlIiByb3c+CiAgICAgICAgICAgIDx2LXJhZGlvCiAgICAgICAgICAgICAgbGFiZWw9IuWKqOaAgei9rOeggSjmmoLml7blj6rmlK/mjIFwcHQscHB0eCkiCiAgICAgICAgICAgICAgOnZhbHVlPSJmYWxzZSIKICAgICAgICAgICAgPjwvdi1yYWRpbz4KICAgICAgICAgICAgPHYtcmFkaW8gbGFiZWw9IumdmeaAgei9rOeggSIgOnZhbHVlPSJ0cnVlIj48L3YtcmFkaW8+CiAgICAgICAgICA8L3YtcmFkaW8tZ3JvdXA+CgogICAgICAgICAgPHYtYnRuIGNvbG9yPSJwcmltYXJ5IiBAY2xpY2s9ImNyZWF0ZVRyYW5zY29kZVRhc2soKSI+CiAgICAgICAgICAgIOW8gOWni+i9rOeggQogICAgICAgICAgPC92LWJ0bj4KICAgICAgICA8L2Rpdj4KCiAgICAgICAgPGRpdiB2LWVsc2U+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJtYi00Ij4KICAgICAgICAgICAgPHYtYWxlcnQgdHlwZT0iZXJyb3IiIHYtaWY9InRyYW5zY29kZUVyck1zZy5zdGF0dXMgPT09ICdFUlJPUiciPgogICAgICAgICAgICAgIDxoND57eyB0cmFuc2NvZGVFcnJNc2cuY29kZSB9fTwvaDQ+CiAgICAgICAgICAgICAge3sgdHJhbnNjb2RlRXJyTXNnLm1lc3NhZ2UgfX0KICAgICAgICAgICAgPC92LWFsZXJ0PgoKICAgICAgICAgICAgPHYtcHJvZ3Jlc3MtbGluZWFyCiAgICAgICAgICAgICAgdi1lbHNlCiAgICAgICAgICAgICAgY2xhc3M9Im1iLTIiCiAgICAgICAgICAgICAgc3RyZWFtCiAgICAgICAgICAgICAgYnVmZmVyLXZhbHVlPSIwIgogICAgICAgICAgICAgIHYtbW9kZWw9InRyYW5zY29kZVBlcmNlbnQiCiAgICAgICAgICAgICAgaGVpZ2h0PSIyNSIKICAgICAgICAgICAgICA6Y29sb3I9InRyYW5zY29kZVBlcmNlbnQgPT09IDEwMCA/ICdzdWNjZXNzJyA6ICdwcmltYXJ5JyIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDxzdHJvbmc+e3sgdHJhbnNjb2RlUGVyY2VudCB9fSU8L3N0cm9uZz4KICAgICAgICAgICAgPC92LXByb2dyZXNzLWxpbmVhcj4KCiAgICAgICAgICAgIDxkaXYgdi1pZj0idHJhbnNjb2RlUGVyY2VudCA9PT0gMTAwIj4KICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6IHJnYmEoMCwgMCwgMCwgMC42KTsgZm9udC1zaXplOiAxMnB4IgogICAgICAgICAgICAgICAgPui9rOeggeaWh+S7tuWQjTwvc3BhbgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8cCBzdHlsZT0id29yZC1icmVhazogYnJlYWstYWxsIj4KICAgICAgICAgICAgICAgIHt7IGNvdXJzZXdhcmUudGl0bGUgfX0KICAgICAgICAgICAgICA8L3A+CiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNik7IGZvbnQtc2l6ZTogMTJweCIKICAgICAgICAgICAgICAgID7ovaznoIHmlofku7blnLDlnYA8L3NwYW4KICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHAgc3R5bGU9IndvcmQtYnJlYWs6IGJyZWFrLWFsbCI+CiAgICAgICAgICAgICAgICB7eyBjb3Vyc2V3YXJlLnVybCB9fQogICAgICAgICAgICAgIDwvcD4KICAgICAgICAgICAgICA8c3BhbiBzdHlsZT0iY29sb3I6IHJnYmEoMCwgMCwgMCwgMC42KTsgZm9udC1zaXplOiAxMnB4IgogICAgICAgICAgICAgICAgPuaWh+S7tumhteaVsDwvc3BhbgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8cCBzdHlsZT0id29yZC1icmVhazogYnJlYWstYWxsIj4KICAgICAgICAgICAgICAgIHt7IGNvdXJzZXdhcmUucGFnZXMgfX0KICAgICAgICAgICAgICA8L3A+CiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9ImNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuNik7IGZvbnQtc2l6ZTogMTJweCIKICAgICAgICAgICAgICAgID7liIbovqjnjoc8L3NwYW4KICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHAgc3R5bGU9IndvcmQtYnJlYWs6IGJyZWFrLWFsbCI+CiAgICAgICAgICAgICAgICB7eyBjb3Vyc2V3YXJlLnJlc29sdXRpb24gfX0KICAgICAgICAgICAgICA8L3A+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgPC9kaXY+CgogICAgICAgICAgPHYtYnRuCiAgICAgICAgICAgIGNvbG9yPSJwcmltYXJ5IgogICAgICAgICAgICA6ZGlzYWJsZWQ9InRyYW5zY29kZVBlcmNlbnQgIT09IDEwMCIKICAgICAgICAgICAgQGNsaWNrPSJhZGRUcmFuc2NvZGVGaWxlIgogICAgICAgICAgPgogICAgICAgICAgICDnoa7orqTmt7vliqAKICAgICAgICAgIDwvdi1idG4+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvdi1zdGVwcGVyLWNvbnRlbnQ+CiAgICA8L3Ytc3RlcHBlcj4KICA8L3YtZGlhbG9nPgo8L2Rpdj4K"}, null]}