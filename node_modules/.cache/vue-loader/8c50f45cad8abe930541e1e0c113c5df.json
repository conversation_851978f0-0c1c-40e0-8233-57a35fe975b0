{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js??ref--4!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--13-0!/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js??ref--1-0!/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue?vue&type=template&id=6c377b2c", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/babel.config.js", "mtime": 1688630955000}, {"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "showDialog", "attrs", "model", "value", "callback", "$$v", "expression", "settingType", "_l", "settingTabs", "item", "key", "_v", "_s", "align", "label", "on", "change", "setDrawEnable", "settingItems", "drawEnable", "$set", "setDataSyncEnable", "dataSyncEnable", "setHandwritingEnable", "handwritingEnable", "click", "setRemoteCursorVisible", "showRemoteCursor", "setDisablePointerEventResponding", "disablePointerEventResponding", "showCursorWhenDisabled", "setEnableMultiTouch", "enableMultiTouch", "graphicAutoSelect", "setMouseToolBehavior", "turnPage", "whiteBoard", "h5PPT", "imgPPT", "imgFile", "authorityStatus", "stat", "index", "text", "e", "onAuthorityChange", "authorityCmd", "cmd", "cols", "disabled", "dense", "onAuthorityCmdChange", "allowCmdList", "_e", "staticRenderFns", "_withStripped"], "sources": ["/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/SettingDialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"setting-dialog__container\" },\n    [\n      _vm.showDialog\n        ? _c(\n            \"v-dialog\",\n            {\n              attrs: { \"max-width\": \"600\" },\n              model: {\n                value: _vm.showDialog,\n                callback: function ($$v) {\n                  _vm.showDialog = $$v\n                },\n                expression: \"showDialog\",\n              },\n            },\n            [\n              _c(\n                \"v-card\",\n                [\n                  _c(\n                    \"v-card-title\",\n                    { staticClass: \"headline lighten-2\" },\n                    [\n                      _c(\n                        \"v-tabs\",\n                        {\n                          model: {\n                            value: _vm.settingType,\n                            callback: function ($$v) {\n                              _vm.settingType = $$v\n                            },\n                            expression: \"settingType\",\n                          },\n                        },\n                        _vm._l(_vm.settingTabs, function (item) {\n                          return _c(\"v-tab\", { key: item }, [\n                            _vm._v(\" \" + _vm._s(item) + \" \"),\n                          ])\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"v-card-text\",\n                    [\n                      _c(\n                        \"v-tabs-items\",\n                        {\n                          model: {\n                            value: _vm.settingType,\n                            callback: function ($$v) {\n                              _vm.settingType = $$v\n                            },\n                            expression: \"settingType\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"v-tab-item\",\n                            [\n                              _c(\n                                \"v-row\",\n                                { attrs: { align: \"center\" } },\n                                [\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"允许涂鸦\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: { change: _vm.setDrawEnable },\n                                        model: {\n                                          value: _vm.settingItems[0].drawEnable,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"drawEnable\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].drawEnable\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"开启数据同步\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: { change: _vm.setDataSyncEnable },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[0].dataSyncEnable,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"dataSyncEnable\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].dataSyncEnable\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"v-row\",\n                                { attrs: { align: \"center\" } },\n                                [\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"开启笔锋\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: {\n                                          change: _vm.setHandwritingEnable,\n                                        },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[0]\n                                              .handwritingEnable,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"handwritingEnable\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].handwritingEnable\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"显示远端画笔\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: {\n                                          click: _vm.setRemoteCursorVisible,\n                                        },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[0]\n                                              .showRemoteCursor,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"showRemoteCursor\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].showRemoteCursor\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"v-row\",\n                                { attrs: { align: \"center\" } },\n                                [\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"禁用鼠标响应\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: {\n                                          change:\n                                            _vm.setDisablePointerEventResponding,\n                                        },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[0]\n                                              .disablePointerEventResponding,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"disablePointerEventResponding\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].disablePointerEventResponding\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"禁用响应也显示光标\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[0]\n                                              .showCursorWhenDisabled,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"showCursorWhenDisabled\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].showCursorWhenDisabled\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"v-row\",\n                                { attrs: { align: \"center\" } },\n                                [\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label:\n                                            \"开启多点触控(只有触摸屏才有效)\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: { click: _vm.setEnableMultiTouch },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[0]\n                                              .enableMultiTouch,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"enableMultiTouch\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].enableMultiTouch\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"几何图形自动选中\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: { click: _vm.setEnableMultiTouch },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[0]\n                                              .graphicAutoSelect,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[0],\n                                              \"graphicAutoSelect\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[0].graphicAutoSelect\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"v-tab-item\",\n                            [\n                              _c(\n                                \"v-row\",\n                                { attrs: { align: \"center\" } },\n                                [\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"允许鼠标点击普通白板翻页\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: {\n                                          change: _vm.setMouseToolBehavior,\n                                        },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[1].turnPage\n                                              .whiteBoard,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[1].turnPage,\n                                              \"whiteBoard\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[1].turnPage.whiteBoard\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"允许鼠标点击H5PPT翻页\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: {\n                                          change: _vm.setMouseToolBehavior,\n                                        },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[1].turnPage.h5PPT,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[1].turnPage,\n                                              \"h5PPT\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[1].turnPage.h5PPT\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"v-row\",\n                                { attrs: { align: \"center\" } },\n                                [\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"允许鼠标点击静态PPT翻页\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: {\n                                          change: _vm.setMouseToolBehavior,\n                                        },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[1].turnPage.imgPPT,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[1].turnPage,\n                                              \"imgPPT\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[1].turnPage.imgPPT\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"v-col\",\n                                    { attrs: { \"align-self\": \"center\" } },\n                                    [\n                                      _c(\"v-switch\", {\n                                        attrs: {\n                                          label: \"允许鼠标点击图片组文件翻页\",\n                                          \"hide-details\": \"\",\n                                        },\n                                        on: { click: _vm.setMouseToolBehavior },\n                                        model: {\n                                          value:\n                                            _vm.settingItems[1].turnPage\n                                              .imgFile,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.settingItems[1].turnPage,\n                                              \"imgFile\",\n                                              $$v\n                                            )\n                                          },\n                                          expression:\n                                            \"settingItems[1].turnPage.imgFile\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                          _c(\n                            \"v-tab-item\",\n                            _vm._l(_vm.authorityStatus, function (stat, index) {\n                              return _c(\n                                \"v-sheet\",\n                                { key: stat.text },\n                                [\n                                  _c(\"v-switch\", {\n                                    attrs: { label: stat.text },\n                                    on: {\n                                      change: (e) =>\n                                        _vm.onAuthorityChange(index, e),\n                                    },\n                                    model: {\n                                      value: stat.value,\n                                      callback: function ($$v) {\n                                        _vm.$set(stat, \"value\", $$v)\n                                      },\n                                      expression: \"stat.value\",\n                                    },\n                                  }),\n                                  _c(\n                                    \"v-sheet\",\n                                    [\n                                      _c(\n                                        \"v-row\",\n                                        _vm._l(\n                                          _vm.authorityCmd[index],\n                                          function (cmd) {\n                                            return _c(\n                                              \"v-col\",\n                                              {\n                                                key: cmd.value,\n                                                attrs: { cols: \"3\" },\n                                              },\n                                              [\n                                                _c(\"v-checkbox\", {\n                                                  attrs: {\n                                                    label: cmd.text,\n                                                    value: cmd.value,\n                                                    disabled: !stat.value,\n                                                    dense: \"\",\n                                                  },\n                                                  on: {\n                                                    change: (e) =>\n                                                      _vm.onAuthorityCmdChange(\n                                                        index,\n                                                        e\n                                                      ),\n                                                  },\n                                                  model: {\n                                                    value:\n                                                      _vm.allowCmdList[index],\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        _vm.allowCmdList,\n                                                        index,\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"allowCmdList[index]\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            )\n                                          }\n                                        ),\n                                        1\n                                      ),\n                                    ],\n                                    1\n                                  ),\n                                  _c(\"v-divider\"),\n                                ],\n                                1\n                              )\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          )\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAA4B,CAAC,EAC5C,CACEH,GAAG,CAACI,UAAU,GACVH,EAAE,CACA,UAAU,EACV;IACEI,KAAK,EAAE;MAAE,WAAW,EAAE;IAAM,CAAC;IAC7BC,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACI,UAAU;MACrBI,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACI,UAAU,GAAGK,GAAG;MACtB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACET,EAAE,CACA,QAAQ,EACR,CACEA,EAAE,CACA,cAAc,EACd;IAAEE,WAAW,EAAE;EAAqB,CAAC,EACrC,CACEF,EAAE,CACA,QAAQ,EACR;IACEK,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACW,WAAW;MACtBH,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACW,WAAW,GAAGF,GAAG;MACvB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACDV,GAAG,CAACY,EAAE,CAACZ,GAAG,CAACa,WAAW,EAAE,UAAUC,IAAI,EAAE;IACtC,OAAOb,EAAE,CAAC,OAAO,EAAE;MAAEc,GAAG,EAAED;IAAK,CAAC,EAAE,CAChCd,GAAG,CAACgB,EAAE,CAAC,GAAG,GAAGhB,GAAG,CAACiB,EAAE,CAACH,IAAI,CAAC,GAAG,GAAG,CAAC,CACjC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDb,EAAE,CACA,aAAa,EACb,CACEA,EAAE,CACA,cAAc,EACd;IACEK,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACW,WAAW;MACtBH,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACW,WAAW,GAAGF,GAAG;MACvB,CAAC;MACDC,UAAU,EAAE;IACd;EACF,CAAC,EACD,CACET,EAAE,CACA,YAAY,EACZ,CACEA,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEjB,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,MAAM;MACb,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAErB,GAAG,CAACsB;IAAc,CAAC;IACjChB,KAAK,EAAE;MACLC,KAAK,EAAEP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACC,UAAU;MACrChB,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,YAAY,EACZd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,QAAQ;MACf,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MAAEC,MAAM,EAAErB,GAAG,CAAC0B;IAAkB,CAAC;IACrCpB,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACI,cAAc;MACpCnB,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,gBAAgB,EAChBd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEjB,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,MAAM;MACb,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MACFC,MAAM,EAAErB,GAAG,CAAC4B;IACd,CAAC;IACDtB,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAChBM,iBAAiB;MACtBrB,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,mBAAmB,EACnBd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,QAAQ;MACf,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MACFU,KAAK,EAAE9B,GAAG,CAAC+B;IACb,CAAC;IACDzB,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAChBS,gBAAgB;MACrBxB,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,kBAAkB,EAClBd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEjB,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,QAAQ;MACf,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MACFC,MAAM,EACJrB,GAAG,CAACiC;IACR,CAAC;IACD3B,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAChBW,6BAA6B;MAClC1B,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,+BAA+B,EAC/Bd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,WAAW;MAClB,cAAc,EAAE;IAClB,CAAC;IACDb,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAChBY,sBAAsB;MAC3B3B,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,wBAAwB,EACxBd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEjB,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EACH,kBAAkB;MACpB,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MAAEU,KAAK,EAAE9B,GAAG,CAACoC;IAAoB,CAAC;IACtC9B,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAChBc,gBAAgB;MACrB7B,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,kBAAkB,EAClBd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,UAAU;MACjB,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MAAEU,KAAK,EAAE9B,GAAG,CAACoC;IAAoB,CAAC;IACtC9B,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAChBe,iBAAiB;MACtB9B,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,EACnB,mBAAmB,EACnBd,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CACA,YAAY,EACZ,CACEA,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEjB,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,cAAc;MACrB,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MACFC,MAAM,EAAErB,GAAG,CAACuC;IACd,CAAC;IACDjC,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,CACzBC,UAAU;MACfjC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,EAC5B,YAAY,EACZ/B,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,eAAe;MACtB,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MACFC,MAAM,EAAErB,GAAG,CAACuC;IACd,CAAC;IACDjC,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAACE,KAAK;MACpClC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,EAC5B,OAAO,EACP/B,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAS;EAAE,CAAC,EAC9B,CACEjB,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,eAAe;MACtB,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MACFC,MAAM,EAAErB,GAAG,CAACuC;IACd,CAAC;IACDjC,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAACG,MAAM;MACrCnC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,EAC5B,QAAQ,EACR/B,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDT,EAAE,CACA,OAAO,EACP;IAAEI,KAAK,EAAE;MAAE,YAAY,EAAE;IAAS;EAAE,CAAC,EACrC,CACEJ,EAAE,CAAC,UAAU,EAAE;IACbI,KAAK,EAAE;MACLc,KAAK,EAAE,eAAe;MACtB,cAAc,EAAE;IAClB,CAAC;IACDC,EAAE,EAAE;MAAEU,KAAK,EAAE9B,GAAG,CAACuC;IAAqB,CAAC;IACvCjC,KAAK,EAAE;MACLC,KAAK,EACHP,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,CACzBI,OAAO;MACZpC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;QACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACuB,YAAY,CAAC,CAAC,CAAC,CAACiB,QAAQ,EAC5B,SAAS,EACT/B,GACF,CAAC;MACH,CAAC;MACDC,UAAU,EACR;IACJ;EACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CACA,YAAY,EACZD,GAAG,CAACY,EAAE,CAACZ,GAAG,CAAC6C,eAAe,EAAE,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACjD,OAAO9C,EAAE,CACP,SAAS,EACT;MAAEc,GAAG,EAAE+B,IAAI,CAACE;IAAK,CAAC,EAClB,CACE/C,EAAE,CAAC,UAAU,EAAE;MACbI,KAAK,EAAE;QAAEc,KAAK,EAAE2B,IAAI,CAACE;MAAK,CAAC;MAC3B5B,EAAE,EAAE;QACFC,MAAM,EAAE,SAARA,MAAMA,CAAG4B,CAAC;UAAA,OACRjD,GAAG,CAACkD,iBAAiB,CAACH,KAAK,EAAEE,CAAC,CAAC;QAAA;MACnC,CAAC;MACD3C,KAAK,EAAE;QACLC,KAAK,EAAEuC,IAAI,CAACvC,KAAK;QACjBC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;UACvBT,GAAG,CAACyB,IAAI,CAACqB,IAAI,EAAE,OAAO,EAAErC,GAAG,CAAC;QAC9B,CAAC;QACDC,UAAU,EAAE;MACd;IACF,CAAC,CAAC,EACFT,EAAE,CACA,SAAS,EACT,CACEA,EAAE,CACA,OAAO,EACPD,GAAG,CAACY,EAAE,CACJZ,GAAG,CAACmD,YAAY,CAACJ,KAAK,CAAC,EACvB,UAAUK,GAAG,EAAE;MACb,OAAOnD,EAAE,CACP,OAAO,EACP;QACEc,GAAG,EAAEqC,GAAG,CAAC7C,KAAK;QACdF,KAAK,EAAE;UAAEgD,IAAI,EAAE;QAAI;MACrB,CAAC,EACD,CACEpD,EAAE,CAAC,YAAY,EAAE;QACfI,KAAK,EAAE;UACLc,KAAK,EAAEiC,GAAG,CAACJ,IAAI;UACfzC,KAAK,EAAE6C,GAAG,CAAC7C,KAAK;UAChB+C,QAAQ,EAAE,CAACR,IAAI,CAACvC,KAAK;UACrBgD,KAAK,EAAE;QACT,CAAC;QACDnC,EAAE,EAAE;UACFC,MAAM,EAAE,SAARA,MAAMA,CAAG4B,CAAC;YAAA,OACRjD,GAAG,CAACwD,oBAAoB,CACtBT,KAAK,EACLE,CACF,CAAC;UAAA;QACL,CAAC;QACD3C,KAAK,EAAE;UACLC,KAAK,EACHP,GAAG,CAACyD,YAAY,CAACV,KAAK,CAAC;UACzBvC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,GAAG,EAAE;YACvBT,GAAG,CAACyB,IAAI,CACNzB,GAAG,CAACyD,YAAY,EAChBV,KAAK,EACLtC,GACF,CAAC;UACH,CAAC;UACDC,UAAU,EACR;QACJ;MACF,CAAC,CAAC,CACH,EACD,CACF,CAAC;IACH,CACF,CAAC,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDT,EAAE,CAAC,WAAW,CAAC,CAChB,EACD,CACF,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,GACDD,GAAG,CAAC0D,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxB5D,MAAM,CAAC6D,aAAa,GAAG,IAAI;AAE3B,SAAS7D,MAAM,EAAE4D,eAAe", "ignoreList": []}]}