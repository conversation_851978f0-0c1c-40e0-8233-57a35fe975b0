{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/WatermarkDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/WatermarkDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["WatermarkDialog.vue"], "names": [], "mappings": ";AAsTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "WatermarkDialog.vue", "sourceRoot": "src/components/ToolbarDialog", "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n    <v-card>\n      <v-card-title class=\"headline lighten-2\"> 水印元素 </v-card-title>\n      <v-card-text>\n        <v-tabs v-model=\"watermarkElementTab\">\n          <v-tab>图片水印</v-tab>\n          <v-tab>静态文字水印</v-tab>\n          <v-tab>动态文字水印</v-tab>\n          <v-tab>水印元素管理</v-tab>\n        </v-tabs>\n        <v-row>\n          <v-col cols=\"12\" md=\"12\">\n            <v-tabs-items v-model=\"watermarkElementTab\">\n              <!-- 图片水印 -->\n              <v-tab-item>\n                <v-form ref=\"watermarkForm\">\n                  <v-row>\n                    <v-col cols=\"4\" md=\"6\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].content\"\n                        :rules=\"rules.urlRule\"\n                        label=\"图片水印url\"\n                        hint=\"请输入https协议的图片url\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].left\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].left, 0),\n                          loanMaxRule(elementParams['watermark'].left, 1000),\n                        ]\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].top\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].top, 0),\n                          loanMaxRule(elementParams['watermark'].top, 500),\n                        ]\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                  <v-row>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].width\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].width, 10),\n                          loanMaxRule(elementParams['watermark'].width, 200),\n                        ]\"\n                        label=\"宽度\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['watermark'].height\"\n                        :rules=\"[\n                          loanMinRule(elementParams['watermark'].height, 10),\n                          loanMaxRule(elementParams['watermark'].height, 200),\n                        ]\"\n                        label=\"高度\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\" md=\"4\">\n                      <v-subheader class=\"pl-0\">\n                        透明度(范围[0.1, 1])\n                      </v-subheader>\n                      <v-slider\n                        v-model.number=\"elementParams['watermark'].opacity\"\n                        step=\"0.1\"\n                        max=\"1\"\n                        min=\"0.1\"\n                        :thumb-size=\"32\"\n                        thumb-label=\"always\"\n                      ></v-slider>\n                    </v-col>\n                  </v-row>\n                </v-form>\n              </v-tab-item>\n              <!-- 静态文字水印 -->\n              <v-tab-item>\n                <v-form ref=\"textWatermarkFixForm\">\n                  <v-row>\n                    <v-col cols=\"4\" md=\"6\">\n                      <v-text-field\n                        counter=\"20\"\n                        v-model.number=\"\n                          elementParams['textWatermarkFix'].content\n                        \"\n                        label=\"文字水印\"\n                        hint=\"文字水印内容\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermarkFix'].deg\"\n                        :rules=\"[\n                          loanMinRule(elementParams['textWatermarkFix'].deg, 0),\n                          loanMaxRule(\n                            elementParams['textWatermarkFix'].deg,\n                            360\n                          ),\n                        ]\"\n                        label=\"旋转角度\"\n                        suffix=\"度\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"4\">\n                      <v-subheader class=\"pl-0\">\n                        透明度(范围[0.1, 1])\n                      </v-subheader>\n                      <v-slider\n                        v-model.number=\"\n                          elementParams['textWatermarkFix'].opacity\n                        \"\n                        step=\"0.1\"\n                        max=\"1\"\n                        min=\"0.1\"\n                        :thumb-size=\"32\"\n                        thumb-label=\"always\"\n                      ></v-slider>\n                    </v-col>\n                  </v-row>\n                  <v-row>\n                    <v-col cols=\"2\">\n                      <v-text-field\n                        v-model.number=\"\n                          elementParams['textWatermarkFix'].textSize\n                        \"\n                        label=\"字号\"\n                        suffix=\"px\"\n                        :rules=\"[\n                          loanMinRule(\n                            elementParams['textWatermarkFix'].textSize,\n                            12\n                          ),\n                          loanMaxRule(\n                            elementParams['textWatermarkFix'].textSize,\n                            48\n                          ),\n                        ]\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\">\n                      <v-color-picker\n                        label=\"颜色\"\n                        v-model=\"elementParams['textWatermarkFix'].color\"\n                        canvas-height=\"60\"\n                        width=\"300\"\n                        hide-inputs\n                        mode=\"hexa\"\n                        flat\n                      ></v-color-picker>\n                    </v-col>\n                  </v-row>\n                </v-form>\n              </v-tab-item>\n              <!-- 动态文字水印 -->\n              <v-tab-item>\n                <v-form ref=\"textWatermarkForm\">\n                  <v-row>\n                    <v-col cols=\"4\" md=\"6\">\n                      <v-text-field\n                        counter=\"20\"\n                        v-model.number=\"elementParams['textWatermark'].content\"\n                        label=\"文字水印\"\n                        hint=\"文字水印内容\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermark'].deg\"\n                        :rules=\"[\n                          loanMinRule(elementParams['textWatermark'].deg, 0),\n                          loanMaxRule(elementParams['textWatermark'].deg, 360),\n                        ]\"\n                        label=\"旋转角度\"\n                        suffix=\"度\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"4\" md=\"4\">\n                      <v-subheader class=\"pl-0\">\n                        透明度(范围[0.1, 1])\n                      </v-subheader>\n                      <v-slider\n                        v-model.number=\"elementParams['textWatermark'].opacity\"\n                        step=\"0.1\"\n                        max=\"1\"\n                        min=\"0.1\"\n                        :thumb-size=\"32\"\n                        thumb-label=\"always\"\n                      ></v-slider>\n                    </v-col>\n                  </v-row>\n                  <v-row>\n                    <v-col cols=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermark'].interval\"\n                        label=\"位置变化间隔\"\n                        suffix=\"ms\"\n                        :rules=\"[\n                          loanMinRule(\n                            elementParams['textWatermark'].interval,\n                            1000\n                          ),\n                          loanMaxRule(\n                            elementParams['textWatermark'].interval,\n                            10000\n                          ),\n                        ]\"\n                      ></v-text-field>\n                    </v-col>\n\n                    <v-col cols=\"2\">\n                      <v-text-field\n                        v-model.number=\"elementParams['textWatermark'].textSize\"\n                        label=\"字号\"\n                        suffix=\"px\"\n                        :rules=\"[\n                          loanMinRule(\n                            elementParams['textWatermark'].textSize,\n                            12\n                          ),\n                          loanMaxRule(\n                            elementParams['textWatermark'].textSize,\n                            48\n                          ),\n                        ]\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"4\">\n                      <v-color-picker\n                        label=\"颜色\"\n                        v-model=\"elementParams['textWatermark'].color\"\n                        canvas-height=\"60\"\n                        width=\"300\"\n                        hide-inputs\n                        mode=\"hexa\"\n                        flat\n                      ></v-color-picker>\n                    </v-col>\n                  </v-row>\n                </v-form>\n              </v-tab-item>\n              <v-tab-item>\n                <v-btn\n                  class=\"ma-2\"\n                  outlined\n                  color=\"indigo\"\n                  @click=\"deleteWatermark(TEduBoard.TEduWatermarkType.Image)\"\n                >\n                  删除图片水印\n                </v-btn>\n                <v-btn\n                  class=\"ma-2\"\n                  outlined\n                  color=\"indigo\"\n                  @click=\"deleteWatermark(TEduBoard.TEduWatermarkType.TextFix)\"\n                >\n                  删除静态文本水印\n                </v-btn>\n                <v-btn\n                  class=\"ma-2\"\n                  outlined\n                  color=\"indigo\"\n                  @click=\"deleteWatermark(TEduBoard.TEduWatermarkType.Text)\"\n                >\n                  删除动态文本水印\n                </v-btn>\n              </v-tab-item>\n            </v-tabs-items>\n          </v-col>\n        </v-row>\n      </v-card-text>\n\n      <v-divider></v-divider>\n\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn color=\"primary\" text @click=\"addElement\">添加</v-btn>\n        <v-btn\n          v-if=\"\n            (watermarkElementTab === 1 && hasTextFixWatermark) ||\n            (watermarkElementTab === 2 && hasTextWatermark)\n          \"\n          color=\"primary\"\n          text\n          @click=\"updateElement\"\n          >修改(暂不支持角度和时间间隔)</v-btn\n        >\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      watermarkElementTab: null,\n      elementParams: {\n        watermark: {\n          title: \"图片水印\",\n          content: \"https://test-1259648581.file.myqcloud.com/image/logo.png\",\n          left: \"10\",\n          top: \"10\",\n          width: \"100\",\n          height: \"74\",\n          opacity: 0.8,\n        },\n\n        textWatermarkFix: {\n          title: \"静态文字水印\",\n          content: \"\",\n          deg: 0,\n          textSize: 16,\n          opacity: 0.8,\n          color: \"#FF0000\",\n          interval: 5000,\n        },\n\n        textWatermark: {\n          title: \"动态文字水印\",\n          content: \"\",\n          deg: 0,\n          textSize: 16,\n          opacity: 0.8,\n          color: \"#FF0000\",\n          interval: 5000,\n        },\n      },\n      rules: {\n        urlRule: [\n          (v) => !!v || \"url is required\",\n          (v) => /^https:\\/\\//.test(v) || \"url必须是https协议\",\n        ],\n      },\n    };\n  },\n\n  computed: {\n    hasTextWatermark() {\n      return !!window.teduBoard.getElementById(\n        TEduBoard.TEduWatermarkType.Text\n      );\n    },\n\n    hasTextFixWatermark() {\n      return !!window.teduBoard.getElementById(\n        TEduBoard.TEduWatermarkType.TextFix\n      );\n    },\n  },\n  watch: {\n    watermarkElementTab(tabIndex) {\n      if (tabIndex === 0) {\n        return;\n      }\n      if (tabIndex === 1) {\n        const element = window.teduBoard.getElementById(\n          TEduBoard.TEduWatermarkType.TextFix\n        );\n        if (element) {\n          this.elementParams.textWatermarkFix = {\n            title: \"静态文字水印\",\n            content: element.content,\n            deg: element.deg,\n            textSize: element.textSize,\n            opacity: element.opacity,\n            color: element.style.fillColor,\n            interval: element.interval,\n          };\n        }\n      } else if (tabIndex === 2) {\n        const element = window.teduBoard.getElementById(\n          TEduBoard.TEduWatermarkType.Text\n        );\n        if (element) {\n          this.elementParams.textWatermark = {\n            title: \"动态文字水印\",\n            content: element.content,\n            deg: element.deg,\n            textSize: element.textSize,\n            opacity: element.opacity,\n            color: element.style.fillColor,\n            interval: element.interval,\n          };\n        }\n      }\n    },\n  },\n\n  methods: {\n    loanMinRule(value, min) {\n      return (value || \"\") >= min || `不能小于最小值 ${min}`;\n    },\n    loanMaxRule(value, max) {\n      return (value || \"\") <= max || `不能大于最大值 ${max}`;\n    },\n    show() {\n      this.showDialog = true;\n    },\n    addElement() {\n      let type = \"\";\n      if (this.watermarkElementTab === 0) {\n        type = \"watermark\";\n      } else if (this.watermarkElementTab === 1) {\n        type = \"textWatermarkFix\";\n      } else if (this.watermarkElementTab === 2) {\n        type = \"textWatermark\";\n      }\n\n      switch (type) {\n        case \"watermark\": {\n          if (!this.$refs.watermarkForm.validate()) {\n            return;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_WATERMARK,\n            {\n              content: `${this.elementParams.watermark.content}`,\n              left: `${this.elementParams.watermark.left}px`,\n              top: `${this.elementParams.watermark.top}px`,\n              width: `${this.elementParams.watermark.width}px`,\n              height: `${this.elementParams.watermark.height}px`,\n              opacity: `${this.elementParams.watermark.opacity}`,\n              fix: false,\n            }\n          );\n          this.elementParams.watermark = {\n            title: \"学科公式\",\n            content: \"https://test-1259648581.file.myqcloud.com/image/logo.png\",\n            left: \"10\",\n            top: \"10\",\n            width: \"100\",\n            height: \"74\",\n            opacity: 0.8,\n          };\n          break;\n        }\n        case \"textWatermarkFix\": {\n          if (!this.$refs.textWatermarkFixForm.validate()) {\n            return;\n          }\n          const params = {\n            content: `${this.elementParams.textWatermarkFix.content}`,\n            deg: this.elementParams.textWatermarkFix.deg,\n            textSize: `${this.elementParams.textWatermarkFix.textSize}px`,\n            color: `${this.elementParams.textWatermarkFix.color}`,\n            fix: true,\n            opacity: this.elementParams.textWatermarkFix.opacity,\n          };\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_WATERMARK,\n            params\n          );\n          this.elementParams.textWatermarkFix = {\n            title: \"文字水印\",\n            content: \"\",\n            deg: 0,\n            textSize: 16,\n            opacity: 0.8,\n            color: \"#FF0000\",\n            interval: 5000,\n          };\n          break;\n        }\n        case \"textWatermark\": {\n          if (!this.$refs.textWatermarkForm.validate()) {\n            return;\n          }\n          const params = {\n            content: `${this.elementParams.textWatermark.content}`,\n            deg: this.elementParams.textWatermark.deg,\n            textSize: `${this.elementParams.textWatermark.textSize}px`,\n            color: `${this.elementParams.textWatermark.color}`,\n            fix: false,\n            opacity: this.elementParams.textWatermark.opacity,\n            interval: this.elementParams.textWatermark.interval,\n          };\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_WATERMARK,\n            params\n          );\n          this.elementParams.textWatermark = {\n            title: \"文字水印\",\n            content: \"\",\n            deg: 0,\n            textSize: 16,\n            opacity: 0.8,\n            color: \"#FF0000\",\n            interval: 5000,\n          };\n          break;\n        }\n      }\n      this.showDialog = false;\n    },\n\n    deleteWatermark(watermarkType) {\n      window.teduBoard.removeElement(watermarkType);\n    },\n\n    updateElement() {\n      if (this.watermarkElementTab === 1) {\n        // 修改内容\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.CONTENT,\n            value: this.elementParams[\"textWatermarkFix\"].content,\n          }\n        );\n        // 修改透明度\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.OPACITY,\n            value: this.elementParams[\"textWatermarkFix\"].opacity,\n          }\n        );\n        // 修改字号\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.CHANGE_TEXT_SIZE,\n            value: this.elementParams[\"textWatermarkFix\"].textSize + \"px\",\n          }\n        );\n        // 修改颜色\n        window.teduBoard.updateElementById(\n          TEduBoard.TEduWatermarkType.TextFix,\n          {\n            type: TEduBoard.TEduElementOperatorType.BOARDER_COLOR,\n            value: this.elementParams[\"textWatermarkFix\"].color,\n          }\n        );\n      } else if (this.watermarkElementTab === 2) {\n        // 修改内容\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.CONTENT,\n          value: this.elementParams[\"textWatermark\"].content,\n        });\n        // 修改透明度\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.OPACITY,\n          value: this.elementParams[\"textWatermark\"].opacity,\n        });\n        // 修改字号\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.CHANGE_TEXT_SIZE,\n          value: this.elementParams[\"textWatermark\"].textSize + \"px\",\n        });\n        // 修改颜色\n        window.teduBoard.updateElementById(TEduBoard.TEduWatermarkType.Text, {\n          type: TEduBoard.TEduElementOperatorType.BOARDER_COLOR,\n          value: this.elementParams[\"textWatermark\"].color,\n        });\n      }\n      this.showDialog = false;\n    },\n  },\n};\n</script>"]}]}