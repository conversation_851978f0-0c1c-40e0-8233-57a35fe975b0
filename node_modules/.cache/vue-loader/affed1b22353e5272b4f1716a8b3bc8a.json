{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CustomGraphDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/CustomGraphDialog.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2hvd0RpYWxvZzogZmFsc2UsCiAgICAgIGN1c3RvbUdyYXBoVXJsOiBudWxsLAogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHNob3coKSB7CiAgICAgIHRoaXMuc2hvd0RpYWxvZyA9IHRydWU7CiAgICB9LAoKICAgIGFkZFRvQ3VzdG9tR3JhcGhMaXN0KCkgewogICAgICB0aGlzLiRlbWl0KCJ1cGRhdGVDdXN0b21HcmFwaCIsIHRoaXMuY3VzdG9tR3JhcGhVcmwpOwogICAgICB0aGlzLmN1c3RvbUdyYXBoVXJsID0gIiI7CiAgICAgIHRoaXMuc2hvd0RpYWxvZyA9IGZhbHNlOwogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["CustomGraphDialog.vue"], "names": [], "mappings": ";AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "CustomGraphDialog.vue", "sourceRoot": "src/components/ToolbarDialog", "sourcesContent": ["<template>\n  <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"650\">\n    <v-card>\n      <v-card-title>添加自定义图形</v-card-title>\n      <v-card-text>\n        <v-text-field\n          v-model=\"customGraphUrl\"\n          label=\"自定义图形URL\"\n          prepend-icon=\"mdi-image\"\n        ></v-text-field>\n      </v-card-text>\n      <v-card-actions>\n        <v-spacer></v-spacer>\n        <v-btn color=\"primary\" text @click=\"addToCustomGraphList()\"\n          >添加至列表</v-btn\n        >\n        <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n      </v-card-actions>\n    </v-card>\n  </v-dialog>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      customGraphUrl: null,\n    };\n  },\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n\n    addToCustomGraphList() {\n      this.$emit(\"updateCustomGraph\", this.customGraphUrl);\n      this.customGraphUrl = \"\";\n      this.showDialog = false;\n    },\n  },\n};\n</script>"]}]}