{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/ElementDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/ToolbarDialog/ElementDialog.vue", "mtime": 1705892418000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2hvd0RpYWxvZzogZmFsc2UsCiAgICAgIGVsZW1lbnRQYXJhbXM6IHsKICAgICAgICBpbWFnZUVsZW1lbnQ6IHsKICAgICAgICAgIHRpdGxlOiAn5Zu+54mH5YWD57SgJywKICAgICAgICAgIGZpbGU6ICcnLAogICAgICAgICAgbGVmdDogJycsCiAgICAgICAgICByaWdodDogJycsCiAgICAgICAgfSwKICAgICAgICBoNUVsZW1lbnQ6IHsKICAgICAgICAgIHRpdGxlOiAnSDXlhYPntKAnLAogICAgICAgICAgZmlsZTogJycsCiAgICAgICAgICBsZWZ0OiAnJywKICAgICAgICAgIHJpZ2h0OiAnJywKICAgICAgICB9LAogICAgICAgIG1hdGhCb2FyZDogewogICAgICAgICAgdGl0bGU6ICfmlbDlrabnlLvmnb/lhYPntKAnLAogICAgICAgICAgb3B0czogewogICAgICAgICAgICBheGlzQ29sb3I6ICcjQUVBRUFFJywKICAgICAgICAgICAgd2lkdGg6ICc2MDAnLAogICAgICAgICAgICBoZWlnaHQ6ICc2MDAnLAogICAgICAgICAgfSwKICAgICAgICAgIGxlZnQ6ICcnLAogICAgICAgICAgcmlnaHQ6ICcnLAogICAgICAgIH0sCiAgICAgICAgbWF0aEdyYXBoOiB7CiAgICAgICAgICB0aXRsZTogJ+aVsOWtpuWHveaVsOWbvuWDj+WFg+e0oCcsCiAgICAgICAgICBvcHRzOiB7CiAgICAgICAgICAgIG1hdGhCb2FyZElkOiAnJywKICAgICAgICAgICAgZXhwcmVzc2lvbjogJycsCiAgICAgICAgICAgIGNvbG9yOiAnI2ZmMDAwMCcsCiAgICAgICAgICAgIHNlbGVjdGVkQ29sb3I6ICcjMDBmZjAwJywKICAgICAgICAgIH0sCiAgICAgICAgICBsZWZ0OiAnJywKICAgICAgICAgIHJpZ2h0OiAnJywKICAgICAgICB9LAogICAgICAgIG1hdGhHcmFwaFR5cGU6IHsKICAgICAgICAgIHRpdGxlOiAn5Yeg5L2V5Zu+5b2iJywKICAgICAgICAgIHR5cGU6IG51bGwsCiAgICAgICAgfSwKICAgICAgICBmb3JtdWxhOiB7CiAgICAgICAgICB0aXRsZTogJ+WtpuenkeWFrOW8jycsCiAgICAgICAgICBleHByZXNzaW9uOiAnJywKICAgICAgICAgIGVyYXNhYmxlOiB0cnVlLCAvLyDmmK/lkKblj6/ku6Xmk6bpmaQKICAgICAgICAgIGxlZnQ6ICcxMCcsCiAgICAgICAgICB0b3A6ICcxMCcsCiAgICAgICAgICBmb250U2l6ZTogMTYsCiAgICAgICAgICBmb250Q29sb3I6ICcjMDAwMDAwRkYnLAogICAgICAgIH0sCiAgICAgIH0sCiAgICAgIGVsZW1lbnRUYWI6IG51bGwsCiAgICAgIG1hdGhCb2FyZElkczogW10sCiAgICAgIG1hdGhHcmFwaFR5cGVzOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfpgInkuK0nLAogICAgICAgICAgdmFsdWU6IFRFZHVCb2FyZC5URWR1Qm9hcmRNYXRoR3JhcGhUeXBlLk5PTkUsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+eCuScsCiAgICAgICAgICB2YWx1ZTogVEVkdUJvYXJkLlRFZHVCb2FyZE1hdGhHcmFwaFR5cGUuUE9JTlQsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+ebtOe6vycsCiAgICAgICAgICB2YWx1ZTogVEVkdUJvYXJkLlRFZHVCb2FyZE1hdGhHcmFwaFR5cGUuTElORSwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn57q/5q61JywKICAgICAgICAgIHZhbHVlOiBURWR1Qm9hcmQuVEVkdUJvYXJkTWF0aEdyYXBoVHlwZS5MSU5FX1NFR01FTlQsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+WwhOe6vycsCiAgICAgICAgICB2YWx1ZTogVEVkdUJvYXJkLlRFZHVCb2FyZE1hdGhHcmFwaFR5cGUuUkFZLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflnIYnLAogICAgICAgICAgdmFsdWU6IFRFZHVCb2FyZC5URWR1Qm9hcmRNYXRoR3JhcGhUeXBlLkNJUkNMRSwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn6KeSJywKICAgICAgICAgIHZhbHVlOiBURWR1Qm9hcmQuVEVkdUJvYXJkTWF0aEdyYXBoVHlwZS5BTkdMRSwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5aSa6L655b2iJywKICAgICAgICAgIHZhbHVlOiBURWR1Qm9hcmQuVEVkdUJvYXJkTWF0aEdyYXBoVHlwZS5QT0xZR09OLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflkJHph48nLAogICAgICAgICAgdmFsdWU6IFRFZHVCb2FyZC5URWR1Qm9hcmRNYXRoR3JhcGhUeXBlLlZFQ1RPUiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5qSt5ZyGJywKICAgICAgICAgIHZhbHVlOiBURWR1Qm9hcmQuVEVkdUJvYXJkTWF0aEdyYXBoVHlwZS5FTExJUFNFLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfnq4vmlrnkvZMnLAogICAgICAgICAgdmFsdWU6IFRFZHVCb2FyZC5URWR1Qm9hcmRNYXRoR3JhcGhUeXBlLkNVQkUsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+WchuafseS9kycsCiAgICAgICAgICB2YWx1ZTogVEVkdUJvYXJkLlRFZHVCb2FyZE1hdGhHcmFwaFR5cGUuQ1lMSU5ERVIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+WchumUpeS9kycsCiAgICAgICAgICB2YWx1ZTogVEVkdUJvYXJkLlRFZHVCb2FyZE1hdGhHcmFwaFR5cGUuQ0lSQ1VMQVJfQ09ORSwKICAgICAgICB9LAogICAgICBdLAogICAgICBmb3JtdWxhRWRpdG9yRGlhbG9nOiBmYWxzZSwKICAgICAgbG9hZGluZ0VkaXRvcjogdHJ1ZSwKICAgIH07CiAgfSwKCiAgd2F0Y2g6IHsKICAgICckc3RvcmUuc3RhdGUuY3VycmVudC5maWxlSW5mbyc6IHsKICAgICAgaGFuZGxlcigpIHsKICAgICAgICB0aGlzLm1hdGhCb2FyZElkcyA9IHdpbmRvdy50ZWR1Qm9hcmQKICAgICAgICAgIC5nZXRCb2FyZEVsZW1lbnRMaXN0KCkKICAgICAgICAgIC5maWx0ZXIoCiAgICAgICAgICAgIChpKSA9PgogICAgICAgICAgICAgIGkudHlwZSA9PT0KICAgICAgICAgICAgICBURWR1Qm9hcmQuVEVkdUJvYXJkRWxlbWVudFR5cGUuVEVEVV9CT0FSRF9FTEVNRU5UX01BVEhfQk9BUkQKICAgICAgICAgICkKICAgICAgICAgIC5tYXAoKGkpID0+IGkuZWxlbWVudElkKTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSwKICAgIH0sCiAgICBlbGVtZW50VGFiKHZhbCkgewogICAgICBpZiAodmFsID09PSAzKSB7CiAgICAgICAgdGhpcy5tYXRoQm9hcmRJZHMgPSB3aW5kb3cudGVkdUJvYXJkCiAgICAgICAgICAuZ2V0Qm9hcmRFbGVtZW50TGlzdCgpCiAgICAgICAgICAuZmlsdGVyKAogICAgICAgICAgICAoaSkgPT4KICAgICAgICAgICAgICBpLnR5cGUgPT09CiAgICAgICAgICAgICAgVEVkdUJvYXJkLlRFZHVCb2FyZEVsZW1lbnRUeXBlLlRFRFVfQk9BUkRfRUxFTUVOVF9NQVRIX0JPQVJECiAgICAgICAgICApCiAgICAgICAgICAubWFwKChpKSA9PiBpLmVsZW1lbnRJZCk7CiAgICAgIH0KICAgIH0sCiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdtZXNzYWdlJywgKGV2ZW50KSA9PiB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcmVjdkRhdGEgPSBldmVudC5kYXRhOwogICAgICAgIGNvbnN0IGlzU3RyaW5nID0KICAgICAgICAgIE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbChyZWN2RGF0YSkgPT09ICdbb2JqZWN0IFN0cmluZ10nOwogICAgICAgIGNvbnN0IGRhdGEgPSBpc1N0cmluZyA/IEpTT04ucGFyc2UocmVjdkRhdGEpIDogcmVjdkRhdGE7CiAgICAgICAgaWYgKGRhdGEuY21kID09PSAndGVkdS1mb3JtdWxhLWRhdGEnKSB7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMuZm9ybXVsYS5leHByZXNzaW9uID0gZGF0YS5kYXRhOwogICAgICAgICAgdGhpcy5mb3JtdWxhRWRpdG9yRGlhbG9nID0gZmFsc2U7CiAgICAgICAgICB0aGlzLmxvYWRpbmdFZGl0b3IgPSB0cnVlOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIC8vIGNvbnNvbGUuZXJyb3IoZSk7CiAgICAgIH0KICAgIH0pOwogIH0sCgogIG1ldGhvZHM6IHsKICAgIHNob3coKSB7CiAgICAgIHRoaXMuc2hvd0RpYWxvZyA9IHRydWU7CiAgICB9LAogICAgbG9hbk1pblJ1bGUodmFsdWUsIG1pbikgewogICAgICByZXR1cm4gKHZhbHVlIHx8ICcnKSA+PSBtaW4gfHwgYOS4jeiDveWwj+S6juacgOWwj+WAvCAke21pbn1gOwogICAgfSwKICAgIGxvYW5NYXhSdWxlKHZhbHVlLCBtYXgpIHsKICAgICAgcmV0dXJuICh2YWx1ZSB8fCAnJykgPD0gbWF4IHx8IGDkuI3og73lpKfkuo7mnIDlpKflgLwgJHttYXh9YDsKICAgIH0sCiAgICBhZGRFbGVtZW50KCkgewogICAgICBsZXQgdHlwZSA9ICcnOwogICAgICBpZiAodGhpcy5lbGVtZW50VGFiID09PSAwKSB7CiAgICAgICAgdHlwZSA9ICdpbWFnZUVsZW1lbnQnOwogICAgICB9IGVsc2UgaWYgKHRoaXMuZWxlbWVudFRhYiA9PT0gMSkgewogICAgICAgIHR5cGUgPSAnaDVFbGVtZW50JzsKICAgICAgfSBlbHNlIGlmICh0aGlzLmVsZW1lbnRUYWIgPT09IDIpIHsKICAgICAgICB0eXBlID0gJ21hdGhCb2FyZCc7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5lbGVtZW50VGFiID09PSAzKSB7CiAgICAgICAgdHlwZSA9ICdtYXRoR3JhcGgnOwogICAgICB9IGVsc2UgaWYgKHRoaXMuZWxlbWVudFRhYiA9PT0gNSkgewogICAgICAgIHR5cGUgPSAnZm9ybXVsYSc7CiAgICAgIH0KCiAgICAgIHN3aXRjaCAodHlwZSkgewogICAgICAgIGNhc2UgJ2g1RWxlbWVudCc6IHsKICAgICAgICAgIC8vIOivpeaOpeWPo+S4ukRlbW/kvZPpqozmjqXlj6PvvIzkuI3kv53or4HnqLPlrprmgKfvvIzlrp7pmYXmlZnlrabkuK3or7fkvb/nlKjkupFBUEnlj5HotbfovaznoIEKICAgICAgICAgIGNvbnN0IG9wdCA9IHt9OwogICAgICAgICAgaWYgKHRoaXMuZWxlbWVudFBhcmFtcy5oNUVsZW1lbnQubGVmdCkgewogICAgICAgICAgICBvcHQubGVmdCA9IHRoaXMuZWxlbWVudFBhcmFtcy5oNUVsZW1lbnQubGVmdDsKICAgICAgICAgIH0KICAgICAgICAgIGlmICh0aGlzLmVsZW1lbnRQYXJhbXMuaDVFbGVtZW50LnRvcCkgewogICAgICAgICAgICBvcHQudG9wID0gdGhpcy5lbGVtZW50UGFyYW1zLmg1RWxlbWVudC50b3A7CiAgICAgICAgICB9CiAgICAgICAgICBpZiAodGhpcy5lbGVtZW50UGFyYW1zLmg1RWxlbWVudC5kZWcpIHsKICAgICAgICAgICAgb3B0LmRlZyA9IHRoaXMuZWxlbWVudFBhcmFtcy5oNUVsZW1lbnQuZGVnOwogICAgICAgICAgfQogICAgICAgICAgd2luZG93LnRlZHVCb2FyZC5hZGRFbGVtZW50KAogICAgICAgICAgICBURWR1Qm9hcmQuVEVkdUJvYXJkRWxlbWVudFR5cGUuVEVEVV9CT0FSRF9FTEVNRU5UX0g1LAogICAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMuaDVFbGVtZW50LmZpbGUsCiAgICAgICAgICAgIG9wdAogICAgICAgICAgKTsKICAgICAgICAgIHRoaXMuZWxlbWVudFBhcmFtcy5oNUVsZW1lbnQuZmlsZSA9IG51bGw7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMuaDVFbGVtZW50LmxlZnQgPSAnJzsKICAgICAgICAgIHRoaXMuZWxlbWVudFBhcmFtcy5oNUVsZW1lbnQudG9wID0gJyc7CiAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgICAgY2FzZSAnaW1hZ2VFbGVtZW50JzogewogICAgICAgICAgLy8g6K+l5o6l5Y+j5Li6RGVtb+S9k+mqjOaOpeWPo++8jOS4jeS/neivgeeos+WumuaAp++8jOWunumZheaVmeWtpuS4reivt+S9v+eUqOS6kUFQSeWPkei1t+i9rOeggQogICAgICAgICAgY29uc3Qgb3B0ID0ge307CiAgICAgICAgICBpZiAodGhpcy5lbGVtZW50UGFyYW1zLmltYWdlRWxlbWVudC5sZWZ0KSB7CiAgICAgICAgICAgIG9wdC5sZWZ0ID0gdGhpcy5lbGVtZW50UGFyYW1zLmltYWdlRWxlbWVudC5sZWZ0OwogICAgICAgICAgfQogICAgICAgICAgaWYgKHRoaXMuZWxlbWVudFBhcmFtcy5pbWFnZUVsZW1lbnQudG9wKSB7CiAgICAgICAgICAgIG9wdC50b3AgPSB0aGlzLmVsZW1lbnRQYXJhbXMuaW1hZ2VFbGVtZW50LnRvcDsKICAgICAgICAgIH0KICAgICAgICAgIHdpbmRvdy50ZWR1Qm9hcmQuYWRkRWxlbWVudCgKICAgICAgICAgICAgVEVkdUJvYXJkLlRFZHVCb2FyZEVsZW1lbnRUeXBlLlRFRFVfQk9BUkRfRUxFTUVOVF9JTUFHRSwKICAgICAgICAgICAgdGhpcy5lbGVtZW50UGFyYW1zLmltYWdlRWxlbWVudC5maWxlLAogICAgICAgICAgICBvcHQKICAgICAgICAgICk7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMuaW1hZ2VFbGVtZW50LmZpbGUgPSBudWxsOwogICAgICAgICAgdGhpcy5lbGVtZW50UGFyYW1zLmltYWdlRWxlbWVudC5sZWZ0ID0gJyc7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMuaW1hZ2VFbGVtZW50LnRvcCA9ICcnOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgfQogICAgICAgIGNhc2UgJ21hdGhCb2FyZCc6IHsKICAgICAgICAgIC8vIOivpeaOpeWPo+S4ukRlbW/kvZPpqozmjqXlj6PvvIzkuI3kv53or4HnqLPlrprmgKfvvIzlrp7pmYXmlZnlrabkuK3or7fkvb/nlKjkupFBUEnlj5HotbfovaznoIEKICAgICAgICAgIGNvbnN0IG9wdCA9IHt9OwogICAgICAgICAgaWYgKHRoaXMuZWxlbWVudFBhcmFtcy5tYXRoQm9hcmQubGVmdCkgewogICAgICAgICAgICBvcHQubGVmdCA9IHRoaXMuZWxlbWVudFBhcmFtcy5tYXRoQm9hcmQubGVmdDsKICAgICAgICAgIH0KICAgICAgICAgIGlmICh0aGlzLmVsZW1lbnRQYXJhbXMubWF0aEJvYXJkLnRvcCkgewogICAgICAgICAgICBvcHQudG9wID0gdGhpcy5lbGVtZW50UGFyYW1zLm1hdGhCb2FyZC50b3A7CiAgICAgICAgICB9CiAgICAgICAgICB3aW5kb3cudGVkdUJvYXJkLmFkZEVsZW1lbnQoCiAgICAgICAgICAgIFRFZHVCb2FyZC5URWR1Qm9hcmRFbGVtZW50VHlwZS5URURVX0JPQVJEX0VMRU1FTlRfTUFUSF9CT0FSRCwKICAgICAgICAgICAgdGhpcy5lbGVtZW50UGFyYW1zLm1hdGhCb2FyZC5vcHRzLAogICAgICAgICAgICBvcHQKICAgICAgICAgICk7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMubWF0aEJvYXJkLm9wdHMgPSB7CiAgICAgICAgICAgIGF4aXNDb2xvcjogJyNBRUFFQUUnLAogICAgICAgICAgICB3aWR0aDogJzYwMCcsCiAgICAgICAgICAgIGhlaWdodDogJzQwMCcsCiAgICAgICAgICB9OwogICAgICAgICAgdGhpcy5lbGVtZW50UGFyYW1zLm1hdGhCb2FyZC5sZWZ0ID0gJyc7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMubWF0aEJvYXJkLnRvcCA9ICcnOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgfQogICAgICAgIGNhc2UgJ21hdGhHcmFwaCc6IHsKICAgICAgICAgIC8vIOivpeaOpeWPo+S4ukRlbW/kvZPpqozmjqXlj6PvvIzkuI3kv53or4HnqLPlrprmgKfvvIzlrp7pmYXmlZnlrabkuK3or7fkvb/nlKjkupFBUEnlj5HotbfovaznoIEKICAgICAgICAgIHdpbmRvdy50ZWR1Qm9hcmQuYWRkRWxlbWVudCgKICAgICAgICAgICAgVEVkdUJvYXJkLlRFZHVCb2FyZEVsZW1lbnRUeXBlLlRFRFVfQk9BUkRfRUxFTUVOVF9NQVRIX0dSQVBILAogICAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMubWF0aEdyYXBoLm9wdHMKICAgICAgICAgICk7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMubWF0aEdyYXBoLm9wdHMgPSB7CiAgICAgICAgICAgIG1hdGhCb2FyZElkOiAnJywKICAgICAgICAgICAgZXhwcmVzc2lvbjogJycsCiAgICAgICAgICAgIGNvbG9yOiAnI2ZmMDAwMCcsCiAgICAgICAgICAgIHNlbGVjdGVkQ29sb3I6ICcjMDBmZjAwJywKICAgICAgICAgIH07CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMubWF0aEdyYXBoLmxlZnQgPSAnJzsKICAgICAgICAgIHRoaXMuZWxlbWVudFBhcmFtcy5tYXRoR3JhcGgudG9wID0gJyc7CiAgICAgICAgICBicmVhazsKICAgICAgICB9CgogICAgICAgIGNhc2UgJ2Zvcm11bGEnOiB7CiAgICAgICAgICBpZiAoIXRoaXMuJHJlZnMuZm9ybXVsYUZvcm0udmFsaWRhdGUoKSkgewogICAgICAgICAgICByZXR1cm47CiAgICAgICAgICB9CiAgICAgICAgICB3aW5kb3cudGVkdUJvYXJkLmFkZEVsZW1lbnQoCiAgICAgICAgICAgIFRFZHVCb2FyZC5URWR1Qm9hcmRFbGVtZW50VHlwZS5URURVX0JPQVJEX0VMRU1FTlRfRk9STVVMQSwKICAgICAgICAgICAgewogICAgICAgICAgICAgIGV4cHJlc3Npb246IHRoaXMuZWxlbWVudFBhcmFtcy5mb3JtdWxhLmV4cHJlc3Npb24sCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBlcmFzYWJsZTogdGhpcy5lbGVtZW50UGFyYW1zLmZvcm11bGEuZXJhc2FibGUsCiAgICAgICAgICAgICAgbGVmdDogYCR7dGhpcy5lbGVtZW50UGFyYW1zLmZvcm11bGEubGVmdH0lYCwKICAgICAgICAgICAgICB0b3A6IGAke3RoaXMuZWxlbWVudFBhcmFtcy5mb3JtdWxhLnRvcH0lYCwKICAgICAgICAgICAgICBmb250U2l6ZTogYCR7dGhpcy5lbGVtZW50UGFyYW1zLmZvcm11bGEuZm9udFNpemV9cHhgLAogICAgICAgICAgICAgIGZvbnRDb2xvcjogYCR7dGhpcy5lbGVtZW50UGFyYW1zLmZvcm11bGEuZm9udENvbG9yfWAsCiAgICAgICAgICAgIH0KICAgICAgICAgICk7CiAgICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMuZm9ybXVsYSA9IHsKICAgICAgICAgICAgdGl0bGU6ICflrabnp5HlhazlvI8nLAogICAgICAgICAgICBleHByZXNzaW9uOiAnJywKICAgICAgICAgICAgZXJhc2FibGU6IHRydWUsIC8vIOaYr+WQpuWPr+S7peaTpumZpAogICAgICAgICAgICBsZWZ0OiAxMCwKICAgICAgICAgICAgdG9wOiAxMCwKICAgICAgICAgICAgZm9udFNpemU6IDE2LAogICAgICAgICAgICBmb250Q29sb3I6ICcjMDAwMDAwRkYnLAogICAgICAgICAgfTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLnNob3dEaWFsb2cgPSBmYWxzZTsKICAgIH0sCiAgICAvLyDorr7nva7mlbDlrablt6XlhbcKICAgIHNldE1hdGhHcmFwaFR5cGUoKSB7CiAgICAgIHdpbmRvdy50ZWR1Qm9hcmQuc2V0TWF0aEdyYXBoVHlwZSgKICAgICAgICB0aGlzLmVsZW1lbnRQYXJhbXMubWF0aEdyYXBoVHlwZS50eXBlLAogICAgICAgIHRydWUKICAgICAgKTsKICAgIH0sCgogICAgZm9ybXVsYUVkaXRvck9ubG9hZCgpIHsKICAgICAgdGhpcy5sb2FkaW5nRWRpdG9yID0gZmFsc2U7CiAgICAgIGNvbnN0IGV4cHJlc3Npb24gPSB0aGlzLmVsZW1lbnRQYXJhbXMuZm9ybXVsYS5leHByZXNzaW9uOwogICAgICBpZiAoZXhwcmVzc2lvbikgewogICAgICAgIHRoaXMuJHJlZnNbJ2Zvcm11YWwtZWRpdG9yLXJlZiddLmNvbnRlbnRXaW5kb3cucG9zdE1lc3NhZ2UoCiAgICAgICAgICB7CiAgICAgICAgICAgIG1vZHVsZTogJ3RpdycsCiAgICAgICAgICAgIGV4cHJlc3Npb246IGV4cHJlc3Npb24sCiAgICAgICAgICB9LAogICAgICAgICAgJ2h0dHBzOi8vZGVtby5xY2xvdWR0aXcuY29tL3dlYi9sYXRlc3QvZm9ybXVsYS1lZGl0b3IvZXhhbXBsZXMvYmFzaWMtdXNhZ2UuaHRtbCcKICAgICAgICApOwogICAgICB9CiAgICB9LAogIH0sCn07Cg=="}, {"version": 3, "sources": ["ElementDialog.vue"], "names": [], "mappings": ";AAsTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ElementDialog.vue", "sourceRoot": "src/components/ToolbarDialog", "sourcesContent": ["<template>\n  <div>\n    <v-dialog v-if=\"showDialog\" v-model=\"showDialog\" max-width=\"1000\">\n      <v-card>\n        <v-card-title class=\"headline lighten-2\"> 白板元素 </v-card-title>\n        <v-card-text>\n          <v-tabs v-model=\"elementTab\">\n            <v-tab>图片元素</v-tab>\n            <v-tab>H5元素</v-tab>\n            <v-tab>数学画板元素</v-tab>\n            <v-tab>数学函数图像元素</v-tab>\n            <v-tab>几何图形</v-tab>\n            <v-tab>学科公式</v-tab>\n          </v-tabs>\n          <v-row>\n            <v-col cols=\"12\" md=\"12\">\n              <v-tabs-items v-model=\"elementTab\">\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"8\">\n                      <v-text-field\n                        v-model=\"elementParams['imageElement'].file\"\n                        label=\"图片元素URL\"\n                        prepend-icon=\"mdi-image\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['imageElement'].left\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['imageElement'].top\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"6\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].file\"\n                        label=\"H5元素URL\"\n                        prepend-icon=\"mdi-language-html5\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].left\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].top\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['h5Element'].deg\"\n                        label=\"旋转角度\"\n                        suffix=\"°\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"3\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].opts.width\"\n                        label=\"宽度\"\n                        suffix=\"px\"\n                        prepend-icon=\"mdi-cog-outline\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].opts.height\"\n                        label=\"高度\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].opts.axisColor\"\n                        label=\"颜色\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].left\"\n                        label=\"左边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathBoard'].top\"\n                        label=\"上边距\"\n                        suffix=\"px\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"4\">\n                      <v-select\n                        v-model=\"elementParams['mathGraph'].opts.mathBoardId\"\n                        :items=\"mathBoardIds\"\n                        label=\"函数画板ID\"\n                      ></v-select>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"4\">\n                      <v-text-field\n                        v-model=\"elementParams['mathGraph'].opts.expression\"\n                        label=\"函数表达式\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathGraph'].opts.color\"\n                        label=\"函数图像颜色\"\n                      ></v-text-field>\n                    </v-col>\n                    <v-col cols=\"12\" md=\"2\">\n                      <v-text-field\n                        v-model=\"elementParams['mathGraph'].opts.selectedColor\"\n                        label=\"选中高亮颜色\"\n                      ></v-text-field>\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <v-row>\n                    <v-col cols=\"12\" md=\"4\">\n                      <v-select\n                        v-model=\"elementParams['mathGraphType'].type\"\n                        :items=\"mathGraphTypes\"\n                        item-text=\"label\"\n                        item-value=\"value\"\n                        label=\"几何图像类型\"\n                      ></v-select>\n                    </v-col>\n                    <v-col cols=\"6\" md=\"4\">\n                      <v-btn\n                        rounded\n                        color=\"primary\"\n                        dark\n                        small\n                        @click=\"setMathGraphType\"\n                        >设置</v-btn\n                      >\n                    </v-col>\n                    <v-col cols=\"18\" md=\"4\">\n                      使用此功能，需要先在白板上添加数学画板，然后在数学画板上进行点击操作，则会绘制出对应的图形\n                    </v-col>\n                  </v-row>\n                </v-tab-item>\n                <v-tab-item>\n                  <!-- 数学公式 -->\n                  <v-form ref=\"formulaForm\">\n                    <v-row>\n                      <v-col cols=\"12\" md=\"6\">\n                        <v-text-field\n                          v-model=\"elementParams['formula'].expression\"\n                          :rules=\"[(value) => !!value || '必填']\"\n                          label=\"latex公式\"\n                          prepend-icon=\"mdi-function-variant\"\n                          readonly\n                          @click=\"\n                            (formulaEditorDialog = true), (loadingEditor = true)\n                          \"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"12\" md=\"2\">\n                        <v-text-field\n                          v-model.number=\"elementParams['formula'].left\"\n                          :rules=\"[\n                            loanMinRule(elementParams['formula'].left, 0),\n                            loanMaxRule(elementParams['formula'].left, 99),\n                          ]\"\n                          label=\"左边距\"\n                          suffix=\"%\"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"12\" md=\"2\">\n                        <v-text-field\n                          v-model.number=\"elementParams['formula'].top\"\n                          :rules=\"[\n                            loanMinRule(elementParams['formula'].top, 0),\n                            loanMaxRule(elementParams['formula'].top, 99),\n                          ]\"\n                          label=\"上边距\"\n                          suffix=\"%\"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"12\" md=\"2\">\n                        <v-switch\n                          v-model=\"elementParams['formula'].erasable\"\n                          label=\"是否可以擦除\"\n                        ></v-switch>\n                      </v-col>\n                    </v-row>\n                    <v-row>\n                      <v-col cols=\"2\">\n                        <v-text-field\n                          v-model.number=\"elementParams['formula'].fontSize\"\n                          label=\"字号\"\n                          suffix=\"px\"\n                          :rules=\"[\n                            loanMinRule(elementParams['formula'].fontSize, 12),\n                            loanMaxRule(elementParams['formula'].fontSize, 48),\n                          ]\"\n                        ></v-text-field>\n                      </v-col>\n                      <v-col cols=\"4\">\n                        <v-color-picker\n                          v-model=\"elementParams['formula'].fontColor\"\n                          canvas-height=\"60\"\n                          width=\"300\"\n                          hide-inputs\n                          mode=\"hexa\"\n                          flat\n                        ></v-color-picker>\n                      </v-col>\n                    </v-row>\n                  </v-form>\n                </v-tab-item>\n              </v-tabs-items>\n            </v-col>\n          </v-row>\n        </v-card-text>\n\n        <v-divider></v-divider>\n\n        <v-card-actions>\n          <v-spacer></v-spacer>\n          <v-btn\n            v-if=\"elementTab !== 4\"\n            color=\"primary\"\n            text\n            @click=\"addElement\"\n            >添加</v-btn\n          >\n          <v-btn text @click=\"showDialog = false\">关闭</v-btn>\n        </v-card-actions>\n      </v-card>\n    </v-dialog>\n\n    <v-dialog\n      v-if=\"formulaEditorDialog\"\n      v-model=\"formulaEditorDialog\"\n      max-width=\"1050\"\n    >\n      <v-card>\n        <v-card-title>\n          公式编辑器\n          <a\n            class=\"ml-4 primary--text text-subtitle-2\"\n            style=\"position: absolute; right: 32px\"\n            @click=\"\n              window.open(\n                'https://demo.qcloudtiw.com/web/latest/res/tiw-formula-editor.zip'\n              )\n            \"\n          >\n            点击此处可下载源码\n          </a>\n        </v-card-title>\n        <v-card-text>\n          <v-progress-circular\n            :size=\"50\"\n            color=\"primary\"\n            indeterminate\n            v-if=\"loadingEditor\"\n            style=\"\n              position: absolute;\n              left: 50%;\n              top: 50%;\n              transform: translate(-50%, -50%);\n              z-index: 1000;\n            \"\n          ></v-progress-circular>\n          <iframe\n            src=\"https://demo.qcloudtiw.com/web/latest/formula-editor/examples/basic-usage.html\"\n            width=\"1000\"\n            height=\"500\"\n            style=\"border: none\"\n            allow=\"clipboard-read; clipboard-write\"\n            @load=\"formulaEditorOnload\"\n            v-if=\"formulaEditorDialog\"\n            ref=\"formual-editor-ref\"\n          />\n        </v-card-text>\n      </v-card>\n    </v-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      showDialog: false,\n      elementParams: {\n        imageElement: {\n          title: '图片元素',\n          file: '',\n          left: '',\n          right: '',\n        },\n        h5Element: {\n          title: 'H5元素',\n          file: '',\n          left: '',\n          right: '',\n        },\n        mathBoard: {\n          title: '数学画板元素',\n          opts: {\n            axisColor: '#AEAEAE',\n            width: '600',\n            height: '600',\n          },\n          left: '',\n          right: '',\n        },\n        mathGraph: {\n          title: '数学函数图像元素',\n          opts: {\n            mathBoardId: '',\n            expression: '',\n            color: '#ff0000',\n            selectedColor: '#00ff00',\n          },\n          left: '',\n          right: '',\n        },\n        mathGraphType: {\n          title: '几何图形',\n          type: null,\n        },\n        formula: {\n          title: '学科公式',\n          expression: '',\n          erasable: true, // 是否可以擦除\n          left: '10',\n          top: '10',\n          fontSize: 16,\n          fontColor: '#000000FF',\n        },\n      },\n      elementTab: null,\n      mathBoardIds: [],\n      mathGraphTypes: [\n        {\n          label: '选中',\n          value: TEduBoard.TEduBoardMathGraphType.NONE,\n        },\n        {\n          label: '点',\n          value: TEduBoard.TEduBoardMathGraphType.POINT,\n        },\n        {\n          label: '直线',\n          value: TEduBoard.TEduBoardMathGraphType.LINE,\n        },\n        {\n          label: '线段',\n          value: TEduBoard.TEduBoardMathGraphType.LINE_SEGMENT,\n        },\n        {\n          label: '射线',\n          value: TEduBoard.TEduBoardMathGraphType.RAY,\n        },\n        {\n          label: '圆',\n          value: TEduBoard.TEduBoardMathGraphType.CIRCLE,\n        },\n        {\n          label: '角',\n          value: TEduBoard.TEduBoardMathGraphType.ANGLE,\n        },\n        {\n          label: '多边形',\n          value: TEduBoard.TEduBoardMathGraphType.POLYGON,\n        },\n        {\n          label: '向量',\n          value: TEduBoard.TEduBoardMathGraphType.VECTOR,\n        },\n        {\n          label: '椭圆',\n          value: TEduBoard.TEduBoardMathGraphType.ELLIPSE,\n        },\n        {\n          label: '立方体',\n          value: TEduBoard.TEduBoardMathGraphType.CUBE,\n        },\n        {\n          label: '圆柱体',\n          value: TEduBoard.TEduBoardMathGraphType.CYLINDER,\n        },\n        {\n          label: '圆锥体',\n          value: TEduBoard.TEduBoardMathGraphType.CIRCULAR_CONE,\n        },\n      ],\n      formulaEditorDialog: false,\n      loadingEditor: true,\n    };\n  },\n\n  watch: {\n    '$store.state.current.fileInfo': {\n      handler() {\n        this.mathBoardIds = window.teduBoard\n          .getBoardElementList()\n          .filter(\n            (i) =>\n              i.type ===\n              TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_BOARD\n          )\n          .map((i) => i.elementId);\n      },\n      deep: true,\n    },\n    elementTab(val) {\n      if (val === 3) {\n        this.mathBoardIds = window.teduBoard\n          .getBoardElementList()\n          .filter(\n            (i) =>\n              i.type ===\n              TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_BOARD\n          )\n          .map((i) => i.elementId);\n      }\n    },\n  },\n\n  mounted() {\n    window.addEventListener('message', (event) => {\n      try {\n        const recvData = event.data;\n        const isString =\n          Object.prototype.toString.call(recvData) === '[object String]';\n        const data = isString ? JSON.parse(recvData) : recvData;\n        if (data.cmd === 'tedu-formula-data') {\n          this.elementParams.formula.expression = data.data;\n          this.formulaEditorDialog = false;\n          this.loadingEditor = true;\n        }\n      } catch (e) {\n        // console.error(e);\n      }\n    });\n  },\n\n  methods: {\n    show() {\n      this.showDialog = true;\n    },\n    loanMinRule(value, min) {\n      return (value || '') >= min || `不能小于最小值 ${min}`;\n    },\n    loanMaxRule(value, max) {\n      return (value || '') <= max || `不能大于最大值 ${max}`;\n    },\n    addElement() {\n      let type = '';\n      if (this.elementTab === 0) {\n        type = 'imageElement';\n      } else if (this.elementTab === 1) {\n        type = 'h5Element';\n      } else if (this.elementTab === 2) {\n        type = 'mathBoard';\n      } else if (this.elementTab === 3) {\n        type = 'mathGraph';\n      } else if (this.elementTab === 5) {\n        type = 'formula';\n      }\n\n      switch (type) {\n        case 'h5Element': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          const opt = {};\n          if (this.elementParams.h5Element.left) {\n            opt.left = this.elementParams.h5Element.left;\n          }\n          if (this.elementParams.h5Element.top) {\n            opt.top = this.elementParams.h5Element.top;\n          }\n          if (this.elementParams.h5Element.deg) {\n            opt.deg = this.elementParams.h5Element.deg;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_H5,\n            this.elementParams.h5Element.file,\n            opt\n          );\n          this.elementParams.h5Element.file = null;\n          this.elementParams.h5Element.left = '';\n          this.elementParams.h5Element.top = '';\n          break;\n        }\n        case 'imageElement': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          const opt = {};\n          if (this.elementParams.imageElement.left) {\n            opt.left = this.elementParams.imageElement.left;\n          }\n          if (this.elementParams.imageElement.top) {\n            opt.top = this.elementParams.imageElement.top;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_IMAGE,\n            this.elementParams.imageElement.file,\n            opt\n          );\n          this.elementParams.imageElement.file = null;\n          this.elementParams.imageElement.left = '';\n          this.elementParams.imageElement.top = '';\n          break;\n        }\n        case 'mathBoard': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          const opt = {};\n          if (this.elementParams.mathBoard.left) {\n            opt.left = this.elementParams.mathBoard.left;\n          }\n          if (this.elementParams.mathBoard.top) {\n            opt.top = this.elementParams.mathBoard.top;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_BOARD,\n            this.elementParams.mathBoard.opts,\n            opt\n          );\n          this.elementParams.mathBoard.opts = {\n            axisColor: '#AEAEAE',\n            width: '600',\n            height: '400',\n          };\n          this.elementParams.mathBoard.left = '';\n          this.elementParams.mathBoard.top = '';\n          break;\n        }\n        case 'mathGraph': {\n          // 该接口为Demo体验接口，不保证稳定性，实际教学中请使用云API发起转码\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_MATH_GRAPH,\n            this.elementParams.mathGraph.opts\n          );\n          this.elementParams.mathGraph.opts = {\n            mathBoardId: '',\n            expression: '',\n            color: '#ff0000',\n            selectedColor: '#00ff00',\n          };\n          this.elementParams.mathGraph.left = '';\n          this.elementParams.mathGraph.top = '';\n          break;\n        }\n\n        case 'formula': {\n          if (!this.$refs.formulaForm.validate()) {\n            return;\n          }\n          window.teduBoard.addElement(\n            TEduBoard.TEduBoardElementType.TEDU_BOARD_ELEMENT_FORMULA,\n            {\n              expression: this.elementParams.formula.expression,\n            },\n            {\n              erasable: this.elementParams.formula.erasable,\n              left: `${this.elementParams.formula.left}%`,\n              top: `${this.elementParams.formula.top}%`,\n              fontSize: `${this.elementParams.formula.fontSize}px`,\n              fontColor: `${this.elementParams.formula.fontColor}`,\n            }\n          );\n          this.elementParams.formula = {\n            title: '学科公式',\n            expression: '',\n            erasable: true, // 是否可以擦除\n            left: 10,\n            top: 10,\n            fontSize: 16,\n            fontColor: '#000000FF',\n          };\n          break;\n        }\n      }\n      this.showDialog = false;\n    },\n    // 设置数学工具\n    setMathGraphType() {\n      window.teduBoard.setMathGraphType(\n        this.elementParams.mathGraphType.type,\n        true\n      );\n    },\n\n    formulaEditorOnload() {\n      this.loadingEditor = false;\n      const expression = this.elementParams.formula.expression;\n      if (expression) {\n        this.$refs['formual-editor-ref'].contentWindow.postMessage(\n          {\n            module: 'tiw',\n            expression: expression,\n          },\n          'https://demo.qcloudtiw.com/web/latest/formula-editor/examples/basic-usage.html'\n        );\n      }\n    },\n  },\n};\n</script>\n"]}]}