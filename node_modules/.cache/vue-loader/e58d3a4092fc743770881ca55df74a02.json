{"remainingRequest": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Downloads/web-demo/src/components/Document.vue?vue&type=template&id=5ffe286a", "dependencies": [{"path": "/Users/<USER>/Downloads/web-demo/src/components/Document.vue", "mtime": 1702021230000}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vuetify-loader/lib/loader.js", "mtime": 1757335233509}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/babel-loader/lib/index.js", "mtime": 1757335230593}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1757335231923}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/cache-loader/dist/cjs.js", "mtime": 1757335229325}, {"path": "/Users/<USER>/Downloads/web-demo/node_modules/vue-loader/lib/index.js", "mtime": 1757335230854}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXY+CiAgPHYtY2Fyb3VzZWwKICAgIGhlaWdodD0iNTAwIgogICAgaGlkZS1kZWxpbWl0ZXJzCiAgICBzaG93LWFycm93cy1vbi1ob3ZlcgogID4KICAgIDx2LWNhcm91c2VsLWl0ZW0KICAgICAgdi1mb3I9IihleGFtcGxlLCBpKSBpbiBleGFtcGxlcyIKICAgICAgOmtleT0iaSIKICAgID4KICAgICAgPHYtc2hlZXQKICAgICAgICB0aWxlCiAgICAgID4KICAgICAgICA8di1yb3cKICAgICAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgICAgICBqdXN0aWZ5PSJjZW50ZXIiCiAgICAgICAgPgogICAgICAgICAgPHYtY29sIGFsaWduPSJjZW50ZXIiIGNsYXNzPSJwdC00IHBiLTEiIHN0eWxlPSJmb250LXNpemU6IDIwcHg7IGJhY2tncm91bmQ6ICM2MTYxNjE7Ij4KICAgICAgICAgICAge3sgZXhhbXBsZS50aXRsZSB9fQogICAgICAgICAgPC92LWNvbD4KICAgICAgICA8L3Ytcm93PgogICAgICAgIDx2LXJvdwogICAgICAgICAgY2xhc3M9ImZpbGwtd2lkdGgiCiAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAganVzdGlmeT0iY2VudGVyIgogICAgICAgID4KICAgICAgICAgIDx2LWltZwogICAgICAgICAgICA6c3JjPSJleGFtcGxlLnVybCIKICAgICAgICAgICAgY29udGFpbgogICAgICAgICAgPgogICAgICAgICAgICA8dGVtcGxhdGUgdi1zbG90OnBsYWNlaG9sZGVyPgogICAgICAgICAgICAgIDx2LXJvdwogICAgICAgICAgICAgICAgY2xhc3M9ImZpbGwtaGVpZ2h0IgogICAgICAgICAgICAgICAgYWxpZ249ImNlbnRlciIKICAgICAgICAgICAgICAgIGp1c3RpZnk9ImNlbnRlciIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8di1wcm9ncmVzcy1jaXJjdWxhcgogICAgICAgICAgICAgICAgICBpbmRldGVybWluYXRlCiAgICAgICAgICAgICAgICAgIGNvbG9yPSJncmV5IGxpZ2h0ZW4tNSIKICAgICAgICAgICAgICAgID48L3YtcHJvZ3Jlc3MtY2lyY3VsYXI+CiAgICAgICAgICAgICAgPC92LXJvdz4KICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvdi1pbWc+CiAgICAgICAgPC92LXJvdz4KICAgICAgPC92LXNoZWV0PgogICAgPC92LWNhcm91c2VsLWl0ZW0+CiAgPC92LWNhcm91c2VsPgogIDx2LXJvdz4KICAgIDx2LWNvbCB2LWZvcj0iaXRlbSBpbiBkb2NVcmxzIiA6a2V5PSJpdGVtLnVybCI+CiAgICAgIDx2LWJ0bgogICAgICAgIDpvdXRsaW5lZD0iaXRlbS5vdXRsaW5lZCIKICAgICAgICA6Y29sb3I9Iml0ZW0uY29sb3IiCiAgICAgICAgZGFyawogICAgICAgIEBjbGljaz0id2luZG93Lm9wZW4oaXRlbS51cmwpIgogICAgICAgIGJsb2NrCiAgICAgID4KICAgICAgICA8di1pY29uCiAgICAgICAgICBsZWZ0CiAgICAgICAgICBkYXJrCiAgICAgICAgPgogICAgICAgICAge3sgaXRlbS5pY29uIH19CiAgICAgICAgPC92LWljb24+CiAgICAgICAge3sgaXRlbS50aXRsZSB9fQogICAgICA8L3YtYnRuPgogICAgPC92LWNvbD4KICA8L3Ytcm93Pgo8L2Rpdj4K"}, null]}