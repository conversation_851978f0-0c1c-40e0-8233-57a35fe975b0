// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VAlert.ts should be dismissible 1`] = `
<div role="alert"
     class="v-alert v-sheet theme--light"
     style="display: none;"
>
  <div class="v-alert__wrapper">
    <div class="v-alert__content">
    </div>
    <button type="button"
            class="v-alert__dismissible v-btn v-btn--icon v-btn--round theme--light v-size--small"
            aria-label="$vuetify.close"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate material-icons theme--light"
        >
          $cancel
        </i>
      </span>
    </button>
  </div>
</div>
`;

exports[`VAlert.ts should be open by default 1`] = `
<div role="alert"
     class="v-alert v-sheet theme--light"
>
  <div class="v-alert__wrapper">
    <div class="v-alert__content">
    </div>
  </div>
</div>
`;

exports[`VAlert.ts should be open by default 2`] = `
<div role="alert"
     class="v-alert v-sheet theme--light"
     style="display: none;"
>
  <div class="v-alert__wrapper">
    <div class="v-alert__content">
    </div>
  </div>
</div>
`;

exports[`VAlert.ts should have a close icon 1`] = `
<div role="alert"
     class="v-alert v-sheet theme--light"
>
  <div class="v-alert__wrapper">
    <div class="v-alert__content">
    </div>
    <button type="button"
            class="v-alert__dismissible v-btn v-btn--icon v-btn--round theme--light v-size--small"
            aria-label="$vuetify.close"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate material-icons theme--light"
        >
          $cancel
        </i>
      </span>
    </button>
  </div>
</div>
`;
