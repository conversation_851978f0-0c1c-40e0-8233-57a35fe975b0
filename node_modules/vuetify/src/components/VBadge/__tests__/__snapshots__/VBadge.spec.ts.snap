// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VBadge.ts should render component and match snapshot 1`] = `
<span class="v-badge theme--light">
  <span>
    element
  </span>
  <span class="v-badge__wrapper">
    <span aria-atomic="true"
          aria-label="$vuetify.badge"
          aria-live="polite"
          role="status"
          class="v-badge__badge primary"
    >
      <span>
        content
      </span>
    </span>
  </span>
</span>
`;

exports[`VBadge.ts should render component with with value=false and match snapshot 1`] = `
<span class="v-badge theme--light">
  <span>
    element
  </span>
  <span class="v-badge__wrapper">
    <span aria-atomic="true"
          aria-label="$vuetify.badge"
          aria-live="polite"
          role="status"
          class="v-badge__badge primary"
          style="display: none;"
    >
      <span>
        content
      </span>
    </span>
  </span>
</span>
`;
