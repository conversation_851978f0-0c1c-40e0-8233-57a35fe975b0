// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VBottomNavigation.ts should be visible with a true value 1`] = `
Object {
  "height": "56px",
  "transform": "none",
}
`;

exports[`VBottomNavigation.ts should be visible with a true value 2`] = `
<div class="v-bottom-navigation v-item-group theme--light"
     style="height: 56px; transform: none;"
>
  <button type="button"
          class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default"
  >
    <span class="v-btn__content">
    </span>
  </button>
  <button type="button"
          class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default"
  >
    <span class="v-btn__content">
    </span>
  </button>
</div>
`;

exports[`VBottomNavigation.ts should be visible with a true value 3`] = `
Object {
  "height": "56px",
  "transform": "translateY(100%)",
}
`;

exports[`VBottomNavigation.ts should be visible with a true value 4`] = `
<div class="v-bottom-navigation v-item-group theme--light"
     style="height: 56px; transform: translateY(100%);"
>
  <button type="button"
          class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default"
  >
    <span class="v-btn__content">
    </span>
  </button>
  <button type="button"
          class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default"
  >
    <span class="v-btn__content">
    </span>
  </button>
</div>
`;
