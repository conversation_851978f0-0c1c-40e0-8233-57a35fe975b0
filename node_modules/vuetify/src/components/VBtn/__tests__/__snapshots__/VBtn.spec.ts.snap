// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VBtn.ts should not add color classes if disabled 1`] = `
<button type="button"
        class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default primary darken-2"
>
  <span class="v-btn__content">
  </span>
</button>
`;

exports[`VBtn.ts should not add color classes if disabled 2`] = `
<button type="button"
        class="v-btn v-btn--disabled v-btn--has-bg theme--light v-size--default"
        disabled="disabled"
>
  <span class="v-btn__content">
  </span>
</button>
`;

exports[`VBtn.ts should render an <a> tag when using href prop 1`] = `
<a href="http://www.google.com"
   class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default"
>
  <span class="v-btn__content">
  </span>
</a>
`;

exports[`VBtn.ts should render component and match snapshot 1`] = `
<button type="button"
        class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default"
>
  <span class="v-btn__content">
  </span>
</button>
`;

exports[`VBtn.ts should render component with color prop and match snapshot 1`] = `
<button type="button"
        class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default green darken-1"
>
  <span class="v-btn__content">
  </span>
</button>
`;

exports[`VBtn.ts should render component with color prop and match snapshot 2`] = `
<button type="button"
        class="v-btn v-btn--text theme--light v-size--default green--text text--darken-1"
>
  <span class="v-btn__content">
  </span>
</button>
`;

exports[`VBtn.ts should render component with loader and match snapshot 1`] = `
<button type="button"
        class="v-btn v-btn--is-elevated v-btn--has-bg v-btn--loading theme--light v-size--default"
>
  <span class="v-btn__content">
  </span>
  <span class="v-btn__loader">
    <div role="progressbar"
         aria-valuemin="0"
         aria-valuemax="100"
         class="v-progress-circular v-progress-circular--indeterminate"
         style="height: 23px; width: 23px;"
    >
      <svg xmlns="http://www.w3.org/2000/svg"
           viewbox="21.904761904761905 21.904761904761905 43.80952380952381 43.80952380952381"
           style="transform: rotate(0deg);"
      >
        <circle fill="transparent"
                cx="43.80952380952381"
                cy="43.80952380952381"
                r="20"
                stroke-width="3.8095238095238093"
                stroke-dasharray="125.664"
                stroke-dashoffset="125.66370614359172px"
                class="v-progress-circular__overlay"
        >
        </circle>
      </svg>
      <div class="v-progress-circular__info">
      </div>
    </div>
  </span>
</button>
`;

exports[`VBtn.ts should render component with loader slot and match snapshot 1`] = `
<button type="button"
        class="v-btn v-btn--is-elevated v-btn--has-bg v-btn--loading theme--light v-size--default"
>
  <span class="v-btn__content">
  </span>
  <span class="v-btn__loader">
    <span>
      loader
    </span>
  </span>
</button>
`;

exports[`VBtn.ts should render specified tag when using tag prop 1`] = `
<a class="v-btn v-btn--is-elevated v-btn--has-bg theme--light v-size--default">
  <span class="v-btn__content">
  </span>
</a>
`;

exports[`VBtn.ts should render tile button and match snapshot 1`] = `
<button type="button"
        class="v-btn v-btn--is-elevated v-btn--has-bg v-btn--tile theme--light v-size--default"
>
  <span class="v-btn__content">
  </span>
</button>
`;
