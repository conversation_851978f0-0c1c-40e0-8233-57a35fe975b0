// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`calendar-base.ts should create a day list 1`] = `
Array [
  Object {
    "date": "2019-01-29",
    "day": 29,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 1,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 2,
    "year": 2019,
  },
  Object {
    "date": "2019-01-30",
    "day": 30,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 1,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  Object {
    "date": "2019-01-31",
    "day": 31,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 1,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  Object {
    "date": "2019-02-01",
    "day": 1,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-02",
    "day": 2,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 6,
    "year": 2019,
  },
  Object {
    "date": "2019-02-03",
    "day": 3,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 0,
    "year": 2019,
  },
  Object {
    "date": "2019-02-04",
    "day": 4,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 1,
    "year": 2019,
  },
  Object {
    "date": "2019-02-05",
    "day": 5,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 2,
    "year": 2019,
  },
  Object {
    "date": "2019-02-06",
    "day": 6,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  Object {
    "date": "2019-02-07",
    "day": 7,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 5,
    "year": 2019,
  },
]
`;

exports[`calendar-base.ts should generate classes 1`] = `
Object {
  "v-future": false,
  "v-outside": false,
  "v-past": false,
  "v-present": false,
}
`;

exports[`calendar-base.ts should generate classes with outside 1`] = `
Object {
  "v-future": false,
  "v-outside": true,
  "v-past": false,
  "v-present": false,
}
`;

exports[`calendar-base.ts should parse start & end 1`] = `
Object {
  "date": "2019-01-29",
  "day": 29,
  "future": false,
  "hasDay": true,
  "hasTime": false,
  "hour": 0,
  "minute": 0,
  "month": 1,
  "past": false,
  "present": false,
  "time": "",
  "weekday": 2,
  "year": 2019,
}
`;

exports[`calendar-base.ts should parse start & end 2`] = `
Object {
  "date": "2019-02-08",
  "day": 8,
  "future": false,
  "hasDay": true,
  "hasTime": false,
  "hour": 0,
  "minute": 0,
  "month": 2,
  "past": false,
  "present": false,
  "time": "",
  "weekday": 5,
  "year": 2019,
}
`;
