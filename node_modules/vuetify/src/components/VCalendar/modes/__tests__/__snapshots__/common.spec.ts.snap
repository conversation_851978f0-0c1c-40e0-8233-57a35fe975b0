// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`common.ts should get visuals 1 1`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 0,
  "input": Object {
    "end": "2019-02-14",
    "start": "2019-02-13",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;

exports[`common.ts should get visuals 1 2`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 0,
  "input": Object {
    "a": "2019-02-13",
    "b": "2019-02-14",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;

exports[`common.ts should get visuals 1 3`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 1,
  "input": Object {
    "end": "2019-02-14",
    "start": "2019-02-13",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;

exports[`common.ts should get visuals 1 4`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 1,
  "input": Object {
    "a": "2019-02-13",
    "b": "2019-02-14",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;
