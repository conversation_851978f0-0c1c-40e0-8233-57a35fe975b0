// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`events.ts should parse events 1`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 0,
  "input": Object {
    "end": "2019-02-14",
    "start": "2019-02-13",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;

exports[`events.ts should parse events 2`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 0,
  "input": Object {
    "a": "2019-02-13",
    "b": "2019-02-14",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;

exports[`events.ts should parse events 3`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 1,
  "input": Object {
    "end": "2019-02-14",
    "start": "2019-02-13",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;

exports[`events.ts should parse events 4`] = `
Object {
  "allDay": true,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902142359,
  "index": 1,
  "input": Object {
    "a": "2019-02-13",
    "b": "2019-02-14",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130000,
}
`;

exports[`events.ts should parse timed events 1`] = `
Object {
  "allDay": false,
  "category": false,
  "end": Object {
    "date": "2019-02-14",
    "day": 14,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "",
    "weekday": 4,
    "year": 2019,
  },
  "endIdentifier": 20190214,
  "endTimestampIdentifier": 201902140000,
  "index": 0,
  "input": Object {
    "end": "2019-02-14",
    "start": "2019-02-13 8:30",
  },
  "start": Object {
    "date": "2019-02-13",
    "day": 13,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 8,
    "minute": 30,
    "month": 2,
    "past": false,
    "present": false,
    "time": "08:30",
    "weekday": 3,
    "year": 2019,
  },
  "startIdentifier": 20190213,
  "startTimestampIdentifier": 201902130830,
}
`;
