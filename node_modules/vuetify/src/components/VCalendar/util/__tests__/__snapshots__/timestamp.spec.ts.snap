// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VCalendar/util/timestamp.ts createDayList should handle skips equal to zero 1`] = `
Array [
  Object {
    "date": "2019-04-22",
    "day": 22,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 4,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 1,
    "year": 2019,
  },
  Object {
    "date": "2019-04-24",
    "day": 24,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 4,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 3,
    "year": 2019,
  },
  Object {
    "date": "2019-04-26",
    "day": 26,
    "future": false,
    "hasDay": true,
    "hasTime": false,
    "hour": 0,
    "minute": 0,
    "month": 4,
    "past": true,
    "present": false,
    "time": "",
    "weekday": 5,
    "year": 2019,
  },
]
`;

exports[`VCalendar/util/timestamp.ts should create interval list 1`] = `
Array [
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 0,
    "minute": 30,
    "month": 2,
    "past": false,
    "present": false,
    "time": "00:30",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 0,
    "minute": 45,
    "month": 2,
    "past": false,
    "present": false,
    "time": "00:45",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:00",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 15,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:15",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 30,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:30",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 45,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:45",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 2,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "02:00",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 2,
    "minute": 15,
    "month": 2,
    "past": false,
    "present": false,
    "time": "02:15",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 2,
    "minute": 30,
    "month": 2,
    "past": false,
    "present": false,
    "time": "02:30",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 2,
    "minute": 45,
    "month": 2,
    "past": false,
    "present": false,
    "time": "02:45",
    "weekday": 5,
    "year": 2019,
  },
]
`;

exports[`VCalendar/util/timestamp.ts should create interval list 2`] = `
Array [
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 0,
    "minute": 15,
    "month": 2,
    "past": false,
    "present": false,
    "time": "00:15",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 0,
    "minute": 30,
    "month": 2,
    "past": false,
    "present": false,
    "time": "00:30",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 0,
    "minute": 45,
    "month": 2,
    "past": false,
    "present": false,
    "time": "00:45",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:00",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 15,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:15",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 30,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:30",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 1,
    "minute": 45,
    "month": 2,
    "past": false,
    "present": false,
    "time": "01:45",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 2,
    "minute": 0,
    "month": 2,
    "past": false,
    "present": false,
    "time": "02:00",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 2,
    "minute": 15,
    "month": 2,
    "past": false,
    "present": false,
    "time": "02:15",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 2,
    "minute": 30,
    "month": 2,
    "past": false,
    "present": false,
    "time": "02:30",
    "weekday": 5,
    "year": 2019,
  },
]
`;

exports[`VCalendar/util/timestamp.ts should create interval list 3`] = `
Array [
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 0,
    "minute": 10,
    "month": 2,
    "past": false,
    "present": false,
    "time": "00:10",
    "weekday": 5,
    "year": 2019,
  },
  Object {
    "date": "2019-02-08",
    "day": 8,
    "future": false,
    "hasDay": true,
    "hasTime": true,
    "hour": 0,
    "minute": 15,
    "month": 2,
    "past": false,
    "present": false,
    "time": "00:15",
    "weekday": 5,
    "year": 2019,
  },
]
`;
