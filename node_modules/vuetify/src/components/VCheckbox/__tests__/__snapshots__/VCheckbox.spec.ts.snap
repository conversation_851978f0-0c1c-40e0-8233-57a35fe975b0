// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VCheckbox.ts should be disabled 1`] = `
<input aria-checked="false"
       disabled="disabled"
       id="input-101"
       role="checkbox"
       type="checkbox"
       value
>
`;

exports[`VCheckbox.ts should be render colored checkbox 1`] = `
<div class="v-input theme--light v-input--selection-controls v-input--checkbox">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-input--selection-controls__input">
        <i aria-hidden="true"
           class="v-icon notranslate material-icons theme--light"
        >
          $checkboxOff
        </i>
        <input aria-checked="false"
               id="input-106"
               role="checkbox"
               type="checkbox"
               value
        >
        <div class="v-input--selection-controls__ripple">
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VCheckbox.ts should render themed component 1`] = `
<div class="v-input theme--light v-input--selection-controls v-input--checkbox">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-input--selection-controls__input">
        <i aria-hidden="true"
           class="v-icon notranslate material-icons theme--light"
        >
          $checkboxOff
        </i>
        <input aria-checked="false"
               id="input-96"
               role="checkbox"
               type="checkbox"
               value
        >
        <div class="v-input--selection-controls__ripple">
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VCheckbox.ts should use custom icons 1`] = `
<div class="v-input theme--light v-input--selection-controls v-input--checkbox v-input--indeterminate">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-input--selection-controls__input">
        <i aria-hidden="true"
           class="v-icon notranslate material-icons theme--light"
        >
          fizzbuzz
        </i>
        <input aria-checked="mixed"
               id="input-91"
               role="checkbox"
               type="checkbox"
               value="fizz"
        >
        <div class="v-input--selection-controls__ripple">
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VCheckbox.ts should use custom icons 2`] = `
<div class="v-input theme--light v-input--selection-controls v-input--checkbox v-input--indeterminate">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-input--selection-controls__input">
        <i aria-hidden="true"
           class="v-icon notranslate material-icons theme--light"
        >
          fizzbuzz
        </i>
        <input aria-checked="mixed"
               id="input-91"
               role="checkbox"
               type="checkbox"
               value="fizz"
        >
        <div class="v-input--selection-controls__ripple">
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VCheckbox.ts should use custom icons 3`] = `
<div class="v-input theme--light v-input--selection-controls v-input--checkbox v-input--indeterminate">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-input--selection-controls__input">
        <i aria-hidden="true"
           class="v-icon notranslate material-icons theme--light"
        >
          fizzbuzz
        </i>
        <input aria-checked="mixed"
               id="input-91"
               role="checkbox"
               type="checkbox"
               value="fizz"
        >
        <div class="v-input--selection-controls__ripple">
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;
