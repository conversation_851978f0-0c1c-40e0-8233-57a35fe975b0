// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VChip.ts should be removable 1`] = `
<span class="v-chip v-chip--no-color v-chip--removable theme--light v-size--default">
  <span class="v-chip__content">
    <button type="button"
            aria-label="$vuetify.close"
            class="v-icon notranslate v-chip__close v-icon--link v-icon--right material-icons theme--light"
            style="font-size: 18px;"
    >
      $delete
    </button>
  </span>
</span>
`;

exports[`VChip.ts should have a v-chip class 1`] = `
<span class="v-chip v-chip--no-color theme--light v-size--default">
  <span class="v-chip__content">
  </span>
</span>
`;
