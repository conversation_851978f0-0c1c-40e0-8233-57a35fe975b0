// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VChipGroup.ts should have a v-chip-group class 1`] = `
<div class="v-item-group theme--light v-slide-group v-chip-group">
  <div class="v-slide-group__prev v-slide-group__prev--disabled">
  </div>
  <div class="v-slide-group__wrapper">
    <div class="v-slide-group__content">
    </div>
  </div>
  <div class="v-slide-group__next v-slide-group__next--disabled">
  </div>
</div>
`;

exports[`VChipGroup.ts should render column 1`] = `
<div class="v-item-group theme--light v-slide-group v-chip-group v-chip-group--column">
  <div class="v-slide-group__prev v-slide-group__prev--disabled">
  </div>
  <div class="v-slide-group__wrapper">
    <div class="v-slide-group__content">
    </div>
  </div>
  <div class="v-slide-group__next v-slide-group__next--disabled">
  </div>
</div>
`;

exports[`VChipGroup.ts should switch to column 1`] = `
<div class="v-item-group theme--light v-slide-group v-chip-group">
  <div class="v-slide-group__prev v-slide-group__prev--disabled">
  </div>
  <div class="v-slide-group__wrapper">
    <div class="v-slide-group__content">
    </div>
  </div>
  <div class="v-slide-group__next v-slide-group__next--disabled">
  </div>
</div>
`;

exports[`VChipGroup.ts should switch to column 2`] = `
<div class="v-item-group theme--light v-slide-group v-chip-group v-chip-group--column">
  <div class="v-slide-group__prev v-slide-group__prev--disabled">
  </div>
  <div class="v-slide-group__wrapper">
    <div class="v-slide-group__content">
    </div>
  </div>
  <div class="v-slide-group__next v-slide-group__next--disabled">
  </div>
</div>
`;
