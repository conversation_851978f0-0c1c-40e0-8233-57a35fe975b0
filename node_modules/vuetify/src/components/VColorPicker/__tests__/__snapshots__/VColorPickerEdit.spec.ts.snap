// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VColorPickerEdit.ts should change mode 1`] = `
<div class="v-color-picker__edit">
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
    >
    <span>
      R
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
    >
    <span>
      G
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
    >
    <span>
      B
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      A
    </span>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--small"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        $unfold
      </i>
    </span>
  </button>
</div>
`;

exports[`VColorPickerEdit.ts should change mode 2`] = `
<div class="v-color-picker__edit">
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="360"
           step="1"
    >
    <span>
      H
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      S
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      L
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      A
    </span>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--small"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        $unfold
      </i>
    </span>
  </button>
</div>
`;

exports[`VColorPickerEdit.ts should change mode 3`] = `
<div class="v-color-picker__edit">
  <div class="v-color-picker__input">
    <input maxlength="9">
    <span>
      HEX
    </span>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--small"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        $unfold
      </i>
    </span>
  </button>
</div>
`;

exports[`VColorPickerEdit.ts should change mode 4`] = `
<div class="v-color-picker__edit">
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="360"
           step="1"
    >
    <span>
      H
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      S
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      L
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      A
    </span>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--small"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        $unfold
      </i>
    </span>
  </button>
</div>
`;

exports[`VColorPickerEdit.ts should hide mode switch button 1`] = `
<div class="v-color-picker__edit">
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
    >
    <span>
      R
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
    >
    <span>
      G
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
    >
    <span>
      B
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
    >
    <span>
      A
    </span>
  </div>
</div>
`;

exports[`VColorPickerEdit.ts should render with disabled 1`] = `
<div class="v-color-picker__edit">
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
           disabled="disabled"
    >
    <span>
      R
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
           disabled="disabled"
    >
    <span>
      G
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="255"
           step="1"
           disabled="disabled"
    >
    <span>
      B
    </span>
  </div>
  <div class="v-color-picker__input">
    <input type="number"
           min="0"
           max="1"
           step="0.01"
           disabled="disabled"
    >
    <span>
      A
    </span>
  </div>
  <button type="button"
          disabled="disabled"
          class="v-btn v-btn--disabled v-btn--icon v-btn--round theme--light v-size--small"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        $unfold
      </i>
    </span>
  </button>
</div>
`;
