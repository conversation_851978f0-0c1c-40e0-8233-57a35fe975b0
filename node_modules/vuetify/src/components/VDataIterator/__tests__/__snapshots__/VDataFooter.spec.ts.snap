// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VDataFooter.ts should disable last page button if no items 1`] = `
<div class="v-data-footer">
  <div class="v-data-footer__select">
    Items per page:
    <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
      <div class="v-input__control">
        <div role="button"
             aria-haspopup="listbox"
             aria-expanded="false"
             aria-owns="list-55"
             class="v-input__slot"
        >
          <div class="v-select__slot">
            <div class="v-select__selections">
              <div class="v-select__selection v-select__selection--comma">
                10
              </div>
              <input aria-label="Items per page:"
                     id="input-55"
                     readonly="readonly"
                     type="text"
                     aria-readonly="false"
                     autocomplete="off"
              >
            </div>
            <div class="v-input__append-inner">
              <div class="v-input__icon v-input__icon--append">
                <i aria-hidden="true"
                   class="v-icon notranslate mdi mdi-menu-down theme--light"
                >
                </i>
              </div>
            </div>
            <input type="hidden"
                   value="10"
            >
          </div>
          <div class="v-menu">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="v-data-footer__pagination">
    –
  </div>
  <div class="v-data-footer__icons-before">
    <button type="button"
            disabled="disabled"
            class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="First page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-page-first theme--light"
        >
        </i>
      </span>
    </button>
    <button type="button"
            disabled="disabled"
            class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Previous page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </span>
    </button>
  </div>
  <div class="v-data-footer__icons-after">
    <button type="button"
            disabled="disabled"
            class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Next page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </span>
    </button>
    <button type="button"
            disabled="disabled"
            class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Last page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-page-last theme--light"
        >
        </i>
      </span>
    </button>
  </div>
</div>
`;

exports[`VDataFooter.ts should render first & last icons with showFirstLastPage 1`] = `
<div class="v-data-footer">
  <div class="v-data-footer__select">
    Items per page:
    <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
      <div class="v-input__control">
        <div role="button"
             aria-haspopup="listbox"
             aria-expanded="false"
             aria-owns="list-24"
             class="v-input__slot"
        >
          <div class="v-select__slot">
            <div class="v-select__selections">
              <div class="v-select__selection v-select__selection--comma">
                10
              </div>
              <input aria-label="Items per page:"
                     id="input-24"
                     readonly="readonly"
                     type="text"
                     aria-readonly="false"
                     autocomplete="off"
              >
            </div>
            <div class="v-input__append-inner">
              <div class="v-input__icon v-input__icon--append">
                <i aria-hidden="true"
                   class="v-icon notranslate mdi mdi-menu-down theme--light"
                >
                </i>
              </div>
            </div>
            <input type="hidden"
                   value="10"
            >
          </div>
          <div class="v-menu">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="v-data-footer__pagination">
    2-10 of 100
  </div>
  <div class="v-data-footer__icons-before">
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="First page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-page-first theme--light"
        >
        </i>
      </span>
    </button>
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Previous page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </span>
    </button>
  </div>
  <div class="v-data-footer__icons-after">
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Next page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </span>
    </button>
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Last page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-page-last theme--light"
        >
        </i>
      </span>
    </button>
  </div>
</div>
`;

exports[`VDataFooter.ts should render in RTL mode 1`] = `
<div class="v-data-footer">
  <div class="v-data-footer__select">
    Items per page:
    <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
      <div class="v-input__control">
        <div role="button"
             aria-haspopup="listbox"
             aria-expanded="false"
             aria-owns="list-11"
             class="v-input__slot"
        >
          <div class="v-select__slot">
            <div class="v-select__selections">
              <div class="v-select__selection v-select__selection--comma">
                10
              </div>
              <input aria-label="Items per page:"
                     id="input-11"
                     readonly="readonly"
                     type="text"
                     aria-readonly="false"
                     autocomplete="off"
              >
            </div>
            <div class="v-input__append-inner">
              <div class="v-input__icon v-input__icon--append">
                <i aria-hidden="true"
                   class="v-icon notranslate mdi mdi-menu-down theme--light"
                >
                </i>
              </div>
            </div>
            <input type="hidden"
                   value="10"
            >
          </div>
          <div class="v-menu">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="v-data-footer__pagination">
    2-10 of 100
  </div>
  <div class="v-data-footer__icons-before">
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="First page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-page-last theme--light"
        >
        </i>
      </span>
    </button>
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Previous page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </span>
    </button>
  </div>
  <div class="v-data-footer__icons-after">
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Next page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </span>
    </button>
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Last page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-page-first theme--light"
        >
        </i>
      </span>
    </button>
  </div>
</div>
`;

exports[`VDataFooter.ts should render with custom itemsPerPage 1`] = `
<div class="v-data-footer">
  <div class="v-data-footer__select">
    Items per page:
    <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
      <div class="v-input__control">
        <div role="button"
             aria-haspopup="listbox"
             aria-expanded="false"
             aria-owns="list-2"
             class="v-input__slot"
        >
          <div class="v-select__slot">
            <div class="v-select__selections">
              <div class="v-select__selection v-select__selection--comma">
                100
              </div>
              <input aria-label="Items per page:"
                     id="input-2"
                     readonly="readonly"
                     type="text"
                     aria-readonly="false"
                     autocomplete="off"
              >
            </div>
            <div class="v-input__append-inner">
              <div class="v-input__icon v-input__icon--append">
                <i aria-hidden="true"
                   class="v-icon notranslate mdi mdi-menu-down theme--light"
                >
                </i>
              </div>
            </div>
            <input type="hidden"
                   value="100"
            >
          </div>
          <div class="v-menu">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="v-data-footer__pagination">
    2-10 of 100
  </div>
  <div class="v-data-footer__icons-before">
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Previous page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </span>
    </button>
  </div>
  <div class="v-data-footer__icons-after">
    <button type="button"
            disabled="disabled"
            class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Next page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </span>
    </button>
  </div>
</div>
`;

exports[`VDataFooter.ts should show current page if has showCurrentPage 1`] = `
<div class="v-data-footer">
  <div class="v-data-footer__select">
    Items per page:
    <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
      <div class="v-input__control">
        <div role="button"
             aria-haspopup="listbox"
             aria-expanded="false"
             aria-owns="list-46"
             class="v-input__slot"
        >
          <div class="v-select__slot">
            <div class="v-select__selections">
              <div class="v-select__selection v-select__selection--comma">
                10
              </div>
              <input aria-label="Items per page:"
                     id="input-46"
                     readonly="readonly"
                     type="text"
                     aria-readonly="false"
                     autocomplete="off"
              >
            </div>
            <div class="v-input__append-inner">
              <div class="v-input__icon v-input__icon--append">
                <i aria-hidden="true"
                   class="v-icon notranslate mdi mdi-menu-down theme--light"
                >
                </i>
              </div>
            </div>
            <input type="hidden"
                   value="10"
            >
          </div>
          <div class="v-menu">
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="v-data-footer__pagination">
    2-10 of 100
  </div>
  <div class="v-data-footer__icons-before">
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Previous page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </span>
    </button>
  </div>
  <span>
    4
  </span>
  <div class="v-data-footer__icons-after">
    <button type="button"
            class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
            aria-label="Next page"
    >
      <span class="v-btn__content">
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </span>
    </button>
  </div>
</div>
`;
