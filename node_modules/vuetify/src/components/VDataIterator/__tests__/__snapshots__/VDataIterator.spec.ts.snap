// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VDataIterator.ts should hide footer 1`] = `
<div class="v-data-iterator">
  <div>
    No data available
  </div>
</div>
`;

exports[`VDataIterator.ts should render and match snapshot 1`] = `
<div class="v-data-iterator">
  <div>
    No data available
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Items per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-4"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Items per page:"
                       id="input-4"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate mdi mdi-menu-down theme--light"
                  >
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-left theme--light"
          >
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-right theme--light"
          >
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataIterator.ts should render and match snapshot with data 1`] = `
<div class="v-data-iterator">
  <div>
    foo
  </div>
  <div>
    bar
  </div>
  <div>
    baz
  </div>
  <div>
    qux
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Items per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-16"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Items per page:"
                       id="input-16"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate mdi mdi-menu-down theme--light"
                  >
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-4 of 4
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-left theme--light"
          >
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-right theme--light"
          >
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataIterator.ts should render valid no-data, loading and no-results states 1`] = `
<div class="v-data-iterator">
  <div>
    No data available
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Items per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-27"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Items per page:"
                       id="input-27"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate mdi mdi-menu-down theme--light"
                  >
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-left theme--light"
          >
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-right theme--light"
          >
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataIterator.ts should render valid no-data, loading and no-results states 2`] = `
<div class="v-data-iterator">
  <div>
    Loading items...
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Items per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-27"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Items per page:"
                       id="input-27"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate mdi mdi-menu-down theme--light"
                  >
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-left theme--light"
          >
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-right theme--light"
          >
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataIterator.ts should render valid no-data, loading and no-results states 3`] = `
<div class="v-data-iterator">
  <div>
    No matching records found
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Items per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-27"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Items per page:"
                       id="input-27"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate mdi mdi-menu-down theme--light"
                  >
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-left theme--light"
          >
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate mdi mdi-chevron-right theme--light"
          >
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;
