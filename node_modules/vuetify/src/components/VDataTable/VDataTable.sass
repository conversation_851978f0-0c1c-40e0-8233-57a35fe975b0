// Imports
@import './_variables.scss'

// Theme
+theme(v-data-table) using ($material)
  tbody
    tr
      &.v-data-table__selected
        background: map-deep-get($material, 'table', 'active')

  .v-row-group
    &__header, &__summary
      background: map-deep-get($material, 'table', 'group')

  .v-data-footer
    border-top: thin solid map-get($material, 'dividers')

  .v-data-table__empty-wrapper
    color: map-deep-get($material, 'text', 'disabled')

.v-data-table
  border-radius: $data-table-border-radius

  &--mobile
    > .v-data-table__wrapper tbody
      display: flex
      flex-direction: column

  > .v-data-table__wrapper
    tbody
      tr
        &.v-data-table__expanded
          border-bottom: 0

        &.v-data-table__expanded__content
          box-shadow: $data-table-expanded-content-box-shadow

        &:first-child
          &:hover
            td
              &:first-child
                border-top-left-radius: $data-table-border-radius

              &:last-child
                border-top-right-radius: $data-table-border-radius

        &:last-child
          &:hover
            td
              &:first-child
                border-bottom-left-radius: $data-table-border-radius

              &:last-child
                border-bottom-right-radius: $data-table-border-radius

    .v-data-table__mobile-table-row
      display: initial

    .v-data-table__mobile-row
      height: initial
      min-height: $data-table-mobile-row-min-height

.v-data-table__empty-wrapper
  text-align: center

.v-data-table__mobile-row
  align-items: center
  display: flex
  justify-content: space-between

  &__header
    font-weight: $data-table-mobile-row-header-font-weight

    +ltr()
      padding-right: 16px

    +rtl()
      padding-left: 16px

  &__cell
    +ltr()
      text-align: right

    +rtl()
      text-align: left

.v-row-group
  &__header, &__summary

    td
      height: $data-table-row-group-children-td-height

.v-data-table__expand-icon
  user-select: none
  cursor: pointer

  &--active
    transform: rotate(-180deg)
