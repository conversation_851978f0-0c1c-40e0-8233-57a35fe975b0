// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MobileRow should render non-string values 1`] = `
<tr class="v-data-table__mobile-table-row">
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
    </div>
    <div class="v-data-table__mobile-row__cell">
      string
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
    </div>
    <div class="v-data-table__mobile-row__cell">
      12.34
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
    </div>
    <div class="v-data-table__mobile-row__cell">
      1,2
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
    </div>
    <div class="v-data-table__mobile-row__cell">
      false
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
    </div>
    <div class="v-data-table__mobile-row__cell">
      [object Object]
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
    </div>
    <div class="v-data-table__mobile-row__cell">
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
    </div>
    <div class="v-data-table__mobile-row__cell">
    </div>
  </td>
</tr>
`;

exports[`MobileRow should render with regular slots 1`] = `
<tr class="v-data-table__mobile-table-row">
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
      Petrol
    </div>
    <div class="v-data-table__mobile-row__cell">
      <p class="test">
        $0.68
      </p>
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
      Diesel
    </div>
    <div class="v-data-table__mobile-row__cell">
      <p class="test">
        $0.65
      </p>
    </div>
  </td>
</tr>
`;

exports[`MobileRow should render with scoped slots 1`] = `
<tr class="v-data-table__mobile-table-row">
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
      Petrol
    </div>
    <div class="v-data-table__mobile-row__cell">
      <p class="test petrol">
        0.68
      </p>
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
      Diesel
    </div>
    <div class="v-data-table__mobile-row__cell">
      <p class="test diesel">
        0.65
      </p>
    </div>
  </td>
</tr>
`;

exports[`MobileRow should render without header when hideDefaultHeader: true 1`] = `
<tr class="v-data-table__mobile-table-row">
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__cell">
      0.68
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__cell">
      0.65
    </div>
  </td>
</tr>
`;

exports[`MobileRow should render without slots 1`] = `
<tr class="v-data-table__mobile-table-row">
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
      Petrol
    </div>
    <div class="v-data-table__mobile-row__cell">
      0.68
    </div>
  </td>
  <td class="v-data-table__mobile-row">
    <div class="v-data-table__mobile-row__header">
      Diesel
    </div>
    <div class="v-data-table__mobile-row__cell">
      0.65
    </div>
  </td>
</tr>
`;
