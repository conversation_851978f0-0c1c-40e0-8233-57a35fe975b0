// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VDataTable.ts should apply class from item to rows 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-699"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-699"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-699"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row test">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row test second">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row test">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-703"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-703"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should apply class function to rows 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-683"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-683"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-683"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row first-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row first-class second-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row second-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row first-class second-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-687"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-687"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should apply class list to rows 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-651"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-651"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-651"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row my-class my-other-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-class my-other-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-class my-other-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-class my-other-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-class my-other-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-655"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-655"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should apply class unique to rows 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-667"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-667"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-667"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row my-unique-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-unique-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-unique-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-unique-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row my-unique-class">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-671"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-671"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should change page if item count decreases below page start 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-483"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-483"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-483"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__pagination">
      3-4 of 4
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should default to first option in itemsPerPageOptions if it does not include itemsPerPage 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-435"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-435"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-435"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-439"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  6
                </div>
                <input aria-label="Rows per page:"
                       id="input-439"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="6"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-6 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should handle object when checking if it should default to first option in itemsPerPageOptions 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-451"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-451"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-451"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              392
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              98
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              2%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              408
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              87
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6.5
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              45%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              452
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              25
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              51
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              22%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              518
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              26
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              65
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-455"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  All
                </div>
                <input aria-label="Rows per page:"
                       id="input-455"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="-1"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-10 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should hide group button when column is not groupable 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-747"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-747"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-747"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              392
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              98
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              2%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              408
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              87
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6.5
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              45%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              452
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              25
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              51
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              22%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              518
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              26
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              65
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-751"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-751"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-10 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should limit page to current page count if not using server-items-length 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-419"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-419"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-419"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              392
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              98
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              2%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              408
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              87
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6.5
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              45%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              452
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              25
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              51
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              22%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              518
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              26
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              65
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-423"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-423"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      6-10 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should not limit page to current item count when using server-items-length 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-371"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-371"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-371"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__empty-wrapper">
          <td>
            No data available
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-375"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-375"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should not limit page to current item count when using server-items-length 2`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-371"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-371"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-371"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              392
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              98
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              2%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              408
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              87
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6.5
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              45%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              452
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              25
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              51
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              22%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              518
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              26
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              65
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-375"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-375"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      6-10 of 20
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should not search column with filterable set to false 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-387"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-387"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-387"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              392
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              98
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              2%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              408
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              87
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6.5
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              45%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              452
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              25
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              51
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              22%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              518
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              26
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              65
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-391"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-391"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-10 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should not search column with filterable set to false 2`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-387"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-387"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-387"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__empty-wrapper">
          <td class>
            No matching records found
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-391"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-391"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should not search column with filterable set to false and has filter function 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-403"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-403"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-403"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              408
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              87
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6.5
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              45%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              452
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              25
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              51
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              22%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              518
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              26
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              65
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-407"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-407"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-3 of 3
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should not search column with filterable set to false and has filter function 2`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-403"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-403"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-403"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              392
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              98
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              2%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              408
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              87
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6.5
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              45%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              452
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              25
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              51
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              22%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              518
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              26
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              65
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-407"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-407"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-10 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should not select item that is not selectable 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div width="1px"
                   class="v-data-table-header-mobile__select"
              >
                <div class="v-data-table__checkbox v-simple-checkbox">
                  <div class="v-input--selection-controls__input">
                    <i aria-hidden="true"
                       class="v-icon notranslate material-icons theme--light"
                    >
                      $checkboxOff
                    </i>
                    <div class="v-input--selection-controls__ripple">
                    </div>
                  </div>
                </div>
              </div>
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-515"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-515"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-515"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <div class="v-data-table__checkbox v-simple-checkbox v-simple-checkbox--disabled">
                <div class="v-input--selection-controls__input">
                  <i aria-hidden="true"
                     class="v-icon notranslate v-icon--disabled material-icons theme--light"
                  >
                    $checkboxOff
                  </i>
                </div>
              </div>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <div class="v-data-table__checkbox v-simple-checkbox">
                <div class="v-input--selection-controls__input">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $checkboxOff
                  </i>
                  <div class="v-input--selection-controls__ripple">
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-521"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-521"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-2 of 2
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should pass kebab-case footer props correctly 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__empty-wrapper">
          <td>
            No data available
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Foo:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-315"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Foo:"
                       id="input-315"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__empty-wrapper">
          <td>
            No data available
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-6"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-6"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render footer.page-text slot content 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-359"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-359"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      <div>
        foo 1 bar 1
      </div>
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render footer.prepend slot content 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div>
      footer.prepend slot content
    </div>
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-345"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-345"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-1 of 1
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render item slot when using group-by function 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-577"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-577"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-577"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Dessert (100g serving): Frozen Yogurt
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <div>
          scoped
        </div>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Dessert (100g serving): Ice cream sandwich
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <div>
          scoped
        </div>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-589"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-589"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-2 of 2
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render loading state 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
        </tr>
      </thead>
      <thead>
        <tr class="v-data-table__progress">
          <th class="column">
            <div role="progressbar"
                 aria-valuemin="0"
                 aria-valuemax="100"
                 class="v-progress-linear v-progress-linear--absolute v-progress-linear--reverse v-progress-linear--visible theme--light"
                 style="height: 4px;"
            >
              <div class="v-progress-linear__background primary"
                   style="opacity: 0.3; right: 0%; width: 100%;"
              >
              </div>
              <div class="v-progress-linear__buffer">
              </div>
              <div class="v-progress-linear__indeterminate v-progress-linear__indeterminate--active">
                <div class="v-progress-linear__indeterminate long primary">
                </div>
                <div class="v-progress-linear__indeterminate short primary">
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__empty-wrapper">
          <td>
            Loading items...
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-238"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-238"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render loading state 2`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-250"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-250"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-250"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <thead>
        <tr class="v-data-table__progress">
          <th class="column">
            <div class="progress">
              50%
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__empty-wrapper">
          <td>
            Loading items...
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-254"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-254"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      –
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with body slot 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-35"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-35"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-35"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <div>
        5
      </div>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-39"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-39"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with data 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-18"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-18"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-18"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-22"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-22"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with foot slot 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-52"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-52"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-52"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
      <tfoot>
        5
      </tfoot>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-56"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-56"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with group scoped slot 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-219"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-219"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-219"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <div>
          {"group":0,"options":{"page":1,"itemsPerPage":5,"sortBy":[],"sortDesc":[false],"groupBy":["protein"],"groupDesc":[false],"mustSort":false,"multiSort":false},"isMobile":true,"items":[{"name":"Jelly bean","calories":375,"fat":0,"carbs":94,"protein":0,"iron":"0%"},{"name":"Lollipop","calories":392,"fat":0.2,"carbs":98,"protein":0,"iron":"2%"}],"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Iron (%)","value":"iron"}]}
        </div>
        <div>
          {"group":3.9,"options":{"page":1,"itemsPerPage":5,"sortBy":[],"sortDesc":[false],"groupBy":["protein"],"groupDesc":[false],"mustSort":false,"multiSort":false},"isMobile":true,"items":[{"name":"Gingerbread","calories":356,"fat":16,"carbs":49,"protein":3.9,"iron":"16%"}],"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Iron (%)","value":"iron"}]}
        </div>
        <div>
          {"group":4,"options":{"page":1,"itemsPerPage":5,"sortBy":[],"sortDesc":[false],"groupBy":["protein"],"groupDesc":[false],"mustSort":false,"multiSort":false},"isMobile":true,"items":[{"name":"Frozen Yogurt","calories":159,"fat":6,"carbs":24,"protein":4,"iron":"1%","class":"test"}],"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Iron (%)","value":"iron"}]}
        </div>
        <div>
          {"group":4.3,"options":{"page":1,"itemsPerPage":5,"sortBy":[],"sortDesc":[false],"groupBy":["protein"],"groupDesc":[false],"mustSort":false,"multiSort":false},"isMobile":true,"items":[{"name":"Ice cream sandwich","calories":237,"fat":9,"carbs":37,"protein":4.3,"iron":"1%","class":["test","second"]}],"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Iron (%)","value":"iron"}]}
        </div>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-223"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-223"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with group.summary scoped slot 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-131"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-131"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-131"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Calories: 159
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__summary">
          <div>
            summary
          </div>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Calories: 237
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__summary">
          <div>
            summary
          </div>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Calories: 262
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__summary">
          <div>
            summary
          </div>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Calories: 305
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__summary">
          <div>
            summary
          </div>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Calories: 356
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__summary">
          <div>
            summary
          </div>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-155"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-155"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with grouped rows 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-185"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-185"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-185"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Protein (g): 0
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              375
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              94
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              392
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              0.2
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              98
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              2%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Protein (g): 3.9
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Protein (g): 4
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Protein (g): 4.3
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-205"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-205"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with item scoped slot 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-169"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-169"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-169"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <div>
          {"item":{"name":"Frozen Yogurt","calories":159,"fat":6,"carbs":24,"protein":4,"iron":"1%","class":"test"},"index":0,"isSelected":false,"isExpanded":false,"isMobile":true,"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Protein (g)","value":"protein"},{"text":"Iron (%)","value":"iron"}]}
        </div>
        <div>
          {"item":{"name":"Ice cream sandwich","calories":237,"fat":9,"carbs":37,"protein":4.3,"iron":"1%","class":["test","second"]},"index":1,"isSelected":false,"isExpanded":false,"isMobile":true,"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Protein (g)","value":"protein"},{"text":"Iron (%)","value":"iron"}]}
        </div>
        <div>
          {"item":{"name":"Eclair","calories":262,"fat":16,"carbs":23,"protein":6,"iron":"7%","class":{"test":true,"second":false}},"index":2,"isSelected":false,"isExpanded":false,"isMobile":true,"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Protein (g)","value":"protein"},{"text":"Iron (%)","value":"iron"}]}
        </div>
        <div>
          {"item":{"name":"Cupcake","calories":305,"fat":3.7,"carbs":67,"protein":4.3,"iron":"8%"},"index":3,"isSelected":false,"isExpanded":false,"isMobile":true,"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Protein (g)","value":"protein"},{"text":"Iron (%)","value":"iron"}]}
        </div>
        <div>
          {"item":{"name":"Gingerbread","calories":356,"fat":16,"carbs":49,"protein":3.9,"iron":"16%"},"index":4,"isSelected":false,"isExpanded":false,"isMobile":true,"headers":[{"text":"Dessert (100g serving)","align":"left","sortable":false,"value":"name"},{"text":"Calories","value":"calories"},{"text":"Fat (g)","value":"fat"},{"text":"Carbs (g)","value":"carbs"},{"text":"Protein (g)","value":"protein"},{"text":"Iron (%)","value":"iron"}]}
        </div>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-173"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-173"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with item.expanded scoped slot 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-113"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-113"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-113"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row v-data-table__expanded v-data-table__expanded__row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__expanded v-data-table__expanded__content">
          <div>
            expanded
          </div>
        </tr>
        <tr class="v-data-table__mobile-table-row v-data-table__expanded v-data-table__expanded__row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__expanded v-data-table__expanded__content">
          <div>
            expanded
          </div>
        </tr>
        <tr class="v-data-table__mobile-table-row v-data-table__expanded v-data-table__expanded__row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__expanded v-data-table__expanded__content">
          <div>
            expanded
          </div>
        </tr>
        <tr class="v-data-table__mobile-table-row v-data-table__expanded v-data-table__expanded__row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__expanded v-data-table__expanded__content">
          <div>
            expanded
          </div>
        </tr>
        <tr class="v-data-table__mobile-table-row v-data-table__expanded v-data-table__expanded__row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__expanded v-data-table__expanded__content">
          <div>
            expanded
          </div>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-117"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-117"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with showExpand 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-68"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-68"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-68"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-77"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-77"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with showExpand 2`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-68"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-68"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-68"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light v-data-table__expand-icon--active"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <button type="button"
                      class="v-icon notranslate v-data-table__expand-icon v-icon--link material-icons theme--light"
              >
                $expand
              </button>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-77"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-77"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should render with showSelect 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div width="1px"
                   class="v-data-table-header-mobile__select"
              >
                <div class="v-data-table__checkbox v-simple-checkbox">
                  <div class="v-input--selection-controls__input">
                    <i aria-hidden="true"
                       class="v-icon notranslate material-icons theme--light"
                    >
                      $checkboxOff
                    </i>
                    <div class="v-input--selection-controls__ripple">
                    </div>
                  </div>
                </div>
              </div>
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-90"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-90"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-90"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <div class="v-data-table__checkbox v-simple-checkbox">
                <div class="v-input--selection-controls__input">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $checkboxOff
                  </i>
                  <div class="v-input--selection-controls__ripple">
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <div class="v-data-table__checkbox v-simple-checkbox">
                <div class="v-input--selection-controls__input">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $checkboxOff
                  </i>
                  <div class="v-input--selection-controls__ripple">
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <div class="v-data-table__checkbox v-simple-checkbox">
                <div class="v-input--selection-controls__input">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $checkboxOff
                  </i>
                  <div class="v-input--selection-controls__ripple">
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <div class="v-data-table__checkbox v-simple-checkbox">
                <div class="v-input--selection-controls__input">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $checkboxOff
                  </i>
                  <div class="v-input--selection-controls__ripple">
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
            </div>
            <div class="v-data-table__mobile-row__cell">
              <div class="v-data-table__checkbox v-simple-checkbox">
                <div class="v-input--selection-controls__input">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $checkboxOff
                  </i>
                  <div class="v-input--selection-controls__ripple">
                  </div>
                </div>
              </div>
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-99"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  5
                </div>
                <input aria-label="Rows per page:"
                       id="input-99"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="5"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              class="v-btn v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should respect mustSort property on options 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-731"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-731"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-731"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Jelly bean
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Lollipop
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Honeycomb
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Donut
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              KitKat
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-735"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-735"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-10 of 10
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should search group-by column 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-552"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-552"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-552"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Name: Assistance
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              ID
            </div>
            <div class="v-data-table__mobile-row__cell">
              1
            </div>
          </td>
        </tr>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Name: Candidat
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              ID
            </div>
            <div class="v-data-table__mobile-row__cell">
              2
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-564"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-564"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-2 of 2
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should search group-by column 2`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-552"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-552"
                             class="v-label theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <input id="input-552"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden">
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-row-group__header">
          <td class="text-start">
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $minus
                </i>
              </span>
            </button>
            Name: Candidat
            <button type="button"
                    class="ma-0 v-btn v-btn--icon v-btn--round theme--light v-size--small"
            >
              <span class="v-btn__content">
                <i aria-hidden="true"
                   class="v-icon notranslate material-icons theme--light"
                >
                  $close
                </i>
              </span>
            </button>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              ID
            </div>
            <div class="v-data-table__mobile-row__cell">
              2
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-564"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-564"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-1 of 1
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should show correct aria-labels when sorting 1`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-633"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-633"
                             class="v-label v-label--active theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <span class="sortable v-chip v-chip--clickable v-chip--no-color theme--light v-size--default">
                          <span class="v-chip__content">
                            Calories
                            <div class="v-chip__close sortable active desc">
                              <i aria-hidden="true"
                                 class="v-icon notranslate v-data-table-header__icon material-icons theme--light"
                                 style="font-size: 18px;"
                              >
                                $sort
                              </i>
                            </div>
                          </span>
                        </span>
                        <input id="input-633"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden"
                             value="calories"
                      >
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-639"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-639"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 5
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`VDataTable.ts should show correct aria-labels when sorting 2`] = `
<div class="v-data-table v-data-table--has-bottom theme--light v-data-table--mobile">
  <div class="v-data-table__wrapper">
    <table>
      <colgroup>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
        <col class>
      </colgroup>
      <thead class="v-data-table-header v-data-table-header-mobile">
        <tr>
          <th>
            <div class="v-data-table-header-mobile__wrapper">
              <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
                <div class="v-input__control">
                  <div role="button"
                       aria-haspopup="listbox"
                       aria-expanded="false"
                       aria-owns="list-633"
                       class="v-input__slot"
                  >
                    <div class="v-select__slot">
                      <label for="input-633"
                             class="v-label v-label--active theme--light"
                             style="left: 0px; position: absolute;"
                      >
                        Sort by
                      </label>
                      <div class="v-select__selections">
                        <span class="sortable v-chip v-chip--clickable v-chip--no-color theme--light v-size--default">
                          <span class="v-chip__content">
                            Calories
                            <div class="v-chip__close sortable active desc">
                              <i aria-hidden="true"
                                 class="v-icon notranslate v-data-table-header__icon material-icons theme--light"
                                 style="font-size: 18px;"
                              >
                                $sort
                              </i>
                            </div>
                          </span>
                        </span>
                        <input id="input-633"
                               readonly="readonly"
                               type="text"
                               aria-readonly="false"
                               autocomplete="off"
                        >
                      </div>
                      <div class="v-input__append-inner">
                        <div class="v-input__icon v-input__icon--append">
                          <i aria-hidden="true"
                             class="v-icon notranslate material-icons theme--light"
                          >
                            $dropdown
                          </i>
                        </div>
                      </div>
                      <input type="hidden"
                             value="calories"
                      >
                    </div>
                    <div class="v-menu">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Gingerbread
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              356
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              49
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Cupcake
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              305
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              3.7
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              67
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              8%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Eclair
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              262
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              16
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              23
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              7%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Ice cream sandwich
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              237
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              9
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              37
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4.3
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
        <tr class="v-data-table__mobile-table-row">
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Dessert (100g serving)
            </div>
            <div class="v-data-table__mobile-row__cell">
              Frozen Yogurt
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Calories
            </div>
            <div class="v-data-table__mobile-row__cell">
              159
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Fat (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              6
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Carbs (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              24
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Protein (g)
            </div>
            <div class="v-data-table__mobile-row__cell">
              4
            </div>
          </td>
          <td class="v-data-table__mobile-row">
            <div class="v-data-table__mobile-row__header">
              Iron (%)
            </div>
            <div class="v-data-table__mobile-row__cell">
              1%
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="v-data-footer">
    <div class="v-data-footer__select">
      Rows per page:
      <div class="v-input v-input--hide-details v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
        <div class="v-input__control">
          <div role="button"
               aria-haspopup="listbox"
               aria-expanded="false"
               aria-owns="list-639"
               class="v-input__slot"
          >
            <div class="v-select__slot">
              <div class="v-select__selections">
                <div class="v-select__selection v-select__selection--comma">
                  10
                </div>
                <input aria-label="Rows per page:"
                       id="input-639"
                       readonly="readonly"
                       type="text"
                       aria-readonly="false"
                       autocomplete="off"
                >
              </div>
              <div class="v-input__append-inner">
                <div class="v-input__icon v-input__icon--append">
                  <i aria-hidden="true"
                     class="v-icon notranslate material-icons theme--light"
                  >
                    $dropdown
                  </i>
                </div>
              </div>
              <input type="hidden"
                     value="10"
              >
            </div>
            <div class="v-menu">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-data-footer__pagination">
      1-5 of 5
    </div>
    <div class="v-data-footer__icons-before">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Previous page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $prev
          </i>
        </span>
      </button>
    </div>
    <div class="v-data-footer__icons-after">
      <button type="button"
              disabled="disabled"
              class="v-btn v-btn--disabled v-btn--icon v-btn--round v-btn--text theme--light v-size--default"
              aria-label="Next page"
      >
        <span class="v-btn__content">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            $next
          </i>
        </span>
      </button>
    </div>
  </div>
</div>
`;
