// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VEditDialog.ts should render 1`] = `
<div class="v-menu v-small-dialog theme--light">
  <div class="v-small-dialog__activator">
    <span class="v-small-dialog__activator__content">
    </span>
  </div>
</div>
`;

exports[`VEditDialog.ts should render actions 1`] = `
<div class="v-small-dialog__actions">
  <button type="button"
          class="v-btn v-btn--text theme--light v-size--default primary--text"
  >
    <span class="v-btn__content">
      Cancel
    </span>
  </button>
  <button type="button"
          class="v-btn v-btn--text theme--light v-size--default primary--text"
  >
    <span class="v-btn__content">
      Save
    </span>
  </button>
</div>
`;

exports[`VEditDialog.ts should render button 1`] = `
<button type="button"
        class="v-btn v-btn--text theme--light v-size--default primary--text"
>
  <span class="v-btn__content">
    test
  </span>
</button>
`;

exports[`VEditDialog.ts should render custom button texts 1`] = `
<div class="v-menu v-small-dialog theme--light">
  <div class="v-small-dialog__activator">
    <span class="v-small-dialog__activator__content">
    </span>
  </div>
</div>
`;
