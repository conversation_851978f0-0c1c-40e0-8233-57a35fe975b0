// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VDatePickerDateTable.ts should match snapshot with first day of week 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
        <th>
          S
        </th>
        <th>
          M
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render component and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render component and match snapshot for multiple selection 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light"
     multiple="multiple"
     selecteddates="2005-11-03,2005-11-05,2005-11-08"
>
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render component with events (array) and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
            <div class="v-date-picker-table__events">
              <div class="red">
              </div>
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render component with events (function) and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
            <div class="v-date-picker-table__events">
              <div class="red">
              </div>
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render component with events colored by function and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
            <div class="v-date-picker-table__events">
              <div class="red">
              </div>
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render component with events colored by object and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
            <div class="v-date-picker-table__events">
              <div class="red">
              </div>
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
            <div class="v-date-picker-table__events">
              <div class="blue lighten-1">
              </div>
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render component with showWeek and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
        <th>
          S
        </th>
        <th>
          M
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <small class="v-date-picker-table--date__week">
            04
          </small>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <small class="v-date-picker-table--date__week">
            05
          </small>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <small class="v-date-picker-table--date__week">
            06
          </small>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <small class="v-date-picker-table--date__week">
            07
          </small>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <small class="v-date-picker-table--date__week">
            08
          </small>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render disabled component and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date v-date-picker-table--disabled theme--light">
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              3
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--flat v-btn--text v-btn--rounded v-btn--disabled theme--light"
                  disabled
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;

exports[`VDatePickerDateTable.ts should render readonly component and match snapshot 1`] = `
<div class="v-date-picker-table v-date-picker-table--date theme--light">
  <table>
    <thead>
      <tr>
        <th>
          S
        </th>
        <th>
          M
        </th>
        <th>
          T
        </th>
        <th>
          W
        </th>
        <th>
          T
        </th>
        <th>
          F
        </th>
        <th>
          S
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              1
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              2
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              3
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              4
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              5
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              6
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              7
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              8
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              9
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              10
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              11
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              12
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              13
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              14
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              15
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              16
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              17
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              18
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              19
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              20
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              21
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              22
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              23
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              24
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              25
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              26
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              27
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              28
            </div>
          </button>
        </td>
      </tr>
      <tr>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              29
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              30
            </div>
          </button>
        </td>
        <td>
          <button type="button"
                  class="v-btn v-btn--text v-btn--rounded theme--light"
          >
            <div class="v-btn__content">
              31
            </div>
          </button>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
`;
