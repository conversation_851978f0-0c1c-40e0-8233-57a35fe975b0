// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VDatePickerHeader.ts should render component and match snapshot 1`] = `
<div class="v-date-picker-header theme--light">
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-left theme--light"
      >
      </i>
    </span>
  </button>
  <div class="v-date-picker-header__value">
    <div class="accent--text">
      <button type="button">
        November 2005
      </button>
    </div>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-right theme--light"
      >
      </i>
    </span>
  </button>
</div>
`;

exports[`VDatePickerHeader.ts should render component in RTL mode and match snapshot 1`] = `
<div class="v-date-picker-header theme--light">
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-right theme--light"
      >
      </i>
    </span>
  </button>
  <div class="v-date-picker-header__value">
    <div class="accent--text">
      <button type="button">
        November 2005
      </button>
    </div>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-left theme--light"
      >
      </i>
    </span>
  </button>
</div>
`;

exports[`VDatePickerHeader.ts should render component with default slot and match snapshot 1`] = `
<div class="v-date-picker-header theme--light">
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-left theme--light"
      >
      </i>
    </span>
  </button>
  <div class="v-date-picker-header__value">
    <div class="accent--text">
      <button type="button">
        <span>
          foo
        </span>
      </button>
    </div>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-right theme--light"
      >
      </i>
    </span>
  </button>
</div>
`;

exports[`VDatePickerHeader.ts should render disabled component and match snapshot 1`] = `
<div class="v-date-picker-header v-date-picker-header--disabled theme--light">
  <button type="button"
          disabled="disabled"
          class="v-btn v-btn--disabled v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-left theme--light"
      >
      </i>
    </span>
  </button>
  <div class="v-date-picker-header__value v-date-picker-header__value--disabled">
    <div>
      <button type="button">
        November 2005
      </button>
    </div>
  </div>
  <button type="button"
          disabled="disabled"
          class="v-btn v-btn--disabled v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-right theme--light"
      >
      </i>
    </span>
  </button>
</div>
`;

exports[`VDatePickerHeader.ts should render readonly component and match snapshot 1`] = `
<div class="v-date-picker-header theme--light">
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-left theme--light"
      >
      </i>
    </span>
  </button>
  <div class="v-date-picker-header__value">
    <div class="accent--text">
      <button type="button">
        November 2005
      </button>
    </div>
  </div>
  <button type="button"
          class="v-btn v-btn--icon v-btn--round theme--light v-size--default"
  >
    <span class="v-btn__content">
      <i aria-hidden="true"
         class="v-icon notranslate mdi mdi-chevron-right theme--light"
      >
      </i>
    </span>
  </button>
</div>
`;
