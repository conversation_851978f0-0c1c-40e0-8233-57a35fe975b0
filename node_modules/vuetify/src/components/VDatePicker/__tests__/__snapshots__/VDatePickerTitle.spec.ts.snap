// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VDatePickerTitle.ts should render component and match snapshot 1`] = `
<div class="v-date-picker-title">
  <div class="v-picker__title__btn v-date-picker-title__year">
    1234
  </div>
  <div class="v-picker__title__btn v-date-picker-title__date v-picker__title__btn--active">
    <div>
      2005-11-01
    </div>
  </div>
</div>
`;

exports[`VDatePickerTitle.ts should render component when selecting year and match snapshot 1`] = `
<div class="v-date-picker-title">
  <div class="v-picker__title__btn v-date-picker-title__year v-picker__title__btn--active">
    1234
  </div>
  <div class="v-picker__title__btn v-date-picker-title__date">
    <div>
      2005-11-01
    </div>
  </div>
</div>
`;

exports[`VDatePickerTitle.ts should render disabled component and match snapshot 1`] = `
<div class="v-date-picker-title v-date-picker-title--disabled">
  <div class="v-picker__title__btn v-date-picker-title__year">
    1234
  </div>
  <div class="v-picker__title__btn v-date-picker-title__date v-picker__title__btn--active">
    <div>
      2005-11-01
    </div>
  </div>
</div>
`;

exports[`VDatePickerTitle.ts should render readonly component and match snapshot 1`] = `
<div class="v-date-picker-title">
  <div class="v-picker__title__btn v-date-picker-title__year">
    1234
  </div>
  <div class="v-picker__title__btn v-date-picker-title__date v-picker__title__btn--active">
    <div>
      2005-11-01
    </div>
  </div>
</div>
`;

exports[`VDatePickerTitle.ts should render year icon 1`] = `
<div class="v-picker__title__btn v-date-picker-title__year">
  1234
  <i aria-hidden="true"
     class="v-icon notranslate material-icons theme--dark"
  >
    year
  </i>
</div>
`;
