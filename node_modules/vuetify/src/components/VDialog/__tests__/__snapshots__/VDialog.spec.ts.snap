// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VDialog.ts should only set tabindex if active 1`] = `
<div class="v-dialog"
     style="transform-origin: center center; display: none;"
>
</div>
`;

exports[`VDialog.ts should only set tabindex if active 2`] = `
<div class="v-dialog v-dialog--active"
     style="transform-origin: center center;"
     tabindex="0"
>
</div>
`;

exports[`VDialog.ts should render a disabled component and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render a eager component and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render a fullscreen component and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render a persistent component and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render a scrollable component and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render component and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render component with custom origin and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render component with custom transition and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render component with custom width (max-width) and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;

exports[`VDialog.ts should render component with custom width and match snapshot 1`] = `
<div class="v-dialog__container">
</div>
`;
