// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VFileInput.ts should be unclearable 1`] = `
<div class="v-input theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
        </div>
        <input id="input-29"
               type="file"
        >
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should display file size 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
          test (2.1 MB)
        </div>
        <input id="input-17"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--clear">
          <button type="button"
                  aria-label="clear icon"
                  tabindex="-1"
                  class="v-icon notranslate v-icon--link material-icons theme--light"
          >
            $clear
          </button>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should display file size 2`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
          test (2.1 MB)
        </div>
        <input id="input-17"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--clear">
          <button type="button"
                  aria-label="clear icon"
                  tabindex="-1"
                  class="v-icon notranslate v-icon--link material-icons theme--light"
          >
            $clear
          </button>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should display total size in counter 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
          2 files
        </div>
        <input id="input-23"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--clear">
          <button type="button"
                  aria-label="clear icon"
                  tabindex="-1"
                  class="v-icon notranslate v-icon--link material-icons theme--light"
          >
            $clear
          </button>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
      <div class="v-counter theme--light">
        0 files (3.1 MB in total)
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should display total size in counter 2`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
          2 files
        </div>
        <input id="input-23"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--clear">
          <button type="button"
                  aria-label="clear icon"
                  tabindex="-1"
                  class="v-icon notranslate v-icon--link material-icons theme--light"
          >
            $clear
          </button>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
      <div class="v-counter theme--light">
        0 files (3.1 MB in total)
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should render 1`] = `
<div class="v-input theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
        </div>
        <input id="input-1"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should render chips 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text v-file-input__text--chips">
          <span class="v-chip v-chip--no-color theme--light v-size--default">
            <span class="v-chip__content">
              test
            </span>
          </span>
        </div>
        <input id="input-67"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--clear">
          <button type="button"
                  aria-label="clear icon"
                  tabindex="-1"
                  class="v-icon notranslate v-icon--link material-icons theme--light"
          >
            $clear
          </button>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should render counter 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
          test
        </div>
        <input id="input-11"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--clear">
          <button type="button"
                  aria-label="clear icon"
                  tabindex="-1"
                  class="v-icon notranslate v-icon--link material-icons theme--light"
          >
            $clear
          </button>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
      <div class="v-counter theme--light">
        0 files
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should render multiple 1`] = `
<div class="v-input theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
        </div>
        <input id="input-6"
               type="file"
               multiple="multiple"
        >
      </div>
      <div class="v-input__append-inner">
        <div>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should render small chips 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text v-file-input__text--chips">
          <span class="v-chip v-chip--no-color theme--light v-size--small">
            <span class="v-chip__content">
              test
            </span>
          </span>
        </div>
        <input id="input-74"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--clear">
          <button type="button"
                  aria-label="clear icon"
                  tabindex="-1"
                  class="v-icon notranslate v-icon--link material-icons theme--light"
          >
            $clear
          </button>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should render without icon 1`] = `
<div class="v-input theme--light v-text-field v-file-input">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
        </div>
        <input id="input-81"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VFileInput.ts should set display none if hide-input prop is set 1`] = `
<div class="v-input theme--light v-text-field v-file-input">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <button type="button"
              aria-label="prepend icon"
              class="v-icon notranslate v-icon--link material-icons theme--light"
      >
        $file
      </button>
    </div>
  </div>
  <div class="v-input__control"
       style="display: none;"
  >
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-file-input__text">
        </div>
        <input id="input-107"
               type="file"
        >
      </div>
      <div class="v-input__append-inner">
        <div>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;
