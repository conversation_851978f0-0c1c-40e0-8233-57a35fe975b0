// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VIcon for component icon should render an svg icon 1`] = `
<span aria-hidden="true"
      class="v-icon notranslate theme--light"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="0 0 24 24"
       role="img"
       aria-hidden="true"
       class="v-icon__svg"
  >
    <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z">
    </path>
  </svg>
</span>
`;

exports[`VIcon for component icon should render an svg icon 2`] = `
<span aria-hidden="true"
      class="v-icon notranslate theme--light"
      style="font-size: 36px; height: 36px; width: 36px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="0 0 24 24"
       role="img"
       aria-hidden="true"
       class="v-icon__svg"
       style="font-size: 36px; height: 36px; width: 36px;"
  >
    <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z">
    </path>
  </svg>
</span>
`;

exports[`VIcon for component icon should render component 1`] = `
<span aria-hidden="true"
      class="v-icon notranslate theme--light"
>
  <div class="test-component v-icon__component theme--light">
    test icon
  </div>
</span>
`;

exports[`VIcon for component icon should set font size from helper prop 1`] = `
<span aria-hidden="true"
      class="v-icon notranslate theme--light"
      style="font-size: 16px; height: 16px; width: 16px;"
>
  <div class="test-component v-icon__component theme--light"
       style="font-size: 16px; height: 16px; width: 16px;"
  >
    test icon
  </div>
</span>
`;

exports[`VIcon for component icon should set font size from helper prop 2`] = `
<span aria-hidden="true"
      class="v-icon notranslate theme--light"
>
  <div class="test-component v-icon__component theme--light">
    test icon
  </div>
</span>
`;

exports[`VIcon for component icon should set font size from helper prop 3`] = `
<span aria-hidden="true"
      class="v-icon notranslate theme--light"
      style="font-size: 36px; height: 36px; width: 36px;"
>
  <div class="test-component v-icon__component theme--light"
       style="font-size: 36px; height: 36px; width: 36px;"
  >
    test icon
  </div>
</span>
`;

exports[`VIcon for component icon should set font size from helper prop 4`] = `
<span aria-hidden="true"
      class="v-icon notranslate theme--light"
      style="font-size: 40px; height: 40px; width: 40px;"
>
  <div class="test-component v-icon__component theme--light"
       style="font-size: 40px; height: 40px; width: 40px;"
  >
    test icon
  </div>
</span>
`;

exports[`VIcon set font size from helper prop 1`] = `
<i aria-hidden="true"
   class="v-icon notranslate material-icons theme--light"
   style="font-size: 16px;"
>
  add
</i>
`;

exports[`VIcon set font size from helper prop 2`] = `
<i aria-hidden="true"
   class="v-icon notranslate material-icons theme--light"
>
  add
</i>
`;

exports[`VIcon set font size from helper prop 3`] = `
<i aria-hidden="true"
   class="v-icon notranslate material-icons theme--light"
   style="font-size: 36px;"
>
  add
</i>
`;

exports[`VIcon set font size from helper prop 4`] = `
<i aria-hidden="true"
   class="v-icon notranslate material-icons theme--light"
   style="font-size: 40px;"
>
  add
</i>
`;
