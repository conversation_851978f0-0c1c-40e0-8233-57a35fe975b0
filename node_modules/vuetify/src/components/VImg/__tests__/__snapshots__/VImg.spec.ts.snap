// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VImg.ts should display placeholders 1`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-image__image v-image__image--preload v-image__image--cover"
       style="background-image: url(lazy_src); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-image__placeholder">
    <div>
      loading...
    </div>
  </div>
  <div class="v-responsive__content">
  </div>
</div>
`;

exports[`VImg.ts should display placeholders 2`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-responsive__sizer"
       style="padding-bottom: 56.25%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(full_src); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content"
       style="width: 1600px;"
  >
  </div>
</div>
`;

exports[`VImg.ts should have aria attributes 1`] = `
<div aria-label="this is not a decorative image"
     role="img"
     class="v-image v-responsive theme--light"
>
  <div class="v-responsive__sizer"
       style="padding-bottom: 56.25%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(LOAD_SUCCESS_SRC); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content"
       style="width: 1600px;"
  >
  </div>
</div>
`;

exports[`VImg.ts should load 1`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-image__image v-image__image--preload v-image__image--cover"
       style="background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content">
  </div>
</div>
`;

exports[`VImg.ts should load 2`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-responsive__sizer"
       style="padding-bottom: 56.25%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(LOAD_SUCCESS_SRC); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content"
       style="width: 1600px;"
  >
  </div>
</div>
`;

exports[`VImg.ts should override vuetify-loader values 1`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-responsive__sizer"
       style="padding-bottom: 50%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(LOAD_SUCCESS_SRC); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content">
  </div>
</div>
`;

exports[`VImg.ts should update src 1`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-responsive__sizer"
       style="padding-bottom: 56.25%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(LOAD_SUCCESS_SRC); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content"
       style="width: 1600px;"
  >
  </div>
</div>
`;

exports[`VImg.ts should update src 2`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-responsive__sizer"
       style="padding-bottom: 56.25%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(LOAD_SUCCESS_SRC1); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content"
       style="width: 1600px;"
  >
  </div>
</div>
`;

exports[`VImg.ts should update src while still loading 1`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-image__image v-image__image--preload v-image__image--cover"
       style="background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content">
  </div>
</div>
`;

exports[`VImg.ts should update src while still loading 2`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-responsive__sizer"
       style="padding-bottom: 56.25%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(LOAD_SUCCESS_SRC1); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content"
       style="width: 1600px;"
  >
  </div>
</div>
`;

exports[`VImg.ts should use vuetify-loader data 1`] = `
<div class="v-image v-responsive theme--light">
  <div class="v-responsive__sizer"
       style="padding-bottom: 100%;"
  >
  </div>
  <div class="v-image__image v-image__image--cover"
       style="background-image: url(LOAD_SUCCESS_SRC); background-position: center center;"
       name="fade-transition"
       mode="in-out"
  >
  </div>
  <div class="v-responsive__content"
       style="width: 1600px;"
  >
  </div>
</div>
`;
