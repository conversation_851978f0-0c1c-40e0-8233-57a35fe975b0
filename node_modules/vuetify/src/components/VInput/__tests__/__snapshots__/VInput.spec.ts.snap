// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VInput.ts should apply theme to label, counter, messages and icons 1`] = `
<div class="v-input theme--light">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        prepend
      </i>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <label for="input-68"
             class="v-label theme--light"
             style="left: 0px; position: relative;"
      >
        foo
      </label>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
        <div class="v-messages__message">
          bar
        </div>
      </span>
    </div>
  </div>
  <div class="v-input__append-outer">
    <div class="v-input__icon v-input__icon--append">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        append
      </i>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should be in an error state 1`] = `
<div class="v-input v-input--has-state theme--light error--text">
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
    <div class="v-messages theme--light error--text">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should be in an error state 2`] = `
<div class="v-input v-input--has-state theme--light error--text">
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
    <div class="v-messages theme--light error--text"
         role="alert"
    >
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
        <div class="v-messages__message">
          required
        </div>
      </span>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should generate an icon and match snapshot 1`] = `
<div class="v-input theme--light">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        list
      </i>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should generate an icon and match snapshot 2`] = `
<div class="v-input theme--light">
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
  <div class="v-input__append-outer">
    <div class="v-input__icon v-input__icon--append">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        list
      </i>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should generate append and prepend slots 1`] = `
<div class="v-input theme--light">
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
  <div class="v-input__append-outer">
    <div>
      append
    </div>
  </div>
</div>
`;

exports[`VInput.ts should generate append and prepend slots 2`] = `
<div class="v-input theme--light">
  <div class="v-input__prepend-outer">
    <div>
      prepend
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should not apply attrs to element 1`] = `
<div class="v-input theme--light">
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should not generate input details 1`] = `
<div class="v-input v-input--hide-details theme--light">
  <div class="v-input__control">
    <div class="v-input__slot">
    </div>
  </div>
</div>
`;

exports[`VInput.ts should render a label 1`] = `
<div class="v-input theme--light">
  <div class="v-input__control">
    <div class="v-input__slot">
      <label for="input-59"
             class="v-label theme--light"
             style="left: 0px; position: relative;"
      >
        foo
      </label>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VInput.ts should render a label 2`] = `
<div class="v-input theme--light">
  <div class="v-input__control">
    <div class="v-input__slot">
      <label for="input-63"
             class="v-label theme--light"
             style="left: 0px; position: relative;"
      >
        <div>
          foo
        </div>
      </label>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;
