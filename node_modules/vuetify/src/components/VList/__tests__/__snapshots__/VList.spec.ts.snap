// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VList.ts should render a dense component and match snapshot 1`] = `
<div role="list"
     class="v-list v-sheet theme--light v-list--dense"
>
</div>
`;

exports[`VList.ts should render a subheader component and match snapshot 1`] = `
<div role="list"
     class="v-list v-sheet theme--light v-list--subheader"
>
</div>
`;

exports[`VList.ts should render a threeLine component and match snapshot 1`] = `
<div role="list"
     class="v-list v-sheet theme--light v-list--three-line"
>
</div>
`;

exports[`VList.ts should render a twoLine component and match snapshot 1`] = `
<div role="list"
     class="v-list v-sheet theme--light v-list--two-line"
>
</div>
`;

exports[`VList.ts should render component and match snapshot 1`] = `
<div role="list"
     class="v-list v-sheet theme--light"
>
</div>
`;
