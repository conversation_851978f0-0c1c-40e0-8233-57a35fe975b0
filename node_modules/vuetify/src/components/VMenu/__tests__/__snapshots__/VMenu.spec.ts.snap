// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VMenu.ts should render multiple content nodes 1`] = `
<div class="v-menu">
  <button>
  </button>
  <div role="menu"
       class="v-menu__content theme--light "
       style="max-height: auto; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 0; display: none;"
  >
    <span>
      foo
    </span>
    <span>
      bar
    </span>
  </div>
</div>
`;

exports[`VMenu.ts should round dimensions 1`] = `"max-height: auto; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 0; display: none;"`;

exports[`VMenu.ts should update position dynamically 1`] = `"max-height: auto; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 8; display: none;"`;

exports[`VMenu.ts should update position dynamically 2`] = `"max-height: auto; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 8; display: none;"`;

exports[`VMenu.ts should work 1`] = `
<div class="v-menu">
  <button>
  </button>
  <div role="menu"
       class="v-menu__content theme--light menuable__content__active "
       style="max-height: auto; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 8; display: none; z-index: 8;"
  >
    <div class="v-card v-sheet theme--light">
    </div>
  </div>
</div>
`;
