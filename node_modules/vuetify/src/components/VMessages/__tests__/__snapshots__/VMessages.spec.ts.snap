// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VMessages.ts should accept a scoped slot 1`] = `
<div class="v-messages theme--light">
  <span name="message-transition"
        tag="div"
        class="v-messages__wrapper"
  >
    <div class="v-messages__message">
      <div>
        Foo
      </div>
    </div>
  </span>
</div>
`;

exports[`VMessages.ts should allow HTML 1`] = `
<div class="v-messages theme--light">
  <span name="message-transition"
        tag="div"
        class="v-messages__wrapper"
  >
    <div class="v-messages__message">
      &lt;a href="#"&gt;a link&lt;/a&gt;
    </div>
  </span>
</div>
`;

exports[`VMessages.ts should not allow HTML 1`] = `
<div class="v-messages theme--light">
  <span name="message-transition"
        tag="div"
        class="v-messages__wrapper"
  >
    <div class="v-messages__message">
      &lt;a href="#"&gt;a link&lt;/a&gt;
    </div>
  </span>
</div>
`;

exports[`VMessages.ts should show messages 1`] = `
<div class="v-messages theme--light">
  <span name="message-transition"
        tag="div"
        class="v-messages__wrapper"
  >
    <div class="v-messages__message">
      foo
    </div>
    <div class="v-messages__message">
      bar
    </div>
  </span>
</div>
`;

exports[`VMessages.ts should show messages 2`] = `
<div class="v-messages theme--light">
  <span name="message-transition"
        tag="div"
        class="v-messages__wrapper"
  >
  </span>
</div>
`;
