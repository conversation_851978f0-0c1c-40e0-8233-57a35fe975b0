// Imports
@import './_variables.scss'

// Theme
+theme(v-otp-input) using ($material)
  .v-input
    .v-input__control
      .v-input__slot
        background: map-get($material, 'bg-color')

.v-otp-input
  display: flex
  flex-wrap: wrap
  flex: 1 1 auto
  margin-right: -$otp-gutter
  margin-left: -$otp-gutter

  input
    text-align: center

  .v-input
    margin: 0
    flex: 1 0 $otp-width + $otp-gutter * 2
    max-width: 100%
    width: 100%
    padding: $otp-gutter

    &.v-otp-input--plain
      fieldset
        display: none

    input[type=number]::-webkit-outer-spin-button,
    input[type=number]::-webkit-inner-spin-button
      -webkit-appearance: none
      margin: 0
    input[type=number]
      -moz-appearance: textfield
