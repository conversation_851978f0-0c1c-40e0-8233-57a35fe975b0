// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VOverflowBtn.js should show label with persistentPlaceholder property set to true 1`] = `
<div class="v-input theme--light v-text-field v-text-field--single-line v-select v-autocomplete v-overflow-btn">
  <div class="v-input__control">
    <div role="combobox"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-15"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <label for="input-15"
               class="v-label theme--light"
        >
          Some label
        </label>
        <div class="v-select__selections">
          <input id="input-15"
                 readonly="readonly"
                 type="text"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VOverflowBtn.js should show label with persistentPlaceholder property set to true 2`] = `
<div class="v-input theme--light v-text-field v-text-field--single-line v-select v-select--is-menu-active v-autocomplete v-overflow-btn">
  <div class="v-input__control">
    <div role="combobox"
         aria-haspopup="listbox"
         aria-expanded="true"
         aria-owns="list-15"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <label for="input-15"
               class="v-label theme--light"
        >
          Some label
        </label>
        <div class="v-select__selections">
          <input id="input-15"
                 readonly="readonly"
                 type="text"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VOverflowBtn.js should use default autocomplete selections 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-text-field--single-line v-select v-select--is-multi v-autocomplete v-overflow-btn">
  <div class="v-input__control">
    <div role="combobox"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-1"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div class="v-select__selection v-select__selection--comma">
            foo
          </div>
          <input id="input-1"
                 readonly="readonly"
                 type="text"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden"
               value="foo"
        >
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VOverflowBtn.js should use default autocomplete selections 2`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-text-field--single-line v-select v-autocomplete v-overflow-btn v-overflow-btn--segmented">
  <div class="v-input__control">
    <div role="combobox"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-1"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <button type="button"
                  class="v-btn v-btn--text theme--light v-size--default"
          >
            <span class="v-btn__content">
              foo
            </span>
          </button>
          <input id="input-1"
                 readonly="readonly"
                 type="text"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden"
               value="foo"
        >
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;
