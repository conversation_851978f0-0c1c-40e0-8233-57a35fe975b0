// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VPagination.ts emits an event when next or previous is clicked 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        1
      </button>
    </li>
    <li>
      <button type="button"
              aria-current="true"
              aria-label="$vuetify.pagination.ariaLabel.currentPage"
              class="v-pagination__item v-pagination__item--active primary"
      >
        2
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        3
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        4
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        5
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;

exports[`VPagination.ts should only render end of range if value is equals "right" 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        1
      </button>
    </li>
    <li>
      <span class="v-pagination__more">
        ...
      </span>
    </li>
    <li>
      <button type="button"
              aria-current="true"
              aria-label="$vuetify.pagination.ariaLabel.currentPage"
              class="v-pagination__item v-pagination__item--active primary"
      >
        98
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        99
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        100
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;

exports[`VPagination.ts should only render middle of range if length is big and value is somewhere in the middle 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        1
      </button>
    </li>
    <li>
      <span class="v-pagination__more">
        ...
      </span>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        41
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        42
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        43
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        44
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        45
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        46
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        47
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        48
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        49
      </button>
    </li>
    <li>
      <button type="button"
              aria-current="true"
              aria-label="$vuetify.pagination.ariaLabel.currentPage"
              class="v-pagination__item v-pagination__item--active primary"
      >
        50
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        51
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        52
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        53
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        54
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        55
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        56
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        57
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        58
      </button>
    </li>
    <li>
      <span class="v-pagination__more">
        ...
      </span>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        100
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;

exports[`VPagination.ts should only render start and end of range if length is big 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button disabled="disabled"
              type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation v-pagination__navigation--disabled"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        1
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        2
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        3
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        4
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        5
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        6
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        7
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        8
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        9
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        10
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        11
      </button>
    </li>
    <li>
      <span class="v-pagination__more">
        ...
      </span>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        91
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        92
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        93
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        94
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        95
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        96
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        97
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        98
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        99
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        100
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;

exports[`VPagination.ts should only render start of range if value is equals "left" 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        1
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        2
      </button>
    </li>
    <li>
      <button type="button"
              aria-current="true"
              aria-label="$vuetify.pagination.ariaLabel.currentPage"
              class="v-pagination__item v-pagination__item--active primary"
      >
        3
      </button>
    </li>
    <li>
      <span class="v-pagination__more">
        ...
      </span>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        100
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;

exports[`VPagination.ts should render component in RTL mode and match snapshot 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        1
      </button>
    </li>
    <li>
      <button type="button"
              aria-current="true"
              aria-label="$vuetify.pagination.ariaLabel.currentPage"
              class="v-pagination__item v-pagination__item--active primary"
      >
        2
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        3
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        4
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        5
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;

exports[`VPagination.ts should render disabled buttons with length equals to 0 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button disabled="disabled"
              type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation v-pagination__navigation--disabled"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button disabled="disabled"
              type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation v-pagination__navigation--disabled"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;

exports[`VPagination.ts should use totalVisible prop if defined 1`] = `
<nav role="navigation"
     aria-label="$vuetify.pagination.ariaLabel.wrapper"
>
  <ul class="v-pagination theme--light">
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.previous"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-left theme--light"
        >
        </i>
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        1
      </button>
    </li>
    <li>
      <span class="v-pagination__more">
        ...
      </span>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        47
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        48
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        49
      </button>
    </li>
    <li>
      <button type="button"
              aria-current="true"
              aria-label="$vuetify.pagination.ariaLabel.currentPage"
              class="v-pagination__item v-pagination__item--active primary"
      >
        50
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        51
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        52
      </button>
    </li>
    <li>
      <span class="v-pagination__more">
        ...
      </span>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.page"
              class="v-pagination__item"
      >
        100
      </button>
    </li>
    <li>
      <button type="button"
              aria-label="$vuetify.pagination.ariaLabel.next"
              class="v-pagination__navigation"
      >
        <i aria-hidden="true"
           class="v-icon notranslate mdi mdi-chevron-right theme--light"
        >
        </i>
      </button>
    </li>
  </ul>
</nav>
`;
