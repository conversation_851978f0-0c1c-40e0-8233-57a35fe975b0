// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VParallax.ts should render 1`] = `
<div class="v-parallax"
     style="height: 500px;"
>
  <div class="v-parallax__image-container">
    <img alt
         class="v-parallax__image"
         style="display: block; opacity: 0; transform: translate(-50%, 0px);"
    >
  </div>
  <div class="v-parallax__content">
  </div>
</div>
`;

exports[`VParallax.ts should use alt tag when supplied 1`] = `
<div class="v-parallax"
     style="height: 500px;"
>
  <div class="v-parallax__image-container">
    <img alt="name"
         class="v-parallax__image"
         style="display: block; opacity: 1; transform: translate(-50%, 0px);"
    >
  </div>
  <div class="v-parallax__content">
  </div>
</div>
`;

exports[`VParallax.ts should use empty alt tag when not supplied 1`] = `
<div class="v-parallax"
     style="height: 500px;"
>
  <div class="v-parallax__image-container">
    <img alt
         class="v-parallax__image"
         style="display: block; opacity: 1; transform: translate(-50%, 0px);"
    >
  </div>
  <div class="v-parallax__content">
  </div>
</div>
`;
