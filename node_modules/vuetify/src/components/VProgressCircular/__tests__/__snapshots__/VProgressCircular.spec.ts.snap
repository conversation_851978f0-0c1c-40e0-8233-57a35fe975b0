// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VProgressCircular.ts should render component and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-circular"
     style="height: 32px; width: 32px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="22.857142857142858 22.857142857142858 45.714285714285715 45.714285714285715"
       style="transform: rotate(0deg);"
  >
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="0"
            class="v-progress-circular__underlay"
    >
    </circle>
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="84.19468311620646px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
    <span>
      content
    </span>
  </div>
</div>
`;

exports[`VProgressCircular.ts should render component with button prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-circular v-progress-circular--button"
     style="height: 40px; width: 40px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="22.857142857142858 22.857142857142858 45.714285714285715 45.714285714285715"
       style="transform: rotate(0deg);"
  >
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="0"
            class="v-progress-circular__underlay"
    >
    </circle>
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="84.19468311620646px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
  </div>
</div>
`;

exports[`VProgressCircular.ts should render component with color prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-circular orange--text text--lighten-1"
     style="height: 32px; width: 32px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="22.857142857142858 22.857142857142858 45.714285714285715 45.714285714285715"
       style="transform: rotate(0deg);"
  >
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="0"
            class="v-progress-circular__underlay"
    >
    </circle>
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="84.19468311620646px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
  </div>
</div>
`;

exports[`VProgressCircular.ts should render component with fill prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-circular"
     style="height: 32px; width: 32px;"
     fill="green lighten-1"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="22.857142857142858 22.857142857142858 45.714285714285715 45.714285714285715"
       style="transform: rotate(0deg);"
  >
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="0"
            class="v-progress-circular__underlay"
    >
    </circle>
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="84.19468311620646px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
  </div>
</div>
`;

exports[`VProgressCircular.ts should render component with indeterminate prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     class="v-progress-circular v-progress-circular--indeterminate"
     style="height: 32px; width: 32px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="22.857142857142858 22.857142857142858 45.714285714285715 45.714285714285715"
       style="transform: rotate(0deg);"
  >
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="125.66370614359172px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
  </div>
</div>
`;

exports[`VProgressCircular.ts should render component with rotate prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-circular"
     style="height: 32px; width: 32px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="22.857142857142858 22.857142857142858 45.714285714285715 45.714285714285715"
       style="transform: rotate(29deg);"
  >
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="0"
            class="v-progress-circular__underlay"
    >
    </circle>
    <circle fill="transparent"
            cx="45.714285714285715"
            cy="45.714285714285715"
            r="20"
            stroke-width="5.714285714285714"
            stroke-dasharray="125.664"
            stroke-dashoffset="84.19468311620646px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
  </div>
</div>
`;

exports[`VProgressCircular.ts should render component with size prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-circular"
     style="height: 17px; width: 17px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="26.153846153846157 26.153846153846157 52.307692307692314 52.307692307692314"
       style="transform: rotate(0deg);"
  >
    <circle fill="transparent"
            cx="52.307692307692314"
            cy="52.307692307692314"
            r="20"
            stroke-width="12.307692307692308"
            stroke-dasharray="125.664"
            stroke-dashoffset="0"
            class="v-progress-circular__underlay"
    >
    </circle>
    <circle fill="transparent"
            cx="52.307692307692314"
            cy="52.307692307692314"
            r="20"
            stroke-width="12.307692307692308"
            stroke-dasharray="125.664"
            stroke-dashoffset="84.19468311620646px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
  </div>
</div>
`;

exports[`VProgressCircular.ts should render component with width prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-circular"
     style="height: 32px; width: 32px;"
>
  <svg xmlns="http://www.w3.org/2000/svg"
       viewbox="33.68421052631579 33.68421052631579 67.36842105263158 67.36842105263158"
       style="transform: rotate(0deg);"
  >
    <circle fill="transparent"
            cx="67.36842105263158"
            cy="67.36842105263158"
            r="20"
            stroke-width="27.36842105263158"
            stroke-dasharray="125.664"
            stroke-dashoffset="0"
            class="v-progress-circular__underlay"
    >
    </circle>
    <circle fill="transparent"
            cx="67.36842105263158"
            cy="67.36842105263158"
            r="20"
            stroke-width="27.36842105263158"
            stroke-dasharray="125.664"
            stroke-dashoffset="84.19468311620646px"
            class="v-progress-circular__overlay"
    >
    </circle>
  </svg>
  <div class="v-progress-circular__info">
  </div>
</div>
`;
