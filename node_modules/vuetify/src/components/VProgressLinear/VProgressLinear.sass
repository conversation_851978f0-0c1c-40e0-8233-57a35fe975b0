@import './_variables.scss'

+theme(v-progress-linear) using ($material)
  color: map-deep-get($material, 'text', 'primary')

// Block
.v-progress-linear
  background: transparent
  overflow: hidden
  position: relative
  transition: .2s map-get($transition, 'ease-in-out')
  width: 100%

// Element
.v-progress-linear__buffer
  height: inherit
  left: 0
  position: absolute
  top: 0
  transition: inherit
  width: 100%

.v-progress-linear--reverse
  .v-progress-linear__buffer
    left: auto
    right: 0

.v-progress-linear__background
  bottom: 0
  left: 0
  position: absolute
  top: 0
  transition: inherit

.v-progress-linear--reverse
  .v-progress-linear__background
    left: auto
    right: 0

.v-progress-linear__content
  align-items: center
  display: flex
  height: 100%
  left: 0
  justify-content: center
  position: absolute
  top: 0
  width: 100%

.v-progress-linear--reverse
  .v-progress-linear__content
    left: auto
    right: 0

.v-progress-linear__determinate
  height: inherit
  left: 0
  position: absolute
  transition: inherit

.v-progress-linear--reverse
  .v-progress-linear__determinate
    left: auto
    right: 0

.v-progress-linear
  .v-progress-linear__indeterminate
    .long, .short
      animation-play-state: paused
      background-color: inherit
      bottom: 0
      height: inherit
      left: 0
      position: absolute
      right: auto
      top: 0
      width: auto
      will-change: left, right

    &--active .long
      animation-name: indeterminate-ltr
      animation-duration: 2.2s
      animation-iteration-count: infinite

    &--active .short
      animation-name: indeterminate-short-ltr
      animation-duration: 2.2s
      animation-iteration-count: infinite

.v-progress-linear--reverse
  .v-progress-linear__indeterminate
    .long, .short
      left: auto
      right: 0

    &--active .long
      animation-name: indeterminate-rtl

    &--active .short
      animation-name: indeterminate-short-rtl

.v-progress-linear__stream
  animation: stream-ltr .25s infinite linear
  animation-play-state: paused
  border-color: currentColor
  border-top: $progress-linear-stream-border-width dotted
  bottom: 0
  left: auto
  right: -8px
  opacity: $progress-linear-stream-opacity
  pointer-events: none
  position: absolute
  top: calc(50% - #{$progress-linear-stream-border-width / 2})
  transition: inherit

.v-progress-linear--reverse
  .v-progress-linear__stream
    animation: stream-rtl .25s infinite linear
    left: -8px
    right: auto

.v-progress-linear__wrapper
  overflow: hidden
  position: relative
  transition: inherit

// Modifier
.v-progress-linear--absolute,
.v-progress-linear--fixed
  left: 0
  z-index: 1

.v-progress-linear--absolute
  position: absolute

.v-progress-linear--fixed
  position: fixed

.v-progress-linear--reactive
  .v-progress-linear__content
    pointer-events: none

.v-progress-linear--rounded
  border-radius: $progress-linear-border-radius

.v-progress-linear--striped
  .v-progress-linear__determinate
    background-image: $progress-linear-stripe-gradient
    background-size: $progress-linear-stripe-background-size
    background-repeat: repeat

.v-progress-linear--query
  .v-progress-linear__indeterminate--active
    .long
      animation-name: query-ltr
      animation-duration: 2s
      animation-iteration-count: infinite

    .short
      animation-name: query-short-ltr
      animation-duration: 2s
      animation-iteration-count: infinite

  &.v-progress-linear--reverse
    .v-progress-linear__indeterminate--active
      .long
        animation-name: query-rtl

      .short
        animation-name: query-short-rtl

.v-progress-linear--visible
  .v-progress-linear__indeterminate--active
    .long, .short
      animation-play-state: running
  .v-progress-linear__stream
    animation-play-state: running

@keyframes indeterminate-ltr
  0%
    left: -90%
    right: 100%
  60%
    left: -90%
    right: 100%
  100%
    left: 100%
    right: -35%

@keyframes indeterminate-rtl
  0%
    left: 100%
    right: -90%
  60%
    left: 100%
    right: -90%
  100%
    left: -35%
    right: 100%

@keyframes indeterminate-short-ltr
  0%
    left: -200%
    right: 100%
  60%
    left: 107%
    right: -8%
  100%
    left: 107%
    right: -8%

@keyframes indeterminate-short-rtl
  0%
    left: 100%
    right: -200%
  60%
    left: -8%
    right: 107%
  100%
    left: -8%
    right: 107%

@keyframes query-ltr
  0%
    right: -90%
    left: 100%
  60%
    right: -90%
    left: 100%
  100%
    right: 100%
    left: -35%

@keyframes query-rtl
  0%
    right: 100%
    left: -90%
  60%
    right: 100%
    left: -90%
  100%
    right: -35%
    left: 100%

@keyframes query-short-ltr
  0%
    right: -200%
    left: 100%
  60%
    right: 107%
    left: -8%
  100%
    right: 107%
    left: -8%

@keyframes query-short-rtl
  0%
    right: 100%
    left: -200%
  60%
    right: -8%
    left: 107%
  100%
    right: -8%
    left: 107%

@keyframes stream-ltr
  to
    transform: translateX(-8px)

@keyframes stream-rtl
  to
    transform: translateX(8px)
