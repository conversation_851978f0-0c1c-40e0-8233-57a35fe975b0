// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VProgressLinear.ts should render component and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component in RTL mode 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear v-progress-linear--reverse theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; right: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with buffer value and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="80"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 33%; width: 47%;"
  >
  </div>
  <div class="v-progress-linear__buffer"
       style="width: 80%;"
  >
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with buffer value and match snapshot 2`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="0"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 33%; width: 0%;"
  >
  </div>
  <div class="v-progress-linear__buffer"
       style="width: 0%;"
  >
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with buffer value and value > buffer value and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="80"
     aria-valuenow="90"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 90%; width: 0%;"
  >
  </div>
  <div class="v-progress-linear__buffer"
       style="width: 80%;"
  >
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 90%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with color and background color and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background blue"
       style="opacity: 1; left: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate orange"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with color and background color and opacity and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background blue"
       style="opacity: 0.5; left: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate orange"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with color and background opacity and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background orange"
       style="opacity: 0.5; left: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate orange"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with color and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background orange"
       style="opacity: 0.3; left: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate orange"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render component with css color and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background"
       style="opacity: 0.3; left: 33%; width: 67%; background-color: rgb(51, 102, 153); border-color: #336699;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate"
       style="width: 33%; background-color: rgb(51, 102, 153); border-color: #336699;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render default slot content 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="80"
     aria-valuenow="90"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 90%; width: 0%;"
  >
  </div>
  <div class="v-progress-linear__buffer"
       style="width: 80%;"
  >
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 90%;"
  >
  </div>
  <div class="v-progress-linear__content">
    <div class="foobar">
      content
    </div>
  </div>
</div>
`;

exports[`VProgressLinear.ts should render inactive component and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 0px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer"
       style="height: 0px;"
  >
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render indeterminate progress and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 0%; width: 100%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__indeterminate v-progress-linear__indeterminate--active">
    <div class="v-progress-linear__indeterminate long primary">
    </div>
    <div class="v-progress-linear__indeterminate short primary">
    </div>
  </div>
</div>
`;

exports[`VProgressLinear.ts should render indeterminate progress with query prop and match snapshot 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     class="v-progress-linear v-progress-linear--query theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 0%; width: 100%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__indeterminate v-progress-linear__indeterminate--active">
    <div class="v-progress-linear__indeterminate long primary">
    </div>
    <div class="v-progress-linear__indeterminate short primary">
    </div>
  </div>
</div>
`;

exports[`VProgressLinear.ts should render reverse component in RTL mode 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; left: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 33%;"
  >
  </div>
</div>
`;

exports[`VProgressLinear.ts should render reversed component 1`] = `
<div role="progressbar"
     aria-valuemin="0"
     aria-valuemax="100"
     aria-valuenow="33"
     class="v-progress-linear v-progress-linear--reverse theme--light"
     style="height: 4px;"
>
  <div class="v-progress-linear__background primary"
       style="opacity: 0.3; right: 33%; width: 67%;"
  >
  </div>
  <div class="v-progress-linear__buffer">
  </div>
  <div class="v-progress-linear__determinate primary"
       style="width: 33%;"
  >
  </div>
</div>
`;
