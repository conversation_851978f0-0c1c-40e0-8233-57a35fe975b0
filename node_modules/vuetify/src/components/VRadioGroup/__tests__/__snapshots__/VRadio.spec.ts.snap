// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VRadio.ts should be render colored radio 1`] = `
<div class="v-radio theme--light">
  <div class="v-input--selection-controls__input">
    <i aria-hidden="true"
       class="v-icon notranslate material-icons theme--light"
    >
      $radioOff
    </i>
    <input aria-checked="false"
           id="input-22"
           role="radio"
           type="radio"
           value
    >
    <div class="v-input--selection-controls__ripple">
    </div>
  </div>
</div>
`;

exports[`VRadio.ts should not render aria-label attribute with no label value on input group 1`] = `
<div class="v-radio theme--light">
  <div class="v-input--selection-controls__input">
    <i aria-hidden="true"
       class="v-icon notranslate material-icons theme--light"
    >
      $radioOff
    </i>
    <input aria-checked="false"
           id="input-4"
           role="radio"
           type="radio"
           value
    >
    <div class="v-input--selection-controls__ripple">
    </div>
  </div>
</div>
`;

exports[`VRadio.ts should render proper input name 1`] = `
<div class="v-radio theme--light">
  <div class="v-input--selection-controls__input">
    <i aria-hidden="true"
       class="v-icon notranslate material-icons theme--light"
    >
      $radioOff
    </i>
    <input aria-checked="false"
           id="input-7"
           role="radio"
           type="radio"
           name="name"
           value
    >
    <div class="v-input--selection-controls__ripple">
    </div>
  </div>
</div>
`;

exports[`VRadio.ts should render role and aria-checked attributes on input group 1`] = `
<div class="v-radio theme--light">
  <div class="v-input--selection-controls__input">
    <i aria-hidden="true"
       class="v-icon notranslate material-icons theme--light primary--text"
    >
      $radioOn
    </i>
    <input aria-checked="true"
           id="input-1"
           role="radio"
           type="radio"
           value
    >
    <div class="v-input--selection-controls__ripple primary--text">
    </div>
  </div>
</div>
`;

exports[`VRadio.ts should use custom icons 1`] = `
<div class="v-radio theme--light">
  <div class="v-input--selection-controls__input">
    <i aria-hidden="true"
       class="v-icon notranslate material-icons theme--light"
    >
      bar
    </i>
    <input aria-checked="false"
           id="input-13"
           role="radio"
           type="radio"
           value
    >
    <div class="v-input--selection-controls__ripple">
    </div>
  </div>
</div>
`;

exports[`VRadio.ts should use custom icons 2`] = `
<div class="v-radio theme--light">
  <div class="v-input--selection-controls__input">
    <i aria-hidden="true"
       class="v-icon notranslate material-icons theme--light primary--text"
    >
      foo
    </i>
    <input aria-checked="true"
           id="input-13"
           role="radio"
           type="radio"
           value
    >
    <div class="v-input--selection-controls__ripple primary--text">
    </div>
  </div>
</div>
`;
