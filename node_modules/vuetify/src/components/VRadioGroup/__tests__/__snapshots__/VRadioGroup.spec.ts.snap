// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VRadioGroup.ts should match dense snapshot 1`] = `
<div class="v-input v-input--dense theme--light v-input--selection-controls v-input--radio-group v-input--radio-group--column">
  <div class="v-input__control">
    <div class="v-input__slot"
         style="height: auto;"
    >
      <div role="radiogroup"
           aria-labelledby="input-5"
           class="v-input--radio-group__input"
      >
        <div class="v-radio theme--light">
          <div class="v-input--selection-controls__input">
            <i aria-hidden="true"
               class="v-icon notranslate v-icon--dense material-icons theme--light"
            >
              $radioOff
            </i>
            <input aria-checked="false"
                   id="input-6"
                   role="radio"
                   type="radio"
                   name="radio-5"
                   value
            >
            <div class="v-input--selection-controls__ripple">
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VRadioGroup.ts should match snapshot 1`] = `
<div class="v-input theme--light v-input--selection-controls v-input--radio-group v-input--radio-group--column">
  <div class="v-input__control">
    <div class="v-input__slot"
         style="height: auto;"
    >
      <div role="radiogroup"
           aria-labelledby="input-1"
           class="v-input--radio-group__input"
      >
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;
