// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VRangeSlider should render a vertical slider 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-input__slider v-input__slider--vertical v-input--range-slider">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-slider v-slider--vertical theme--light">
        <input value="0"
               id="input-min-25"
               disabled="disabled"
               readonly="readonly"
               tabindex="-1"
        >
        <input value="0"
               id="input-max-25"
               disabled="disabled"
               readonly="readonly"
               tabindex="-1"
        >
        <div class="v-slider__track-container">
          <div class="v-slider__track-background primary lighten-3">
          </div>
          <div class="v-slider__track-fill primary">
          </div>
          <div class="v-slider__track-background primary lighten-3">
          </div>
        </div>
        <div role="slider"
             tabindex="0"
             aria-valuemin="0"
             aria-valuemax="100"
             aria-valuenow="0,0"
             aria-readonly="false"
             aria-orientation="vertical"
             class="v-slider__thumb-container primary--text"
             style="top: 100%;"
        >
          <div class="v-slider__thumb primary">
          </div>
        </div>
        <div role="slider"
             tabindex="0"
             aria-valuemin="0"
             aria-valuemax="100"
             aria-valuenow="0,0"
             aria-readonly="false"
             aria-orientation="vertical"
             class="v-slider__thumb-container primary--text"
             style="top: 100%;"
        >
          <div class="v-slider__thumb primary">
          </div>
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;

exports[`VRangeSlider should render disabled slider 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty v-input--is-disabled theme--light v-input__slider v-input--range-slider">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-slider v-slider--horizontal v-slider--disabled theme--light">
        <input value="0"
               id="input-min-29"
               disabled="disabled"
               readonly="readonly"
               tabindex="-1"
        >
        <input value="0"
               id="input-max-29"
               disabled="disabled"
               readonly="readonly"
               tabindex="-1"
        >
        <div class="v-slider__track-container">
          <div class="v-slider__track-background">
          </div>
          <div class="v-slider__track-background">
          </div>
          <div class="v-slider__track-background">
          </div>
        </div>
        <div role="slider"
             tabindex="-1"
             aria-valuemin="0"
             aria-valuemax="100"
             aria-valuenow="0,0"
             aria-readonly="false"
             aria-orientation="horizontal"
             class="v-slider__thumb-container"
             style="left: 0%;"
        >
          <div class="v-slider__thumb">
          </div>
        </div>
        <div role="slider"
             tabindex="-1"
             aria-valuemin="0"
             aria-valuemax="100"
             aria-valuenow="0,0"
             aria-readonly="false"
             aria-orientation="horizontal"
             class="v-slider__thumb-container"
             style="left: 0%;"
        >
          <div class="v-slider__thumb">
          </div>
        </div>
      </div>
    </div>
    <div class="v-messages theme--light">
      <span name="message-transition"
            tag="div"
            class="v-messages__wrapper"
      >
      </span>
    </div>
  </div>
</div>
`;
