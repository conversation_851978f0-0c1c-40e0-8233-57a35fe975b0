@import './_variables.scss'

+theme(v-select) using ($material)
  .v-select__selections
    color: map-deep-get($material, 'text', 'primary')

  &.v-input--is-disabled
    .v-select__selections
      color: map-deep-get($material, 'text', 'disabled')

  .v-select__selection--disabled
    color: map-deep-get($material, 'text', 'disabled')

  &.v-text-field--solo-inverted.v-input--is-focused
    .v-select__selections
      color: map-deep-get($material, 'inputs', 'solo-inverted-focused-text')

.v-select
  position: relative // For **attach** prop

  &:not(.v-select--is-multi).v-text-field--single-line
    .v-select__selections
      flex-wrap: nowrap

  & > .v-input__control > .v-input__slot
    cursor: pointer

  .v-chip
    flex: 0 1 auto
    margin: $select-chip-margin

    &--selected:after
      opacity: $select-selected-chip-opacity

  .fade-transition-leave-active
    position: absolute
    left: 0

  &.v-input--is-dirty
    ::placeholder
      color: transparent !important

  &:not(.v-input--is-dirty)
    &:not(.v-input--is-focused)
      .v-text-field__prefix
          line-height: $select-prefix-line-height
          top: $select-prefix-top
          transition: $primary-transition

  &.v-text-field--enclosed:not(.v-text-field--single-line):not(.v-text-field--outlined)
    .v-select__selections
      padding-top: $select-selections-padding-top

  &.v-text-field--outlined:not(.v-text-field--single-line)
    .v-select__selections
      padding: $select-outlined-selections-padding-top

    &.v-input--dense
      .v-select__selections
        padding: $select-outlined-dense-selections-padding-top

  &.v-text-field
    input
      flex: 1 1 // Doesn't resize on IE11 with 3rd param
      min-width: 0
      position: relative

    &:not(.v-text-field--single-line) input
      margin-top: 0

  @if $select-active-icon-flip
    &.v-select--is-menu-active
      .v-input__icon--append .v-icon
        transform: rotate(180deg)

  &.v-select--chips
    input
      margin: 0

    .v-select__selections
      min-height: $select-chips-selections-padding-top

    &.v-input--dense
      .v-select__selections
        min-height: $select-chips-dense-selections-padding-top

    // Explicit override since active chips
    // in select are not focused
    .v-chip--select.v-chip--active::before
      opacity: $select-active-chip-opacity

    &.v-select--chips--small
      .v-select__selections
        min-height: $select-small-chips-selections-min-height

    &:not(.v-text-field--single-line)
      &.v-text-field--box,
      &.v-text-field--enclosed
        .v-select__selections
          min-height: $select-chips-box-enclosed-selections-min-height

        &.v-input--dense
          .v-select__selections
            min-height: $select-chips-dense-selections-min-height

        &.v-select--chips--small
          .v-select__selections
            min-height: $select-small-chips-selections-min-height

          &.v-input--dense
            .v-select__selections
              min-height: $select-small-chips-dense-selections-min-height


  &.v-text-field--reverse
    .v-select__slot,
    .v-select__selections
      flex-direction: row-reverse

  &.v-input--is-disabled:not(.v-input--is-readonly):not(.v-autocomplete)
    pointer-events: none

  &__selections
    align-items: center
    display: flex
    flex: 1 1
    flex-wrap: wrap
    line-height: $select-selections-line-height
    max-width: 100%
    min-width: 0

  &__selection
    max-width: 90%

    &--comma
      margin: $select-selections-margin
      min-height: $select-selections-min-height
      overflow: hidden
      text-overflow: ellipsis
      white-space: nowrap

  &.v-input--dense
    .v-select__selection--comma
      margin: $select-dense-selections-margin

    .v-chip
      margin: $select-dense-chip-margin

  &__slot
    position: relative
    align-items: center
    display: flex
    max-width: 100%
    min-width: 0
    width: 100%

  &:not(.v-text-field--single-line):not(.v-text-field--outlined)
    .v-select__slot
      // If an input is a direct child
      // there is no selections div
      // and we must move input to
      // the end of the container
      > input
        align-self: flex-end
