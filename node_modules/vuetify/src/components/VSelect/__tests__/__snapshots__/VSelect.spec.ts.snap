// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VSelect.ts should escape items in menu 1`] = `
<div class="v-list-item__title">
  &lt;strong&gt;foo&lt;/strong&gt;
</div>
`;

exports[`VSelect.ts should only show items if they are in items 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-67"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div class="v-select__selection v-select__selection--comma">
            foo
          </div>
          <input id="input-67"
                 readonly="readonly"
                 type="text"
                 aria-readonly="true"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
        <div class="v-menu__content theme--light "
             style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 0; display: none;"
        >
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should only show items if they are in items 2`] = `
<div class="v-input theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-67"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <input id="input-67"
                 readonly="readonly"
                 type="text"
                 aria-readonly="true"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
        <div class="v-menu__content theme--light "
             style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 0; display: none;"
        >
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should only show items if they are in items 3`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-67"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div class="v-select__selection v-select__selection--comma">
            bar
          </div>
          <input id="input-67"
                 readonly="readonly"
                 type="text"
                 aria-readonly="true"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
        <div class="v-menu__content theme--light "
             style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 0; display: none;"
        >
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should only show items if they are in items 4`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select v-select--is-multi">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-67"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div class="v-select__selection v-select__selection--comma">
            foo,
          </div>
          <div class="v-select__selection v-select__selection--comma">
            bar
          </div>
          <input id="input-67"
                 readonly="readonly"
                 type="text"
                 aria-readonly="true"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
        <div class="v-menu__content theme--light "
             style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 0; display: none;"
        >
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should render v-select correctly when not using scope slot 1`] = `
<div class="v-input theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-32"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <input id="input-32"
                 readonly="readonly"
                 type="text"
                 aria-readonly="false"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should render v-select correctly when not using v-list-item in item scope slot 1`] = `
<div class="v-input theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-26"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <input id="input-26"
                 readonly="readonly"
                 type="text"
                 aria-readonly="false"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should render v-select correctly when using v-list-item in item scope slot 1`] = `
<div class="v-input theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-19"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <input id="input-19"
                 readonly="readonly"
                 type="text"
                 aria-readonly="false"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden">
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should use slotted no-data 1`] = `
<div role="listbox"
     tabindex="-1"
     class="v-list v-select-list v-sheet theme--light theme--light"
     id="list-134"
>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-139-0"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        foo
      </div>
    </div>
  </div>
</div>
`;
