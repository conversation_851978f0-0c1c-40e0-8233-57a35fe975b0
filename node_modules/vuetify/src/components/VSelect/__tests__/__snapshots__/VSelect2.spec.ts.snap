// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VSelect.ts should be clearable with prop, dirty and multi select 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select v-select--is-multi">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-112"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div class="v-select__selection v-select__selection--comma">
            1
          </div>
          <input id="input-112"
                 readonly="readonly"
                 type="text"
                 aria-readonly="false"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--clear">
            <button type="button"
                    aria-label="clear icon"
                    tabindex="-1"
                    class="v-icon notranslate v-icon--link material-icons theme--light"
            >
              $clear
            </button>
          </div>
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden"
               value="1"
        >
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should be clearable with prop, dirty and single select 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-105"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div class="v-select__selection v-select__selection--comma">
            1
          </div>
          <input id="input-105"
                 readonly="readonly"
                 type="text"
                 aria-readonly="false"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--clear">
            <button type="button"
                    aria-label="clear icon"
                    tabindex="-1"
                    class="v-icon notranslate v-icon--link material-icons theme--light"
            >
              $clear
            </button>
          </div>
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden"
               value="1"
        >
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should use scoped slot for selection generation 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-26"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div>
            foo - from slot
          </div>
          <input id="input-26"
                 readonly="readonly"
                 type="text"
                 aria-readonly="false"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden"
               value="foo"
        >
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should use slotted append-item 1`] = `
<div role="listbox"
     tabindex="-1"
     class="v-list v-select-list v-sheet theme--light theme--light"
     id="list-13"
>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-18-0"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        foo
      </div>
    </div>
  </div>
  <div>
    foo
  </div>
</div>
`;

exports[`VSelect.ts should use slotted prepend-item 1`] = `
<div role="listbox"
     tabindex="-1"
     class="v-list v-select-list v-sheet theme--light theme--light"
     id="list-1"
>
  <div>
    foo
  </div>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-6-0"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        foo
      </div>
    </div>
  </div>
</div>
`;
