// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VSelect.ts should add color to selected index 1`] = `
<div class="v-input v-input--is-label-active v-input--is-dirty theme--light v-text-field v-select v-select--is-multi">
  <div class="v-input__control">
    <div role="button"
         aria-haspopup="listbox"
         aria-expanded="false"
         aria-owns="list-50"
         class="v-input__slot"
    >
      <div class="v-select__slot">
        <div class="v-select__selections">
          <div class="v-select__selection v-select__selection--comma primary--text">
            foo
          </div>
          <input id="input-50"
                 readonly="readonly"
                 type="text"
                 aria-readonly="false"
                 autocomplete="off"
          >
        </div>
        <div class="v-input__append-inner">
          <div class="v-input__icon v-input__icon--append">
            <i aria-hidden="true"
               class="v-icon notranslate material-icons theme--light"
            >
              $dropdown
            </i>
          </div>
        </div>
        <input type="hidden"
               value="foo"
        >
      </div>
      <div class="v-menu">
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should open the select is multiple and key down is pressed 1`] = `
<div
  data-app="true"
>
  <div
    class="v-menu__content theme--light menuable__content__active "
    style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 8; display: none;"
  >
    <div
      class="v-list v-select-list v-sheet theme--light theme--light"
      id="list-106"
      role="listbox"
      tabindex="-1"
    >
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-113-0"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__action"
        >
          <div
            class="v-simple-checkbox"
          >
            <div
              class="v-input--selection-controls__input"
            >
              <i
                aria-hidden="true"
                class="v-icon notranslate material-icons theme--light"
              >
                $checkboxOff
              </i>
            </div>
          </div>
        </div>
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            foo
          </div>
        </div>
      </div>
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-113-1"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__action"
        >
          <div
            class="v-simple-checkbox"
          >
            <div
              class="v-input--selection-controls__input"
            >
              <i
                aria-hidden="true"
                class="v-icon notranslate material-icons theme--light"
              >
                $checkboxOff
              </i>
            </div>
          </div>
        </div>
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            bar
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should open the select is multiple and key up is pressed 1`] = `
<div
  data-app="true"
>
  <div
    class="v-menu__content theme--light menuable__content__active "
    style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 8; display: none;"
  >
    <div
      class="v-list v-select-list v-sheet theme--light theme--light"
      id="list-92"
      role="listbox"
      tabindex="-1"
    >
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-99-0"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__action"
        >
          <div
            class="v-simple-checkbox"
          >
            <div
              class="v-input--selection-controls__input"
            >
              <i
                aria-hidden="true"
                class="v-icon notranslate material-icons theme--light"
              >
                $checkboxOff
              </i>
            </div>
          </div>
        </div>
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            foo
          </div>
        </div>
      </div>
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-99-1"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__action"
        >
          <div
            class="v-simple-checkbox"
          >
            <div
              class="v-input--selection-controls__input"
            >
              <i
                aria-hidden="true"
                class="v-icon notranslate material-icons theme--light"
              >
                $checkboxOff
              </i>
            </div>
          </div>
        </div>
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            bar
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should open the select when enter is pressed 1`] = `
<div
  data-app="true"
>
  <div
    class="v-menu__content theme--light menuable__content__active "
    style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 8; display: none;"
  >
    <div
      class="v-list v-select-list v-sheet theme--light theme--light"
      id="list-68"
      role="listbox"
      tabindex="-1"
    >
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-75-0"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            foo
          </div>
        </div>
      </div>
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-75-1"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            bar
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelect.ts should open the select when space is pressed 1`] = `
<div
  data-app="true"
>
  <div
    class="v-menu__content theme--light menuable__content__active "
    style="max-height: 304px; min-width: 0px; max-width: auto; top: 12px; left: 0px; transform-origin: top left; z-index: 8; display: none;"
  >
    <div
      class="v-list v-select-list v-sheet theme--light theme--light"
      id="list-80"
      role="listbox"
      tabindex="-1"
    >
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-87-0"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            foo
          </div>
        </div>
      </div>
      <div
        aria-selected="false"
        class="v-list-item v-list-item--link theme--light"
        id="list-item-87-1"
        role="option"
        tabindex="0"
      >
        <div
          class="v-list-item__content"
        >
          <div
            class="v-list-item__title"
          >
            bar
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
