// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VSelectList.ts should display falsy items 1`] = `
<div role="listbox"
     tabindex="-1"
     class="v-list v-select-list v-sheet theme--light theme--light"
>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-29-0"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        0
      </div>
    </div>
  </div>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-29-1"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        null
      </div>
    </div>
  </div>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-29-2"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        false
      </div>
    </div>
  </div>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-29-3"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        undefined
      </div>
    </div>
  </div>
  <div tabindex="0"
       aria-selected="false"
       id="list-item-29-4"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelectList.ts should display no-data-text when item slot is provided 1`] = `
<div role="listbox"
     tabindex="-1"
     class="v-list v-select-list v-sheet theme--light theme--light"
>
  <div tabindex="-1"
       class="v-list-item theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        undefined
      </div>
    </div>
  </div>
</div>
`;

exports[`VSelectList.ts should generate children 1`] = `
<div role="listbox"
     tabindex="-1"
     class="v-list v-select-list v-sheet theme--light theme--light"
>
  <div class="v-subheader theme--light">
    true
  </div>
  <hr role="separator"
      aria-orientation="horizontal"
      class="v-divider theme--light"
  >
  <div tabindex="0"
       aria-selected="false"
       id="list-item-14-2"
       role="option"
       class="v-list-item v-list-item--link theme--light"
  >
    <div class="v-list-item__content">
      <div class="v-list-item__title">
        foo
      </div>
    </div>
  </div>
</div>
`;
