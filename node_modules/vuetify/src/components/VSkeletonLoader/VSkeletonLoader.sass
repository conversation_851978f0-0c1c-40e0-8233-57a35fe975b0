// Imports
@import './_variables.scss'
@import '../VAvatar/_variables.scss'

// Theme
+theme(v-skeleton-loader) using ($material)
  .v-skeleton-loader__bone::after
    background: map-get($material, 'skeleton')

  .v-skeleton-loader__avatar,
  .v-skeleton-loader__button,
  .v-skeleton-loader__chip,
  .v-skeleton-loader__divider,
  .v-skeleton-loader__heading,
  .v-skeleton-loader__image,
  .v-skeleton-loader__text
    background: map-get($material, 'dividers')

  .v-skeleton-loader__actions,
  .v-skeleton-loader__article,
  .v-skeleton-loader__card-heading,
  .v-skeleton-loader__card-text,
  .v-skeleton-loader__date-picker,
  .v-skeleton-loader__list-item,
  .v-skeleton-loader__list-item-avatar,
  .v-skeleton-loader__list-item-text,
  .v-skeleton-loader__list-item-two-line,
  .v-skeleton-loader__list-item-avatar-two-line,
  .v-skeleton-loader__list-item-three-line,
  .v-skeleton-loader__list-item-avatar-three-line,
  .v-skeleton-loader__table-heading,
  .v-skeleton-loader__table-thead,
  .v-skeleton-loader__table-tbody,
  .v-skeleton-loader__table-tfoot
    background: map-get($material, 'cards')

.v-skeleton-loader
  border-radius: $skeleton-loader-border-radius
  position: relative
  vertical-align: top

  &__actions
    padding: $skeleton-loader-actions-padding
    text-align: right

    .v-skeleton-loader__button
      display: inline-block

      &:first-child
        +ltr()
          margin-right: $skeleton-loader-actions-button-margin

        +rtl()
          margin-left: $skeleton-loader-actions-button-margin


  .v-skeleton-loader__list-item,
  .v-skeleton-loader__list-item-avatar,
  .v-skeleton-loader__list-item-text,
  .v-skeleton-loader__list-item-two-line,
  .v-skeleton-loader__list-item-avatar-two-line,
  .v-skeleton-loader__list-item-three-line,
  .v-skeleton-loader__list-item-avatar-three-line
    border-radius: $skeleton-loader-border-radius

  .v-skeleton-loader__actions,
  .v-skeleton-loader__article,
  .v-skeleton-loader__card,
  .v-skeleton-loader__card-avatar,
  .v-skeleton-loader__card-heading,
  .v-skeleton-loader__card-text,
  .v-skeleton-loader__date-picker,
  .v-skeleton-loader__date-picker-options,
  .v-skeleton-loader__date-picker-days,
  .v-skeleton-loader__list-item,
  .v-skeleton-loader__list-item-avatar,
  .v-skeleton-loader__list-item-text,
  .v-skeleton-loader__list-item-two-line,
  .v-skeleton-loader__list-item-avatar-two-line,
  .v-skeleton-loader__list-item-three-line,
  .v-skeleton-loader__list-item-avatar-three-line,
  .v-skeleton-loader__paragraph,
  .v-skeleton-loader__sentences,
  .v-skeleton-loader__table,
  .v-skeleton-loader__table-cell,
  .v-skeleton-loader__table-heading,
  .v-skeleton-loader__table-thead,
  .v-skeleton-loader__table-tbody,
  .v-skeleton-loader__table-tfoot,
  .v-skeleton-loader__table-row,
  .v-skeleton-loader__table-row-divider
    &::after
      display: none

  &__article
    .v-skeleton-loader__heading
      +ltr()
        margin: $skeleton-loader-article-heading-margin-top-left 0 $skeleton-loader-article-heading-margin-x $skeleton-loader-article-heading-margin-top-left

      +rtl()
        margin: $skeleton-loader-article-heading-margin-top-left $skeleton-loader-article-heading-margin-x 0 $skeleton-loader-article-heading-margin-top-left

    .v-skeleton-loader__paragraph
      padding: $skeleton-loader-article-paragraph-padding

  &__bone
    border-radius: inherit
    overflow: hidden
    position: relative

    &::after
      animation: $skeleton-loader-loading-animation
      content: ''
      height: 100%
      left: 0
      position: absolute
      right: 0
      top: 0
      transform: $skeleton-loader-loading-transform
      z-index: 1

  &__avatar
    border-radius: $avatar-border-radius
    height: $skeleton-loader-avatar-height
    width: $skeleton-loader-avatar-width

  &__button
    border-radius: $skeleton-loader-button-border-radius
    height: $skeleton-loader-button-height
    width: $skeleton-loader-button-width

  &__card
    .v-skeleton-loader__image
      border-radius: 0

  &__card-heading
    .v-skeleton-loader__heading
      margin: $skeleton-loader-card-heading-loader-heading-margin

  &__card-text
    padding: $skeleton-loader-card-text-padding

  &__chip
    border-radius: $skeleton-loader-chip-border-radius
    height: $skeleton-loader-chip-height
    width: $skeleton-loader-chip-width

  &__date-picker
    border-radius: $skeleton-loader-date-picker-border-radius

    .v-skeleton-loader__list-item:first-child
      .v-skeleton-loader__text
        max-width: 88px
        width: 20%

    .v-skeleton-loader__heading
      max-width: 256px
      width: 40%

  &__date-picker-days
    display: flex
    flex-wrap: wrap
    padding: $skeleton-loader-date-picker-days-padding
    margin: 0 auto

    .v-skeleton-loader__avatar
      border-radius: $skeleton-loader-border-radius
      flex: 1 1 auto
      margin: $skeleton-loader-date-picker-days-margin
      height: $skeleton-loader-date-picker-days-height
      width: $skeleton-loader-date-picker-days-width

  &__date-picker-options
    align-items: center
    display: flex
    padding: $skeleton-loader-date-picker-options-padding

    .v-skeleton-loader__avatar
      height: $skeleton-loader-date-picker-options-avatar-height
      width: $skeleton-loader-date-picker-options-avatar-width

      &:nth-child(2)
        margin-left: auto

        +ltr()
          margin-right: $skeleton-loader-date-picker-options-avatar-child-margin

        +rtl()
          margin-left: $skeleton-loader-date-picker-options-avatar-child-margin

    .v-skeleton-loader__text.v-skeleton-loader__bone:first-child
      margin-bottom: 0px
      max-width: 50%
      width: 456px

  &__divider
    border-radius: $skeleton-loader-divider-border-radius
    height: $skeleton-loader-divider-height

  &__heading
    border-radius: $skeleton-loader-heading-border-radius
    height: $skeleton-loader-heading-height
    width: 45%

  &__image
    height: $skeleton-loader-image-height
    border-radius: 0

    ~ .v-skeleton-loader__card-heading
      border-radius: 0

    &:first-child, &:last-child
      border-radius: inherit

  &__list-item
    height: $skeleton-loader-list-item-height

  &__list-item-three-line
    flex-wrap: wrap

    > *
      flex: 1 0 100%
      width: 100%

  &__list-item-avatar,
  &__list-item-avatar-two-line,
  &__list-item-avatar-three-line
    .v-skeleton-loader__avatar
      height: $skeleton-loader-item-avatar-height
      width: $skeleton-loader-item-avatar-width

  &__list-item-avatar
    height: $skeleton-loader-avatar-height

  &__list-item-two-line,
  &__list-item-avatar-two-line
    height: $skeleton-loader-item-two-line-height

  &__list-item-three-line,
  &__list-item-avatar-three-line
    height: $skeleton-loader-item-three-line-height

  &__list-item-avatar-three-line
    .v-skeleton-loader__avatar
      align-self: flex-start

  &__list-item,
  &__list-item-avatar,
  &__list-item-two-line,
  &__list-item-three-line,
  &__list-item-avatar-two-line,
  &__list-item-avatar-three-line
    align-content: center
    align-items: center
    display: flex
    flex-wrap: wrap
    padding: $skeleton-loader-item-padding

    .v-skeleton-loader__avatar
      +ltr()
        margin-right: $skeleton-loader-item-avatar-margin

      +rtl()
        margin-left: $skeleton-loader-item-avatar-margin

    .v-skeleton-loader__text:last-child,
    .v-skeleton-loader__text:only-child
      margin-bottom: 0

  &__paragraph,
  &__sentences
    flex: 1 0 auto

  &__paragraph
    &:not(:last-child)
      margin-bottom: 6px

    .v-skeleton-loader__text:nth-child(1)
      max-width: 100%

    .v-skeleton-loader__text:nth-child(2)
      max-width: 50%

    .v-skeleton-loader__text:nth-child(3)
      max-width: 70%

  &__sentences
    .v-skeleton-loader__text:nth-child(2)
      max-width: 70%

    &:not(:last-child)
      margin-bottom: 6px

  &__table-heading
    align-items: center
    display: flex
    justify-content: space-between
    padding: $skeleton-loader-table-heading-padding

    .v-skeleton-loader__heading
      max-width: 15%

    .v-skeleton-loader__text
      max-width: 40%

  &__table-thead
    display: flex
    justify-content: space-between
    padding: $skeleton-loader-table-thead-padding

    .v-skeleton-loader__heading
      max-width: 5%

  &__table-tbody
    padding: $skeleton-loader-table-tbody-padding

  &__table-tfoot
    align-items: center
    display: flex
    justify-content: flex-end
    padding: $skeleton-loader-table-tfoot-padding

    > *
      +ltr()
        margin-left: $skeleton-loader-table-tfoot-children-margin

      +rtl()
        margin-right: $skeleton-loader-table-tfoot-children-margin

    .v-skeleton-loader__avatar
      height: $skeleton-loader-table-tfoot-avatar-height
      width: $skeleton-loader-table-tfoot-avatar-width

    .v-skeleton-loader__text
      margin-bottom: 0

      &:nth-child(1)
        max-width: 128px

      &:nth-child(2)
        max-width: 64px

  &__table-row
    display: flex
    justify-content: space-between

  &__table-cell
    align-items: center
    display: flex
    height: $skeleton-loader-table-cell-height
    width: $skeleton-loader-table-cell-width

    .v-skeleton-loader__text
      margin-bottom: 0

  &__text
    border-radius: $skeleton-loader-text-border-radius
    flex: 1 0 auto
    height: $skeleton-loader-text-height
    margin-bottom: 6px

  &--boilerplate
    .v-skeleton-loader__bone:after
      display: none

  &--is-loading
    overflow: hidden

  &--tile
    border-radius: 0

    .v-skeleton-loader__bone
      border-radius: 0

@keyframes loading
  100%
    transform: translateX(100%)
