// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VSkeletonLoader.ts should dynamically render content 1`] = `
<div class="v-skeleton-loader theme--light">
  <div>
    foobar
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should dynamically render content 2`] = `
<div class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
     aria-busy="true"
     aria-live="polite"
     role="alert"
>
  <div class="v-skeleton-loader__ v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 1`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__button v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__button v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 2`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__paragraph v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 3`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 4`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__button v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 5`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__image v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__card-heading v-skeleton-loader__bone">
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 6`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__image v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__list-item-avatar v-skeleton-loader__bone">
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 7`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 8`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__chip v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 9`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__list-item v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__card-heading v-skeleton-loader__bone">
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__date-picker-options v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__date-picker-days v-skeleton-loader__bone">
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__actions v-skeleton-loader__bone">
    <div class="v-skeleton-loader__button v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__button v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 10`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 11`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 12`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 13`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__image v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 14`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 15`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 16`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__sentences v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 17`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__sentences v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 18`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__paragraph v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 19`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__paragraph v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 20`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 21`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 22`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__table-heading v-skeleton-loader__bone">
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-thead v-skeleton-loader__bone">
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-tbody v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
      </div>
      <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
      </div>
      <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
      </div>
      <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
      </div>
      <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
      </div>
      <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
        <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
          <div class="v-skeleton-loader__text v-skeleton-loader__bone">
          </div>
        </div>
      </div>
      <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
      </div>
    </div>
  </div>
  <div class="v-skeleton-loader__table-tfoot v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
    <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 23`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 24`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__heading v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 25`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
    </div>
    <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
    </div>
    <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
    </div>
    <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
    </div>
    <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
    </div>
    <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-row-divider v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
      <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
        <div class="v-skeleton-loader__text v-skeleton-loader__bone">
        </div>
      </div>
    </div>
    <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 26`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__table-row v-skeleton-loader__bone">
    <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
      <div class="v-skeleton-loader__text v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
      <div class="v-skeleton-loader__text v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
      <div class="v-skeleton-loader__text v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
      <div class="v-skeleton-loader__text v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
      <div class="v-skeleton-loader__text v-skeleton-loader__bone">
      </div>
    </div>
    <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
      <div class="v-skeleton-loader__text v-skeleton-loader__bone">
      </div>
    </div>
  </div>
  <div class="v-skeleton-loader__divider v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 27`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
  <div class="v-skeleton-loader__table-cell v-skeleton-loader__bone">
    <div class="v-skeleton-loader__text v-skeleton-loader__bone">
    </div>
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 28`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 29`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
  <div class="v-skeleton-loader__avatar v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should generate a skeleton recursive tree 30`] = `
<div aria-busy="true"
     aria-live="polite"
     role="alert"
     class="v-skeleton-loader v-skeleton-loader--is-loading theme--light"
>
  <div class="v-skeleton-loader__text v-skeleton-loader__bone">
  </div>
</div>
`;

exports[`VSkeletonLoader.ts should remove transition when loading content 1`] = `
Object {
  "display": "",
  "transition": "",
}
`;

exports[`VSkeletonLoader.ts should remove transition when loading content 2`] = `
Object {
  "display": "inline-block",
  "transition": ".3s ease",
}
`;
