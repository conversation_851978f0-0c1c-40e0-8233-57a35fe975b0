// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VSlideGroup.ts should match snapshot in rtl 1`] = `
<div class="v-item-group theme--light v-slide-group v-slide-group--has-affixes">
  <div class="v-slide-group__prev">
    <fade-transition-stub origin="top center 0">
      <vuecomponent-stub>
        $next
      </vuecomponent-stub>
    </fade-transition-stub>
  </div>
  <div class="v-slide-group__wrapper">
    <div class="v-slide-group__content">
    </div>
  </div>
  <div class="v-slide-group__next">
    <fade-transition-stub origin="top center 0">
      <vuecomponent-stub>
        $prev
      </vuecomponent-stub>
    </fade-transition-stub>
  </div>
</div>
`;

exports[`VSlideGroup.ts should match snapshot in rtl 2`] = `
<div class="v-item-group theme--light v-slide-group v-slide-group--has-affixes">
  <div class="v-slide-group__prev">
    <fade-transition-stub origin="top center 0">
      <vuecomponent-stub>
        $prev
      </vuecomponent-stub>
    </fade-transition-stub>
  </div>
  <div class="v-slide-group__wrapper">
    <div class="v-slide-group__content">
    </div>
  </div>
  <div class="v-slide-group__next">
    <fade-transition-stub origin="top center 0">
      <vuecomponent-stub>
        $next
      </vuecomponent-stub>
    </fade-transition-stub>
  </div>
</div>
`;
