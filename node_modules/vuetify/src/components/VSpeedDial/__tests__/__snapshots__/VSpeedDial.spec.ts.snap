// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VSpeedDial.ts should render active component and match snapshot 1`] = `
<div class="v-speed-dial v-speed-dial--direction-top v-speed-dial--is-active">
  <span class="v-speed-dial__list">
    <span>
      test
    </span>
  </span>
</div>
`;

exports[`VSpeedDial.ts should render component and match snapshot 1`] = `
<div class="v-speed-dial v-speed-dial--direction-top">
  <span class="v-speed-dial__list">
  </span>
</div>
`;

exports[`VSpeedDial.ts should render component with custom direction and match snapshot 1`] = `
<div class="v-speed-dial v-speed-dial--direction-right">
  <span class="v-speed-dial__list">
  </span>
</div>
`;
