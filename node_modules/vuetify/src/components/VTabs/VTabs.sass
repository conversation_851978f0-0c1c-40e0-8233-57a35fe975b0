// Imports
@import './_variables.scss'

+theme(v-tabs) using ($material)
  > .v-tabs-bar
    background-color: map-get($material, $tabs-bar-background-color)

    .v-tab:not(.v-tab--active),
    .v-tab:not(.v-tab--active) > .v-icon,
    .v-tab:not(.v-tab--active) > .v-btn,
    .v-tab--disabled
      color: map-get($material, 'tabs')

  .v-tab
    +states($material)

+theme(v-tabs-items) using ($material)
  background-color: map-get($material, 'cards')

.v-tabs-bar
  &.primary,
  &.secondary,
  &.accent,
  &.success,
  &.error,
  &.warning,
  &.info
    .v-tab,
    .v-tabs-slider
      color: map-deep-get($material-dark, 'text', 'primary')

// Block
.v-tabs
  flex: 1 1 auto
  width: 100%

  .v-menu__activator
    height: 100%

  &.v.tabs--vertical.v-tabs--right
    flex-direction: row-reverse

  &:not(.v-tabs--vertical)
    .v-tab
      white-space: normal

    // https://github.com/vuetifyjs/vuetify/issues/8294
    &.v-tabs--right
      > .v-slide-group--is-overflowing.v-tabs-bar--is-mobile:not(.v-slide-group--has-affixes)
        .v-slide-group__next
          display: initial
          visibility: hidden

    // https://github.com/vuetifyjs/vuetify/issues/6932
    &:not(.v-tabs--right)
      > .v-slide-group--is-overflowing.v-tabs-bar--is-mobile:not(.v-slide-group--has-affixes)
        .v-slide-group__prev
          display: initial
          visibility: hidden

// Element
.v-tabs-bar
  border-radius: inherit
  height: $tabs-bar-height

  &.v-item-group > *
    cursor: initial

.v-tab
  align-items: center
  cursor: pointer
  display: flex
  flex: 0 1 auto
  font-size: $tab-font-size
  font-weight: $tab-font-weight
  justify-content: center
  letter-spacing: $tabs-item-letter-spacing
  line-height: $tab-line-height
  min-width: $tabs-item-min-width
  max-width: $tabs-item-max-width
  outline: none
  padding: $tabs-item-padding
  position: relative
  text-align: center
  text-decoration: none
  text-transform: uppercase
  transition: none
  user-select: none

  // Needs increased specificity
  &.v-tab
    color: inherit

  &:before
    background-color: currentColor
    bottom: 0
    content: ''
    left: 0
    opacity: 0
    pointer-events: none
    position: absolute
    right: 0
    top: 0
    transition: $primary-transition


.v-tabs-slider
  background-color: currentColor
  height: 100%
  width: 100%

  &-wrapper
    bottom: 0
    margin: 0 !important
    position: absolute
    transition: $primary-transition
    z-index: 1

// Modifier
.v-tabs--align-with-title > .v-tabs-bar:not(.v-tabs-bar--show-arrows):not(.v-slide-group--is-overflowing) > .v-slide-group__wrapper > .v-tabs-bar__content
  & > .v-tab:first-child,
  & > .v-tabs-slider-wrapper + .v-tab
    +ltr()
      margin-left: $tabs-item-align-with-title-margin

    +rtl()
      margin-right: $tabs-item-align-with-title-margin

.v-tabs--fixed-tabs > .v-tabs-bar,
.v-tabs--centered > .v-tabs-bar
  .v-tabs-bar__content > *:last-child
    +ltr()
      margin-right: auto

    +rtl()
      margin-left: auto

  .v-tabs-bar__content > *:first-child:not(.v-tabs-slider-wrapper),
  .v-tabs-slider-wrapper + *
    +ltr()
      margin-left: auto

    +rtl()
      margin-right: auto

.v-tabs--fixed-tabs > .v-tabs-bar
  .v-tab
    flex: 1 1 auto
    width: 100%

.v-tabs--grow > .v-tabs-bar
  .v-tab
    flex: 1 0 auto
    max-width: none

.v-tabs--icons-and-text > .v-tabs-bar
  height: $tabs-icons-and-text-bar-height

  .v-tab
    flex-direction: column-reverse

    > *:first-child
      margin-bottom: $tabs-icons-and-text-first-tab-margin-bottom

.v-tabs--overflow > .v-tabs-bar
  .v-tab
    flex: 1 0 auto

.v-tabs--right > .v-tabs-bar
  .v-tab:first-child,
  .v-tabs-slider-wrapper + .v-tab
    +ltr()
      margin-left: auto

    +rtl()
      margin-right: auto

  .v-tab:last-child
    +ltr()
      margin-right: 0

    +rtl()
      margin-left: 0

.v-tabs--vertical
  display: flex

  & > .v-tabs-bar
    flex: 1 0 auto
    height: auto

    .v-slide-group__next,
    .v-slide-group__prev
      display: none

    .v-tabs-bar__content
      flex-direction: column

    .v-tab
      height: $tabs-item-vertical-height

    .v-tabs-slider
      height: 100%

  & > .v-window
    flex: 0 1 100%

  &.v-tabs--icons-and-text
    & > .v-tabs-bar
      .v-tab
        height: $tabs-item-vertical-icons-and-text-height

.v-tab--active
  color: inherit

  &.v-tab:not(:focus)::before
    opacity: 0

  .v-icon,
  .v-btn.v-btn--flat
    color: inherit

.v-tab--disabled
  opacity: $tab-disabled-opacity

  &,
  & *
    pointer-events: none
