// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTabs.ts should render generic elements in the tab container 1`] = `
<div class="v-tabs theme--light">
  <div role="tablist"
       class="v-item-group theme--light v-slide-group v-tabs-bar primary--text"
  >
    <div class="v-slide-group__prev v-slide-group__prev--disabled">
    </div>
    <div class="v-slide-group__wrapper">
      <div class="v-slide-group__content v-tabs-bar__content">
        <div class="test-element">
          foobar
        </div>
      </div>
    </div>
    <div class="v-slide-group__next v-slide-group__next--disabled">
    </div>
  </div>
</div>
`;
