@import './_mixins.sass'
@import './_variables.scss'

+theme(v-text-field) using ($material)
  > .v-input__control > .v-input__slot:before
    border-color: map-get($material, 'input-bottom-line')

  &:not(.v-input--has-state):hover > .v-input__control > .v-input__slot:before
    border-color: map-deep-get($material, 'text', 'primary')

  &.v-input--is-disabled .v-input__slot::before
    border-image: repeating-linear-gradient(to right, #{map-deep-get($material, 'text', 'disabled')} 0px, #{map-deep-get($material, 'text', 'disabled')} 2px, transparent 2px, transparent 4px) 1 repeat

  &--filled
    > .v-input__control > .v-input__slot
      background: map-deep-get($material, 'text-fields', 'filled')

    &:not(.v-input--is-focused):not(.v-input--has-state)
      > .v-input__control > .v-input__slot:hover
        background: map-deep-get($material, 'text-fields', 'filled-hover')

  &--solo
    > .v-input__control > .v-input__slot
      background: map-get($material, 'cards')

  &--solo-inverted
    > .v-input__control > .v-input__slot
      background: map-deep-get($material, 'inputs', 'solo-inverted')

    &.v-input--is-focused
      > .v-input__control > .v-input__slot
        background: map-deep-get($material, 'inputs', 'solo-inverted-focused')

        input
          color: map-deep-get($material, 'inputs', 'solo-inverted-focused-text')

        input::placeholder
          color: map-deep-get($material, 'inputs', 'solo-inverted-focused-placeholder')

        .v-label
          color: map-deep-get($material, 'inputs', 'solo-inverted-focused-label')

  &--outlined
    &:not(.v-input--is-focused)
      &:not(.v-input--has-state)
        > .v-input__control > .v-input__slot fieldset
          color: map-deep-get($material, 'text-fields', 'outlined')

        &:not(.v-input--is-disabled)
          > .v-input__control > .v-input__slot:hover fieldset
            color: map-deep-get($material, 'text-fields', 'outlined-hover')

      &.v-input--is-disabled
        > .v-input__control > .v-input__slot fieldset
          color: map-deep-get($material, 'text-fields', 'outlined-disabled')

.v-text-field
  padding-top: $text-field-active-label-height
  margin-top: $input-top-spacing - $text-field-active-label-height

  &__prefix,
  &__suffix
    line-height: $text-field-line-height
  input
    flex: 1 1 auto
    line-height: $text-field-line-height
    padding: $text-field-padding
    max-width: 100%
    min-width: 0px
    width: 100%

  fieldset,
  .v-input__control,
  .v-input__slot
    border-radius: inherit

  fieldset,
  .v-input__control
    color: currentColor

  &.v-input--has-state
    .v-input__control > .v-text-field__details > .v-counter
      color: currentColor

  &.v-input--is-disabled
    .v-input__control > .v-text-field__details > .v-counter,
    .v-input__control > .v-text-field__details > .v-messages
      color: currentColor

  &.v-input--dense
    padding-top: 0

    .v-label
      top: 4px

    &:not(.v-text-field--outlined)
      .v-text-field__prefix,
      .v-text-field__suffix,
      input
        padding: $text-field-dense-padding
      .v-text-field__prefix
        padding-right: $text-field-append-prepend-margin
      .v-text-field__suffix
        padding-left: $text-field-append-prepend-margin

    &[type=text]::-ms-clear
      display: none

    .v-input__prepend-inner,
    .v-input__append-inner
      margin-top: $text-field-dense-append-prepend-margin

  .v-input__prepend-inner,
  .v-input__append-inner
    align-self: flex-start
    display: inline-flex
    margin-top: $text-field-append-prepend-margin
    line-height: 1
    user-select: none

  .v-input__prepend-inner
    +ltr()
      margin-right: auto
      padding-right: $text-field-append-prepend-margin

    +rtl()
      margin-left: auto
      padding-left: $text-field-append-prepend-margin

  .v-input__append-inner
    +ltr()
      margin-left: auto
      padding-left: $text-field-append-prepend-margin

    +rtl()
      margin-right: auto
      padding-right: $text-field-append-prepend-margin

  .v-counter
    white-space: nowrap

    +ltr()
      margin-left: $text-field-counter-margin

    +rtl()
      margin-right: $text-field-counter-margin

  .v-label
    max-width: 90%
    overflow: hidden
    text-overflow: ellipsis
    top: $text-field-label-top // equal to the margin top of icons
    white-space: nowrap
    pointer-events: none

    +ltr()
      transform-origin: top left

    +rtl()
      transform-origin: top right

    &--active
      max-width: 133%
      transform: $text-field-label-active-transform
      pointer-events: auto // Show title attr on label hover

  & > .v-input__control > .v-input__slot
    cursor: text

    &:before,
    &:after
      bottom: -1px // Needed for browser autocomplete styles
      content: ''
      left: 0
      position: absolute
      transition: $primary-transition
      width: 100%

    &:before
      border-color: inherit
      border-style: solid
      border-width: thin 0 0 0

    &:after
      background-color: currentColor
      border-color: currentColor
      border-style: solid
      border-width: thin 0 thin 0
      transform: scaleX(0)

  &__details
    display: flex
    flex: 1 0 auto
    max-width: 100%
    min-height: $text-field-details-min-height
    overflow: hidden

  &__prefix,
  &__suffix
    align-self: center
    cursor: default
    transition: color $primary-transition
    white-space: nowrap

  &__prefix
    +ltr()
      text-align: right
      padding-right: $text-field-append-prepend-margin

    +rtl()
      text-align: left
      padding-left: $text-field-append-prepend-margin

  &__suffix
    white-space: nowrap

    +ltr()
      padding-left: $text-field-append-prepend-margin

    +rtl()
      padding-right: $text-field-append-prepend-margin

  &--reverse
    .v-text-field__prefix

      +ltr()
        text-align: left
        padding-right: 0
        padding-left: $text-field-append-prepend-margin

      +rtl()
        text-align: right
        padding-right: $text-field-append-prepend-margin
        padding-left: 0

    .v-text-field__suffix
      +ltr()
        padding-left: 0
        padding-right: $text-field-append-prepend-margin

      +rtl()
        padding-left: $text-field-append-prepend-margin
        padding-right: 0

  & > .v-input__control > .v-input__slot > .v-text-field__slot
    display: flex
    flex: 1 1 auto
    position: relative

  &:not(.v-text-field--is-booted)
    .v-label,
    legend
      transition: none

  &--filled,
  &--full-width,
  &--outlined
    position: relative

    & > .v-input__control > .v-input__slot
      align-items: stretch
      min-height: $text-field-filled-full-width-outlined-slot-min-height

    &.v-input--dense
      > .v-input__control > .v-input__slot
        min-height: $text-field-filled-full-width-outlined-dense-slot-min-height

      &.v-text-field--single-line,
      &.v-text-field--outlined,
      &.v-text-field--outlined.v-text-field--filled
        > .v-input__control > .v-input__slot
          min-height: $text-field-filled-full-width-outlined-single-line-slot-min-height

  &--outlined
    border-radius: $text-field-border-radius

  &--full-width,
  &--enclosed
    +prepend-append-margin-top($text-field-enclosed-prepend-append-margin-top)

    &.v-input--dense:not(.v-text-field--solo)
      +prepend-append-margin-top($text-field-dense-prepend-append-margin-top)

      &.v-text-field--single-line
        +prepend-append-margin-top($text-field-single-line-prepend-append-margin-top)

      &.v-text-field--outlined
        +prepend-append-margin-top($text-field-outlined-prepend-append-margin-top)

  &--filled,
  &--full-width
    .v-label
      top: $text-field-filled-full-width-label-top

      &--active
        transform: $text-field-filled-full-width-label-active-transform

    &.v-input--dense
      .v-label
        top: $text-field-dense-label-top

        &--active
          transform: $text-field-dense-label-active-transform

      &.v-text-field--single-line
        .v-label
          top: $text-field-single-line-label-top

  &--filled
    border-radius: $text-field-filled-border-radius

    &:not(.v-text-field--single-line)
      input
        margin-top: $text-field-filled-margin-top

    &.v-input--dense:not(.v-text-field--single-line)
      &.v-text-field--outlined
        input
          margin-top: 0

    .v-text-field__prefix,
    .v-text-field__suffix
      max-height: 32px
      margin-top: 20px

  &--full-width
    border-radius: 0

  &--outlined,
  &--single-line
    .v-text-field__slot
      align-items: center

  &.v-text-field--enclosed
    margin: 0
    padding: 0

    &.v-text-field--single-line
      .v-text-field__prefix,
      .v-text-field__suffix
        margin-top: 0

    &:not(.v-text-field--filled)
      .v-progress-linear__background
        display: none

    &:not(.v-text-field--rounded) > .v-input__control > .v-input__slot,
    .v-text-field__details
      padding: $text-field-enclosed-details-padding

    .v-text-field__details
      padding-top: $text-field-details-padding-top
      margin-bottom: $text-field-details-margin-bottom

  &--reverse
    input
      +ltr()
        text-align: right

      +rtl()
        text-align: left

    .v-label
      +ltr()
        transform-origin: top right

      +rtl()
        transform-origin: top left

    & > .v-input__control > .v-input__slot,
    .v-text-field__slot
      flex-direction: row-reverse

  &--outlined,
  &--solo,
  &--rounded
    & > .v-input__control > .v-input__slot
      &:before,
      &:after
        display: none

  &--outlined,
  &--solo
    border-radius: $text-field-border-radius

  &--outlined
    margin-bottom: $text-field-outlined-margin-bottom
    transition: border $primary-transition

    +label-position($text-field-outlined-label-position-x, $text-field-outlined-label-position-y)

    &.v-input--dense
      +label-position($text-field-outlined-dense-label-position-x, $text-field-outlined-dense-label-position-y)

    fieldset
      border-collapse: collapse
      border-color: currentColor
      border-style: solid
      border-width: $text-field-outlined-fieldset-border-width
      bottom: 0
      left: 0
      pointer-events: none
      position: absolute
      right: 0
      top: $text-field-outlined-fieldset-top
      transition-duration: 0.15s
      transition-property: color
      transition-timing-function: map-get($transition, 'fast-in-fast-out')

      +ltr()
        padding-left: $text-field-outlined-fieldset-padding

      +rtl()
        padding-right: $text-field-outlined-fieldset-padding


    &.v-text-field--reverse fieldset
      +ltr()
        padding-right: $text-field-outlined-fieldset-padding

      +rtl()
        padding-left: $text-field-outlined-fieldset-padding

    legend
      line-height: $text-field-outlined-legend-line-height
      padding: 0
      transition: width $primary-transition

      +ltr()
        text-align: left

      +rtl()
        text-align: right

    &.v-text-field--reverse legend
      +ltr()
        margin-left: auto

      +rtl()
        margin-right: auto

    &.v-text-field--rounded
      legend
        +ltr()
          margin-left: $text-field-rounded-legend-margin

        +rtl()
          margin-right: $text-field-rounded-legend-margin

    & > .v-input__control > .v-input__slot
      background: transparent

    .v-text-field__prefix
      max-height: $text-field-prefix-max-height

    .v-input__prepend-outer,
    .v-input__append-outer
      margin-top: $text-field-outlined-append-prepend-outer-margin-top // 2px for border

    &.v-input--is-focused,
    &.v-input--has-state
      fieldset
        border: $text-field-outlined-fieldset-border

  &--rounded
    border-radius: $text-field-rounded-border-radius

    > .v-input__control > .v-input__slot
      padding: $text-field-outlined-rounded-slot-padding

  &--shaped
    border-radius: $text-field-shaped-border-radius

  &.v-text-field--solo
    .v-label
      top: $text-field-solo-label-top

    .v-input__control
      min-height: $text-field-solo-control-min-height
      padding: 0

      input
        caret-color: auto

    &.v-input--dense > .v-input__control
      min-height: $text-field-solo-dense-control-min-height

    &:not(.v-text-field--solo-flat) > .v-input__control > .v-input__slot
      +elevation(2)

    .v-input__append-inner,
    .v-input__prepend-inner
      align-self: center
      margin-top: 0

    +outer-margin-top($text-field-solo-outer-margin-top)

    &.v-input--dense
      +outer-margin-top($text-field-solo-dense-outer-margin-top)

  &.v-input--is-focused > .v-input__control > .v-input__slot:after
    transform: scaleX(1)

  &.v-input--has-state > .v-input__control > .v-input__slot:before
    border-color: currentColor

  .v-input__icon--clear
    opacity: 0
    transition: opacity $primary-transition

  &.v-input--is-focused,
  &.v-input--is-dirty:hover
    .v-input__icon--clear
      opacity: 1

  // TODO: where are the corresponding rules for LTR?
  //
  // +rtl()
  //   &--enclosed
  //     .v-input__append-outer
  //       margin-left: 0
  //       margin-right: 16px

  //     .v-input__prepend-outer
  //       margin-left: 16px
  //       margin-right: 0
