// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTextField.ts should apply theme to label, counter, messages and icons 1`] = `
<div class="v-input theme--light v-text-field">
  <div class="v-input__prepend-outer">
    <div class="v-input__icon v-input__icon--prepend">
      <i aria-hidden="true"
         class="v-icon notranslate material-icons theme--light"
      >
        prepend
      </i>
    </div>
  </div>
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-input__prepend-inner">
        <div class="v-input__icon v-input__icon--prepend-inner">
          <i aria-hidden="true"
             class="v-icon notranslate prepend prepend-inner theme--light"
          >
          </i>
        </div>
      </div>
      <div class="v-text-field__slot">
        <label for="input-129"
               class="v-label theme--light"
               style="left: 0px; position: absolute;"
        >
          foo
        </label>
        <input id="input-129"
               type="text"
        >
      </div>
      <div class="v-input__append-inner">
        <div class="v-input__icon v-input__icon--append">
          <i aria-hidden="true"
             class="v-icon notranslate material-icons theme--light"
          >
            append
          </i>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
          <div class="v-messages__message">
            bar
          </div>
        </span>
      </div>
      <div class="v-counter theme--light">
        0
      </div>
    </div>
  </div>
  <div class="v-input__append-outer">
    <div class="v-input__icon v-input__icon--append-outer">
      <i aria-hidden="true"
         class="v-icon notranslate append append-outer theme--light"
      >
      </i>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should have prefix and suffix 1`] = `
<div class="v-input theme--light v-text-field v-text-field--prefix">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <div class="v-text-field__prefix">
          $
        </div>
        <input id="input-87"
               type="text"
        >
        <div class="v-text-field__suffix">
          .com
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should hide messages if no messages and hide-details is auto 1`] = `
<div class="v-input v-input--hide-details theme--light v-text-field">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input id="input-141"
               type="text"
        >
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should hide messages if no messages and hide-details is auto 2`] = `
<div class="v-input theme--light v-text-field">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input id="input-141"
               type="text"
        >
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
      <div class="v-counter theme--light">
        0 / 7
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should hide messages if no messages and hide-details is auto 3`] = `
<div class="v-input v-input--has-state theme--light v-text-field error--text">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input id="input-141"
               type="text"
        >
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light error--text"
           role="alert"
      >
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
          <div class="v-messages__message">
            required
          </div>
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should not display counter when set to false/undefined/null 1`] = `
<div class="v-input theme--light v-text-field">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input maxlength="50"
               id="input-34"
               type="text"
        >
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
      <div class="v-counter theme--light">
        0 / 50
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should not display counter when set to false/undefined/null 2`] = `
<div class="v-input theme--light v-text-field">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input maxlength="50"
               id="input-34"
               type="text"
        >
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should render component and match snapshot 1`] = `
<div class="v-input theme--light v-text-field">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input id="input-1"
               type="text"
        >
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should render component with async loading and custom progress and match snapshot 1`] = `
<div class="v-input v-input--is-loading theme--light v-text-field">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input id="input-72"
               type="text"
        >
      </div>
      <div role="progressbar"
           aria-valuemin="0"
           aria-valuemax="100"
           class="v-progress-linear v-progress-linear--visible theme--light"
           style="height: 7px;"
      >
        <div class="v-progress-linear__background orange"
             style="opacity: 0.3; left: 0%; width: 100%;"
        >
        </div>
        <div class="v-progress-linear__buffer">
        </div>
        <div class="v-progress-linear__indeterminate v-progress-linear__indeterminate--active">
          <div class="v-progress-linear__indeterminate long orange">
          </div>
          <div class="v-progress-linear__indeterminate short orange">
          </div>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTextField.ts should render component with async loading and match snapshot 1`] = `
<div class="v-input v-input--is-loading theme--light v-text-field">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <input id="input-66"
               type="text"
        >
      </div>
      <div role="progressbar"
           aria-valuemin="0"
           aria-valuemax="100"
           class="v-progress-linear v-progress-linear--absolute theme--light"
           style="height: 2px;"
      >
        <div class="v-progress-linear__background primary"
             style="opacity: 0.3; left: 0%; width: 100%;"
        >
        </div>
        <div class="v-progress-linear__buffer">
        </div>
        <div class="v-progress-linear__indeterminate v-progress-linear__indeterminate--active">
          <div class="v-progress-linear__indeterminate long primary">
          </div>
          <div class="v-progress-linear__indeterminate short primary">
          </div>
        </div>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;
