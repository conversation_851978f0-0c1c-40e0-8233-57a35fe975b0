// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTextarea.ts should calculate element height when using auto-grow prop 1`] = `
<div class="v-input v-textarea v-textarea--auto-grow v-textarea--no-resize v-input--is-label-active v-input--is-dirty v-input--is-focused theme--light v-text-field primary--text">
  <div class="v-input__control">
    <div class="v-input__slot">
      <div class="v-text-field__slot">
        <textarea id="input-1"
                  rows="5"
                  style="height: 120px;"
        >
        </textarea>
      </div>
    </div>
    <div class="v-text-field__details">
      <div class="v-messages theme--light primary--text">
        <span name="message-transition"
              tag="div"
              class="v-messages__wrapper"
        >
        </span>
      </div>
    </div>
  </div>
</div>
`;
