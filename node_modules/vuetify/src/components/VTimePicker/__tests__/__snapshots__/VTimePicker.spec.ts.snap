// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTimePicker.ts should accept a date object for a value 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          12
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(0deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should accept a date object for a value. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          12
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(0deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should accept a value 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should accept a value. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          34
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should change am/pm when updated from model 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          PM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn">
          AM
        </div>
        <div class="v-picker__title__btn v-picker__title__btn--active">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should change am/pm when updated from model. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          PM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn">
          AM
        </div>
        <div class="v-picker__title__btn v-picker__title__btn--active">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render colored time picker 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title orange darken-1">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm orange--text text--darken-1">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand orange darken-1"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active orange darken-1"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render colored time picker, header 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title orange darken-1">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand primary"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active primary"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render colored time picker, header. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title orange darken-1">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand primary"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active primary"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render colored time picker. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title orange darken-1">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          00
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm orange--text text--darken-1">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand orange darken-1"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active orange darken-1"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render component with elevation 1`] = `
<div class="v-picker v-card v-picker--time theme--light elevation-15">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render component with elevation. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--light elevation-15">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          34
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render dark time picker 1`] = `
<div class="v-picker v-card v-picker--time theme--dark">
  <div class="v-picker__title">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          --
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          --
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--dark"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock v-time-picker-clock--indeterminate theme--dark">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand"
               style="transform: rotate(0deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render dark time picker. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--dark">
  <div class="v-picker__title">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          --
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          --
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          --
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--dark"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock v-time-picker-clock--indeterminate theme--dark">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand"
               style="transform: rotate(0deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render disabled component 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn v-picker__title__btn--readonly">
          12
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
          AM
        </div>
        <div class="v-picker__title__btn v-picker__title__btn--readonly">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active v-time-picker-clock__item--disabled accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render disabled component. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn v-picker__title__btn--readonly">
          12
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn v-picker__title__btn--readonly">
          34
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
          AM
        </div>
        <div class="v-picker__title__btn v-picker__title__btn--readonly">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active v-time-picker-clock__item--disabled accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--disabled"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render flat component 1`] = `
<div class="v-picker v-card v-picker--time v-picker--flat theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render flat component. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time v-picker--flat theme--light">
  <div class="v-picker__title primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          34
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render landscape component 1`] = `
<div class="v-picker v-card v-picker--time v-picker--landscape theme--light">
  <div class="v-picker__title v-picker__title--landscape primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTimePicker.ts should render landscape component. with useSeconds 1`] = `
<div class="v-picker v-card v-picker--time v-picker--landscape theme--light">
  <div class="v-picker__title v-picker__title--landscape primary">
    <div class="v-time-picker-title">
      <div class="v-time-picker-title__time">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          9
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          12
        </div>
        <span>
          :
        </span>
        <div class="v-picker__title__btn">
          34
        </div>
      </div>
      <div class="v-time-picker-title__ampm v-time-picker-title__ampm--readonly">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
      </div>
    </div>
  </div>
  <div class="v-picker__body theme--light"
       style="width: 290px;"
  >
    <div class="v-time-picker-clock__container">
      <div class="v-time-picker-clock__ampm primary--text">
        <div class="v-picker__title__btn v-picker__title__btn--active">
          AM
        </div>
        <div class="v-picker__title__btn">
          PM
        </div>
      </div>
      <div class="v-time-picker-clock theme--light">
        <div class="v-time-picker-clock__inner">
          <div class="v-time-picker-clock__hand accent"
               style="transform: rotate(270deg) scaleY(1);"
          >
          </div>
          <span class="v-time-picker-clock__item"
                style="left: 50%; top: 0%;"
          >
            <span>
              12
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75%; top: 6.698729810778062%;"
          >
            <span>
              1
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922192%; top: 24.999999999999993%;"
          >
            <span>
              2
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 100%; top: 50%;"
          >
            <span>
              3
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 93.30127018922194%; top: 74.99999999999999%;"
          >
            <span>
              4
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 75.00000000000001%; top: 93.30127018922192%;"
          >
            <span>
              5
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 50.00000000000001%; top: 100%;"
          >
            <span>
              6
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 25.000000000000014%; top: 93.30127018922194%;"
          >
            <span>
              7
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.698729810778076%; top: 75.00000000000003%;"
          >
            <span>
              8
            </span>
          </span>
          <span class="v-time-picker-clock__item v-time-picker-clock__item--active accent"
                style="left: 0%; top: 50.00000000000001%;"
          >
            <span>
              9
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 6.6987298107780475%; top: 25.000000000000032%;"
          >
            <span>
              10
            </span>
          </span>
          <span class="v-time-picker-clock__item"
                style="left: 24.99999999999998%; top: 6.698729810778083%;"
          >
            <span>
              11
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;
