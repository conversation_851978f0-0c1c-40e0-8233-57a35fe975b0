// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTimePickerTitle.ts should render component in 12hr 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn">
      2
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      13
    </div>
  </div>
  <div class="v-time-picker-title__ampm">
    <div class="v-picker__title__btn">
      AM
    </div>
    <div class="v-picker__title__btn v-picker__title__btn--active">
      PM
    </div>
  </div>
</div>
`;

exports[`VTimePickerTitle.ts should render component in 12hr. with useSeconds 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn">
      2
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      13
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      25
    </div>
  </div>
  <div class="v-time-picker-title__ampm">
    <div class="v-picker__title__btn">
      AM
    </div>
    <div class="v-picker__title__btn v-picker__title__btn--active">
      PM
    </div>
  </div>
</div>
`;

exports[`VTimePickerTitle.ts should render component in 24hr 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn">
      14
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      13
    </div>
  </div>
</div>
`;

exports[`VTimePickerTitle.ts should render component in 24hr. with useSeconds 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn">
      14
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      13
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      25
    </div>
  </div>
</div>
`;

exports[`VTimePickerTitle.ts should render component when selecting hour 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn v-picker__title__btn--active">
      14
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      13
    </div>
  </div>
</div>
`;

exports[`VTimePickerTitle.ts should render component when selecting hour. with useSeconds 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn v-picker__title__btn--active">
      14
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      13
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn">
      25
    </div>
  </div>
</div>
`;

exports[`VTimePickerTitle.ts should render disabled component 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn v-picker__title__btn--readonly">
      2
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn v-picker__title__btn--readonly">
      13
    </div>
  </div>
  <div class="v-time-picker-title__ampm">
    <div class="v-picker__title__btn v-picker__title__btn--readonly">
      AM
    </div>
    <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
      PM
    </div>
  </div>
</div>
`;

exports[`VTimePickerTitle.ts should render disabled component. with useSeconds 1`] = `
<div class="v-time-picker-title">
  <div class="v-time-picker-title__time">
    <div class="v-picker__title__btn v-picker__title__btn--readonly">
      2
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn v-picker__title__btn--readonly">
      13
    </div>
    <span>
      :
    </span>
    <div class="v-picker__title__btn v-picker__title__btn--readonly">
      --
    </div>
  </div>
  <div class="v-time-picker-title__ampm">
    <div class="v-picker__title__btn v-picker__title__btn--readonly">
      AM
    </div>
    <div class="v-picker__title__btn v-picker__title__btn--active v-picker__title__btn--readonly">
      PM
    </div>
  </div>
</div>
`;
