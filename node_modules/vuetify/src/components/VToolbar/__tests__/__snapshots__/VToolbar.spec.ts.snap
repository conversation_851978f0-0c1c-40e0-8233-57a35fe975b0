// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VToolbar.ts should render an extended toolbar 1`] = `
<header class="v-sheet theme--light v-toolbar v-toolbar--extended"
        style="height: 112px;"
>
  <div class="v-toolbar__content"
       style="height: 64px;"
  >
  </div>
  <div class="v-toolbar__extension"
       style="height: 48px;"
  >
  </div>
</header>
`;

exports[`VToolbar.ts should render an extended toolbar with specific height 1`] = `
<header class="v-sheet theme--light v-toolbar v-toolbar--extended"
        style="height: 106px;"
>
  <div class="v-toolbar__content"
       style="height: 64px;"
  >
  </div>
  <div class="v-toolbar__extension"
       style="height: 42px;"
  >
  </div>
</header>
`;
