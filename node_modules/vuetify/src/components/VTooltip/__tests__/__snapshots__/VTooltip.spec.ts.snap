// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTooltip should render component with bottom and match snapshot 1`] = `
<span class="v-tooltip v-tooltip--bottom">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with bottom and match snapshot 2`] = `
<span class="v-tooltip v-tooltip--bottom">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with custom eager and match snapshot 1`] = `
<span class="v-tooltip">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with left and match snapshot 1`] = `
<span class="v-tooltip v-tooltip--left">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with left and match snapshot 2`] = `
<span class="v-tooltip v-tooltip--left">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with min/max width and match snapshot 1`] = `
<span class="v-tooltip">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with right and match snapshot 1`] = `
<span class="v-tooltip v-tooltip--right">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with right and match snapshot 2`] = `
<span class="v-tooltip v-tooltip--right">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with top and match snapshot 1`] = `
<span class="v-tooltip v-tooltip--top">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with top and match snapshot 2`] = `
<span class="v-tooltip v-tooltip--top">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with value=true and match snapshot 1`] = `
<span class="v-tooltip">
  <span>
    activator
  </span>
</span>
`;

exports[`VTooltip should render component with zIndex prop and match snapshot 1`] = `
<span class="v-tooltip">
</span>
`;
