@import './_variables.scss'
@import './_mixins.sass'

// Theme
+theme(v-treeview) using ($material)
  color: map-deep-get($material, 'text', 'primary')

  &--hoverable .v-treeview-node__root,
  .v-treeview-node--click > .v-treeview-node__root
    +states($material)

  .v-treeview-node__root.v-treeview-node--active
    +active-states($material)

  .v-treeview-node--disabled
    > .v-treeview-node__root > .v-treeview-node__content
      color: map-deep-get($material, 'text', 'disabled') !important

.v-treeview-node
  &.v-treeview-node--shaped
    +treeview-shaped($treeview-node-height, $treeview-node-shaped-margin)

  &.v-treeview-node--rounded
    +treeview-rounded($treeview-node-height, $treeview-node-shaped-margin)

  &--click
    > .v-treeview-node__root,
    > .v-treeview-node__root > .v-treeview-node__content > *
      cursor: pointer
      user-select: none

  &.v-treeview-node--active .v-treeview-node__content .v-icon
    color: inherit

.v-treeview-node__root
  display: flex
  align-items: center
  min-height: $treeview-node-height
  padding-left: $treeview-node-padding
  padding-right: $treeview-node-padding
  position: relative

  &::before
    background-color: currentColor
    bottom: 0
    content: ''
    left: 0
    opacity: 0
    pointer-events: none
    position: absolute
    right: 0
    top: 0
    transition: $primary-transition

  // Fix for IE11 where min-height does not work with
  // align-items: center in flex containers
  // https://github.com/philipwalton/flexbugs/issues/231
  &::after
    content: ''
    font-size: 0
    min-height: inherit

.v-treeview-node__children
  transition: all $treeview-transition

.v-treeview--dense
  .v-treeview-node__root
    min-height: $treeview-node-height-dense

  &.v-treeview-node--shaped
    +treeview-shaped($treeview-node-height-dense, $treeview-node-shaped-margin)

  &.v-treeview-node--rounded
    +treeview-rounded($treeview-node-height-dense, $treeview-node-shaped-margin)

.v-treeview-node__checkbox
  width: $treeview-node-level-width
  user-select: none

  +ltr()
    margin-left: $treeview-node-margin
  +rtl()
    margin-right: $treeview-node-margin

.v-treeview-node__toggle
  width: $treeview-node-level-width
  user-select: none

  &--loading
    animation: progress-circular-rotate 1s linear infinite

  +ltr()
    transform: rotate(-90deg)
    &--open
      transform: none

  +rtl()
    transform: rotate(90deg)
    &--open
      transform: none

.v-treeview-node__prepend
  min-width: $treeview-node-level-width

  +ltr()
    margin-right: $treeview-node-margin
  +rtl()
    margin-left: $treeview-node-margin

.v-treeview-node__append
  min-width: $treeview-node-level-width

  +ltr()
    margin-left: $treeview-node-margin
  +rtl()
    margin-right: $treeview-node-margin

.v-treeview-node__level
  width: $treeview-node-level-width

.v-treeview-node__label
  flex: 1
  font-size: $treeview-label-font-size
  overflow: hidden
  text-overflow: ellipsis
  white-space: nowrap

.v-treeview-node__content
  align-items: center
  display: flex
  flex-basis: 0%
  flex-grow: 1
  flex-shrink: 0
  min-width: 0

  // TODO: this is temporary fix for d-flex * shenanigans
  .v-btn
    flex-grow: 0 !important
    flex-shrink: 1 !important

  +ltr()
    margin-left: $treeview-node-margin
  +rtl()
    margin-right: $treeview-node-margin
