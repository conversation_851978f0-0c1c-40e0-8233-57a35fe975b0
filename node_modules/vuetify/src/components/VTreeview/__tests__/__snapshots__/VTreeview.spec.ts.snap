// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTreeView.ts should emit opened node when using open-on-click and load-children 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--click"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should emit opened node when using open-on-click and load-children 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--click"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--loading"
      >
        $loading
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should filter items 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          one
        </div>
      </div>
    </div>
  </div>
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          two
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should filter items 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          two
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should filter items using custom item filter 1`] = `
<div class="v-treeview theme--light">
</div>
`;

exports[`VTreeView.ts should filter items using custom item filter 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          one
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should handle initial active/open/selected values when using return-object prop 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf v-treeview-node--selected"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <button type="button"
              class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light accent--text"
      >
        $checkboxOn
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          One
        </div>
      </div>
    </div>
  </div>
  <div aria-expanded="true"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        $subgroup
      </button>
      <button type="button"
              class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
      >
        $checkboxOff
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Two
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf"
      >
        <div class="v-treeview-node__root v-treeview-node--active primary--text">
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__level">
          </div>
          <button type="button"
                  class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
          >
            $checkboxOff
          </button>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Three
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should handle replacing items with new array of equal length 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          one
        </div>
      </div>
    </div>
  </div>
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          two
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should handle replacing items with new array of equal length 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          one
        </div>
      </div>
    </div>
  </div>
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          three
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should load children when expanding 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should load children when expanding 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--loading"
      >
        $loading
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should load children when selecting, but not render 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <button type="button"
              class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
      >
        $checkboxOff
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should load children when selecting, but not render 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--selected"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <button type="button"
              class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light accent--text"
      >
        $checkboxOn
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should not show expand icon when children is empty 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should open all children when using open-all prop 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="true"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="true"
           class="v-treeview-node"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <button type="button"
                  class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
          >
            $subgroup
          </button>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
        <div class="v-treeview-node__children">
          <div aria-expanded="false"
               class="v-treeview-node v-treeview-node--leaf"
          >
            <div class="v-treeview-node__root">
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__content">
                <div class="v-treeview-node__label">
                  Grandchild
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should react to open changes 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="true"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="true"
           class="v-treeview-node"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <button type="button"
                  class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
          >
            $subgroup
          </button>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
        <div class="v-treeview-node__children">
          <div aria-expanded="false"
               class="v-treeview-node v-treeview-node--leaf"
          >
            <div class="v-treeview-node__root">
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__content">
                <div class="v-treeview-node__label">
                  Grandchild
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should react to open changes 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="true"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="false"
           class="v-treeview-node"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <button type="button"
                  class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
          >
            $subgroup
          </button>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should react to open changes 3`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="true"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="true"
           class="v-treeview-node"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <button type="button"
                  class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
          >
            $subgroup
          </button>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
        <div class="v-treeview-node__children">
          <div aria-expanded="false"
               class="v-treeview-node v-treeview-node--leaf"
          >
            <div class="v-treeview-node__root">
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__level">
              </div>
              <div class="v-treeview-node__content">
                <div class="v-treeview-node__label">
                  Grandchild
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should react to open changes 4`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should recalculate tree when loading async children using custom key 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--loading"
      >
        $loading
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          One
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should remove old nodes 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          one
        </div>
      </div>
    </div>
  </div>
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          two
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should remove old nodes 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          one
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should remove old nodes 3`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          one
        </div>
      </div>
    </div>
  </div>
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--leaf"
  >
    <div class="v-treeview-node__root">
      <div class="v-treeview-node__level">
      </div>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          three
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should render items 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should render items in dense mode 1`] = `
<div class="v-treeview v-treeview--dense theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should select all leaf nodes 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--selected"
  >
    <div class="v-treeview-node__root">
      <i role="button"
         class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        arrow_drop_down
      </i>
      <i role="button"
         class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light accent--text"
      >
        check_box
      </i>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should select only leaf nodes 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="true"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <i role="button"
         class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        arrow_drop_down
      </i>
      <i role="button"
         class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
      >
        $checkboxIndeterminate
      </i>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="false"
           class="v-treeview-node"
      >
        <div class="v-treeview-node__root">
          <i role="button"
             class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
          >
            arrow_drop_down
          </i>
          <i role="button"
             class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
          >
            check_box_outline_blank
          </i>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf v-treeview-node--selected"
      >
        <div class="v-treeview-node__root">
          <i role="button"
             class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light accent--text"
          >
            check_box
          </i>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should select only root node 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node v-treeview-node--selected"
  >
    <div class="v-treeview-node__root">
      <i role="button"
         class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        arrow_drop_down
      </i>
      <i role="button"
         class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light accent--text"
      >
        check_box
      </i>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should show expand icon when children is empty and load-children prop used 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should update selection when selected prop changes 1`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="false"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
      >
        $subgroup
      </button>
      <button type="button"
              class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
      >
        $checkboxOff
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should update selection when selected prop changes 2`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="true"
       class="v-treeview-node v-treeview-node--selected"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        $subgroup
      </button>
      <button type="button"
              class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light accent--text"
      >
        $checkboxOn
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf v-treeview-node--selected"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__level">
          </div>
          <button type="button"
                  class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light accent--text"
          >
            $checkboxOn
          </button>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeView.ts should update selection when selected prop changes 3`] = `
<div class="v-treeview theme--light">
  <div aria-expanded="true"
       class="v-treeview-node"
  >
    <div class="v-treeview-node__root">
      <button type="button"
              class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light v-treeview-node__toggle--open"
      >
        $subgroup
      </button>
      <button type="button"
              class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
      >
        $checkboxOff
      </button>
      <div class="v-treeview-node__content">
        <div class="v-treeview-node__label">
          Root
        </div>
      </div>
    </div>
    <div class="v-treeview-node__children">
      <div aria-expanded="false"
           class="v-treeview-node v-treeview-node--leaf"
      >
        <div class="v-treeview-node__root">
          <div class="v-treeview-node__level">
          </div>
          <div class="v-treeview-node__level">
          </div>
          <button type="button"
                  class="v-icon notranslate v-treeview-node__checkbox v-icon--link material-icons theme--light"
          >
            $checkboxOff
          </button>
          <div class="v-treeview-node__content">
            <div class="v-treeview-node__label">
              Child
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
