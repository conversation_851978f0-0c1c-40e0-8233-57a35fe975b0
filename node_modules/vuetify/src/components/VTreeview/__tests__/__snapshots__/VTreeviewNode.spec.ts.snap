// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VTreeViewNode.ts should be able to have active children with empty array 1`] = `
<div aria-expanded="false"
     class="v-treeview-node v-treeview-node--leaf v-treeview-node--click"
>
  <div class="v-treeview-node__root">
    <div class="v-treeview-node__level">
    </div>
    <div class="v-treeview-node__content">
      <div class="v-treeview-node__label">
        Child
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeViewNode.ts should generate a transition element 1`] = `
<div aria-expanded="false"
     class="v-treeview-node v-treeview-node--leaf"
>
  <div class="v-treeview-node__root">
    <div class="v-treeview-node__level">
    </div>
    <div class="v-treeview-node__content">
      <div class="v-treeview-node__label">
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeViewNode.ts should not be able to have active children with empty array when disabled 1`] = `
<div aria-expanded="false"
     class="v-treeview-node v-treeview-node--leaf v-treeview-node--click v-treeview-node--disabled"
>
  <div class="v-treeview-node__root">
    <div class="v-treeview-node__level">
    </div>
    <div class="v-treeview-node__content">
      <div class="v-treeview-node__label">
        Child
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeViewNode.ts should not be able to have active children with empty array when loadChildren is specified 1`] = `
<div aria-expanded="false"
     class="v-treeview-node v-treeview-node--click"
>
  <div class="v-treeview-node__root">
    <button type="button"
            class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
    >
      arrow_drop_down
    </button>
    <div class="v-treeview-node__content">
      <div class="v-treeview-node__label">
        Child
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeViewNode.ts should render disabled item 1`] = `
<div aria-expanded="false"
     class="v-treeview-node v-treeview-node--disabled"
>
  <div class="v-treeview-node__root">
    <button type="button"
            class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
    >
      arrow_drop_down
    </button>
    <div class="v-treeview-node__content">
      <div class="v-treeview-node__prepend">
        <div>
          foobar
        </div>
      </div>
      <div class="v-treeview-node__label">
        Root
      </div>
      <div class="v-treeview-node__append">
        <div>
          foobar
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeViewNode.ts should use label slot 1`] = `
<div aria-expanded="false"
     class="v-treeview-node"
>
  <div class="v-treeview-node__root">
    <button type="button"
            class="v-icon notranslate v-treeview-node__toggle v-icon--link material-icons theme--light"
    >
      arrow_drop_down
    </button>
    <div class="v-treeview-node__content">
      <div class="v-treeview-node__label">
        <div>
          ROOT
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`VTreeViewNode.ts should use scoped slots 1`] = `
<div aria-expanded="false"
     class="v-treeview-node v-treeview-node--leaf"
>
  <div class="v-treeview-node__root">
    <div class="v-treeview-node__level">
    </div>
    <div class="v-treeview-node__content">
      <div class="v-treeview-node__prepend">
        <div>
          foobar
        </div>
      </div>
      <div class="v-treeview-node__label">
      </div>
      <div class="v-treeview-node__append">
        <div>
          foobar
        </div>
      </div>
    </div>
  </div>
</div>
`;
