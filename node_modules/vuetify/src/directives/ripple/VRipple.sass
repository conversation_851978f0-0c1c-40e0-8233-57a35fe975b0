@import '../../styles/styles.sass'

.v-ripple
  &__container
    color: inherit
    border-radius: inherit
    position: absolute
    width: 100%
    height: 100%
    left: 0
    top: 0
    overflow: hidden
    z-index: 0
    pointer-events: none
    contain: strict

  &__animation
    color: inherit
    position: absolute
    top: 0
    left: 0
    border-radius: 50%
    background: currentColor
    opacity: 0
    pointer-events: none
    overflow: hidden
    will-change: transform, opacity

    &--enter
      transition: none
      opacity: 0

    &--in
      transition: $ripple-animation-transition-in
      opacity: $ripple-animation-visible-opacity

    &--out
      transition: $ripple-animation-transition-out
      opacity: 0
