export default {
  badge: 'شارة',
  close: 'إغلاق',
  dataIterator: {
    noResultsText: 'لا توجد سجلات مطابقة',
    loadingText: 'تحميل العنصر...',
  },
  dataTable: {
    itemsPerPageText: 'الصفوف لكل صفحة:',
    ariaLabel: {
      sortDescending: 'مفروز تنازلي. تنشيط لإزالة الفرز.',
      sortAscending: 'مفروز تصاعدي. تنشيط للفرز التنازلي.',
      sortNone: 'غير مفروزة. تفعيل لفرز تصاعدي.',
      activateNone: 'Activate to remove sorting.',
      activateDescending: 'Activate to sort descending.',
      activateAscending: 'Activate to sort ascending.',
    },
    sortBy: 'مفروزة حسب',
  },
  dataFooter: {
    itemsPerPageText: 'العناصر لكل صفحة:',
    itemsPerPageAll: 'الكل',
    nextPage: 'الصفحة التالية',
    prevPage: 'الصفحة السابقة',
    firstPage: 'الصفحة الأولى',
    lastPage: 'الصفحة الأخيرة',
    pageText: '{0}-{1} من {2}',
  },
  datePicker: {
    itemsSelected: '{0} مختارة',
    nextMonthAriaLabel: 'الشهر القادم',
    nextYearAriaLabel: 'العام القادم',
    prevMonthAriaLabel: 'الشهر الماضى',
    prevYearAriaLabel: 'السنة الماضية',
  },
  noDataText: 'لا توجد بيانات متاحة',
  carousel: {
    prev: 'البصري السابق',
    next: 'البصري التالي',
    ariaLabel: {
      delimiter: 'Carousel slide {0} of {1}',
    },
  },
  calendar: {
    moreEvents: '{0} أكثر',
  },
  fileInput: {
    counter: '{0} ملفات',
    counterSize: '{0} ملفات ({1} في المجموع)',
  },
  timePicker: {
    am: 'صباحاً',
    pm: 'مساءً',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'ترقيم الصفحات الملاحة',
      next: 'الصفحة التالية',
      previous: 'الصفحة السابقة',
      page: '{0} انتقل إلى صفحة',
      currentPage: '{0} الصفحة الحالية ، الصفحة',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
