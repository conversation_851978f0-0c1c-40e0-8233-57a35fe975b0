export default {
  badge: 'ni<PERSON><PERSON>',
  close: '<PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: '<PERSON>yğ<PERSON> məlumat tapılmadı',
    loadingText: 'Yüklənir... Zəhmət olmasa, gözləyin.',
  },
  dataTable: {
    itemsPerPageText: 'Səhifə başı sətir sayı:',
    ariaLabel: {
      sortDescending: '<PERSON><PERSON><PERSON> sıra ilə düzülmüş.',
      sortAscending: '<PERSON>an sıra ilə düzülmüş.',
      sortNone: 'Sıralanmamışdır. ',
      activateNone: 'Sıralamanı yığışdır.',
      activateDescending: '<PERSON><PERSON><PERSON> sıra ilə düz.',
      activateAscending: '<PERSON>an sıra ilə düz.',
    },
    sortBy: 'Sırala',
  },
  dataFooter: {
    itemsPerPageText: 'Səhifə başı sətir sayı:',
    itemsPerPageAll: 'Hamısı',
    nextPage: '<PERSON>öv<PERSON><PERSON>ti səhifə',
    prevPage: 'Əvvəlk<PERSON> səhifə',
    firstPage: 'İlk səhifə',
    lastPage: 'Son səhifə',
    pageText: '{0} - {1} arası, Cəmi: {2} qeydiyyat',
  },
  datePicker: {
    itemsSelected: '{0} element seçildi',
    nextMonthAriaLabel: 'Növbəti ay',
    nextYearAriaLabel: 'Növbəti yıl',
    prevMonthAriaLabel: 'Keçən ay',
    prevYearAriaLabel: 'Keçən yıl',
  },
  noDataText: 'Bu görüntüdə məlumat yoxdur.',
  carousel: {
    prev: 'Əvvəlki görüntü',
    next: 'Növbəti görüntü',
    ariaLabel: {
      delimiter: 'Galereya səhifə {0} / {1}',
    },
  },
  calendar: {
    moreEvents: '{0} ədad daha',
  },
  fileInput: {
    counter: '{0} fayl',
    counterSize: '{0} fayl (cəmi {1})',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Səhifələmə Naviqasiyası',
      next: 'Növbəti səhifə',
      previous: 'Əvəvlki səhifə',
      page: 'Səhifəyə get {0}',
      currentPage: 'Cari səhifə, Səhifə {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
