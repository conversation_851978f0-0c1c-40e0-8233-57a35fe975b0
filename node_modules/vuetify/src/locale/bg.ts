export default {
  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  close: 'Затвори',
  dataIterator: {
    noResultsText: 'Не са намерени записи',
    loadingText: 'Зареждане на елементи...',
  },
  dataTable: {
    itemsPerPageText: 'Редове на страница:',
    ariaLabel: {
      sortDescending: 'Подреди в намаляващ ред.',
      sortAscending: 'Подреди в нарастващ ред.',
      sortNone: 'Без подредба.',
      activateNone: 'Активирай за премахване на подредбата.',
      activateDescending: 'Активирай за подредба в намаляващ ред.',
      activateAscending: 'Активирай за подредба в нарастващ ред.',
    },
    sortBy: 'Сорти<PERSON><PERSON>й по',
  },
  dataFooter: {
    itemsPerPageText: 'Елементи на страница:',
    itemsPerPageAll: 'Всички',
    nextPage: 'Следваща страница',
    prevPage: 'Предишна страница',
    firstPage: 'Първа страница',
    lastPage: 'Последна страница',
    pageText: '{0}-{1} от {2}',
  },
  datePicker: {
    itemsSelected: '{0} избрани',
    nextMonthAriaLabel: 'Следващ месец',
    nextYearAriaLabel: 'Следващата година',
    prevMonthAriaLabel: 'Предишен месец',
    prevYearAriaLabel: 'Предишна година',
  },
  noDataText: 'Няма налични данни',
  carousel: {
    prev: 'Предишна визуализация',
    next: 'Следваща визуализация',
    ariaLabel: {
      delimiter: 'Кадър {0} от {1} на въртележката',
    },
  },
  calendar: {
    moreEvents: 'Още {0}',
  },
  fileInput: {
    counter: '{0} файла',
    counterSize: '{0} файла ({1} общо)',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Странициране',
      next: 'Следваща страница',
      previous: 'Предишна страница',
      page: 'Отиди на страница {0}',
      currentPage: 'Текуща страница, Страница {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
