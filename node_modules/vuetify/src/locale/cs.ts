export default {
  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: 'Nenalezeny žádné záznamy',
    loadingText: 'Načítám položky...',
  },
  dataTable: {
    itemsPerPageText: 'Řádků na stránku:',
    ariaLabel: {
      sortDescending: 'Řazeno sestupně.',
      sortAscending: 'Řazeno vzestupně.',
      sortNone: 'Neseřazeno.',
      activateNone: 'Aktivováním vypnete řazení.',
      activateDescending: 'Aktivováním se bude řadit sestupně.',
      activateAscending: 'Aktivováním se bude řadit vzestupně.',
    },
    sortBy: 'Řadit dle',
  },
  dataFooter: {
    itemsPerPageText: 'Položek na stránku:',
    itemsPerPageAll: 'Vše',
    nextPage: '<PERSON><PERSON><PERSON> strana',
    prevPage: 'Předchozí strana',
    firstPage: 'Prvn<PERSON> strana',
    lastPage: 'Poslední strana',
    pageText: '{0}-{1} z {2}',
  },
  datePicker: {
    itemsSelected: '{0} vybráno',
    nextMonthAriaLabel: 'Příští měsíc',
    nextYearAriaLabel: 'Příští rok',
    prevMonthAriaLabel: 'Předchozí měsíc',
    prevYearAriaLabel: 'Předchozí rok',
  },
  noDataText: 'Nejsou dostupná žádná data',
  carousel: {
    prev: 'Předchozí obrázek',
    next: 'Další obrázek',
    ariaLabel: {
      delimiter: 'Slide {0} z {1}',
    },
  },
  calendar: {
    moreEvents: '{0} dalších',
  },
  fileInput: {
    counter: '{0} souborů',
    counterSize: '{0} souborů ({1} celkem)',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Navigace po stránkách',
      next: 'Další strana',
      previous: 'Předchozí strana',
      page: 'Přejít na stránku {0}',
      currentPage: 'Aktuální stránka, stránka {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Hodnocení {0} z {1}',
    },
  },
}
