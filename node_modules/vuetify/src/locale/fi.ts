export default {
  badge: 'Infopiste',
  close: '<PERSON><PERSON>',
  dataIterator: {
    noResultsText: 'Ei osumia',
    loadingText: '<PERSON><PERSON><PERSON> kohteita...',
  },
  dataTable: {
    itemsPerPageText: 'Rivejä sivulla:',
    ariaLabel: {
      sortDescending: ': <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laskevasti. Poista järjestäminen aktivoimalla.',
      sortAscending: ': Järjestetty nousevasti. Järjest<PERSON> laskevasti aktivoimalla.',
      sortNone: ': <PERSON>i järjestetty. Järjestä nousevasti aktivoimalla.',
      activateNone: 'Aktivoi lajittelun poistamiseksi.',
      activateDescending: 'Aktivoi laskevien laskevien lajittelemiseksi.',
      activateAscending: 'Aktivoi lajitella nouseva.',
    },
    sortBy: 'Järjestä',
  },
  dataFooter: {
    itemsPerPageText: 'Kohteita sivulla:',
    itemsPerPageAll: '<PERSON>k<PERSON>',
    nextPage: 'Se<PERSON><PERSON> sivu',
    prevPage: 'Edellinen sivu',
    firstPage: 'Ensimmäinen sivu',
    lastPage: 'Viimeinen sivu',
    pageText: '{0}-{1} ({2})',
  },
  datePicker: {
    itemsSelected: '{0} valittu',
    nextMonthAriaLabel: 'Seuraava kuukausi',
    nextYearAriaLabel: 'Ensi vuosi',
    prevMonthAriaLabel: 'Edellinen kuukausi',
    prevYearAriaLabel: 'Edellinen vuosi',
  },
  noDataText: 'Ei dataa',
  carousel: {
    prev: 'Edellinen kuva',
    next: 'Seuraava kuva',
    ariaLabel: {
      delimiter: 'Karusellin kuva {0}/{1}',
    },
  },
  calendar: {
    moreEvents: '{0} lisää',
  },
  fileInput: {
    counter: '{0} tiedostoa',
    counterSize: '{0} tiedostoa ({1} yhteensä)',
  },
  timePicker: {
    am: 'ap.',
    pm: 'ip.',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Pagination Navigation',
      next: 'Seuraava sivu',
      previous: 'Edellinen sivu',
      page: 'Mene sivulle {0}',
      currentPage: 'Nykyinen sivu, Sivu {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Luokitus {0}/{1}',
    },
  },
}
