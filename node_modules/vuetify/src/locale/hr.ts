export default {
  badge: '<PERSON><PERSON>',
  close: '<PERSON><PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: 'Nisu pronađene odgo<PERSON>aju<PERSON>e stavke',
    loadingText: 'Učitavanje...',
  },
  dataTable: {
    itemsPerPageText: '<PERSON>aka po stranici:',
    ariaLabel: {
      sortDescending: 'Sortirano silazno.',
      sortAscending: 'Sortirano uzlazno.',
      sortNone: '<PERSON><PERSON> sortirano.',
      activateNone: 'Odaberite za uklanjanje sortiranja.',
      activateDescending: 'Odaberite za silazno sortiranje.',
      activateAscending: 'Odaberite za uzlazno sortiranje.',
    },
    sortBy: 'Sortirajte po',
  },
  dataFooter: {
    itemsPerPageText: 'Stavki po stranici:',
    itemsPerPageAll: 'Sve',
    nextPage: 'Sljedeća stranica',
    prevPage: 'Prethodna stranica',
    firstPage: 'Prva stranica',
    lastPage: 'Posljednja stranica',
    pageText: '{0}-{1} od {2}',
  },
  datePicker: {
    itemsSelected: '{0} odabrano',
    nextMonthAriaLabel: 'Sljedeći mjesec',
    nextYearAriaLabel: 'Slijedeće godine',
    prevMonthAriaLabel: 'Prethodni mjesec',
    prevYearAriaLabel: 'Prošla godina',
  },
  noDataText: 'Nema dostupnih podataka',
  carousel: {
    prev: 'Prethodno',
    next: 'Sljedeće',
    ariaLabel: {
      delimiter: 'Carousel slide {0} of {1}',
    },
  },
  calendar: {
    moreEvents: 'Još {0}',
  },
  fileInput: {
    counter: 'Odabranih datoteka: {0}',
    counterSize: 'Odabranih datoteka: {0} ({1} ukupno)',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Navigacija stranicama',
      next: 'Sljedeća stranica',
      previous: 'Prethodna stranica',
      page: 'Idi na stranicu {0}',
      currentPage: 'Trenutna stranica, stranica {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
