export default {
  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: '<PERSON><PERSON><PERSON> e<PERSON> találat',
    loadingText: 'Betöltés...',
  },
  dataTable: {
    itemsPerPageText: 'Elem oldalanként:',
    ariaLabel: {
      sortDescending: 'Csökkenő sorrendbe rendezve.',
      sortAscending: 'Növekvő sorrendbe rendezve.',
      sortNone: 'Rendezetlen.',
      activateNone: 'Rendezés törlése.',
      activateDescending: 'Aktiváld a csökkenő rendezésért.',
      activateAscending: 'Aktiváld a növekvő rendezésért.',
    },
    sortBy: 'Rendezés',
  },
  dataFooter: {
    itemsPerPageText: 'Elem oldalanként:',
    itemsPerPageAll: 'Mind',
    nextPage: 'Következ<PERSON> oldal',
    prevPage: '<PERSON><PERSON><PERSON><PERSON> oldal',
    firstPage: '<PERSON><PERSON><PERSON> oldal',
    lastPage: 'U<PERSON><PERSON><PERSON> oldal',
    pageText: '{0}-{1} / {2}',
  },
  datePicker: {
    itemsSelected: '{0} kiválasztva',
    nextMonthAriaLabel: 'Következő hónap',
    nextYearAriaLabel: 'Következő év',
    prevMonthAriaLabel: 'Előző hónap',
    prevYearAriaLabel: 'Előző év',
  },
  noDataText: 'Nincs elérhető adat',
  carousel: {
    prev: 'Előző',
    next: 'Következő',
    ariaLabel: {
      delimiter: 'Dia {0}/{1}',
    },
  },
  calendar: {
    moreEvents: '{0} további',
  },
  fileInput: {
    counter: '{0} fájl',
    counterSize: '{0} fájl ({1} összesen)',
  },
  timePicker: {
    am: 'de',
    pm: 'du',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Oldal navigáció',
      next: 'Következő oldal',
      previous: 'Előző oldal',
      page: 'Menj a(z) {0}. oldalra',
      currentPage: 'Aktuális oldal: {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
