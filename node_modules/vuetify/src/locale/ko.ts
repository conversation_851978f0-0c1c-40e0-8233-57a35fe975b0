export default {
  badge: '배지',
  close: '닫기',
  dataIterator: {
    noResultsText: '일치하는 항목이 없습니다.',
    loadingText: '불러오는 중...',
  },
  dataTable: {
    itemsPerPageText: '페이지 당 행 수:',
    ariaLabel: {
      sortDescending: '내림차순 정렬.',
      sortAscending: '오름차순 정렬.',
      sortNone: '정렬하지 않음.',
      activateNone: '정렬을 취소하려면 활성화하세요.',
      activateDescending: '내림차순 정렬을 위해 활성화하세요.',
      activateAscending: '오름차순 정렬을 위해 활성화하세요.',
    },
    sortBy: 'Sort by',
  },
  dataFooter: {
    itemsPerPageText: '페이지 당 항목 수:',
    itemsPerPageAll: '전체',
    nextPage: '다음 페이지',
    prevPage: '이전 페이지',
    firstPage: '첫 페이지',
    lastPage: '마지막 페이지',
    pageText: '{2} 중 {0}-{1}',
  },
  datePicker: {
    itemsSelected: '{0} 선택됨',
    nextMonthAriaLabel: '다음 달',
    nextYearAriaLabel: '내년',
    prevMonthAriaLabel: '지난달',
    prevYearAriaLabel: '전년도',
  },
  noDataText: '데이터가 없습니다.',
  carousel: {
    prev: '이전 화면',
    next: '다음 화면',
    ariaLabel: {
      delimiter: 'Carousel slide {0} of {1}',
    },
  },
  calendar: {
    moreEvents: '{0} 더보기',
  },
  fileInput: {
    counter: '{0} files',
    counterSize: '{0} files ({1} in total)',
  },
  timePicker: {
    am: '오전',
    pm: '오후',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Pagination Navigation',
      next: '다음 페이지',
      previous: '이전 페이지',
      page: '고토 페이지 {0}',
      currentPage: '현재 페이지, 페이지 {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
