export default {
  badge: '<PERSON><PERSON><PERSON><PERSON>',
  close: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: 'Nerasta atitinkanč<PERSON>ų įrašų',
    loadingText: 'Kraunama...',
  },
  dataTable: {
    itemsPerPageText: 'Eilutės per puslapį:',
    ariaLabel: {
      sortDescending: 'Išrikiuota mažėjimo tvarka.',
      sortAscending: 'Išrikiu<PERSON> didėjimo tvarka.',
      sortNone: 'Nerikiuota.',
      activateNone: '<PERSON><PERSON><PERSON><PERSON>kite, jei norite rikiavimą pašalinti.',
      activateDescending: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jei norite rikiuoti ma<PERSON> tvarka.',
      activateAscending: '<PERSON><PERSON><PERSON><PERSON>ki<PERSON>, jei norite rikiuoti didėjimo tvarka.',
    },
    sortBy: 'Sort by',
  },
  dataFooter: {
    itemsPerPageText: 'Įrašai per puslapį:',
    itemsPerPageAll: 'Visi',
    nextPage: '<PERSON><PERSON> puslapis',
    prevPage: '<PERSON><PERSON><PERSON><PERSON> puslapis',
    firstPage: '<PERSON><PERSON><PERSON> puslapis',
    lastPage: 'Pask<PERSON><PERSON> puslapis',
    pageText: '{0}-{1} iš {2}',
  },
  datePicker: {
    itemsSelected: '{0} pasirinkta',
    nextMonthAriaLabel: 'Kitą mėnesį',
    nextYearAriaLabel: 'Kitais metais',
    prevMonthAriaLabel: 'Praeita mėnesį',
    prevYearAriaLabel: 'Praeiti metai',
  },
  noDataText: 'Nėra duomenų',
  carousel: {
    prev: 'Ankstesnioji skaidrė',
    next: 'Kita skaidrė',
    ariaLabel: {
      delimiter: 'Carousel slide {0} of {1}',
    },
  },
  calendar: {
    moreEvents: 'Daugiau {0}',
  },
  fileInput: {
    counter: '{0} failų',
    counterSize: '{0} failų ({1} iš viso)',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Puslapio naršymas',
      next: 'Kitas puslapis',
      previous: 'Ankstesnis puslapis',
      page: 'Eiti į puslapį {0}',
      currentPage: 'Dabartinis puslapis, puslapis {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
