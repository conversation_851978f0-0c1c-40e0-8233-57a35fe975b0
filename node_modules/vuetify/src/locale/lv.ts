export default {
  badge: 'Žetons',
  close: 'Aizvērt',
  dataIterator: {
    noResultsText: 'Nekas netika atrasts',
    loadingText: 'Ielādē...',
  },
  dataTable: {
    itemsPerPageText: 'Rād<PERSON>t lapā:',
    ariaLabel: {
      sortDescending: '<PERSON>k<PERSON>rtots dilstošā secībā.',
      sortAscending: 'Sak<PERSON>rtots augošā secībā.',
      sortNone: 'Nav sakārtots.',
      activateNone: 'Aktivizēt, lai noņemtu kārtošanu.',
      activateDescending: 'Aktivizēt, lai sakārtotu dilstošā secībā.',
      activateAscending: 'Aktivizēt, lai sakārtotu augošā secībā.',
    },
    sortBy: 'Sort by',
  },
  dataFooter: {
    itemsPerPageText: 'Rādīt lapā:',
    itemsPerPageAll: 'Visu',
    nextPage: 'Nāka<PERSON>ā lapa',
    prevPage: 'Iepriekšējā lapa',
    firstPage: '<PERSON><PERSON><PERSON> lapa',
    lastPage: 'Pēdējā lapa',
    pageText: '{0}-{1} no {2}',
  },
  datePicker: {
    itemsSelected: '{0} izvēlēts',
    nextMonthAriaLabel: 'Nākammēnes',
    nextYearAriaLabel: 'Nākamgad',
    prevMonthAriaLabel: 'Iepriekšējais mēnesis',
    prevYearAriaLabel: 'Iepriekšējais gads',
  },
  noDataText: 'Nav pieejamu datu',
  carousel: {
    prev: 'Iepriekšējais slaids',
    next: 'Nākamais slaids',
    ariaLabel: {
      delimiter: 'Carousel slide {0} of {1}',
    },
  },
  calendar: {
    moreEvents: 'Vēl {0}',
  },
  fileInput: {
    counter: '{0} files',
    counterSize: '{0} files ({1} in total)',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Navigācija paginationā',
      next: 'Nākamā lapa',
      previous: 'Iepriekšējā lapa',
      page: 'Iet uz lapu {0}',
      currentPage: 'Pašreizējā lapa, lapa {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
