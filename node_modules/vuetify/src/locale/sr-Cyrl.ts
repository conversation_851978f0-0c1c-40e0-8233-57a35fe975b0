export default {
  badge: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  close: 'Затвори',
  dataIterator: {
    noResultsText: 'Ни један запис није пронађен',
    loadingText: 'Учитавање ставке...',
  },
  dataTable: {
    itemsPerPageText: 'Редова по страници:',
    ariaLabel: {
      sortDescending: 'Сортирано опадајуће.',
      sortAscending: 'Сортирано растуће.',
      sortNone: 'Није сортирано.',
      activateNone: 'Кликни да уклониш сортирање.',
      activateDescending: 'Кликни да сортираш опадајуће.',
      activateAscending: 'Кликни да сортираш растуће.',
    },
    sortBy: 'Сортирај по',
  },
  dataFooter: {
    itemsPerPageText: 'Ставки по страници:',
    itemsPerPageAll: 'Све',
    nextPage: 'Следећа страница',
    prevPage: 'Претходна страница',
    firstPage: 'Прва страница',
    lastPage: 'Последња страница',
    pageText: '{0}-{1} од {2}',
  },
  datePicker: {
    itemsSelected: '{0} одабрано',
    nextMonthAriaLabel: 'Следећег месеца',
    nextYearAriaLabel: 'Следеће године',
    prevMonthAriaLabel: 'Претходни месец',
    prevYearAriaLabel: 'Претходна година',
  },
  noDataText: 'Нема доступних података',
  carousel: {
    prev: 'Претходна слика',
    next: 'Следећа слика',
    ariaLabel: {
      delimiter: 'Слика {0} од {1}',
    },
  },
  calendar: {
    moreEvents: '{0} више',
  },
  fileInput: {
    counter: '{0} фајлова',
    counterSize: '{0} фајлова ({1} укупно)',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Навигација страницама',
      next: 'Следећа страница',
      previous: 'Претходна страница',
      page: 'Иди на страну {0}',
      currentPage: 'Тренутна страница, страница {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Оцена {0} од {1}',
    },
  },
}
