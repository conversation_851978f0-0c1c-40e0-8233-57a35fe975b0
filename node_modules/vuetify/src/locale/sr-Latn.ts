export default {
  badge: '<PERSON><PERSON><PERSON><PERSON>',
  close: '<PERSON><PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: '<PERSON><PERSON><PERSON> zap<PERSON> nije pronađen',
    loadingText: 'Učitavanje stavke...',
  },
  dataTable: {
    itemsPerPageText: '<PERSON><PERSON> po stranici:',
    ariaLabel: {
      sortDescending: 'Sortirano opadajuće.',
      sortAscending: 'Sortirano rastuće.',
      sortNone: '<PERSON><PERSON> sortirano.',
      activateNone: 'Klikni da ukloniš sortiranje.',
      activateDescending: '<PERSON><PERSON><PERSON> da sortiraš opadajuće.',
      activateAscending: '<PERSON><PERSON><PERSON> da sortiraš rastuće.',
    },
    sortBy: 'Sortiraj po',
  },
  dataFooter: {
    itemsPerPageText: 'Stavki po stranici:',
    itemsPerPageAll: 'Sve',
    nextPage: 'Sledeća stranica',
    prevPage: 'Prethodna stranica',
    firstPage: 'Prva stranica',
    lastPage: 'Poslednja stranica',
    pageText: '{0}-{1} od {2}',
  },
  datePicker: {
    itemsSelected: '{0} odabrano',
    nextMonthAriaLabel: 'Sledećeg meseca',
    nextYearAriaLabel: 'Sledeće godine',
    prevMonthAriaLabel: 'Prethodni mesec',
    prevYearAriaLabel: 'Prethodna godina',
  },
  noDataText: 'Nema dostupnih podataka',
  carousel: {
    prev: 'Prethodna slika',
    next: 'Sledeća slika',
    ariaLabel: {
      delimiter: 'Slika {0} od {1}',
    },
  },
  calendar: {
    moreEvents: '{0} više',
  },
  fileInput: {
    counter: '{0} fajlova',
    counterSize: '{0} fajlova ({1} ukupno)',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Navigacija stranicama',
      next: 'Sledeća stranica',
      previous: 'Prethodna stranica',
      page: 'Idi na stranu {0}',
      currentPage: 'Trenutna stranica, stranica {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Ocena {0} od {1}',
    },
  },
}
