export default {
  badge: 'สัญลักษณ์',
  close: 'ปิด',
  dataIterator: {
    noResultsText: 'ไม่พบข้อมูลที่ค้นหา',
    loadingText: 'กำลังโหลดข้อมูล...',
  },
  dataTable: {
    itemsPerPageText: 'แถวต่อหน้า:',
    ariaLabel: {
      sortDescending: 'เรียงจากมากไปน้อยอยู่',
      sortAscending: 'เรียงจากน้อยไปมากอยู่',
      sortNone: 'ไม่ได้เรียงลำดับ',
      activateNone: 'กดเพื่อปิดการเรียงลำดับ',
      activateDescending: 'กดเพื่อเรียงจากมากไปน้อย',
      activateAscending: 'กดเพื่อเรียงจากน้อยไปมาก',
    },
    sortBy: 'เรียงตาม',
  },
  dataFooter: {
    itemsPerPageText: 'รายการต่อหน้า:',
    itemsPerPageAll: 'ทั้งหมด',
    nextPage: 'หน้าต่อไป',
    prevPage: 'หน้าที่แล้ว',
    firstPage: 'หน้าแรก',
    lastPage: 'หน้าสุดท้าย',
    pageText: '{0}-{1} จาก {2}',
  },
  datePicker: {
    itemsSelected: 'เลือก {0} วัน',
    nextMonthAriaLabel: 'เดือนถัดไป',
    nextYearAriaLabel: 'ปีถัดไป',
    prevMonthAriaLabel: 'เดือนก่อนหน้า',
    prevYearAriaLabel: 'ปีก่อนหน้า',
  },
  noDataText: 'ไม่มีข้อมูล',
  carousel: {
    prev: 'ภาพก่อนหน้า',
    next: 'ภาพถัดไป',
    ariaLabel: {
      delimiter: 'ภาพสไลด์ที่ {0} จาก {1}',
    },
  },
  calendar: {
    moreEvents: 'มีอีก {0}',
  },
  fileInput: {
    counter: '{0} ไฟล์',
    counterSize: '{0} ไฟล์ (รวม {1})',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'การนำทางไปยังหน้า',
      next: 'หน้าต่อไป',
      previous: 'หน้าที่แล้ว',
      page: 'ไปที่หน้า {0}',
      currentPage: 'หน้าปัจจุบัน (หน้า {0})',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
