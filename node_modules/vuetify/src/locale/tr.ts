export default {
  badge: 'rozet',
  close: '<PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: 'Eşleşen veri bulunamadı',
    loadingText: 'Yükleniyor... Lütfen bekleyin.',
  },
  dataTable: {
    itemsPerPageText: '<PERSON><PERSON> başına satır:',
    ariaLabel: {
      sortDescending: 'Z den A ya sıralı.',
      sortAscending: 'A dan Z ye sıralı.',
      sortNone: 'Sıral<PERSON> değil. ',
      activateNone: 'Sıralamayı kaldırmak için etkinleştir.',
      activateDescending: 'Z den A ya sıralamak için etkinleştir.',
      activateAscending: 'A dan Z ye sıralamak için etkinleştir.',
    },
    sortBy: 'Sırala',
  },
  dataFooter: {
    itemsPerPageText: 'Sayfa başına satır:',
    itemsPerPageAll: 'Hepsi',
    nextPage: '<PERSON><PERSON><PERSON> sayfa',
    prevPage: '<PERSON><PERSON><PERSON> sayfa',
    firstPage: '<PERSON>lk sayfa',
    lastPage: '<PERSON> sayfa',
    pageText: '{0} - {1} arası, Toplam: {2} kayıt',
  },
  datePicker: {
    itemsSelected: '{0} öge seçildi',
    nextMonthAriaLabel: 'Gelecek ay',
    nextYearAriaLabel: 'Gelecek yıl',
    prevMonthAriaLabel: 'Geçtiğimiz ay',
    prevYearAriaLabel: 'Geçen yıl',
  },
  noDataText: 'Bu görünümde veri yok.',
  carousel: {
    prev: 'Önceki görsel',
    next: 'Sonraki görsel',
    ariaLabel: {
      delimiter: 'Galeri sayfa {0} / {1}',
    },
  },
  calendar: {
    moreEvents: '{0} tane daha',
  },
  fileInput: {
    counter: '{0} dosya',
    counterSize: '{0} dosya (toplamda {1})',
  },
  timePicker: {
    am: 'AM',
    pm: 'PM',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Sayfalandırma Navigasyonu',
      next: 'Sonraki sayfa',
      previous: 'Önceki sayfa',
      page: 'Sayfaya git {0}',
      currentPage: 'Geçerli Sayfa, Sayfa {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Rating {0} of {1}',
    },
  },
}
