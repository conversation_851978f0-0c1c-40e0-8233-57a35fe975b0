export default {
  badge: '<PERSON><PERSON> hiệu',
  close: '<PERSON><PERSON><PERSON>',
  dataIterator: {
    noResultsText: '<PERSON>hông tìm thấy kết quả nào',
    loadingText: '<PERSON>ang tải...',
  },
  dataTable: {
    itemsPerPageText: 'Số hàng mỗi trang:',
    ariaLabel: {
      sortDescending: 'Sắp xếp giảm dần.',
      sortAscending: 'Sắp xếp tăng dần.',
      sortNone: 'Không sắp xếp.',
      activateNone: '<PERSON>ích hoạt để bỏ sắp xếp.',
      activateDescending: '<PERSON><PERSON><PERSON> hoạt để sắp xếp giảm dần.',
      activateAscending: '<PERSON>ích hoạt để sắp xếp tăng dần.',
    },
    sortBy: 'Sắp xếp',
  },
  dataFooter: {
    itemsPerPageText: 'S<PERSON> mục mỗi trang:',
    itemsPerPageAll: 'Toàn bộ',
    nextPage: 'Trang tiếp theo',
    prevPage: 'Trang trước',
    firstPage: 'Trang đầu',
    lastPage: 'Trang cuối',
    pageText: '{0}-{1} trên {2}',
  },
  datePicker: {
    itemsSelected: '{0} được chọn',
    nextMonthAriaLabel: 'Tháng sau',
    nextYearAriaLabel: 'Năm sau',
    prevMonthAriaLabel: 'Tháng trước',
    prevYearAriaLabel: 'Năm trước',
  },
  noDataText: 'Không có dữ liệu',
  carousel: {
    prev: 'Ảnh tiếp theo',
    next: 'Ảnh trước',
    ariaLabel: {
      delimiter: 'Carousel slide {0} trên {1}',
    },
  },
  calendar: {
    moreEvents: '{0} nữa',
  },
  fileInput: {
    counter: '{0} tệp',
    counterSize: '{0} tệp (tổng cộng {1})',
  },
  timePicker: {
    am: 'SA',
    pm: 'CH',
  },
  pagination: {
    ariaLabel: {
      wrapper: 'Điều hướng phân trang',
      next: 'Trang tiếp theo',
      previous: 'Trang trước',
      page: 'Đến trang {0}',
      currentPage: 'Trang hiện tại, Trang {0}',
    },
  },
  rating: {
    ariaLabel: {
      icon: 'Đánh giá {0} trên {1}',
    },
  },
}
