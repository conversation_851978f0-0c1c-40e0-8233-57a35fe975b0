// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Icons.ts should generate default icons 1`] = `
Object {
  "cancel": "mdi-close-circle",
  "checkboxIndeterminate": "mdi-minus-box",
  "checkboxOff": "mdi-checkbox-blank-outline",
  "checkboxOn": "mdi-checkbox-marked",
  "clear": "mdi-close",
  "close": "mdi-close",
  "complete": "mdi-check",
  "delete": "mdi-close-circle",
  "delimiter": "mdi-circle",
  "dropdown": "mdi-menu-down",
  "edit": "mdi-pencil",
  "error": "mdi-alert",
  "expand": "mdi-chevron-down",
  "file": "mdi-paperclip",
  "first": "mdi-page-first",
  "info": "mdi-information",
  "last": "mdi-page-last",
  "loading": "mdi-cached",
  "menu": "mdi-menu",
  "minus": "mdi-minus",
  "next": "mdi-chevron-right",
  "plus": "mdi-plus",
  "prev": "mdi-chevron-left",
  "radioOff": "mdi-radiobox-blank",
  "radioOn": "mdi-radiobox-marked",
  "ratingEmpty": "mdi-star-outline",
  "ratingFull": "mdi-star",
  "ratingHalf": "mdi-star-half-full",
  "sort": "mdi-arrow-up",
  "subgroup": "mdi-menu-down",
  "success": "mdi-check-circle",
  "unfold": "mdi-unfold-more-horizontal",
  "warning": "mdi-exclamation",
}
`;

exports[`Icons.ts should use a custom iconfont preset 1`] = `
Object {
  "cancel": "fa fa-times-circle",
  "checkboxIndeterminate": "fa fa-minus-square",
  "checkboxOff": "fa fa-square-o",
  "checkboxOn": "fa fa-check-square",
  "clear": "fa fa-times-circle",
  "close": "fa fa-times",
  "complete": "fa fa-check",
  "delete": "fa fa-times-circle",
  "delimiter": "fa fa-circle",
  "dropdown": "fa fa-caret-down",
  "edit": "fa fa-pencil",
  "error": "fa fa-exclamation-triangle",
  "expand": "fa fa-chevron-down",
  "file": "fa fa-paperclip",
  "first": "fa fa-step-backward",
  "info": "fa fa-info-circle",
  "last": "fa fa-step-forward",
  "loading": "fa fa-refresh",
  "menu": "fa fa-bars",
  "minus": "fa fa-minus",
  "next": "fa fa-chevron-right",
  "plus": "fa fa-plus",
  "prev": "fa fa-chevron-left",
  "radioOff": "fa fa-circle-o",
  "radioOn": "fa fa-dot-circle-o",
  "ratingEmpty": "fa fa-star-o",
  "ratingFull": "fa fa-star",
  "ratingHalf": "fa fa-star-half-o",
  "sort": "fa fa-sort-up",
  "subgroup": "fa fa-caret-down",
  "success": "fa fa-check-circle",
  "unfold": "fa fa-angle-double-down",
  "warning": "fa fa-exclamation",
}
`;
