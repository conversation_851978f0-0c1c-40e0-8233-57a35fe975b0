// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`$vuetify.presets should merge user and default preset 1`] = `"{\\"breakpoint\\":{\\"mobileBreakpoint\\":1264,\\"scrollBarWidth\\":16,\\"thresholds\\":{\\"xs\\":200,\\"sm\\":960,\\"md\\":1280,\\"lg\\":1920}},\\"icons\\":{\\"iconfont\\":\\"md\\",\\"values\\":{\\"complete\\":\\"bar\\"}},\\"lang\\":{\\"current\\":\\"en\\",\\"locales\\":{\\"en\\":{\\"badge\\":\\"Foobar\\",\\"close\\":\\"Close\\",\\"dataIterator\\":{\\"noResultsText\\":\\"Fizzbuzz\\",\\"loadingText\\":\\"Loading items...\\"},\\"dataTable\\":{\\"itemsPerPageText\\":\\"Rows per page:\\",\\"ariaLabel\\":{\\"sortDescending\\":\\"Sorted descending.\\",\\"sortAscending\\":\\"Sorted ascending.\\",\\"sortNone\\":\\"Not sorted.\\",\\"activateNone\\":\\"Activate to remove sorting.\\",\\"activateDescending\\":\\"Activate to sort descending.\\",\\"activateAscending\\":\\"Activate to sort ascending.\\"},\\"sortBy\\":\\"Sort by\\"},\\"dataFooter\\":{\\"itemsPerPageText\\":\\"Items per page:\\",\\"itemsPerPageAll\\":\\"All\\",\\"nextPage\\":\\"Next page\\",\\"prevPage\\":\\"Previous page\\",\\"firstPage\\":\\"First page\\",\\"lastPage\\":\\"Last page\\",\\"pageText\\":\\"{0}-{1} of {2}\\"},\\"datePicker\\":{\\"itemsSelected\\":\\"{0} selected\\",\\"nextMonthAriaLabel\\":\\"Next month\\",\\"nextYearAriaLabel\\":\\"Next year\\",\\"prevMonthAriaLabel\\":\\"Previous month\\",\\"prevYearAriaLabel\\":\\"Previous year\\"},\\"noDataText\\":\\"No data available\\",\\"carousel\\":{\\"prev\\":\\"Previous visual\\",\\"next\\":\\"Next visual\\",\\"ariaLabel\\":{\\"delimiter\\":\\"Carousel slide {0} of {1}\\"}},\\"calendar\\":{\\"moreEvents\\":\\"{0} more\\"},\\"fileInput\\":{\\"counter\\":\\"{0} files\\",\\"counterSize\\":\\"{0} files ({1} in total)\\"},\\"timePicker\\":{\\"am\\":\\"AM\\",\\"pm\\":\\"PM\\"},\\"pagination\\":{\\"ariaLabel\\":{\\"wrapper\\":\\"Pagination Navigation\\",\\"next\\":\\"Next page\\",\\"previous\\":\\"Previous page\\",\\"page\\":\\"Goto Page {0}\\",\\"currentPage\\":\\"Current Page, Page {0}\\"}},\\"rating\\":{\\"ariaLabel\\":{\\"icon\\":\\"Rating {0} of {1}\\"}}}}},\\"rtl\\":true,\\"theme\\":{\\"dark\\":true,\\"default\\":\\"light\\",\\"disable\\":false,\\"options\\":{\\"variations\\":true},\\"themes\\":{\\"light\\":{\\"primary\\":\\"blue\\",\\"secondary\\":{\\"darken4\\":\\"red\\"},\\"accent\\":\\"#82B1FF\\",\\"error\\":\\"#FF5252\\",\\"info\\":\\"#2196F3\\",\\"success\\":\\"#4CAF50\\",\\"warning\\":\\"#FB8C00\\"},\\"dark\\":{\\"primary\\":\\"#2196F3\\",\\"secondary\\":\\"#424242\\",\\"accent\\":\\"#FF4081\\",\\"error\\":\\"#FF5252\\",\\"info\\":\\"#2196F3\\",\\"success\\":\\"#4CAF50\\",\\"warning\\":\\"#FB8C00\\"}}}}"`;

exports[`$vuetify.presets should merge user and default preset 2`] = `undefined`;

exports[`$vuetify.presets should merge user and default preset 3`] = `"{\\"en\\":{\\"badge\\":\\"Foobar\\",\\"close\\":\\"Close\\",\\"dataIterator\\":{\\"noResultsText\\":\\"Fizzbuzz\\",\\"loadingText\\":\\"Loading items...\\"},\\"dataTable\\":{\\"itemsPerPageText\\":\\"Rows per page:\\",\\"ariaLabel\\":{\\"sortDescending\\":\\"Sorted descending.\\",\\"sortAscending\\":\\"Sorted ascending.\\",\\"sortNone\\":\\"Not sorted.\\",\\"activateNone\\":\\"Activate to remove sorting.\\",\\"activateDescending\\":\\"Activate to sort descending.\\",\\"activateAscending\\":\\"Activate to sort ascending.\\"},\\"sortBy\\":\\"Sort by\\"},\\"dataFooter\\":{\\"itemsPerPageText\\":\\"Items per page:\\",\\"itemsPerPageAll\\":\\"All\\",\\"nextPage\\":\\"Next page\\",\\"prevPage\\":\\"Previous page\\",\\"firstPage\\":\\"First page\\",\\"lastPage\\":\\"Last page\\",\\"pageText\\":\\"{0}-{1} of {2}\\"},\\"datePicker\\":{\\"itemsSelected\\":\\"{0} selected\\",\\"nextMonthAriaLabel\\":\\"Next month\\",\\"nextYearAriaLabel\\":\\"Next year\\",\\"prevMonthAriaLabel\\":\\"Previous month\\",\\"prevYearAriaLabel\\":\\"Previous year\\"},\\"noDataText\\":\\"No data available\\",\\"carousel\\":{\\"prev\\":\\"Previous visual\\",\\"next\\":\\"Next visual\\",\\"ariaLabel\\":{\\"delimiter\\":\\"Carousel slide {0} of {1}\\"}},\\"calendar\\":{\\"moreEvents\\":\\"{0} more\\"},\\"fileInput\\":{\\"counter\\":\\"{0} files\\",\\"counterSize\\":\\"{0} files ({1} in total)\\"},\\"timePicker\\":{\\"am\\":\\"AM\\",\\"pm\\":\\"PM\\"},\\"pagination\\":{\\"ariaLabel\\":{\\"wrapper\\":\\"Pagination Navigation\\",\\"next\\":\\"Next page\\",\\"previous\\":\\"Previous page\\",\\"page\\":\\"Goto Page {0}\\",\\"currentPage\\":\\"Current Page, Page {0}\\"}},\\"rating\\":{\\"ariaLabel\\":{\\"icon\\":\\"Rating {0} of {1}\\"}}}}"`;

exports[`$vuetify.presets should merge user and default preset 4`] = `"{\\"dark\\":{\\"primary\\":\\"#2196F3\\",\\"secondary\\":\\"#424242\\",\\"accent\\":\\"#FF4081\\",\\"error\\":\\"#FF5252\\",\\"info\\":\\"#2196F3\\",\\"success\\":\\"#4CAF50\\",\\"warning\\":\\"#FB8C00\\"},\\"light\\":{\\"primary\\":\\"blue\\",\\"secondary\\":{\\"darken4\\":\\"red\\"},\\"accent\\":\\"#82B1FF\\",\\"error\\":\\"#FF5252\\",\\"info\\":\\"#2196F3\\",\\"success\\":\\"#4CAF50\\",\\"warning\\":\\"#FB8C00\\"}}"`;

exports[`$vuetify.presets should merge user and default preset 5`] = `"{\\"xs\\":200,\\"sm\\":960,\\"md\\":1280,\\"lg\\":1920}"`;
