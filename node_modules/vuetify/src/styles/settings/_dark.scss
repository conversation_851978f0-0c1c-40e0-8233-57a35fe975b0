@import '../tools/_functions.sass';

$material-dark-elevation-colors: () !default;
// https://material.io/design/color/dark-theme.html#properties
// https://material.io/design/environment/elevation.html#default-elevations
$material-dark-elevation-colors: map-deep-merge(
  (
    '0': #000000,
    '1': #1E1E1E,
    '2': #222222,
    '3': #252525,
    '4': #272727,
    '6': #2C2C2C,
    '8': #2E2E2E,
    '12': #333333,
    '16': #363636,
    '24': #373737
  ),
  $material-dark-elevation-colors
);

$material-dark: () !default;
$material-dark: map-deep-merge(
  (
    'status-bar': (
      'regular': map-get($shades, 'black'),
      'lights-out': rgba(map-get($shades, 'black'), 0.2)
    ),
    'app-bar': map-get($material-dark-elevation-colors, '4'),
    'background': #121212,
    'bottom-navigation': map-get($material-dark-elevation-colors, '8'),
    'surface': #121212,
    'calendar': (
      'background-color': #303030,
      'outside-background-color': #202020,
      'weeknumber-background-color': #202020,
      'line-color': map-get($grey, 'base'),
      'interval-color': map-get($grey, 'lighten-3'),
      'interval-line-color': map-get($grey, 'darken-2'),
      'text-color': map-get($shades, 'white'),
      'past-color': rgba(map-get($shades, 'white'), .50)
      ),
    'cards': map-get($material-dark-elevation-colors, '1'),
    'chips': #555,
    'code': (
      'background': rgba(map-get($shades, 'white'), 0.1),
      'color': currentColor
    ),
    'kbd': (
      'background': #212529,
      'color': map-get($shades, 'white')
    ),
    'dividers': rgba(map-get($shades, 'white'), 0.12),
    'text': (
      'theme': map-get($shades, 'white'),
      'primary': map-get($shades, 'white'),
      'secondary': rgba(map-get($shades, 'white'), 0.7),
      'disabled': rgba(map-get($shades, 'white'), 0.5),
      'link': map-get($blue, 'accent-1'),
      'link-hover': map-get($grey, 'lighten-3')
    ),
    'icons': (
      'active': map-get($shades, 'white'),
      'inactive': rgba(map-get($shades, 'white'), 0.5)
    ),
    'inputs': (
      'box': map-get($shades, 'white'),
      'solo-inverted': rgba(map-get($shades, 'white'), 0.16),
      'solo-inverted-focused': map-get($shades, 'white'),
      'solo-inverted-focused-label': rgba(map-get($shades, 'black'), .6),
      'solo-inverted-focused-placeholder': rgba(map-get($shades, 'black'), 0.38),
      'solo-inverted-focused-text': rgba(map-get($shades, 'black'), 0.87)
    ),
    'buttons': (
      'disabled': rgba(map-get($shades, 'white'), 0.3),
      'focused': rgba(map-get($shades, 'white'), 0.12),
      'focused-alt': rgba(map-get($shades, 'white'), 0.1),
      'pressed': rgba(#ccc, 0.25)
    ),
    'expansion-panels': (
      'focus': #494949
    ),
    'navigation-drawer': map-get($material-dark-elevation-colors, '16'),
    'selection-controls': (
      'disabled': rgba(map-get($shades, 'white'), 0.3),
      'thumb': (
        'inactive': map-get($grey, 'lighten-1'),
        'disabled': map-get($grey, 'darken-3')
      ),
      'track': (
        'inactive': rgba(map-get($shades, 'white'), 0.3),
        'disabled': rgba(map-get($shades, 'white'), 0.1)
      )
    ),
    'slider': (
      'active': rgba(map-get($shades, 'white'), 0.3),
      'inactive': rgba(map-get($shades, 'white'), 0.2),
      'disabled': rgba(map-get($shades, 'white'), 0.2),
      'discrete': map-get($shades, 'white')
    ),
    'skeleton': linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, .05), rgba(255, 255, 255, 0)),
    'states': (
      'hover': 0.08,
      'focus': 0.24,
      'selected': 0.16,
      'activated': 0.24,
      'pressed': 0.32,
      'dragged': 0.16
    ),
    'tabs': rgba(map-get($shades, 'white'), 0.6),
    'toggle-buttons': (
      'color': map-get($shades, 'white')
    ),
    'text-fields': (
      'filled': rgba(map-get($shades, 'white'), 0.08),
      'filled-hover': rgba(map-get($shades, 'white'), 0.16),
      'outlined': rgba(map-get($shades, 'white'), 0.24),
      'outlined-disabled': rgba(map-get($shades, 'white'), 0.16),
      'outlined-hover': map-get($shades, 'white')
    ),
    'input-bottom-line': rgba(map-get($shades, 'white'), 0.7),
    'stepper': (
      'active': rgba(map-get($shades, 'white'), 1),
      'completed': rgba(map-get($shades, 'white'), 0.87),
      'hover': rgba(map-get($shades, 'white'), 0.75)
    ),
    'table': (
      'active': #505050,
      'hover': map-get($grey, 'darken-2'),
      'group': map-get($grey, 'darken-2')
    ),
    'toolbar': map-get($material-dark-elevation-colors, '4'),
    'picker': (
      'body': map-get($grey, 'darken-3'),
      'clock': map-get($grey, 'darken-2'),
      'indeterminateTime': map-get($grey, 'darken-1'),
      'title': map-get($grey, 'darken-2')
    ),
    'color-picker': (
      'checkerboard': rgba(map-get($shades, 'white'), 0.12)
    ),
    'bg-color': #303030,
    'fg-color': map-get($shades, 'white'),
    'text-color': map-get($shades, 'white'),
    'primary-text-percent': 1,
    'secondary-text-percent': 0.7,
    'disabledORhints-text-percent': 0.5,
    'divider-percent': 0.12,
    'active-icon-percent': 1,
    'inactive-icon-percent': 0.5
  ),
  $material-dark
);
