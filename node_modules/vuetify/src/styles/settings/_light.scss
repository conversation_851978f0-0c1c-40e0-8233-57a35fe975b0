@import '../tools/_functions.sass';

$material-light: () !default;
$material-light: map-deep-merge(
  (
    'status-bar': (
      'regular': map-get($grey, 'lighten-2'),
      'lights-out': rgba(map-get($shades, 'white'), 0.7)
    ),
    'app-bar': map-get($grey, 'lighten-4'),
    'background': map-get($shades, 'white'),
    'bottom-navigation': map-get($shades, 'white'),
    'surface': map-get($shades, 'white'),
    'calendar': (
      'background-color': map-get($shades, 'white'),
      'outside-background-color': #f7f7f7,
      'weeknumber-background-color': #f1f3f4,
      'line-color': map-get($grey, 'lighten-2'),
      'interval-color': map-get($grey, 'darken-3'),
      'interval-line-color': map-get($grey, 'lighten-2'),
      'text-color': map-get($shades, 'black'),
      'past-color': rgba(map-get($shades, 'black'), .38)
    ),
    'cards': map-get($shades, 'white'),
    'chips': #e0e0e0,
    'code': (
      'background': rgba(map-get($shades, 'black'), 0.05),
      'color': currentColor
    ),
    'kbd': (
      'background': #212529,
      'color': map-get($shades, 'white')
    ),
    'dividers': rgba(map-get($shades, 'black'), 0.12),
    'text': (
      'theme': map-get($shades, 'white'),
      'primary': rgba(map-get($shades, 'black'), 0.87),
      'secondary': rgba(map-get($shades, 'black'), 0.6),
      'disabled': rgba(map-get($shades, 'black'), 0.38),
      'link': map-get($blue, 'darken-2'),
      'link-hover': map-get($grey, 'darken-3')
    ),
    'icons': (
      'active': rgba(map-get($shades, 'black'), 0.54),
      'inactive': rgba(map-get($shades, 'black'), 0.38)
    ),
    'inputs': (
      'box': rgba(map-get($shades, 'black'), 0.04),
      'solo-inverted': rgba(map-get($shades, 'black'), 0.06),
      'solo-inverted-focused': map-get($grey, 'darken-3'),
      'solo-inverted-focused-label': rgba(map-get($shades, 'white'), .7),
      'solo-inverted-focused-placeholder': rgba(map-get($shades, 'white'), 0.5),
      'solo-inverted-focused-text': map-get($shades, 'white')
    ),
    'buttons': (
      'disabled': rgba(map-get($shades, 'black'), 0.26),
      'focused': rgba(map-get($shades, 'black'), 0.12),
      'focused-alt': rgba(map-get($shades, 'white'), 0.6),
      'pressed': rgba(#999, 0.4)
    ),
    'expansion-panels': (
      'focus': map-get($grey, 'lighten-3')
    ),
    'navigation-drawer': map-get($shades, 'white'),
    'selection-controls': (
      'disabled': rgba(map-get($shades, 'black'), 0.26),
      'thumb': (
        'inactive': map-get($shades, 'white'),
        'disabled': map-get($grey, 'lighten-5')
      ),
      'track': (
        'inactive': rgba(map-get($shades, 'black'), 0.38),
        'disabled': rgba(map-get($shades, 'black'), 0.12)
      )
    ),
    'slider': (
      'active': rgba(map-get($shades, 'black'), 0.38),
      'inactive': rgba(map-get($shades, 'black'), 0.26),
      'disabled': rgba(map-get($shades, 'black'), 0.26),
      'discrete': map-get($shades, 'black')
    ),
    'skeleton': linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, .3), rgba(255, 255, 255, 0)),
    'states': (
      'hover': 0.04,
      'focus': 0.12,
      'selected': 0.08,
      'activated': 0.12,
      'pressed': 0.16,
      'dragged': 0.08
    ),
    'tabs': rgba(0, 0, 0, 0.54),
    'toggle-buttons': (
      'color': map-get($shades, 'black')
    ),
    'text-fields': (
      'filled': rgba(map-get($shades, 'black'), 0.06),
      'filled-hover': rgba(map-get($shades, 'black'), 0.12),
      'outlined': rgba(map-get($shades, 'black'), 0.38),
      'outlined-disabled': rgba(map-get($shades, 'black'), 0.26),
      'outlined-hover': rgba(map-get($shades, 'black'), 0.86)
    ),
    'toolbar': map-get($shades, 'white'),
    'input-bottom-line': rgba(map-get($shades, 'black'), 0.42),
    'stepper': (
      'active': rgba(map-get($shades, 'white'), 1),
      'completed': rgba(map-get($shades, 'black'), 0.87),
      'hover': rgba(map-get($shades, 'black'), 0.54)
    ),
    'table': (
      'active': map-get($grey, 'lighten-4'),
      'hover': map-get($grey, 'lighten-3'),
      'group': map-get($grey, 'lighten-3')
    ),
    'picker': (
      'body': map-get($shades, 'white'),
      'clock': map-get($grey, 'lighten-2'),
      'indeterminateTime': map-get($grey, 'lighten-1'),
      'title': map-get($grey, 'lighten-2')
    ),
    'color-picker': (
      'checkerboard': rgba(map-get($shades, 'white'), 0)
    ),
    'bg-color': map-get($shades, 'white'),
    'fg-color': map-get($shades, 'black'),
    'text-color': map-get($shades, 'black'),
    'primary-text-percent': 0.87,
    'secondary-text-percent': 0.6,
    'disabledORhints-text-percent': 0.38,
    'divider-percent': 0.12,
    'active-icon-percent': 0.54,
    'inactive-icon-percent': 0.38
  ),
  $material-light
);
