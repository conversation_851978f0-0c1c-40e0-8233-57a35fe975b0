@import '../tools/_functions.sass';

$color-pack: true !default;

$body-font-family: 'Roboto', sans-serif !default;
$font-size-root: 16px !default;
$line-height-root: 1.5 !default;
$border-radius-root: 4px !default;

$rounded: () !default;
$rounded: map-deep-merge(
  (
    0: 0,
    'sm': $border-radius-root / 2,
    null: $border-radius-root,
    'lg': $border-radius-root * 2,
    'xl': $border-radius-root * 6,
    'pill': 9999px,
    'circle': 50%
  ),
  $rounded
);

$spacer: 4px !default;
$spacers-steps: 16 !default; 

$spacers: () !default;
@if (type-of($spacers) == list) {
  @for $i from 0 through $spacers-steps {
    $spacers: map-merge($spacers, ($i: $spacer * $i))
  }
}

$negative-spacers: () !default;
@if (type-of($negative-spacers) == list) {
  @for $i from 1 through $spacers-steps {
    $negative-spacers: map-merge($negative-spacers, ("n" + $i: -$spacer * $i))
  }
}

$grid-breakpoints: () !default;
$grid-breakpoints: map-deep-merge(
  (
    'xs': 0,
    'sm': 600px,
    'md': 960px,
    'lg': 1280px - 16px,
    'xl': 1920px - 16px
  ),
  $grid-breakpoints
);

$grid-gutter: $spacer * 6 !default;
$form-grid-gutter: $spacer * 2 !default;
$grid-columns: 12 !default;

$container-padding-x: $grid-gutter / 2 !default;

$grid-gutters: () !default;
$grid-gutters: map-deep-merge(
  (
    'xs': $grid-gutter / 12,
    'sm': $grid-gutter / 6,
    'md': $grid-gutter / 3,
    'lg': $grid-gutter * 2/3,
    'xl': $grid-gutter
  ),
  $grid-gutters
);

$container-max-widths: () !default;
$container-max-widths: map-deep-merge(
  (
    'md': map-get($grid-breakpoints, 'md') * 0.9375,
    'lg': map-get($grid-breakpoints, 'lg') * 0.9375,
    'xl': map-get($grid-breakpoints, 'xl') * 0.9375
  ),
  $container-max-widths
);

$display-breakpoints: () !default;
$display-breakpoints: map-deep-merge(
  (
    'print-only': 'only print',
    'screen-only': 'only screen',
    'xs-only': 'only screen and (max-width: #{map-get($grid-breakpoints, 'sm') - 0.02})',
    'sm-only': 'only screen and (min-width: #{map-get($grid-breakpoints, 'sm')}) and (max-width: #{map-get($grid-breakpoints, 'md') - 0.02})',
    'sm-and-down': 'only screen and (max-width: #{map-get($grid-breakpoints, 'md') - 0.02})',
    'sm-and-up': 'only screen and (min-width: #{map-get($grid-breakpoints, 'sm')})',
    'md-only': 'only screen and (min-width: #{map-get($grid-breakpoints, 'md')}) and (max-width: #{map-get($grid-breakpoints, 'lg') - 0.02})',
    'md-and-down': 'only screen and (max-width: #{map-get($grid-breakpoints, 'lg') - 0.02})',
    'md-and-up': 'only screen and (min-width: #{map-get($grid-breakpoints, 'md')})',
    'lg-only': 'only screen and (min-width: #{map-get($grid-breakpoints, 'lg')}) and (max-width: #{map-get($grid-breakpoints, 'xl') - 0.02})',
    'lg-and-down': 'only screen and (max-width: #{map-get($grid-breakpoints, 'xl') - 0.02})',
    'lg-and-up': 'only screen and (min-width: #{map-get($grid-breakpoints, 'lg')})',
    'xl-only': 'only screen and (min-width: #{map-get($grid-breakpoints, 'xl')})'
  ),
  $display-breakpoints
);

$font-weights: () !default;
$font-weights: map-deep-merge(
  (
    'thin': 100,
    'light': 300,
    'regular': 400,
    'medium': 500,
    'bold': 700,
    'black': 900
  ),
  $font-weights
);

$heading-font-family: $body-font-family !default;

$headings: () !default;
$headings: map-deep-merge(
  (
    'h1': (
      'size': 6rem,
      'weight': 300,
      'line-height': 6rem,
      'letter-spacing': -.015625em,
      'font-family': $heading-font-family,
      'text-transform': false
    ),
    'h2': (
      'size': 3.75rem,
      'weight': 300,
      'line-height': 3.75rem,
      'letter-spacing': -.0083333333em,
      'font-family': $heading-font-family,
      'text-transform': false
    ),
    'h3': (
      'size': 3rem,
      'weight': 400,
      'line-height': 3.125rem,
      'letter-spacing': normal,
      'font-family': $heading-font-family,
      'text-transform': false
    ),
    'h4': (
      'size': 2.125rem,
      'weight': 400,
      'line-height': 2.5rem,
      'letter-spacing': .0073529412em,
      'font-family': $heading-font-family,
      'text-transform': false
    ),
    'h5': (
      'size': 1.5rem,
      'weight': 400,
      'line-height': 2rem,
      'letter-spacing': normal,
      'font-family': $heading-font-family,
      'text-transform': false
    ),
    'h6': (
      'size': 1.25rem,
      'weight': 500,
      'line-height': 2rem,
      'letter-spacing': .0125em,
      'font-family': $heading-font-family,
      'text-transform': false
    ),
    'subtitle-1': (
      'size': 1rem,
      'weight': normal,
      'line-height': 1.75rem,
      'letter-spacing': .009375em,
      'font-family': $body-font-family,
      'text-transform': false
    ),
    'subtitle-2': (
      'size': .875rem,
      'weight': 500,
      'line-height': 1.375rem,
      'letter-spacing': .0071428571em,
      'font-family': $body-font-family,
      'text-transform': false
    ),
    'body-1': (
      'size': 1rem,
      'weight': 400,
      'line-height': 1.5rem,
      'letter-spacing': .03125em,
      'font-family': $body-font-family,
      'text-transform': false
    ),
    'body-2': (
      'size': .875rem,
      'weight': 400,
      'line-height': 1.25rem,
      'letter-spacing': .0178571429em,
      'font-family': $body-font-family,
      'text-transform': false
    ),
    'button': (
      'size': .875rem,
      'weight': 500,
      'line-height': 2.25rem,
      'letter-spacing': .0892857143em,
      'font-family': $body-font-family,
      'text-transform': uppercase
    ),
    'caption': (
      'size': .75rem,
      'weight': 400,
      'line-height': 1.25rem,
      'letter-spacing': .0333333333em,
      'font-family': $body-font-family,
      'text-transform': false
    ),
    'overline': (
      'size': .75rem,
      'weight': 500,
      'line-height': 2rem,
      'letter-spacing': .1666666667em,
      'font-family': $body-font-family,
      'text-transform': uppercase
    )
  ),
  $headings
);

$typography: () !default;
@each $type, $values in $headings {
  $typography: map-deep-merge(
    $typography,
    (#{$type}: map-values($values))
  );
}

$transition: () !default;
$transition: map-deep-merge(
  (
    'fast-out-slow-in': cubic-bezier(0.4, 0, 0.2, 1),
    'linear-out-slow-in': cubic-bezier(0, 0, 0.2, 1),
    'fast-out-linear-in': cubic-bezier(0.4, 0, 1, 1),
    'ease-in-out': cubic-bezier(0.4, 0, 0.6, 1),
    'fast-in-fast-out': cubic-bezier(0.25, 0.8, 0.25, 1),
    'swing': cubic-bezier(0.25, 0.8, 0.5, 1)
  ),
  $transition
);
$primary-transition: 0.3s map-get($transition, 'swing') !default;
$secondary-transition: 0.2s map-get($transition, 'ease-in-out') !default;

// Ripples //;
$ripple-animation-transition-in: transform 0.25s map-get($transition, 'fast-out-slow-in'), opacity 0.1s map-get($transition, 'fast-out-slow-in') !default;
$ripple-animation-transition-out: opacity 0.3s map-get($transition, 'fast-out-slow-in') !default;
$ripple-animation-visible-opacity: 0.25 !default;

// Elements //;
$bootable-transition: 0.2s map-get($transition, 'fast-out-slow-in') !default;
$blockquote-font-size: 18px !default;
$blockquote-font-weight: 300 !default;
$code-kbd-border-radius: 3px !default;
$code-kbd-font-size: 85% !default;
$code-kbd-font-weight: normal !default;
$code-padding: .2em .4em !default;
$kbd-padding: .2em .4rem !default;
$input-top-spacing: 16px !default;
$text-field-active-label-height: 12px !default;
